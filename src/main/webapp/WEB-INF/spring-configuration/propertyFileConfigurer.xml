<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to <PERSON><PERSON>g under one or more contributor license
    agreements. See the NOTICE file distributed with this work
    for additional information regarding copyright ownership.
    Jasig licenses this file to you under the Apache License,
    Version 2.0 (the "License"); you may not use this file
    except in compliance with the License.  You may obtain a
    copy of the License at the following location:

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
	<description>
		This file lets CAS know where you've stored the cas.properties file which details some of the configuration options
		that are specific to your environment.  You can specify the location of the file here.  You may wish to place the file outside
		of the Servlet context if you have options that are specific to a tier (i.e. test vs. production) so that the WAR file 
		can be moved between tiers without modification.
	</description>

    <util:properties id="casProperties" location="classpath:cas.properties"/>

    <context:property-placeholder location="classpath:*.properties" ignore-unresolvable="true" order="2"/>
</beans>