<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <alias name="personDirectoryPrincipalResolver" alias="primaryPrincipalResolver" />

    <!--多认证器的配置-->
    <util:map id="authenticationHandlersResolvers">
        <entry key-ref="proxyAuthenticationHandler" value-ref="proxyPrincipalResolver" />
        <entry key-ref="primaryAuthenticationHandler" value-ref="primaryPrincipalResolver" />
        <entry key-ref="wxAuthenticationHandler" value-ref="primaryPrincipalResolver"/>
        <entry key-ref="adminAuthenticationHandler" value-ref="primaryPrincipalResolver"/>
    </util:map>
    <alias name="anyAuthenticationPolicy" alias="authenticationPolicy" />
    <alias name="dataSource" alias="queryDatabaseDataSource"/>
    <alias name="shiroAuthenticationHandler" alias="primaryAuthenticationHandler" />
    <util:list id="authenticationMetadataPopulators">
        <ref bean="successfulHandlerMetaDataPopulator" />
        <ref bean="rememberMeAuthenticationMetaDataPopulator" />
    </util:list>
    <!--<bean id="attributeRepository" class="org.jasig.services.persondir.support.NamedStubPersonAttributeDao"-->
          <!--p:backingMap-ref="attrRepoBackingMap" />-->
    <alias name="singleRowJdbcPersonAttributeDao" alias="attributeRepository"/>

    <util:map id="attrRepoBackingMap">
        <entry key="uid" value="uid" />
        <entry key="eduPersonAffiliation" value="eduPersonAffiliation" />
        <entry key="groupMembership" value="groupMembership" />
        <entry>
            <key><value>memberOf</value></key>
            <list>
                <value>faculty</value>
                <value>staff</value>
                <value>org</value>
            </list>
        </entry>
    </util:map>

    <alias name="serviceThemeResolver" alias="themeResolver" />

    <alias name="dataSource" alias="dataSourceService"/>
    <alias name="redisServiceRegistryDao" alias="serviceRegistryDao" />

    <alias name="redisTicketRegistry" alias="ticketRegistry" />

    <alias name="defaultTcgCookie" alias="ticketGrantingTicketCookieGenerator"/>
    <alias name="ticketGrantingTicketExpirationPolicy" alias="grantingTicketExpirationPolicy" />
    <alias name="multiTimeUseOrTimeoutExpirationPolicy" alias="serviceTicketExpirationPolicy" />
    <alias name="acceptAnyAuthenticationPolicyFactory" alias="authenticationPolicyFactory" />

    <alias name="redisAuthenticationThrottle" alias="authenticationThrottle" />
    <import resource="classpath:inspektr-throttle-jdbc-config.xml"/>
    <util:list id="monitorsList">
        <ref bean="memoryMonitor" />
        <ref bean="sessionMonitor" />
    </util:list>
    <alias name="ticketGrantingTicketsCache" alias="ehcacheMonitorCache" />
    <alias name="defaultPrincipalFactory" alias="principalFactory" />
    <alias name="defaultAuthenticationTransactionManager" alias="authenticationTransactionManager" />
    <alias name="defaultPrincipalElectionStrategy" alias="principalElectionStrategy" />
    <alias name="noIpCasCookieValueManager" alias="defaultCookieValueManager"/>
    <alias name="tgcCipherExecutor" alias="defaultCookieCipherExecutor" />
</beans>
