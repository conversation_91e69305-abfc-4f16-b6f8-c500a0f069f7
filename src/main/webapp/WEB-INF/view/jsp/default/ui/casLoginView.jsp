<!DOCTYPE html>
<html lang="zh-cn">
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<head>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="UTF-8">
    <title>豫情通鉴</title>
    <%@ include file="/WEB-INF/view/jsp/taglib.jsp" %>
    <link rel="stylesheet" href="${ctxResource}/layui/css/layui.css"/>

    <script src="${ctxResource}/js/jquery.min.js"></script>
    <script src="${ctxResource}/layui/layui.js"></script>

    <meta http-equiv="refresh" content="300">
    <link rel="shortcut icon" href="/favicon.ico?v=20220620">
    <link rel="stylesheet" href="${ctxResource}/css/login.css">
</head>

<body>
<div class="layui-inline">
    <%--
    <img src="${ctxResource}/img/login_logo2.png" class="logo fl"/>
    <div class="logoTitle fl">豫情通鉴</div>
    --%>
</div>

<div class="login-box">
    <div class="welcome">欢迎登录</div>
    <div class="form">
        <div>
            <form id="fm1" class="layui-form" action="/login?service=${param.service}" method="post">
                <div class="layui-form-item mt23">
                    <label class="layui-form-label">
                        <img src="${ctxResource}/img/phone.png" alt="" class="phone_pic">
                    </label>
                    <div class="layui-input-inline phoneLine">
                        <input type="number" oninput="value = value.replace(/[^\d]/g, '')" onKeypress="return(/[\d\.]/.test(String.fromCharCode(event.keyCode)))" autocomplete="off" lay-verify="required" placeholder="请输入手机号" name="username" id="username"
                               class="layui-input fl">
                        <div id="yzm_text" onselectstart="return false" class="get-verify fr">获取验证码</div>
                    </div>
                </div>

                <div class="verify mt28">
                    <div class="layui-form-item">
                        <label class="layui-form-label">
                            <img src="${ctxResource}/img/yzm_icon.png" alt="" class="yzm_pic">
                        </label>
                        <div class="layui-input-inline">
                            <input type="tel" oninput="value = value.replace(/[^\d]/g, '')" onKeypress="return(/[\d\.]/.test(String.fromCharCode(event.keyCode)))" autocomplete="off" lay-verify="required" placeholder="请输入验证码" id="password"
                                   name="password" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="verify mt28">
                    <div class="layui-form-item fl verify-input-width">
                        <div class="layui-input-inline">
                            <input type="text" name="imageCode" autocomplete="off" placeholder="请输入验证码" id="imgCode"
                                   class="layui-input">
                        </div>
                    </div>
                    <div class="fr mr10">
                        <img id="yzm_pic" class="yzm_text veryCode changeImg" src="/dahe/pub/getVerifyCodeImage?i=" + Math.random()>
                    </div>
                </div>

                <div class="cl"></div>
                <div class="login-button">
                    <input type="hidden" name="execution" value="${flowExecutionKey}"/>
                    <input type="hidden" name="_eventId" value="submit"/>
                    <button class="login-btn layui-btn layui-btn-normal layui-btn-fluid" type="submit" name="submit" lay-submit
                            lay-filter="formDemo" id="loginBtn">登&nbsp;&nbsp;录
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="${ctxResource}/js/jquery.min.js"></script>
<script type="text/javascript" src="${ctxResource}/js/login.js?md5=64dee7ab2e53f160288ff92f9257280f"></script>
</html>
