<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/WEB-INF/view/jsp/taglib.jsp" %>
<%--
  Created by IntelliJ IDEA.
  User: kitty_tiger
  Date: 2019/7/8
  Time: 9:31
  To change this template use File | Settings | File Templates.
--%>
<script>
    /**
     * 以下逻辑在在弹出框的条件下才执行
     */
        //如果发生错误，根据错误类型进行不同处理
        <c:if test="${flowRequestContext.flowScope.get('dialog') ne null&&flowRequestContext.flowScope.get('dialog') eq true}">

        <c:if test="${flowRequestContext.flowScope.get('errorCode') ne null}">
    var code = ${flowRequestContext.flowScope.get("errorCode")};
    parent.${flowRequestContext.flowScope.get("jsCallback")}(${flowRequestContext.flowScope.get("errorCode")});
    </c:if>
    //如果无错误发生且可以获取到redirectUrl,则返回跳转url
    <c:if test="${flowRequestContext.requestScope.get('response') ne null&&flowRequestContext.requestScope.get('response').url ne null}">
      window.location = '/home';
    </c:if>
    </c:if>
    <c:if test="${flowRequestContext.flowScope.get('dialog') eq null||flowRequestContext.flowScope.get('dialog') eq false}">
    //如果无错误发生且可以获取到redirectUrl,则返回跳转url
    <c:if test="${flowRequestContext.requestScope.get('response') ne null&&flowRequestContext.requestScope.get('response').url ne null}">
    window.location = '${flowRequestContext.requestScope.get('response').url}';
    </c:if>
    <c:if test="${flowRequestContext.requestScope.get('response') eq null}">
    window.location = '/home';
    </c:if>
    </c:if>
    /**
     * 以下逻辑在非弹窗下进行
     */
    //如果发生错误，不予处理
    //如果未发生错误，且有redirectUrl，进行正常跳转

</script>