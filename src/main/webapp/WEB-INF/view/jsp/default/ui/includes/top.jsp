<!DOCTYPE html>

<%@ page pageEncoding="UTF-8" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<c:set var="ctxResource" value="${pageContext.request.contextPath}/resources"/>

<html lang="en">
<head>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

  <meta charset="UTF-8">

    <title>豫情通用户中心</title>

    <meta name="_csrf" content="${_csrf.token}"/>
    <meta name="_csrf_header" content="${_csrf.headerName}"/>


    <spring:theme code="standard.custom.css.file" var="customCssFile" />
    <%--<link rel="stylesheet" href="<c:url value="${customCssFile}" />" />--%>
    <link rel="icon" href="<c:url value="/favicon.ico?v=20220620" />" type="image/x-icon" />
  <link rel="stylesheet" href="${ctxResource}/theme/css/pintuer.css">
  <link rel="stylesheet" href="${ctxResource}/theme/css/style.css">

</head>
<body id="cas">

<div id="container">
  <header>
  </header>
  <div id="content">
