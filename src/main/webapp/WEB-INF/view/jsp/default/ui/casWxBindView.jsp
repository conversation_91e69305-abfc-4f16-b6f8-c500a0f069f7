<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

    <meta charset="UTF-8">
    <title>豫情通用户中心</title>
    <%@ include file="/WEB-INF/view/jsp/taglib.jsp" %>
    <meta http-equiv="refresh" content="300">
    <link rel="shortcut icon" href="/favicon.ico?v=20220620">
    <link rel="stylesheet" href="${ctxResource}/css/login.css">
</head>
<body style="height: 100%;">
<div class="bb-box">
    <div id="bg" class="login-box">
        <h1>豫情通用户中心</h1>
        <div class="swiper form-box">
            <form:form method="post" id="fm1" htmlEscape="true"
                       class="layui-form login">
                <p class='login-title'>绑定账号<span class="tip-bind">${flowRequestContext.viewScope.get("bindError")}</span></p>
                <form:errors path="*" id="msg" cssClass="errors" element="div" htmlEscape="false"/>

                <div class="layui-form-item">
                    <div class="login-input phone">
                        <i class="login-icon phone-icon"></i>
                        <input type="tel" autocomplete="off" lay-verify="required|phone" placeholder="请输入您的手机号"
                               name="phone" id="phone" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item cl">
                    <div class="login-input yzm-input verify input fl">
                        <i class="login-icon yzm-icon"></i>
                        <input type="text" autocomplete="off" lay-verify="required" placeholder="请输入验证码" id="password"
                               name="smsCode" class="layui-input">
                    </div>
                    <div class="yzm-btn fr">
                        <a class="get-verify" onselectstart="return false">获取验证码</a>
                    </div>
                </div>
                <div class="login-button">
                    <input type="hidden" name="execution" value="${flowExecutionKey}"/>
                    <input type="hidden" name="_eventId" value="submit"/>
                    <button class="login-btn" type="submit" name="submit" lay-submit lay-filter="formDemo" id="loginBtn">绑定
                    </button>
                        <%--<a class="login-btn" href="javascript:;" id="loginBtn">登录</a>--%>
                </div>
            </form:form>
        </div>

    </div>
    <div id="pop-up"></div>
    <div class="login-footer">
        <P class='copyright'>版权所有：河南大河网数字科技有限公司</P>
        <P>技术支持：<a href="https://www.dahe.cn" target="_blank">大河网</a></P>
    </div>
</div>
</body>

<script type="text/javascript" src="${ctxResource}/js/jquery.min.js"></script>
<script type="text/javascript" src="${ctxResource}/js/bind_wx.js"></script>
</html>
<script>
    layui.use(['form', 'layedit', 'laydate'], function() {
        var form = layui.form
            , layer = layui.layer
        form.on('submit(formDemo)', function(data){


        });
    })
</script>
