<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
  <title>登录助手</title>
  <!-- 引入jQuery -->
  <script src="/resources/js/jquery-3.2.1.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
    }
    .loading {
      text-align: center;
      color: #666;
      padding: 20px;
    }
    .error {
      color: #d32f2f;
      background-color: #ffebee;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .success {
      color: #388e3c;
      background-color: #e8f5e8;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f8f9fa;
      font-weight: bold;
      color: #333;
    }
    tr:hover {
      background-color: #f8f9fa;
    }
    .btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.3s;
    }
    .btn-danger {
      background-color: #dc3545;
      color: white;
    }
    .btn-danger:hover {
      background-color: #c82333;
    }
    .btn-refresh {
      background-color: #007bff;
      color: white;
      margin-bottom: 20px;
    }
    .btn-refresh:hover {
      background-color: #0056b3;
    }
    .no-data {
      text-align: center;
      color: #666;
      padding: 40px;
      font-style: italic;
    }
  </style>
</head>
<body>
<div class="container">
  <h1>账号解禁管理</h1>

  <button class="btn btn-refresh">刷新数据</button>

  <div id="messageArea"></div>

  <div id="loadingArea" class="loading" style="display: none;">
    正在加载数据...
  </div>

  <div id="dataArea">
    <table id="dataTable" style="display: none;">
      <thead>
      <tr>
        <th>账号信息</th>
        <th>操作</th>
      </tr>
      </thead>
      <tbody id="dataTableBody">
      </tbody>
    </table>

    <div id="noDataMessage" class="no-data" style="display: none;">
      暂无数据
    </div>
  </div>
</div>
<br/>
<div class="container">
  <h1>验证码查验表</h1>
  <button class="btn btn-refresh">刷新数据</button>
  <ul id="smscodes">
  </ul>
</div>
<script>
  var tableBody = $('#dataTableBody');
  $(document).ready(function() {
    // 页面加载时获取数据
    loadData();

    // 刷新按钮点击事件
    $('.btn-refresh').click(function() {
      loadData();
    });
  });

  // 获取数据列表
  function loadData() {
    tableBody.empty();
    showLoading(true);
    hideMessage();
    //获取短信验证码锁定列表
    $.ajax({
      url: '/admin/cache/sso:access-limit:sms', // 根据实际API路径调整
      type: 'GET',
      dataType: 'json',
      success: function(response) {
        showLoading(false);
        if (response.success) {
          displayData(response.obj);
        } else {
          showMessage('获取数据失败: ' + (response.msg || '未知错误'), 'error');
        }
        //执行登录锁定列表
        $.ajax({
          url: '/admin/cache/listlockeduser', // 根据实际API路径调整
          type: 'GET',
          dataType: 'json',
          success: function(response) {
            showLoading(false);
            if (response.success) {
              displayData(response.obj.content);
            } else {
              showMessage('获取数据失败: ' + (response.msg || '未知错误'), 'error');
            }
          },
          error: function(xhr, status, error) {
            showLoading(false);
            showMessage('网络请求失败: ' + error, 'error');
          }
        });
      },
      error: function(xhr, status, error) {
        showLoading(false);
        showMessage('网络请求失败: ' + error, 'error');
      }
    });
  }

  // 显示数据
  function displayData(dataList) {
    if (!dataList || dataList.length === 0) {
      return;
    }

    // 遍历数据并生成表格行
    let ifToShow = 0;
    $.each(dataList, function(index, item) {
      if (item && !(typeof item === 'string')) {
        ifToShow++;
        var row = '<tr>' +
                '<td>' + ('禁止登录账号——'+item.phone || '') + '</td>' +
                '<td>' +
                '<button class="btn btn-danger delete-btn" data-id="' + item.uid + '">' +
                '解禁' +
                '</button>' +
                '</td>' +
                '</tr>';
        tableBody.append(row);
      } else if(item) {
        let tmp = extractPhoneNumberByRegex(item);
        if (tmp == null) {
          return true;
        }
        ifToShow++;
        var row = '<tr>' +
                '<td>' + ('禁止发送短信——'+ tmp || '') + '</td>' +
                '<td>' +
                '<button class="btn btn-danger delete-btn" data-id="' + item + '">' +
                '解禁' +
                '</button>' +
                '</td>' +
                '</tr>';
        tableBody.append(row);
      }
    });

    // 显示表格
    if (ifToShow > 0) {
      $('#dataTable').show();
    }
    $('#noDataMessage').hide();

    // 绑定删除按钮事件
    bindDeleteEvents();
  }

  // 方法1: 使用正则表达式匹配11位数字
  function extractPhoneNumberByRegex(input) {
    // 检查输入是否为字符串
    if (typeof input !== 'string') {
      console.error('错误: 输入不是字符串类型');
      return null;
    }
    // 检查字符串是否为空
    if (input.trim() === '') {
      console.error('错误: 输入字符串为空');
      return null;
    }
    // 使用 split 方法提取手机号
    const parts = input.split(':');
    // 检查是否成功分割
    if (parts.length === 0) {
      console.error('错误: 无法分割字符串');
      return null;
    }
    // 返回最后一个元素，即手机号
    return parts[parts.length - 1];
  }

  // 绑定删除按钮事件
  function bindDeleteEvents() {
    $('.delete-btn').off('click').on('click', function() {
      var id = $(this).data('id');
      var row = $(this).closest('tr');

      if (confirm('确定要解禁吗？')) {
        deleteData(id, row);
      }
    });
  }

  // 删除数据
  function deleteData(id, row) {
    // 禁用删除按钮防止重复点击
    row.find('.delete-btn').prop('disabled', true).text('解禁中...');

    $.ajax({
      url: '/admin/cache/clearlogindata?key=' + id, // 根据实际API路径调整
      type: 'GET', // 或者使用 POST，根据后端接口定义
      dataType: 'json',
      success: function(response) {
        if (response.success) {
          showMessage('解禁成功', 'success');
          // 刷新页面数据
          loadData();
        } else {
          showMessage('解禁失败: ' + (response.msg || '未知错误'), 'error');
          // 恢复按钮状态
          row.find('.delete-btn').prop('disabled', false).text('解禁');
        }
      },
      error: function(xhr, status, error) {
        showMessage('解禁失败: ' + error, 'error');
        // 恢复按钮状态
        row.find('.delete-btn').prop('disabled', false).text('解禁');
      }
    });
  }

  // 显示/隐藏加载状态
  function showLoading(show) {
    if (show) {
      $('#loadingArea').show();
      $('#dataTable').hide();
      $('#noDataMessage').hide();
    } else {
      $('#loadingArea').hide();
    }
  }

  // 显示无数据状态
  function showNoData() {
    $('#dataTable').hide();
    $('#noDataMessage').show();
  }

  // 显示消息
  function showMessage(message, type) {
    var className = type === 'error' ? 'error' : 'success';
    var messageHtml = '<div class="' + className + '">' + message + '</div>';
    $('#messageArea').html(messageHtml);

    // 3秒后自动隐藏消息
    setTimeout(function() {
      hideMessage();
    }, 3000);
  }

  // 隐藏消息
  function hideMessage() {
    $('#messageArea').empty();
  }


  //执行登录锁定列表
  $.ajax({
    url: '/admin/cache/smscodes', // 根据实际API路径调整
    type: 'GET',
    dataType: 'json',
    success: function(response) {
      let dataList = response.obj;
      $.each(dataList, function(index, item) {
        var row = '<li style="font-size: 23px;"><span style="font-weight: bold;">'+index+'</span>&nbsp;登录短信验证码是：&nbsp;<span style="font-weight: bold;">'+item+'<span/></li><br/>';
        $('#smscodes').append(row);
      });
    }
  });
</script>
</body>
</html>