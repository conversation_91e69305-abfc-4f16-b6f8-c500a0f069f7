<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="UTF-8">
    <title>豫情通管理平台</title>
    <%@ include file="/WEB-INF/view/jsp/taglib.jsp" %>
    <link rel="stylesheet" href="${ctxResource}/css/base.css">
    <link rel="stylesheet" href="${ctxResource}/css/jquery.Jcrop.css" type="text/css"/>
    <link rel="stylesheet" href="${ctxResource}/css/index.css">
    <style>
        .editimg {
            padding: 5px;
        }

        .editimg img {
            width: 100%;
        }

        .btns {
            text-align: center;
            margin-bottom: 5px;
        }

        .mr-10 {
            margin-right: 10px;
        }

        .sfLogin {
            padding-left: 40px;
        }

        .sfLogin li {
            width: 80px;
            height: 40px;
            float: left;
            margin-right: 20px;
            cursor: pointer;
        }
    </style>
</head>

<body>
<!-- 头部 -->
<div class="header">
    <div class="width-primary cl">
        <%--
           <a href="/home" class="logo fl">
               <img src="${ctxResource}/img/logo_home.png" title="豫情通管理平台" alt="豫情通管理平台">
           </a>
        --%>
        <span class="crumb fl">个人中心</span>
        <a href="/home/<USER>" class="logout fr">退出登录</a>
    </div>
</div>
<!-- content -->
<div class="content width-primary cl">

    <!-- 左侧部分 -->
    <div class="leftBox fl">
        <div class="userInfo">
            <div class="editicon">
                <img src="${user.icon}?w=100" alt="${user.truename}" title="${user.truename}" class="userIcon"
                     onclick="operation.editicon()">
            </div>
            <p class="userName">${unit}</p>
            <p class="userName">${user.truename}</p>
        </div>
        <div class="optionArea">
            <ul class="optionList">
                <li class="on index" onclick="operation.tableSwitch('#indexTalbe', this)">
                    <span class="icon"></span>首页
                </li>
<%--                <li class="message">--%>
<%--                    <span class="icon"></span>--%>
<%--                    <a href="${allmessage}" target="_blank">我的消息</a>--%>
<%--                    <c:if test="${count != 0}">--%>
<%--                        <span class="msg_num">${count}</span>--%>
<%--                    </c:if>--%>
<%--                </li>--%>
<%--                <li class="setting" onclick="operation.tableSwitch('#setTable', this)">--%>
<%--                    <span class="icon"></span>个人设置--%>
<%--                </li>--%>
                <%--用户中心--%>
                <c:if test="${admin == true}">
                    <li class="usercenter">
                        <span class="icon"></span><a href="/admin" target="_blank">用户中心</a>
                    </li>
                </c:if>
            </ul>
        </div>
    </div>

    <!-- 右侧部分 -->
    <div class="rightContent fl">
        <div class="rightBox rightIndex" id="indexTalbe">
            <!-- 天气 -->
            <div class="tianqi">
                <a class="weather"><span class="city"></span><span class="weatherimg"></span><span
                        class="temperature"></span><span class="winddirect"></span><span class="windpower"></span><span
                        class="windspeed"></span></a>
                <span class="nowtime"></span>
            </div>
            <div class="myApp">
                <h3 class="panelTitle myAppTitle">我的应用</h3>
                <div class="appArea">
                    <div class="appPage">
                        <c:forEach var="i" begin="0" end="${siteCount}">
                            <ul class="appList cl">
                                <c:forEach var="site" items="${site}" begin="${10*i}" end="${10*i+9}">
                                    <li class="fl">
                                        <a href="${site.indexUrl}" target="_blank">
                                            <img src="${site.imageUrl}" alt="${site.description}"
                                                 title="${site.description}">
                                            <p class="appName">${site.description}</p>
                                        </a>
                                    </li>
                                </c:forEach>
                            </ul>
                        </c:forEach>
                    </div>
                    <!-- 第二页应用 -->
                    <c:if test="${siteCount >0}">
                        <a class="prev" href="javascript:void(0)"></a>
                        <a class="next" href="javascript:void(0)"></a>
                    </c:if>
                    <ul class="hd">
                        <c:if test="${siteCount >0}">
                            <c:forEach var="i" begin="0" end="${siteCount}">
                                <li></li>
                            </c:forEach>
                        </c:if>
                    </ul>
                </div>
            </div>
<%--            <div class="rightBotttom">--%>
<%--                <div class="newsArea cl rightTable">--%>
<%--                    <div class="newPaper fl newsBox">--%>
<%--                        <h3 class="panelTitle newPaperTitle">最新稿件--%>
<%--                            &lt;%&ndash;<a href="" target="_blank" class="more fr">查看全部&gt;</a>&ndash;%&gt;--%>
<%--                        </h3>--%>
<%--                        <ul class="newsList"></ul>--%>
<%--                    </div>--%>
<%--                    <div class="myMsg fl newsBox">--%>
<%--                        <h3 class="panelTitle myMsg">我的消息--%>
<%--                            <a href="${allmessage}" target="_blank" class="more fr">查看全部&gt;</a>--%>
<%--                        </h3>--%>
<%--                        <ul class="newsList"></ul>--%>
<%--                    </div>--%>
<%--                </div>--%>
<%--            </div>--%>
        </div>
        <div class="rightBox rightSet hide" id="setTable">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>基本信息</legend>
            </fieldset>
            <form class="layui-form editeForm" action="">
                <!-- 登录名 -->
                <%--<div class="layui-form-item">
                  <label class="layui-form-label">登录名</label>
                  <div class="layui-input-block">
                    <input type="hidden" name="username" lay-verify="required" value="${user.username}" disabled class="layui-input">
                  </div>
                </div>--%>
                <!-- 姓名 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">姓名</label>
                    <div class="layui-input-block">
                        <input hidden name="uid" id="uid" value="${user.uid}">
                        <input type="hidden" name="username" lay-verify="required" value="${user.username}" disabled
                               class="layui-input">
                        <input type="text" name="truename" disabled value="${user.truename}" class="layui-input">
                    </div>
                </div>
                <!-- 性别 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">性别</label>
                    <div class="layui-input-block">
                        <input type="text" name="sex" lay-verify="required"
                               <c:if test="${user.sex.sex == 0}">value="男" </c:if>
                               <c:if test="${user.sex.sex == 1}">value="女" </c:if> disabled class="layui-input">
                    </div>
                </div>
                <!-- 部门 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">部门</label>
                    <div class="layui-input-block">
                        <input type="text" name="username" lay-verify="required" value="${depart}" disabled
                               class="layui-input">
                    </div>
                </div>
            </form>
            <!-- 修改手机号 -->
            <fieldset class="layui-elem-field layui-field-title">
                <legend>修改手机号</legend>
            </fieldset>
            <form class="layui-form editeForm" action="">
                <!-- 手机号 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">旧手机号</label>
                    <div class="layui-input-block">
                        <input hidden name="uid" value="${user.uid}">
                        <input hidden id="allmessage" value="${allmessage}">
                        <input type="tel" id="oldPhone" disabled value="${user.phone}" lay-verify="required|phone"
                               placeholder="请输入旧手机号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <!-- 新手机号 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">新手机号</label>
                    <div class="layui-input-block">
                        <input style="display: inline;width: 250px;" type="text" id="newphone" name="phone"
                               placeholder="请输入要更换的手机号" lay-verify="required|phone" value="" class="layui-input">
                        <button class="layui-btn getyzm" type="button" style="width: 100px;">获取验证码</button>
                    </div>
                </div>
                <!-- 验证码 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">验证码</label>
                    <div class="layui-input-block">
                        <input name="code" onkeyup="value=value.replace(/[^\d]/g,'')" lay-verify="required"
                               placeholder="请输入验证码" class="layui-input">
                    </div>
                </div>

                <!-- 提交 取消 -->
                <div class="layui-form-item btnGroup">
                    <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="submitPhone">提交</button>
                </div>
            </form>
            <fieldset class="layui-elem-field layui-field-title">
                <legend>第三方登录管理</legend>
            </fieldset>
            <ul class="sfLogin" id="sfLogin">

            </ul>
        </div>
        <div class="copyRight">
            <p>版权所有：河南大河网数字科技有限公司</p>
            <p> 技术支持：<a href="https://www.dahe.cn/" target="_blank">大河网</a></p>
        </div>
    </div>
</div>
<script src="${ctxResource}/js/jquery.SuperSlide.2.1.1.js" charset="utf-8"></script>
<script src="${ctxResource}/js/jquery.Jcrop.js"></script>
<script src="${ctxResource}/js/index.js" charset="utf-8"></script>
<script src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js" charset="utf-8"></script>
<script src="https://cdn.bootcss.com/mustache.js/3.0.1/mustache.js"></script>
<script id="demo" type="x-tmpl-mustache">
    {{#obj}}
    <li class="bind_li">
        <img style="width: 40px;height: 40px;display:block;margin:auto" data-binded="{{ binded }}" src="{{icon}}" alt="">
        <span style="width: 80px;height: 20px;display:block;margin:auto;text-align:center">{{tip}}</span>
    </li>
    {{/obj}}
</script>
<script>
    var socketUrl = "${socketUrl}";
    var uid = ${user.uid};
    function disPlayBindInfo() {
        layui.use(['laytpl', 'layer'], function () {
            var laytpl = layui.laytpl,
                layer = layui.layer;
            $.ajax({
                url: '/home/<USER>',
                success: function (data) {
                    var getTpl = demo.innerHTML
                        , view = document.getElementById('sfLogin');
                    //略微对data进行下处理
                    data.obj.forEach(function(item){
                            if(item.type.type==2){
                                item['icon'] = '${ctxResource}/img/wx.png'
                            }
                            item['tip'] = item.binded?"微信解绑":"微信绑定";
                    });
                    var output = Mustache.render(getTpl, data);
                    view.innerHTML = output;
                    $(".bind_li").click(function () {
                        var bindFt = $(this).find('img').attr("data-binded");
                        layer.open({
                            title: bindFt=="true"?"解除微信绑定":"绑定微信",
                            type: 1,
                            skin: 'layui-layer-demo', //样式类名
                            closeBtn: 0, //不显示关闭按钮
                            area: 'auto',
                            offset: '200px',
                            anim: 2,
                            shadeClose: true, //开启遮罩关闭
                            content: '<div id="wx_ewm" ></div>',
                            success: function (layero, index) {
                                var obj = new WxLogin({
                                    self_redirect: true,
                                    id: "wx_ewm",
                                    appid: "wx319f7ab18f45e18a",
                                    scope: "snsapi_login",
                                    redirect_uri: encodeURIComponent("https://id.henan.gov.cn/home/<USER>/wx?callback=closeBind"),
                                    state: "",
                                    style: "",
                                    href: ""
                                });
                            }
                        })
                    })
                }
            })
        })
    }
    layui.use(['laytpl', 'layer'], function () {
        var layer = layui.layer;
        window.closeBind = function (message) {
            layer.closeAll();
            layer.msg(message);
            disPlayBindInfo();
        }
    })
    disPlayBindInfo();
</script>
<script src="${websocket}"></script>
</body>
</html>
