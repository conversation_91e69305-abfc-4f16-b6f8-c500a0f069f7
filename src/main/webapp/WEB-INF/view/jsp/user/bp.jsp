<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/jsp/taglib.jsp"%>

<!DOCTYPE html>
<html>

<head>
<meta charset="UTF-8">
<title>绑定手机号</title>
<link rel="stylesheet" href="${ctxResource}/css/style.css" />
<link rel="stylesheet" type="text/css" href="${ctxResource}/theme/css/mobile_home.css"/>
<!--[if lt IE 9]>  
<script src="${ctxResource}/theme/js/respond.js"></script>
<script src="${ctxResource}/theme/js/html5shiv.min.js"></script>
<![endif]-->
	<style>
		#veryfyCodeDivId .p1{
			display: inline-block;
		}
		.p1{text-align: center;}
	</style>
</head>

<body>
<form method="post" id="myForm">
	<p>
		<label>用户名： </label>
		<span class="mobile_80">
			<input type="text"   id="username" name="username" size="30" disabled="disabled" value="${username}" />
		</span>
	</p>
	<p>
		<label>密码： </label>
		<span class="mobile_80">
			<input type="password"   id="pwd" name="pwd" size="30" placeholder="请输入密码"  oninput="OnInput(event,$(this).val())" />
		</span>
	</p>
	<p>
		<label>手机号： </label>
		<span class="mobile_80">
			<input type="text"   id="phone" name="phone" size="30" placeholder="请输入手机号" oninput="OnInput(event,$(this).val())" />
			<button id='sendCheck'  >发送验证码</button>
			<button style="display: none;"  >倒计时 120S</button>
		</span>
	</p>
	<p>
		<label>验证码： </label>
		<span class="mobile_80">
				<input type="text" class="input mobile_50" id="checkCode" name="checkCode" size="30" placeholder="请输入验证码" oninput="OnInput (event,$(this).val())" />
		</span>
	</p>
	<p>
		<label>&nbsp; </label> 
		<span class="mobile_80">
			<input type="button"   onclick="myformAjaxSubmit();" value="确定" />
		</span>
	</p>
</form>
<div  id="veryfyCodeDivId" style="display: none;width: 300px;height: 180px;background-color: white; ">
	<h4 style="background-color: #ccc; padding: 4px; text-align: center;" >验证码</h4>
	<p class="p1" style="margin: 7px;">请输入验证码：<input style="display: inline-block;height:15px;" id="veryfyCodeValue" name="veryfyCodeValue"  size="6" /><img style="margin-top: 10px" alt="点击更换验证码"  id="veryfyCodeImage" onclick="reload_VerifyCodeImage();" src="<%=request.getContextPath()%>/dahe/pub/getVerifyCodeImage"></p>
	<p style="text-align: center; margin: 7px;"><input style="display: inline-block;"  type="button"   onclick="send_sms_code()"   value="获取短信" />  <input style="display: inline-block;"  type="button" onclick="cancle_VerifyCodeImage();" value="取消" /></p>
</div>
<script type="text/javascript" src="${ctxResource}/js/jquery.min.js"></script>
<script src="${ctxResource}/js/layer/layer.js"></script>
<script src="${ctxResource}/js/jquery.lightbox_me.js"></script>
</body>
<script type="text/javascript">
	function OnInput(event,val) {
		var num = 11;
		if (event != null) {
			num = event.target.value.length;
		}
		var phoneNumber = $("#phone").val();
		if(val==phoneNumber){
			if(num == 11) {
				var re = /1(\d)(\d)(\d{8})/;//进行匹配字符串
				var arrNum = val.match(re);//如果匹配无结果
				if (arrNum == null) {
					layer.msg("注：输入的手机号码包含非数字字符！",{time:1800});
					$("#sendCheck").attr("disabled");
					$("#sendCheck").addClass("disabled");

				}
				else{
					$("#sendCheck").removeAttr("disabled");
					$("#sendCheck").removeClass("disabled");
				}
			}
			else if(num > 11) {
				$("#phone").val(phoneNumber.substr(0,11));
				var re = /1(\d)(\d)(\d{8})/;//进行匹配字符串
				var arrNum = val.match(re);//如果匹配无结果
				if (arrNum == null) {
					layer.msg("注：输入的手机号码包含非数字字符！",{time:1500});
				}
				$("#sendCheck").attr("disabled");
				$("#sendCheck").addClass("disabled");
			}
		}
		/* var checkCode = $("#checkCode").val();
		if(val==checkCode){
			if(num > 4) {
				layer.msg("注：验证码位数为4位！",{time:1500});
				$("#checkCode").val(checkCode.substr(0,4));
			}
		} */
	}





/* 	$(function(){
		$("#pwd").blur(function(){
			var len = $("#pwd").val().length;
			if(len<6){
				layer.msg("密码不能少于6位！",{time:1000});
				$("#pwd").focus();
				return false;
			}
		});
	}); */


	var timout_len_int=60*3;//3分钟
	var timeout_len=timout_len_int;
	function  check_code_click(){
		$.post("<%=request.getContextPath()%>/home/<USER>", {
			'phone' : $('#phone').val()
		}, function(data) {
			if(data.success==false)
			{
				layer.msg(data.msg,{time:1000});
			}
			else{
				//alert('true');
				//--------------发送成功后写入cookie
				var timestamp = Date.parse(new Date());
				SetCookie('send_time',timestamp);
				//--------------
				//--------------倒计时按钮部分
				//document.getElementById("sendCheck").disabled='false';
				$("#sendCheck").css("display","none").siblings("button").css("display","block");
				timeout_len=timout_len_int;
				countSecond();
			}
		});

	}
	
	function countSecond(){
		timeout_len = timeout_len-1;
		document.getElementById("sendCheck").innerHTML=timeout_len;
		//$("#sendCheck").val('value',timeout_len);
		//alert(timeout);
		if(timeout_len>0){
			setTimeout("countSecond()", 1000);
		}else{
			document.getElementById("sendCheck").innerHTML="发送验证码";
			document.getElementById("sendCheck").removeAttribute("disabled"); //.disabled=false;
			//document.getElementById("sendCheck").onclick=function(){check_code_click();};
		}
	}
	
	
	function reload_VerifyCodeImage() {
	$("#veryfyCodeImage").attr("src","<%=request.getContextPath()%>/home/<USER>/getVerifyCodeImage?i="+Math.random());
   }
	
	function cancle_VerifyCodeImage(){
	$("#veryfyCodeDivId").trigger('close');
    }
	


	//提交
	function  myformAjaxSubmit(){
		var username=$('#username').val();
		var pwd =$('#pwd').val();
		if(!pwd){ $('#pwd').focus(); return false; }
		var phone =$('#phone').val();
		if(!phone){ $('#phone').focus(); return false; }
		var checkCode =$('#checkCode').val();
		if(!checkCode){ $('#checkCode').focus(); return false; }

		$.post("<%=request.getContextPath()%>/dahe/self/bindPhone",
				{
					'username' : username,
					'password' : pwd,
					'phone':phone,
					'checkCode':checkCode
				},
				function(data) {
					if (data.success == false) {
						layer.msg(data.msg,{time:1000});
					} else {
						layer.msg(data.msg,{time:1000});
						$('#pwd').val('');
						$('#phone').val('');
						$('#checkCode').val('');
						layer_close();
					}

				});
	}

	$("#sendCheck").click(function(){
		var exp = new RegExp(/^1\d{10}$/);
		var len = $("#pwd").val().length;
		if(! exp.test(document.getElementById("phone").value)){
			layer.msg("手机号不符合规则！",{time:1000});
			return false;
		}
		if(len<6){
			layer.msg("密码不能少于6位！",{time:1000});
			$("#pwd").focus();
			return false;
		}
		/////-------------获取验证码
		$("#veryfyCodeImage").attr("src","<%=request.getContextPath()%>/dahe/pub/getVerifyCodeImage?i="+Math.random());
		$("#veryfyCodeDivId").lightbox_me(
				{
					centered: true,
					onLoad: function() {
						$('#veryfyCodeDivId').find('input:first').focus()
					}
				}
		);
		return false;
	});
	function  send_sms_code(){
		var veryfiCode = $('#veryfyCodeValue').val();
		if (veryfiCode.length<4) {
			alert("请正确填写验证码");
			return ;
		}

		$.post("<%=request.getContextPath()%>/dahe/common/sms", {
			'phone' : $('#phone').val(),
			'checkCode':veryfiCode
		}, function(data) {
			if(data.success==false)
			{
				alert(data.msg);
			}
			else{
				$("#veryfyCodeDivId").hide();
				var timestamp = Date.parse(new Date());
				SetCookie('send_time',timestamp);
				document.getElementById("sendCheck").disabled='false';
				timeout_len=timout_len_int;
				countSecond();
			}
		});
	}

</script>
</html>