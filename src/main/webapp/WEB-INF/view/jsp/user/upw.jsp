<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/jsp/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>修改密码</title>
<link rel="stylesheet" href="${ctxResource}/css/style.css" />
<link rel="stylesheet" type="text/css" href="${ctxResource}/theme/css/mobile_home.css"/>
</head>

<body>
<form method="post" id="myForm" action="">
	<p><label>用户名： </label><input type="text" name="username" id="username" disabled="disabled" placeholder="请输入用户名" value="${username}" /></p>
	<p><label>老密码： </label><input type="password" name="oldPwd" id="oldPwd" placeholder="请输入老密码" /></p>
	<p><label>新密码： </label> <input type="password" name="password" id="newPwd" placeholder="请输入新密码" /></p>
	<p><label>确认新密码： </label> <input type="password" name="confirmPwd" id="comfirmPwd" placeholder="确认新密码" /></p>
	<p><label>&nbsp; </label> <input type="button" onclick="myformAjax()" value="修改密码" /></p>
</form>

<script type="text/javascript" src="${ctxResource}/js/jquery.min.js"></script>
<script type="text/javascript" src="${ctxResource}/js/collection.js"></script>
<script src="${ctxResource}/js/layer/layer.js"></script>
<script type="text/javascript">
    function myformAjax() {
        var uname=$('#username').val();
        var oldPwd =$('#oldPwd').val();
        var newPwd = $("#newPwd").val();
        var comfirmPwd = $("#comfirmPwd").val();
        if(!uname || !oldPwd || !newPwd || !comfirmPwd){
            layer.alert("请填写完整！");
        }
        if(newPwd!=comfirmPwd){
            layer.alert("前后密码不一致！");
        }

        $.post("<%=request.getContextPath()%>/dahe/self/updatepw", {
            'username' : uname,
            'password' : newPwd,
            'oldPwd':oldPwd,
            'confirmPwd':comfirmPwd
        }, function(data) {
            if (data.success == false) {
                layer.alert(data.msg);
            }
            else {
                parent.layer.alert(data.msg);
                parent.layer.closeAll('iframe');

            }
        });
    }
</script>
</body>

</html>