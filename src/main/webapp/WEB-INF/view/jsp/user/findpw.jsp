<%@ page language="java" import="java.util.*" pageEncoding="UTF-8" %>
<%@ include file="/WEB-INF/view/jsp/taglib.jsp" %>

<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <title>豫情通管理平台 找回密码</title>
    <meta name="viewport" content="width=device-width,initial-scale=1, maximum-scale=1" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <link rel="stylesheet" href="${ctxResource}/theme/css/mobile_login.css">
    <link rel="stylesheet" href="${ctxResource}/css/layui.css">
    <script type="text/javascript"
            src="${ctxResource}/js/jquery1.9.min.js"></script>
    <script type="text/javascript"
            src="${ctxResource}/js/jquery.validate.js"></script>
    <script type="text/javascript"
            src="${ctxResource}/js/cookie.js"></script>
    <script type="text/javascript"
            src="${ctxResource}/js/jquery.lightbox_me.js"></script>


    <style type="text/css">
        #myForm label.error {
            color: Red;
            font-size: 13px;
            margin-left: 5px;
            padding-left: 3px;
            /*background:url("error.png") left no-repeat; */
        }

    </style>

    <script type="text/javascript">

        var timout_len_int = 60 * 3;//3分钟
        var timeout_len = timout_len_int;


        function check_code_click() {
            //alert($);
            var exp = new RegExp(/^1\d{10}$/);
            if (!exp.test(document.getElementById("phone").value)) {
                alert("手机号不符合规则");
                return;
            }

            /////-------------获取验证码
            $("#veryfyCodeImage").attr("src", "<%=request.getContextPath()%>/dahe/pub/getVerifyCodeImage?i=" + Math.random());
            $("#veryfyCodeDivId").lightbox_me(
                {
                    centered: true,
                    onLoad: function () {
                        $('#veryfyCodeDivId').find('input:first').focus()
                    }
                }
            );
            return;


        }

        //----------

        function send_sms_code() {
            var veryfiCode = $('#veryfyCodeValue').val();
            if (veryfiCode.length < 4) {
                alert("请正确填写验证码");
                return;
            }

            $.post("<%=request.getContextPath()%>/dahe/common/sms", {
                'phone': $('#phone').val(),
                'checkCode': veryfiCode
            }, function (data) {
                if (data.success == false) {
                    alert(data.msg);
                }
                else {
                    $("#veryfyCodeDivId").hide();
                    var timestamp = Date.parse(new Date());
                    SetCookie('send_time', timestamp);
                    document.getElementById("sendCheck").disabled = 'false';
                    timeout_len = timout_len_int;
                    countSecond();
                }
            });
        }

        //-----------


        function check_code_click____() {
            //alert($);
            var exp = new RegExp(/^1\d{10}$/);
            if (!exp.test(document.getElementById("phone").value)) {
                alert("手机号不符合规则");
                return;
            }


            $.post("<%=request.getContextPath()%>/dahe/common/sms", {
                'phone': $('#phone').val()
            }, function (data) {
                if (data.success == false) {
                    alert(data.msg);
                }
                else {
                    var timestamp = Date.parse(new Date());
                    SetCookie('send_time', timestamp);
                    document.getElementById("sendCheck").disabled = 'false';
                    timeout_len = timout_len_int;
                    countSecond();
                }
            });

        }

        //-----------------------


        function countSecond() {
            timeout_len = timeout_len - 1;
            document.getElementById("sendCheck").innerHTML = timeout_len;
            if (timeout_len > 0) {
                setTimeout("countSecond()", 1000);
            } else {
                document.getElementById("sendCheck").innerHTML = "发送验证码";
                document.getElementById("sendCheck").removeAttribute("disabled");
            }
        }


        function f_check_ZhOrNumOrLett(obj) {    //判断是否是汉字、字母、数字组成
            var regu = "^[0-9a-zA-Z\u4e00-\u9fa5]+$";
            var re = new RegExp(regu);
            if (re.test(obj.value)) {
                return true;
            }
            f_alert(obj, "请输入汉字、字母或数字");
            return false;
        }

        function getStrLeng(str) {
            var realLength = 0;
            var len = str.length;
            var charCode = -1;
            for (var i = 0; i < len; i++) {
                charCode = str.charCodeAt(i);
                if (charCode >= 0 && charCode <= 128) {
                    realLength += 1;
                } else {
                    // 如果是中文则长度加2
                    realLength += 2;
                }
            }
            return realLength;
        }


        $(function () {


            //--------------验证短信发送时间
            var old_time = getCookie('send_time');
            var curr_time = Date.parse(new Date());
            //alert(curr_time-old_time);
            if (old_time) {
                //过去了多久
                var s_time = curr_time - old_time;
                //转换成秒
                var t_time = s_time / 1000;
                //是否超过timeout时间
                var tt_time = timout_len_int - t_time;
                if (tt_time > 0) {
                    timeout_len = tt_time;
                    //document.getElementById("sendCheck").disabled='false';
                    //countSecond();
                }

            }
            //--------------

            jQuery.validator.addMethod("regex",  //addMethod第1个参数:方法名称
                function (value, element, params) {     //addMethod第2个参数:验证方法，参数（被验证元素的值，被验证元素，参数）
                    var exp = new RegExp(params);     //实例化正则对象，参数为传入的正则表达式
                    return exp.test(value);                    //测试是否匹配
                },
                "格式错误");    //addMethod第3个参数:默认错误信息

            jQuery.validator.addMethod("myminlen",  //addMethod第1个参数:方法名称
                function (value, element, params) {     //addMethod第2个参数:验证方法，参数（被验证元素的值，被验证元素，参数）
                    // var exp = new RegExp(params);     //实例化正则对象，参数为传入的正则表达式
                    //return exp.test(value);                    //测试是否匹配  \
                    var str = value;
                    var realLength = 0;
                    var len = str.length;
                    var charCode = -1;

                    for (var i = 0; i < len; i++) {
                        charCode = str.charCodeAt(i);
                        if (charCode >= 0 && charCode <= 128) {
                            realLength += 1;
                        } else {
                            // 如果是中文则长度加2
                            realLength += 2;
                        }
                    }
                    return realLength >= params;
                },
                "长度不够");    //addMethod第3个参数:默认错误信息

            jQuery.validator.addMethod("mymaxlen",  //addMethod第1个参数:方法名称
                function (value, element, params) {     //addMethod第2个参数:验证方法，参数（被验证元素的值，被验证元素，参数）
                    // var exp = new RegExp(params);     //实例化正则对象，参数为传入的正则表达式
                    //return exp.test(value);                    //测试是否匹配  \
                    var str = value;
                    var realLength = 0;
                    var len = str.length;
                    var charCode = -1;

                    for (var i = 0; i < len; i++) {
                        charCode = str.charCodeAt(i);
                        if (charCode >= 0 && charCode <= 128) {
                            realLength += 1;
                        } else {
                            // 如果是中文则长度加2
                            realLength += 2;
                        }
                    }
                    return realLength <= params;
                },
                "长度太长");    //addMethod第3个参数:默认错误信息


            $("#myForm").validate({
                //debug:true,

                rules: {
                    username: {
                        required: true,
                        regex: /^[0-9a-zA-Z\u4e00-\u9fa5]+$/,
                        mymaxlen: 20,
                        myminlen: 4
                    },
                    phone: {
                        required: true,
                        digits: true,
                        regex: /^1\d{10}$/
                    }
                },
                messages: {
                    username: {
                        required: "用户名不能为空",
                        mymaxlen: jQuery.format("用户名{0}个字符以下，汉字算2个字符"),
                        myminlen: jQuery.format("用户名{0}个字符以上，汉字算2个字符"),
                        regex: "用户名由汉字、字母、数字组成"
                    },
                    phone: {
                        required: "手机号不能为空",
                        digits: "手机号码必须是数字",
                        regex: "手机号不对"
                    }

                },
                submitHandler: function (form) {
                    formAjaxSubmit();
                },
                success: function (label) {
                    //label.html(" ").addClass("checked");
                    label.html("<font color='green'>ok</font>");
                }

            });
        });

        function formAjaxSubmit() {
            if ($("#myForm").valid()) {
                var username = $('#username').val();
                var phone = $('#phone').val();
                //var checkCode = $('#checkCode').val();
                //var email = $('#email').val();
                $.post("<%=request.getContextPath()%>/dahe/self/findpw", {
                    'username': username, 'phone': phone, 'token': '6ef2eb3938fe6af1bc09a161766ff1ac'
                }, function (data) {
                    if (data.success == false) {
                        alert(data.msg);
                    }
                    else {
                        alert(data.msg);
                        var url = window.location.search;
                        if (url.length > 0) {
                            var loc = url.substring(url.lastIndexOf('=') + 1,
                                url.length);
                            loc = decodeURIComponent(loc);
                            location.href = loc;
                        } else {
                            location.href = '<%=request.getContextPath()%>/login';
                        }

                    }
                });
            }
        }

    </script>
    <!--[if lt IE 9]>
    <script src="${ctxResource}/theme/js/respond.js"></script>
    <script src="${ctxResource}/theme/js/html5shiv.min.js"></script>
    <![endif]-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="${ctxResource}/theme/css/pintuer.css">
    <link rel="stylesheet" href="${ctxResource}/theme/css/style.css">
</head>
<body>

<div class="container padding mobile_head_blank">
    <div class="navbar navbar-big ">
        <div class="navbar-head">
            <button class="button bg icon-navicon" data-target="#navbar-big4" onclick="showNav()"></button>
            <a href="/"><img class="logo" src="${ctxResource}/theme/icon/logo.png"></a>
        </div>
        <div class="navbar-body nav-navicon mobile_nav" id="navbar-big4">
            <ul class="nav nav-inline nav-menu nav-pills nav-big float-right mobile_nav_ul">
                <li><a href="http://news.dahe.cn" target="_blank">新闻</a></li>
                <li><a href="http://bbs.dahe.cn" target="_blank">论坛</a></li>
                <li><a href="http://bang.dahe.cn" target="_blank">眼遇</a></li>
                <li><a href="http://jr.dahe.cn" target="_blank">财经</a></li>
                <li><a href="http://house.dahe.cn" target="_blank">地产</a></li>
                <li><a href="http://edu.dahe.cn" target="_blank">教育</a></li>
                <li><a href="http://auto.dahe.cn" target="_blank">汽车</a></li>
            </ul>
        </div>
    </div>
</div>


<div class="layout mobile_layout" id="layerm<%=(new Random()).nextInt(5)+1 %>">
    <br/>
    <div class="container">
        <div class="margin-large-left margin-large-right">
            <div class="line">
                <div class="x12">
                    <div class="panel margin-large" style="background:#ffffff;height:500px;">
                        <div class="panel-head"><h1>找回密码</h1></div>
                        <div class="panel-body">

                            <div class="line">
                                <div class="x3 mobile_hiden">
                                    <div class="biaotou">
                                        <ul class="list-unstyle">
                                            <li>用户名：</li>
                                            <li>手机号：</li>


                                        </ul>
                                    </div>
                                </div>
                                <form id="myForm" action="" class="form-x form-big form-reg" method="post">

                                    <div class="x4 mobile_100">

                                        <div class="form-group">
                                        	<div class="mobile_x3 mobile_show mobile_fl mobile_lable">
                                        		用户名：
                                        	</div>
                                            <div class="field mobile_x8 mobile_fl">
                                                <input type="text" class="input mobile_input" id="username" name="username" size="30"
                                                       placeholder="用户名">
                                                <label for="username" generated="true" class="mobile_error mobile_show"></label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                        	<div class="mobile_x3 mobile_show mobile_fl mobile_lable">
                                        		手机号：
                                        	</div>
                                            <div class="field mobile_x8 mobile_fl">
                                                <input type="text" class="input mobile_input" id="phone" name="phone" size="30"
                                                       placeholder="手机号">
                                            	<label for="phone" generated="true" class="mobile_error mobile_show"></label>
                                            </div>
                                        </div>


                                        <div class="form-button" style="margin-left:0;">
                                            <button type="reset" class="layui-btn mobile_img"  type="submit" name="submit" onclick="formAjaxSubmit()">确认</button>
                                    	</div>
                                    </div>
                                    <div class="x5 mobile_hiden">
                                        <div class="biaowei">
                                            <ul class="list-unstyle">
                                                <li><label for="username" generated="true" class="error"></label></li>
                                                <li><label for="phone" generated="true" class="error"></label></li>

                                            </ul>
                                        </div>
                                    </div>
                                </form>

                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<jsp:include page="../includes/footer.jsp"/>


<div id="veryfyCodeDivId" style="display: none;width: 300px;height: 120px;background-color: white; ">
    <h4 style="background-color: #ccc; padding: 4px; text-align: center;">验证码</h4>
    <p style="margin: 7px;">请输入验证码：<input id="veryfyCodeValue" name="veryfyCodeValue" size="6"/><img alt="点击更换验证码"
                                                                                                     id="veryfyCodeImage"
                                                                                                     onclick="reload_VerifyCodeImage()"
                                                                                                     src="<%=request.getContextPath()%>/dahe/pub/getVerifyCodeImage">
    </p>
    <p style="text-align: center; margin: 7px;"><input type="button" onclick="send_sms_code()" value="获取短信"/> <input
            type="button" onclick="cancle_VerifyCodeImage();" value="取消"/></p>
</div>

<script type="text/javascript">
	var documentWidth;
	var documentHeight;
	function view() {
			documentWidth = document.documentElement.clientWidth;
			documentHeight = document.documentElement.clientHeight;
			document.body.style.height = documentHeight + "px";
			document.body.style.width = documentWidth + "px";
		}
	view();
	function showNav(){
		var display = $('#navbar-big4').css('display');
		if (display === 'none') {
			$('#navbar-big4').show();
		}else{
			$('#navbar-big4').hide();
		}
	}
	function addErrorClass(){
		if (documentWidth > 700) {
			return;
		}
		var form = $('#myForm');
		var mobile_errors = form.find('.mobile_error');
		$.each(mobile_errors, function(i) {
			var item = mobile_errors.eq(i);
			item.addClass('error');
		});
	}
	addErrorClass();
</script>
</body>
</html>














