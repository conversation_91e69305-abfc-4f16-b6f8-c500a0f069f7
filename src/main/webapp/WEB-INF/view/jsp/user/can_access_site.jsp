<!DOCTYPE html>
<html lang="zh-cn">
<%@ page language="java" import="java.util.*" contentType="text/html;charset=UTF-8"%>
<head>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="UTF-8">
    <title>豫情通鉴</title>
    <link rel="stylesheet" href="/resources/layui/css/layui.css" />
    <script src="/resources/js/jquery.min.js"></script>
    <script src="/resources/layui/layui.js"></script>
    <meta http-equiv="refresh" content="300">
    <link rel="shortcut icon" href="/favicon.ico?v=20220620">
    <link rel="stylesheet" href="/resources/css/login.css">
    <style>
        ::-webkit-scrollbar {
            /*隐藏滚轮*/
            display: none;
        }
        .welcome {
            width: fit-content;
        }

        .selectIdentyBox {
            width: 350px;
            margin: 0 auto;
        }

        .selectIdentyBox li {
            width: 348px;
            height: 48px;
            background: #FBFDFF;
            border: 1px solid #E5E7E9;
            border-radius: 6px;
            margin-top: 12px;
            line-height: 48px;
            padding-left: 20px;
            padding-right: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 16px;
            box-sizing: border-box;
            color: #AABBCD;
        }

        .selectIdentyBox li input.fr {
            margin-top: 17px;
        }

        .login-btn {
            color: #fff;
            font-size: 18px;
            text-align: center;
            letter-spacing: 0;
            line-height: 54px;
            display: block;
            border: none;
            cursor: pointer;
            width: 348px;
            height: 56px;
            background: #006AFF;
            border-radius: 4px;
            margin-top: 20px;
        }

        .welcome {
            margin-bottom: 27px;
        }

        #idendityList {
            max-height: 280px;
            overflow-y: scroll;

        }
    </style>
</head>

<body>
<div class="layui-inline">
    <%--
    <img src="/resources/img/login_logo2.png" class="logo fl" />
    <div class="logoTitle fl">豫情通鉴</div>
    --%>
</div>

<div class="login-box">
    <div class="welcome">请确认您的登录账号</div>
    <div class="selectIdentyBox">
        <ul id="idendityList">

        </ul>
        <button class="login-btn" id="loginBtn">确认登录
        </button>
    </div>
</div>
</body>
<script type="text/javascript" src="/resources/js/jquery.min.js"></script>
<script>
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = window.location.search.substr(1).match(reg); //匹配目标参数
        if (r != null) return unescape(r[2]); return null; //返回参数值
    }
    var version = '${version}';
    window.onload = function () {
        layui.use(['layer'], function () {
            var layer = layui.layer;
            $.ajax({
                url: '/dahe/user/2site?version=' + version,
                method: 'post',
                beforeSend: function () {
                    var loadIndex = 0
                    _this = this
                    _this.loadIndex = layer.load();
                    $(".layui-layer-shade").css('opacity', '0.2')
                },
                success: function (res) {
                    var html = ''
                    if (res.obj == null) {
                        setTimeout(function () {
                            layer.close(_this.loadIndex);
                            layer.msg('系统正在维护升级！', {
                                time: 3000
                            });
                        }, 500);
                        return
                    }
                    for (var i = 0; i < res.obj.length; i++) {
                        let data = res.obj;
                        html += '<li class="cl"><span class="fl">' + data[i].name + '</span><input class="fr" type="radio" name="identity" value=' + data[i].url + '></li>';
                    }
                    $('#idendityList').html(html)
                    var localUrl = ''
                    $('input[name="identity"]').click(function () {
                        localUrl = this.value
                        $(this).parent().css({ 'border': '1px solid #006AFF', 'color': '#006AFF' }).siblings().css({ 'border': '1px solid #E5E7E9', 'color': '#E5E7E9' })
                    })
                    $('#loginBtn').click(function () {
                        if (localUrl != '' || localUrl != null) {
                            window.location.href = localUrl
                        }
                    })
                    setTimeout(function () {
                        layer.close(_this.loadIndex);
                    }, 500);
                }
            })
        });
    }
</script>
</html>
