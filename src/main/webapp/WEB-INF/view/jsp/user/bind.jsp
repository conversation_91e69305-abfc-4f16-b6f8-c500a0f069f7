<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ include file="/WEB-INF/view/jsp/taglib.jsp"%>


<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>

<title>豫情通管理平台 绑定手机号</title>

<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
<meta http-equiv="description" content="This is my page">
<script type="text/javascript"
	src="${ctxResource}/js/jquery1.9.min.js"></script>
<script type="text/javascript"
	src="${ctxResource}/js/jquery.validate.js"></script>
<script type="text/javascript"
	src="${ctxResource}/js/cookie.js"></script>
	<style type="text/css">
	#myForm label.error
	{
		color:Red;
		font-size:13px;
		margin-left:5px;
		padding-left:16px;
		background:url("error.png") left no-repeat;
	}

</style>
<script type="text/javascript">

	  var timout_len_int=60*3;//3分钟
        var timeout_len=timout_len_int;


		function  check_code_click(){
			//alert($);
			var exp = new RegExp(/^1\d{10}$/);
		    if(! exp.test(document.getElementById("phone").value)){
		        alert("手机号不符合规则");
		        return ;
		    }


			$.post("<%=request.getContextPath()%>/dahe/common/sms", {
				'phone' : $('#phone').val()
			}, function(data) {
				if(data.success==false)
				{
				   alert(data.msg);


				}
				else{
				   //alert('true');
				   //--------------发送成功后写入cookie
				   var timestamp = Date.parse(new Date());
				   SetCookie('send_time',timestamp);
				   //--------------
				   //--------------倒计时按钮部分
				   document.getElementById("sendCheck").disabled='false';
				   //$("#sendCheck").attr("disabled","false");
				   timeout_len=timout_len_int;
				   countSecond();
				   //--------------
				}
			});

		}
       function countSecond()
		{
			timeout_len = timeout_len-1;
			document.getElementById("sendCheck").value=timeout_len;
			//$("#sendCheck").val('value',timeout_len);
			//alert(timeout);
			if(timeout_len>0){
		    	setTimeout("countSecond()", 1000);
		    }else{
		    	document.getElementById("sendCheck").value="发送验证码";
		    	document.getElementById("sendCheck").removeAttribute("disabled"); //.disabled=false;
		    	//document.getElementById("sendCheck").onclick=function(){check_code_click();};
		    }
		}


      $(function(){
          $("#myForm").validate({

             rules:{
               username:{
                    required:true,
                    maxlength:20,
                    minlength:3
               },
               password:{
                   required:true,
                   minlength:5

               }
             },
             messages:{
                username:{
                    required:"请输入用户名",
                    maxlength:"最多20位",
                    minlength:"最少3位"
                  },
               password:{
                    required:"请输入密码",
                    minlength:jQuery.format("密码不能小于{0}个字符")
                  },             },
             submitHandler:function(form){
                 myformAjaxSubmit();
             },
             success: function(label) {
				 //label.html(" ").addClass("checked");
				 label.html("<font color='green'>ok</font>");
			 }
          });


          //$("#sendButton").click(function(){
		  //alert('aaa');
			//$("#myForm").submit();


				//$("#myForm").unbind('load');
				// $("#focus_pic_iframe_id").load(function (){
				//	var txt=$("#focus_pic_iframe_id").contents().text();
				//	alert(text);
				// });

		//});

      });



      function  myformAjaxSubmit(){
        if($("#myForm").valid()){


         //$("#myForm").submit();


		//$("#myForm").unbind('load');
		//$("#focus_pic_iframe_id").load(function (){
		//	var txt=$("#focus_pic_iframe_id").contents().text();
		//	alert(text);
		// });



            var username=$('#username').val();
			var password =$('#password').val();
			//alert(username);
			$.post("<%=request.getContextPath()%>/dahe/self/login", {
				'username' : username,
				'password' : password
			}, function(data) {
				//alert(data.success);
				if (data.success == false) {
					alert(data.msg);
				} else {
					alert(data.msg);
					//alert(data.obj);

					//SetCookie('dahepp_un',data.obj);

					//写cookie信息

					//判断跳转
					var url = window.location.search;
					//alert(url);
					if (url.length > 0 ) {
						//alert(url.lastIndexOf('='));
						var loc = url.substring(url.lastIndexOf('=') + 1,
								url.length);
						loc = decodeURIComponent(loc);
						//alert(loc);
						location.href = loc;
					}
				}

			});
		}

	}
</script>
</head>

<body>
	<form method="post"
		action="<%=request.getContextPath()%>/dahe/self/bindPhone" id="myForm">
		用户名： <input type="text" id="username" name="username" /><br> 密码：
		<input type="password" id="password" name="password" /><br> <input
			type="button" id="sendButton" onclick="myformAjaxSubmit()" value="登陆" /><br>

	</form>

</body>
</html>
