<?xml version="1.0" encoding="UTF-8"?>
<flow xmlns="http://www.springframework.org/schema/webflow"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.springframework.org/schema/webflow http://www.springframework.org/schema/webflow/spring-webflow-2.4.xsd">
    <!--微信绑定页面 绑定失败进行消息提示即可 绑定成功直接跳到登录页面，简单粗暴有疗效-->
    <var name="bindPhoneParameter" class="cn.dahe.cas.auth.dto.WxBindPhoneParameter"/>
    <input name="openid"/>
    <!--绑定成功进入默认登录流程-->
    <view-state id="wxBindView" view="casWxBindView" model="bindPhoneParameter">
        <transition on="submit" to="end">
            <evaluate expression="wxBindAction.bind(bindPhoneParameter,flowRequestContext)"/>
        </transition>
    </view-state>
    <end-state id="end"/>
</flow>