<?xml version="1.0" encoding="UTF-8"?>
<flow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xmlns="http://www.springframework.org/schema/webflow"
      xsi:schemaLocation="http://www.springframework.org/schema/webflow
                          http://www.springframework.org/schema/webflow/spring-webflow.xsd">

    <var name="credential" class="cn.dahe.cas.auth.realm.ImageCodeCredential"/>
    <on-start>
        <evaluate expression="initialFlowSetupAction"/>
        <set name="flowScope.showCode" value="true"/>
        <set name="flowScope.tab" value="0"/>
    </on-start>

    <!--微信登录 失败直接跳转到登录页面 未绑定进行绑定流程，绑定流程可以设计为子流程-->
    <action-state id="wxLoginCheck">
        <evaluate expression="wxLoginAction.loginByCode(flowRequestContext)"/>
        <transition on="success" to="sendTicketGrantingTicket">
            <set name="flowScope.authentication_type" value="T(cn.dahe.cas.auth.constants.LoginType).WX" type="cn.dahe.cas.auth.constants.LoginType"/>
            <set name="flowScope.dialog" value="true "/>
        </transition>
        <transition on-exception="cn.dahe.cas.auth.exception.WxNeedBindException" to="wx_login_result">
            <set name="flowScope.dialog" value="true "/>
            <set name="flowScope.errorCode" value="1"/>
        </transition>
        <!--not wx login,jump to next action-->
        <transition on="no" to="ticketGrantingTicketCheck">
            <set name="flowScope.dialog" value="false"/>
        </transition>
    </action-state>

    <view-state id="wx_login_result" view="casLoginResult"/>

    <action-state id="ticketGrantingTicketCheck">
        <evaluate expression="ticketGrantingTicketCheckAction"/>
        <transition on="notExists" to="gatewayRequestCheck"/>
        <transition on="invalid" to="terminateSession"/>
        <!--如果tgt合法，则先进行站点访问权限检查，成功后走正常逻辑-->
        <transition on="valid" to="hasServiceCheck"/>
    </action-state>

    <action-state id="terminateSession">
        <evaluate expression="terminateSessionAction.terminate(flowRequestContext)"/>
        <transition to="gatewayRequestCheck"/>
    </action-state>

    <decision-state id="gatewayRequestCheck">
        <if test="requestParameters.gateway != '' and requestParameters.gateway != null and flowScope.service != null"
            then="gatewayServicesManagementCheck" else="serviceAuthorizationCheck"/>
    </decision-state>

    <decision-state id="hasServiceCheck">
        <if test="flowScope.service != null" then="renewRequestCheck" else="viewGenericLoginSuccess"/>
    </decision-state>

    <decision-state id="renewRequestCheck">
        <if test="requestParameters.renew != '' and requestParameters.renew != null" then="serviceAuthorizationCheck"
            else="generateServiceTicket"/>
    </decision-state>

    <!-- Do a service authorization check early without the need to login first -->
    <action-state id="serviceAuthorizationCheck">
        <evaluate expression="serviceAuthorizationCheck"/>
        <transition to="initializeLogin"/>
    </action-state>

    <!--
        The "warn" action makes the determination of whether to redirect directly to the requested
        service or display the "confirmation" page to go back to the server.
    -->
    <decision-state id="warn">
        <if test="flowScope.warnCookieValue" then="showWarningView" else="redirect"/>
    </decision-state>

    <action-state id="initializeLogin">
        <evaluate expression="'success'"/>
        <transition on="success" to="viewLoginForm"/>
    </action-state>

    <view-state id="viewLoginForm" view="casLoginView2" model="credential">
        <binder>
            <binding property="username" required="true"/>
            <binding property="password" required="true"/>
            <binding property="imageCode"/>
        </binder>
        <on-entry>
            <set name="viewScope.commandName" value="'credential'"/>
            <set name="viewScope.showCode" value="flowScope.showCode"/>
            <set name="flowScope.jsCallback" value="null"/>
            <!--
            <evaluate expression="samlMetadataUIParserAction" />
            -->
        </on-entry>
        <transition on="submit" bind="true" validate="true" to="imageCodeSubmit">
            <set name="flowScope.tab" value="1"/>
            <set name="flowScope.credential" value="credential"/>
        </transition>
        <!--<transition on="submit" bind="true" validate="true" to="realSubmit"/>-->
    </view-state>

    <!--微信绑定 绑定失败进行消息提示即可 绑定成功直接跳到登录页面，简单粗暴有疗效-->
    <view-state id="wxBindView" view="casWxBindView">
        <transition on="submit" to="viewLoginForm">
            <evaluate expression="wxBindAction.bind(flowRequestContext,messageContext)"/>
        </transition>
    </view-state>
    <action-state id="imageCodeSubmit">
        <evaluate expression="captchaAction.validayorCode(flowRequestContext, flowScope.credential, messageContext)"/>
        <transition on="success" to="realSubmit"/>
        <transition on="error" to="viewLoginForm"/>
    </action-state>
    <action-state id="realSubmit">
        <on-entry>
            <set name="flowScope.authentication_type" value="T(cn.dahe.cas.auth.constants.LoginType).PHONE" type="cn.dahe.cas.auth.constants.LoginType"/>
        </on-entry>
        <evaluate
                expression="authenticationViaFormAction.submit(flowRequestContext, flowScope.credential, messageContext)"
                result="flowScope.authenticationResult"/>
        <transition on="warn" to="warn"/>
        <!--
        To enable AUP workflows, replace the 'success' transition with the following:
        <transition on="success" to="acceptableUsagePolicyCheck" />
        -->
        <transition on="success" to="sendTicketGrantingTicket"/>
        <transition on="successWithWarnings" to="showMessages"/>
        <transition on="authenticationFailure" to="handleAuthenticationFailure"/>
        <transition on="error" to="viewLoginForm"/>
    </action-state>

    <view-state id="showMessages" view="casLoginMessageView">
        <on-entry>
            <evaluate expression="sendTicketGrantingTicketAction"/>
            <set name="requestScope.messages" value="messageContext.allMessages"/>
        </on-entry>
        <transition on="proceed" to="serviceCheck"/>
    </view-state>

    <action-state id="handleAuthenticationFailure">
        <on-entry>
            <set name="flowScope.showCode" value="true"/>
            <evaluate expression="authenticationRecordAction.recordLoginFailure(flowRequestContext)"/>
        </on-entry>
        <evaluate expression="ssoAuthenticationExceptionHandler.handle(currentEvent.attributes.error, messageContext)"/>
        <transition on="AccountDisabledException" to="casAccountDisabledView"/>
        <transition on="AccountLockedException" to="casAccountLockedView"/>
        <transition on="AccountPasswordMustChangeException" to="casMustChangePassView"/>
        <transition on="CredentialExpiredException" to="casExpiredPassView"/>
        <transition on="InvalidLoginLocationException" to="casBadWorkstationView"/>
        <transition on="InvalidLoginTimeException" to="casBadHoursView"/>
        <transition on="FailedLoginException" to="viewLoginForm"/>
        <transition on="AccountNotFoundException" to="initializeLogin"/>
        <transition on="UNKNOWN" to="initializeLogin"/>
    </action-state>

    <action-state id="sendTicketGrantingTicket">
        <!--在分配tgt时首先检测用户是否已经登录，如果登录先将该用户踢出，然后继续后续操作-->
        <on-entry>
            <evaluate expression="duplicateLoginCheckAction.check"/>
            <evaluate expression="authenticationRecordAction.recordLoginInformation(flowRequestContext)"/>
        </on-entry>
        <evaluate expression="sendTicketGrantingTicketAction"/>
        <transition to="serviceCheck"/>
        <!--记录登录状态-->
        <on-exit>
            <evaluate expression="duplicateLoginCheckAction.record"/>
        </on-exit>
    </action-state>

    <decision-state id="serviceCheck">
        <if test="flowScope.service != null" then="generateServiceTicket" else="viewGenericLoginSuccess"/>
    </decision-state>
    <!--在生成服务票据时进行服务可访问性检查-->
    <action-state id="generateServiceTicket">
        <on-entry>
            <evaluate expression="servicePermissionCheckAction"/>
        </on-entry>
        <evaluate expression="generateServiceTicketAction"/>
        <transition on="success" to="warn"/>
        <transition on="authenticationFailure" to="handleAuthenticationFailure"/>
        <transition on="error" to="initializeLogin"/>
        <transition on="gateway" to="gatewayServicesManagementCheck"/>
    </action-state>

    <action-state id="gatewayServicesManagementCheck">
        <evaluate expression="gatewayServicesManagementCheck"/>
        <transition on="success" to="redirect"/>
    </action-state>

    <action-state id="redirect">
        <evaluate expression="flowScope.service.getResponse(requestScope.serviceTicketId)"
                  result-type="org.jasig.cas.authentication.principal.Response" result="requestScope.response"/>
        <transition to="postRedirectDecision"/>
    </action-state>

    <decision-state id="postRedirectDecision">
        <if test="requestScope.response.responseType.name() == 'POST'" then="postView" else="redirectView"/>
    </decision-state>

    <!--
        the "viewGenericLoginSuccess" is the end state for when a user attempts to login without coming directly from a service.
        They have only initialized their single-sign on session.we simply redirect to home
    -->

    <end-state id="viewGenericLoginSuccess" view="casLoginResult">
        <on-entry>
            <evaluate expression="genericSuccessViewAction.getAuthenticationPrincipal(flowScope.ticketGrantingTicketId)"
                      result="requestScope.principal"
                      result-type="org.jasig.cas.authentication.principal.Principal"/>
        </on-entry>
    </end-state>


    <!--
    The "showWarningView" end state is the end state for when the user has requested privacy settings (to be "warned")
    to be turned on.  It delegates to a view defines in default_views.properties that display the
    "Please click here to go to the service." message.
    -->
    <end-state id="showWarningView" view="casConfirmView"/>

    <!-- Password policy failure states -->
    <end-state id="abstactPasswordChangeView">
        <on-entry>
            <set name="flowScope.passwordPolicyUrl" value="passwordPolicyConfiguration.passwordPolicyUrl"/>
        </on-entry>
    </end-state>
    <end-state id="casExpiredPassView" view="casExpiredPassView" parent="#abstactPasswordChangeView"/>
    <end-state id="casMustChangePassView" view="casMustChangePassView" parent="#abstactPasswordChangeView"/>
    <end-state id="casAccountDisabledView" view="casAccountDisabledView"/>
    <end-state id="casAccountLockedView" view="casAccountLockedView"/>
    <end-state id="casBadHoursView" view="casBadHoursView"/>
    <end-state id="casBadWorkstationView" view="casBadWorkstationView"/>

    <end-state id="postView" view="postResponseView">
        <on-entry>
            <set name="requestScope.parameters" value="requestScope.response.attributes"/>
            <set name="requestScope.originalUrl" value="flowScope.service.id"/>
        </on-entry>
    </end-state>

    <!--
        The "redirect" end state allows CAS to properly end the workflow while still redirecting
        the user back to the service required.
    -->
    <end-state id="redirectView" view="casLoginResult"/>

    <end-state id="viewServiceErrorView" view="serviceErrorView"/>
    <end-state id="home" view="externalRedirect:home"/>
    <!--此处为服务可用性校验结果处理-->
    <decision-state id="serviceUnauthorizedCheck">
        <if test="flowScope.unauthorizedRedirectUrl != null"
            then="home"
            else="home"/>
    </decision-state>

    <!--一般不会对首页有权限，所以一般s得这样写吧-->
    <view-state id="sitePermissionLack" view="sitePermissionLackView"/>

    <end-state id="viewRedirectToUnauthorizedUrlView" view="externalRedirect:#{flowScope.unauthorizedRedirectUrl}"/>
    <end-state id="viewServiceSsoErrorView" view="serviceErrorSsoView"/>

    <global-transitions>
        <!--when getWxInfo exception occurs,jump to ticketGrantingTicketCheck-->
        <transition to="ticketGrantingTicketCheck" on-exception="cn.dahe.cas.auth.exception.GetWxInfoException"/>
        <transition to="viewLoginForm" on-exception="org.jasig.cas.services.UnauthorizedSsoServiceException"/>
        <transition to="sitePermissionLack" on-exception="cn.dahe.cas.auth.exception.SitePermissionLackException"/>
        <transition to="sitePermissionLack" on-exception="cn.dahe.cas.auth.exception.GetUserFromTgtException"/>
        <transition to="viewServiceErrorView"
                    on-exception="org.springframework.webflow.execution.repository.NoSuchFlowExecutionException"/>
        <transition to="serviceUnauthorizedCheck" on-exception="org.jasig.cas.services.UnauthorizedServiceException"/>
        <transition to="serviceUnauthorizedCheck" on-exception="org.jasig.cas.services.UnauthorizedServiceForPrincipalException"/>
    </global-transitions>
</flow>
