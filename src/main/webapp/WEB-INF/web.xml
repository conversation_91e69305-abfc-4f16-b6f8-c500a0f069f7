<?xml version="1.0" encoding="ISO-8859-1"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         metadata-complete="true"
         version="3.0">
    <display-name>sso</display-name>

    <listener>
        <listener-class>
            org.springframework.web.context.ContextLoaderListener
        </listener-class>
    </listener>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>
            /WEB-INF/spring-configuration/*.xml
            /WEB-INF/deployerConfigContext.xml
            classpath*:/META-INF/spring/*.xml
            classpath:manager-context.xml
        </param-value>
    </context-param>


    <!--1.????springfox???Swagger????-->
    <!--Knife4j???Swagger????,Filter????Swagger??-->
    <!--Swagger???Basic??????-->
    <filter>
        <filter-name>knife4jSecurityBasic</filter-name>
        <filter-class>com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter</filter-class>
        <!--??basic??-->
        <init-param>
            <param-name>enableBasicAuth</param-name>
            <!--???????true?????-->
            <param-value>true</param-value>
        </init-param>
        <!--???&??-->
        <init-param>
            <param-name>userName</param-name>
            <param-value>admin</param-value>
        </init-param>
        <init-param>
            <param-name>password</param-name>
            <param-value>admin123</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>knife4jSecurityBasic</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>characterEncodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>characterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>CAS Client Info Logging Filter</filter-name>
        <filter-class>org.jasig.inspektr.common.web.ClientInfoThreadLocalFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>
    <filter-mapping>
        <filter-name>CAS Client Info Logging Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>requestParameterSecurityFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <async-supported>true</async-supported>
    </filter>
    <filter-mapping>
        <filter-name>requestParameterSecurityFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>responseHeadersSecurityFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <async-supported>true</async-supported>
    </filter>
    <filter-mapping>
        <filter-name>responseHeadersSecurityFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>shiroFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>shiroFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>ipFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>ipFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>xssFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>xssFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- ??????? -->
    <filter>
        <filter-name>CORS</filter-name>
        <filter-class>cn.dahe.cas.auth.filter.CORSFilter</filter-class>
        <init-param>
            <param-name>cors.allowOrigin</param-name>
            <param-value>*</param-value>
        </init-param>
        <init-param>
            <param-name>cors.supportedMethods</param-name>
            <param-value>GET, POST, HEAD, PUT, DELETE</param-value>
        </init-param>
        <init-param>
            <param-name>cors.supportedHeaders</param-name>
            <param-value>Accept, Origin, X-Requested-With, Content-Type, Last-Modified</param-value>
        </init-param>
        <init-param>
            <param-name>cors.exposedHeaders</param-name>
            <param-value>Set-Cookie</param-value>
        </init-param>
        <init-param>
            <param-name>cors.supportsCredentials</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CORS</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!--
      - This is the Spring dispatcher servlet which delegates all requests to the
      - Spring WebMVC controllers as configured in cas-servlet.xml.
      - This configuration also provides a plugin mechanism which enables un-intrusive contributions to the DispatcherServlet
      - child application context (via local Maven or Gradle war overlays for example)
      - and thus an ability to override beans defined in cas-servlet.xml by means of including additional
      - Spring XML config files with a naming convention pattern of /WEB-INF/cas-servlet-*.xml
    -->
    <servlet>
        <servlet-name>cas</servlet-name>
        <servlet-class>
            org.springframework.web.servlet.DispatcherServlet
        </servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <!-- Load the child application context. Start with the default, then modules, then overlays. -->
            <param-value>/WEB-INF/cas-servlet.xml,classpath*:/META-INF/cas-servlet-*.xml,/WEB-INF/cas-servlet-*.xml
            </param-value>
        </init-param>
        <init-param>
            <param-name>publishContext</param-name>
            <param-value>false</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
        <async-supported>true</async-supported>
    </servlet>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/login</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/logout</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/validate</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/serviceValidate</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/p3/serviceValidate</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/proxy</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/proxyValidate</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/p3/proxyValidate</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/CentralAuthenticationService</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/authorizationFailure.html</url-pattern>
    </servlet-mapping>
    <!-- REST support if cas-server-support-rest is included -->
    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/v1/tickets/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/v1/services/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>cas</servlet-name>
        <url-pattern>/oauth2.0/*</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>DruidStatView</servlet-name>
        <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>DruidStatView</servlet-name>
        <url-pattern>/druid/*</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>sso</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <!-- Load the child application context. Start with the default, then modules, then overlays. -->
            <param-value>classpath:sso-mvc.xml</param-value>
        </init-param>
        <load-on-startup>2</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>sso</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <error-page>
        <error-code>401</error-code>
        <location>/authorizationFailure.html</location>
    </error-page>

    <error-page>
        <error-code>403</error-code>
        <location>/authorizationFailure.html</location>
    </error-page>

    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/view/jsp/404.jsp</location>
    </error-page>

    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/view/jsp/errors.jsp</location>
    </error-page>

    <error-page>
        <error-code>501</error-code>
        <location>/WEB-INF/view/jsp/errors.jsp</location>
    </error-page>

    <error-page>
        <error-code>503</error-code>
        <location>/WEB-INF/view/jsp/errors.jsp</location>
    </error-page>

    <welcome-file-list>
        <welcome-file>/home</welcome-file>
    </welcome-file-list>
</web-app>

