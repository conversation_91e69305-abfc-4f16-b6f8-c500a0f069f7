* { margin: 0; padding: 0; }
html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, abbr, address, cite, code, del, dfn, em, img, ins, kbd, q, samp, small, strong, sub, sup, var, b, i, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, article, aside, canvas, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary, time, mark, audio, video { margin: 0; padding: 0;border: 0;outline: 0;font-size: 100%;vertical-align: baseline;  background: transparent;}
h1, h2, h3, h4, h5, h6{font-weight:normal;}
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section { display: block;}
input, textarea, select {  margin: 0; padding: 0; outline: 0;}
h1, h2, h3, h4, h5, h6 { font-size: 100%;}
address, cite, dfn, em, var {  font-style: normal;}
blockquote, q { quotes: none;}
blockquote:before, blockquote:after, q:before, q:after { content: ''; content: none;}
fieldset, img { border: 0;}
select, input, button, button img, label { vertical-align: middle;}
input[type="reset"]::-moz-focus-inner, input[type="button"]::-moz-focus-inner, input[type="submit"]::-moz-focus-inner, input[type="file"] > input[type="button"]::-moz-focus-inner {  border: 0; padding: 0;}
input::-ms-clear {  display: none;}
table {  border-collapse: collapse;  border-spacing: 0;}
hr { border: 0;  height: 1px;}
.clear { clear: both; display: block; overflow: hidden; visibility: hidden;width: 0;   height: 0;}
.clearfix:before, .clearfix:after {
    content: '.';
    display: block;
    overflow: hidden;
    visibility: hidden;
    font-size: 0;
    line-height: 0;
    width: 0;
    height: 0;
}
.clearfix:after {  clear: both;}
.clearfix {  zoom: 1;}
img{ vertical-align:middle; }
.inline-block{display:inline-block;*display:inline;*zoom:1;	}
body{ font-family: "Microsoft YaHei","simsun","Helvetica Neue", Arial, Helvetica, sans-serif;}
i,em{font-style: normal;}
ul, ol{ list-style: none;}
button, input, select, textarea {  font-size: 100%;}
.fr{ float: right;}
.fl{float: left;}
.c-red{color:#EA5455;}
a{ text-decoration: none; color: #333;}
a.success{ color:#fff; background:#5EB95E;}
a.danger{ color:#fff; background:#DD514C; }
a.warning{ color: #fff; background:#F37B1D; }
a:hover{ color:#1D88CE;}
.border-blue{border: 1px solid #78BBFE;}
.borderR-ddd{ border-right: 1px solid #ddd;}
.borderL-ddd{border-left: 1px solid #ddd;}
input,button{
    font-size: 16px;
    padding:5px;
    border: solid 1px #ddd;
    height: 30px;
    display: block;
    border-radius: 5px;
    -webkit-appearance: none;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    transition: all 1s cubic-bezier(0.175, 0.885, 0.32, 1) 0s;
    font-family: "寰蒋闆呴粦";
}
/*------澶栧～鍏�----*/
.mt-5{margin-top:5px}.mt-10{margin-top:10px}.mt-15{margin-top:15px}.mt-20{margin-top:20px}.mt-25{margin-top:25px}.mt-30{margin-top:30px}.mt-35{margin-top:35px}.mt-40{margin-top:40px}.mt-50{margin-top:50px}
.mb-5{margin-bottom:5px}.mb-10{margin-bottom:10px}.mb-15{margin-bottom:15px}.mb-20{margin-bottom:20px}.mb-30{margin-bottom:30px}.mb-40{margin-bottom:40px}.mb-50{margin-bottom:50px}
.ml-5{margin-left:5px}.ml-10{margin-left:10px}.ml-15{margin-left:15px}.ml-20{margin-left:20px}.ml-30{margin-left:30px}.ml-40{margin-left:40px}.ml-50{margin-left:50px}
.mr-5{margin-right:5px}.mr-10{margin-right:10px}.mr-15{margin-right:15px}.mr-20{margin-right:20px}.mr-30{margin-right:30px}.mr-40{margin-right:40px}.mr-50{margin-right:50px}
/*-----鍐呭～鍏�-------*/
.pt-5{padding-top:5px}.pt-10{padding-top:10px}.pt-15{padding-top:15px}.pt-20{padding-top:20px}.pt-30{padding-top:30px}
.pb-5{padding-bottom:5px}.pb-10{padding-bottom:10px}.pb-15{padding-bottom:15px}.pb-20{padding-bottom:20px}.pb-30{padding-bottom:30px}
.pl-5{padding-left:5px}.pl-10{padding-left:10px}.pl-15{padding-left:15px}.pl-20{padding-left:20px}.pl-30{padding-left:30px}
.pr-5{padding-right:5px}.pr-10{padding-right:10px}.pr-15{padding-right:15px}.pr-20{padding-right:20px}.pr-30{padding-right:30px}
.pd-5{padding:5px}.pd-10{padding:10px}.pd-15{padding:15px}.pd-20{padding:20px}.pd-30{padding:30px}.pd-40{padding:40px}
/*瀛椾綋澶у皬*/
.f-14{font-size: 14px;}.f-15{font-size: 15px;}.f-16{font-size: 16px;}.f-18{font-size: 20px;font-weight:900;}.f-20{font-size:20px;}.f-22{font-size: 22px;}.f-24{font-size: 24px;}
.text-l{text-align: left;}.text-c{text-align: center;}.text-r{text-align: right;}
.bold{font-weight: bold;}
.w-1200{ width: 1050px; margin: 0 auto;}
.nav a{ display: block; float: left; padding:9px 22px; border-radius: 2em; }
.nav a:hover{  background:#f5f5f5;transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1) 0s; }
.banner{height: 150px;background: url(../img/banner.png) no-repeat center; width: 100%; color: #eee;}
.banner h1{ line-height: 150px; font-size: 40px; }
.weather_mod { height: 150px;}
.weather_mod .weatherDate span {
    font: 16px/18px 'Microsoft YaHei';
}
.weather_mod .weatherData .weatherTemperature, .weather_mod .weatherData .weatherTemperature span { font: 500 40px/42px 'Microsoft YaHei';}
.weather_mod .tipsTxtShow {
    width: 100%;
    transition: all 600ms linear 0s;
    -webkit-transition: all 600ms linear 0s;
    -moz-transition: all 600ms linear 0s;
    -o-transition: all 600ms linear 0s;
    -ms-transition: all 600ms linear 0s;
}
.weather_mod .tipsTxt{width:0;height: 19px; overflow: hidden;}
.weather_mod .tipsTxtShow{
	width: 100%;
	transition: all 600ms linear 0s;
    -webkit-transition: all 600ms linear 0s;
    -moz-transition: all 600ms linear 0s;
    -o-transition: all 600ms linear 0s;
    -ms-transition: all 600ms linear 0s;
}

.userInfo{width: 150px; line-height:35px;}
.userInfo h2{ border-left: 4px solid #78BBFE; height: 18px; line-height: 18px;}
.userImg img{width: 99px; height: 99px; border-radius: 50%;}
.userInfo .operate a{ display: block;width: 100%; text-align: center; color:#78BBFE; border-radius: 5px; margin:12px auto; font-size: 15px;}
.waiL a{display: block; text-align: center; float: left; font-size: 15px; }
.collectList{ width: 685px;}
.list{ border-bottom: 1px solid #E5E5E5;}
.list-img{ width: 180px; height: 130px; background:url(../img/loading.gif) no-repeat; background-position: center;}
.list-img img{  width: 180px; height: 130px;}
.list-info{ width: 460px; height: 130px; position: relative; line-height:24px;}
.listInfoBox{ bottom: 0px; color: #999; font-size: 12px; position: absolute; width: 100%;}
.list-col{display: block; padding:0 10px; border-radius: 2em; border: 1px solid #ddd; cursor: pointer; font-size: 12px;}
.list-date{ color: #999; height:26px; line-height:30px; }
.share_box{ position: relative; height: 26px; width: 26px;}
.list-share{ display: block; background:url(../img/fenx.png) no-repeat center; background-size: 19px; width: 26px; height: 26px;}
#bdshare{display: none; position: absolute; top: 25px; right: 0; z-index: 999; width: 145px; padding:15px 5px 15px 25px; line-height: 28px; background: #fff;}
.adList{ width: 298px;}
#pageArea{ clear: both; overflow: hidden;  text-align: center; padding-top: 20px;  font-size: 12px;}
#pageArea #pageDec { float: left; width: 510px;}
#pageArea #pageDec span { float: left; margin: 0px 3px;height: 28px; line-height: 28px;border: 1px solid #ecebeb; padding: 0px 10px; color: #999;}
#pageArea #pageDec strong {float: left; background: #93c7ef; border: 1px solid #85b3d6; font-weight: bold; color: #fff; padding: 0px 12px; height: 28px; line-height: 28px; margin: 0px 3px;}
#pageArea #pageDec a, #main #pageArea #pageDec a:visited { float: left; margin: 0px 3px; height: 28px;line-height: 28px;  border: 1px solid #d6d6d6; padding: 0px 10px;}

#pageSelect{display: none;}
.footer { width: 100%; margin-top: 50px; background: #eee;  border-top: 1px solid #e8e8e8; text-align: center; line-height: 30px; padding: 30px 0; color: #777;}
.footer p,.footer p a{text-align: center;font-size: 14px; color:#777778;}


/*淇敼瀵嗙爜*/
#myForm{width:500px; height: auto;margin:40px auto;font-size: 16px; color: #444;}
#myForm p{margin:18px auto; height: 42px;line-height: 42px;}
#myForm input{ float: left; width: 250px;}
#myForm label{width:110px; text-align: right; display: block; float: left;}
#myForm input[type="submit"],#myForm input[type="button"]{ padding:0 30px; background: #8BD231; color: #fff; height: 43px; width: 260px; cursor: pointer;}

#myForm  button{float: left; width: 120px;height: 40px;margin-left: 10px;background:#fff; border-color: #09f; color: #09f;}
#myForm  button.disabled{background:#ddd; border-color: #ddd; color: #333;}
#phone{  width: 120px !important;}



/*xinzeng*/
.yh_center{display: inline-block;text-align: center;margin-top: 35px;margin-right: 30px;}
		.yh_center img{width:85px;height:85px;border-radius:85px;}
		.yh_center span{font-size: 26px;}
		.w-960{width:960px;margin: 0 auto;}
		.weatherBox{margin-top: 30px;}
		.yanyu{margin-right: 30px;margin-left:15px;}
		.luntan{margin-left:15px;margin-right: 30px;}
		.yanyu_luntan{text-align: center;border: 1px solid #ddd;padding: 20px;}
		.yanyu_luntan div{display: inline-block;text-align: center;width:120px;height:120px;margin-right:30px;}
		#listAll{padding-top: 0px !important}
		.userInfo{padding-top: 0px!important;}
		.undatepwd{margin-top: 0px !important}
		.readMore{text-align: center;}
		.daluntan_img img{width: 120px;height:120px;}
		.yanyu_img img{width: 120px;height:120px;}
		.yanyu img,.luntan img{width:100px;height:100px;}
		.yanyu p,.luntan p{font-size:18px;}
		
		
/*搜索框*/
#siteSearch {
    width: 277px;
    float: right;
    height: 37px;
    font-size: 14px;
    cursor: pointer;
    padding-top: 2px;
}
#siteSearch .input1 {
    border: 1px solid #2C73AC;
    /* border-left: none; */
    border-right: none;
    width: 200px;
    height: 35px;
    line-height: 35px;
    float: left;
    padding: 0px;
    padding: 0px 5px;
    border-radius: 0px;
}
#siteSearch .btn1 {
    border: 1px solid #2C73AC;
    background: #2C73AC;
    width: 66px;
    height: 37px;
    line-height: 37px;
    float: left;
    padding: 0px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    border-radius: 0px;
}

