* {
	padding:0px; margin:0px; font-size:12px;
}
#error_main_container {
  float:left; width:500px; height:300px; position: relative; left:50%; margin-left:-260px; margin-top:20px; padding:10px; background:#f5f5f5;
  
	border: 1px solid #ddd;
	-webkit-border-radius: 8px;
	-moz-border-radius: 8px;
	border-radius: 8px;
	-webkit-box-shadow: #000 0px 0px 10px;
	-moz-box-shadow: #eee 0px 0px 10px;
	box-shadow: #ddd 0px 0px 10px;
}

div.error_title_div {
	width:100%; height:44px; background:url("error_img/bg_line_1x44.jpg"); float:left;	
}
div.error_title_div h3 {
	height:44px; line-height: 44px; padding-left:60px; font-size:17px; color:#FFF; background: url("error_img/bg_41x44.jpg") no-repeat 0% 0%;
}
#error_content {
	width:100%; height:230px; float:left; margin-top:12px;
}
.error_title_h3 {
	width:232px; height:64px; background: url("error_img/error_232x64.jpg") no-repeat; float:left; position: relative;
}
.error_title_h3 span {
	position: absolute; bottom:10px; right:25px; color:#777; font-weight:normal;
}

dl.error_dl {
	float:left; width:100%; margin-top:15px;
}

dl.error_dl dt {
	padding:8px 0px 8px 20px;
}

dl.error_dl dd {
	padding:8px 0px 8px 40px; color:#ff2200;
}

a.error_href_class {
	float:left; width:111px; height:35px; background:url("error_img/btn_111x35.jpg") no-repeat; text-align: center; line-height:35px; letter-spacing: 2px; display: inline; margin-right:10px; 
}

a:link.error_href_class, a:visited.error_href_class {
	color:#FFF; text-decoration: none;
}
a:hover.error_href_class {
	text-decoration: underline;
}
