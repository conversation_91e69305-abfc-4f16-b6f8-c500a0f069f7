#jpage{margin-top: 15px; left: 35%;}
.jPaginate{
    height:50px;
    position:relative;
    color:#a5a5a5;
    font-size:small;   
	width:100%;
}
.jPaginate a{
    line-height:24px;
    height:30px;
    cursor:pointer;
    padding:2px 10px;
    margin:0 2px;
    float:left;
}
.jPag-control-back{
	position:absolute;
	left:0px;
}
.jPag-control-front{
	position:absolute;
	top:0px;
    left: 300px;
}
.jPaginate span{
    cursor:pointer;
    padding:2px 10px;
}
ul.jPag-pages{
    float:left;
    list-style-type:none;
    margin:0px 0px 0px 0px;
    padding:0px;
    width: 650px;
}
ul.jPag-pages li{
    display:inline;
    float:left;
    padding:0px;
    margin:0px;
}
ul.jPag-pages li a{
    float:left;
    padding:2px 10px;
}
span.jPag-current{
    cursor:default;
    font-weight:normal;
    line-height:24px;
    height:30px;
    float:left;
    margin: 0 2px;
}
.jPag-con{overflow: hidden; width: 200px;}

ul.jPag-pages li span.jPag-previous,
ul.jPag-pages li span.jPag-next,
span.jPag-sprevious,
span.jPag-snext,
ul.jPag-pages li span.jPag-previous-img,
ul.jPag-pages li span.jPag-next-img,
span.jPag-sprevious-img,
span.jPag-snext-img{
    height:22px;
    margin:2px;
    float:left;
    line-height:18px;
}

ul.jPag-pages li span.jPag-previous,
ul.jPag-pages li span.jPag-previous-img{
    margin:2px 0px 2px 2px;
    font-size:12px;
    font-weight:bold;
    width:10px;

}
ul.jPag-pages li span.jPag-next,
ul.jPag-pages li span.jPag-next-img{
    margin:2px 2px 2px 0px;
    font-size:12px;
    font-weight:bold;
    width:10px;
}
span.jPag-sprevious,
span.jPag-sprevious-img{
    font-size:18px;
    text-align:center;
    padding: 2px;
}
span.jPag-snext,
span.jPag-snext-img{
    margin:2px 2px 2px 0px;
    font-size:18px;
    width:15px;
     text-align:right;
}


