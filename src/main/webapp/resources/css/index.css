@charset "utf-8";
html,body {
  background: #f3f3f3;
}

.hide {
  display: none;
}

.header {
  height: 70px;
  background: #0849a0;
}

.width-primary {
  width: 1200px;
  margin: 0 auto;
}

.header .logo {
  height: 70px;
  line-height: 70px;
}

.header .logo img {
  height: 40px;
}

.header .crumb {
  display: inline-block;
  height: 30px;
  line-height: 30px;
  color: #fff;
  font-size: 16px;
  padding-top: 25px;
  margin-left: 10px;
}

.header .logout {
  display: inline-block;
  height: 15px;
  line-height: 15px;
  color: #fff;
  font-size: 14px;
  margin-top: 25px;
  padding: 5px;
  border: 1px solid #ffffff;
}

.content {
  background: #fff;
}

.leftBox {
  width: 220px;
  border-right: 1px solid #f3f3f3;
  min-height: 775px;
}

.userInfo {
  padding: 36px 0 40px;
}

.userInfo .userIcon {
  display: block;
  width: 100px;
  height: 100px;
  border-radius: 100px;
  margin: 0 auto;
}

.userInfo .userName {
  margin-top: 18px;
  text-align: center;
  font-size: 16px;
  color: #262626;
}

.optionList li.on {
  color: #fff;
  background: url(../img/list_bg.png) no-repeat center;
}

.optionList li {
  width: 220px;
  height: 52px;
  line-height: 52px;
  font-size: 16px;
  letter-spacing: 0;
  color: #262626;
  cursor: pointer;
  margin-bottom: 18px;
}

.optionList li .icon {
  display: inline-block;
  width: 25px;
  height: 52px;
  margin: 0 15px 0 36px;
  vertical-align: middle;
  background-position: center 15px;
  background-repeat: no-repeat;
}

.optionList .index .icon {
  background-image: url(../img/index.png);
}

.optionList .index.on .icon {
  background-image: url(../img/index_on.png);
}

.optionList .message .icon {
  background-image: url(../img/message.png);
}

.optionList .message.on .icon {
  background-image: url(../img/message_on.png);
}

.optionList .setting .icon {
  background-image: url(../img/setting.png);
}
.optionList .usercenter .icon {
  background-image: url(../img/usercenter.png);
}
.optionList .usercenter.on .icon {
  background-image: url(../img/usercenter_click.png);
}
.optionList .setting.on .icon {
  background-image: url(../img/setting_on.png);
}

.message {
  position: relative;
}

.msg_num {
  position: absolute;
  width: 25px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  font-size: 13px;
  color: #fff;
  background: #e40700;
  top: 12px;
  right: 45px;
  border-radius: 50%;
}

/* 右侧区域 */

.rightBox {
  width: 980px;
  position: relative;
  padding-top: 10px;
  min-height: 670px;
}

.myApp {
  margin-bottom: 20px;
  height: 345px;
  overflow: hidden;
  /* padding: 0 32px 0 40px; */
}

/* 我的应用 */

.panelTitle {
  font-size: 16px;
  font-weight: bold;
  color: #1f1f1f;
  letter-spacing: 0;
  padding-left: 36px;
}

.panelTitle.myAppTitle {
  height: 34px;
  line-height: 34px;
  margin-left: 40px;
  margin-bottom: 25px;
  background: url(../img/myApp.png) no-repeat left center;
}

.appList {
  /* padding: 0 32px 0 40px; */
  padding-left: 40px;
  box-sizing: content-box;
}

.appList li {
  margin: 0 45px;
}

.appList li img {
  width: 90px;
  height: 90px;
}

.appList li .appName {
  font-size: 14px;
  color: #262626;
  text-align: center;
  margin: 12px 0 22px;
}

/* 右侧底部 */

.rightBotttom {
  padding: 0 32px 0 40px;
}

.rightBotttom .newsBox {
  width: 443px;
}

.newsBox .panelTitle {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px dashed #ededed;
}

.newPaper.newsBox {
  margin-right: 21px;
}

.panelTitle .more {
  font-size: 13px;
  color: #8e8e8e;
  letter-spacing: 0;
  font-weight: normal;
  padding-right: 15px;
}

/* 最新稿件 */

.panelTitle.newPaperTitle {
  background: url(../img/newPaper.png) no-repeat left center;
}

/* 我的消息 */

.panelTitle.myMsg {
  background: url(../img/myMsg.png) no-repeat left center;
}

.newsList li {
  font-size: 14px;
  color: #1f1f1f;
  height: 30px;
  line-height: 30px;
  margin: 15px 0;
}

.newsList li a {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.newPaper .newsList li {
  background: url(../img/dot.png) no-repeat left center;
  padding-left: 15px;
}

.newPaper .newsList li a {
  width: 270px;
  margin-right: 15px;
}

.myMsg .newsList li {
  padding-left: 28px;
}

.myMsg .newsList li.msg_t {
  background: url(../img/msg_t.png) no-repeat left center;
}

.myMsg .newsList li.msg_g {
  background: url(../img/msg_g.png) no-repeat left center;
}

.myMsg .newsList li a {
  width: 260px;
  margin-right: 10px;
}

.copyRight {
  color: #999;
  font-size: 13px;
  letter-spacing: 0;
  text-align: center;
  height: 50px;
  line-height: 22px;
  margin-top: 30px;
}
.copyRight a{
  color: #999;
}

  /* 我的应用轮播 */

.appArea {
  height: 285px;
  position: relative;
  overflow-y: hidden;
}

.appArea .appPage {
  position: relative;
  z-index: 0;
}

.appArea .hd {
  width: 100%;
  position: absolute;
  bottom: 0;
  text-align: center;
  font-size: 0;
  z-index: 1;
}

.appArea .hd li {
  margin: 0 5px;
  background: url(../img/dot_big_blue.png) no-repeat 0 -16px;
  height: 16px;
  overflow: hidden;
  width: 16px;
  cursor: pointer;
  display: inline-block;
  *display: inline;
  zoom: 1;
  _background: url(../img/dot_blue.png) no-repeat 0 -16px;
}

.appArea .hd .on {
  background-position: 0 0;
}
.appArea:hover .prev{
  display: block;
  opacity: 0.2;
}
.appArea:hover .next{
  display: block;
  opacity: 0.2;
}
.appArea .prev,
.appArea .next {
  width: 40px;
  height: 60px;
  margin: -60px 0 0;
  display: none;
  background: url(../img/ad_ctr.png) no-repeat -5px -12px;
  position: absolute;
  top: 50%;
  z-index: 10;
  cursor: pointer;
  text-indent: -9999px;
  filter: alpha(opacity=20);
  opacity: 0.5;
  border-radius: 5px;
}

.appArea .prev {
  left: 15px;
}

.appArea .next {
  background-position: -12px -102px;
  right: 15px;
}

.appArea .prev:hover,
.appArea .next:hover {
  filter: alpha(opacity=50) !important;
  opacity: 0.5 !important;
}

/* 表单 */

.editeForm {
  width: 475px;
  margin: 0 auto;
  padding: 15px;
}

/* label */

.layui-form-label {
  width: 58px;
}

/* block */

.layui-input-block {
  margin-left: 88px;
}

/* 按钮组 */

.btnGroup {
  text-align: center
}

.layui-elem-field legend {
  font-size: 17px;
}

.tianqi {
  position: relative;
  height: 50px;
  margin: 0 30px;
}
.nowtime{
  position: absolute;
  right:55px;
  top:10px;
}
/* 天气 */
/*.weather:hover{*/
  /*text-decoration:underline;  !*鼠标放上去有下划线*!*/
  /*color: red;*/
 /*}*/
 .weather{
  display: block;
  float: left;
  margin-left: 15px;
  padding-top: 5px;
 }
.weather span{
  margin-right: 10px;
  font-size: 14px
}
.weatherimg img{
  width: 34px;
}