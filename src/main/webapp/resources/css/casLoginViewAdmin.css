/*Lpp2018/1/24*/

* {
    padding: 0;
    margin: 0;
    font-family: '<PERSON><PERSON>,' Microsoft YaHei UI ',' Microsoft YaHei ',<PERSON><PERSON><PERSON><PERSON>,' Segoe UI ',Tahoma,Helvetica,sans-serif';
}

/** css clearfix start */
.cl:after {
    display: block;
    clear: both;
    visibility: hidden;
    font-size: 0;
    height: 0;
    content: "";
}

.cl {
    *zoom: 1;
}

/** css clearfix end */
/** css public start */
body {
    font-size: 16px;
    background: url("../img/login_bg.png") no-repeat;
    background-size: 100%;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.layui-form {
    width: 80%;
    margin: auto;
}

.layui-form-item {
    background: #FBFDFF;
    border: 1px solid #E5E7E9;
    border-radius: 6px;
    width: 348px;
}

.layui-form-label {
    width: 15px;
}

/*左上角logo*/
.logo {
    margin-top: 50.2px;
    margin-left: 74px;
    width: 137.26px;
    height: 43.28px;
}

.logoTitle {
    margin-top: 46px;
    margin-left: 23.84px;
    padding-left: 23.84px;
    border-left: 1.4px solid rgba(225, 225, 225, 0.6);;
    height: 51px;
    font-family: AlibabaPuHuiTi-Medium;
    font-weight: 500;
    font-size: 28px;
    color: #FFFFFF;
    letter-spacing: 1.14px;
    line-height: 50.4px;
}

/*登录框*/
.login-box {
    position: absolute;
    left: 50%;
    margin-left: 100px;
    top: 50%;
    margin-top: -230px;
    width: 452px;
    height: 502px;
    background: #FFFFFF;
    box-shadow: 0 6px 18px 0 rgba(7, 64, 136, 0.51);
    border-radius: 8px;
}

/*欢迎登陆*/
.welcome {
    letter-spacing: 0;
    margin-top: 56px;
    margin-left: 44px;
    width: 100px;
    height: 30px;
    font-weight: bold;
    width: 104px;
    height: 37px;
    font-family: PingFangSC-SNaNpxibold;
    font-weight: 600;
    font-size: 26px;
    color: #111111;
}

#msg {
    display: block;
    font-size: 12px;
    margin: -15px 0px 15px 10px;
    color: #FF5722;
}


.login-input {
    position: relative;
}

.login-icon {
    position: absolute;
    top: 12px;
    left: 16px;
}

.phone-icon {
    width: 13px;
    height: 21px;
    background: url("../img/mobile.png") center center;
}

.yzm-input {
    width: 220px;
}

.yzm-btn {
    width: 104px;
}

.lock-btn {
    width: 104px;
}

/*图片验证码图片*/
.veryCode {
    width: 109px;
    height: 56px;
    cursor: pointer;
}

.login-btn {
    color: #fff;
    font-size: 18px;
    text-align: center;
    letter-spacing: 0;
    line-height: 54px;
    display: block;
    border: none;
    cursor: pointer;
    width: 348px;
    height: 56px;
    background: #006AFF;
    border-radius: 4px;
}

.login-btn:hover {
    color: #fff;
}

.login-button {
    margin-top: 20px;
}

.login-input input::-webkit-input-placeholder {
    /* WebKit browsers*/
    color: #C3C3C3;
}

.login-input input:-moz-placeholder {
    /* Mozilla Firefox 4 to 18*/
    color: #C3C3C3;
}

.login-input input::-moz-placeholder {
    /* Mozilla Firefox 19+*/
    color: #C3C3C3;
}

.login-input input:-ms-input-placeholder {
    /* Internet Explorer 10+*/
    color: #C3C3C3;
}

.yzm-btn .get-verify {
    opacity: .5;
    filter: alpha(opacity=65);
    cursor: not-allowed;
}

.yzm-btn .get-verify.active {
    cursor: pointer;
    opacity: 1;
}

.tip-bind {
    font-size: 12px;
    color: red;
    margin-left: 10px;
}

.layui-tab-content {
    padding: 0px !important;
}

.layui-tab-title li {
    width: 50%;
    padding: 0px;
}

.layui-tab-brief > .layui-tab-title .layui-this {
    color: #146FB8;
}

.layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-bottom: 2px solid #146FB8;
}

#login_container {
    width: 300px;
    height: 237px;
    margin: 0 auto;
}

.nobind {
    color: #FF5722;
    font-size: 18px;
    text-align: center;
    height: 230px;
    line-height: 230px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

.login {
    display: flex;
    align-items: center;
    max-width: 100vw;
}

/*背景图片*/
.login_pic {
    width: 100%;
    height: 100vh;
    background: url(../img/login_bg.png) top left no-repeat;
    background-size: cover;
    display: block;
}

.right {
    width: 617px;
    flex-shrink: 0;
    flex-grow: 0;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 92px;
    box-sizing: border-box;
}

.right h1 {
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    font-size: 42px;
    color: #000000;
    letter-spacing: 0;
    text-align: center;
    line-height: 59px;
}

.right .form {
    margin-top: 62px;
}

.right .label {
    display: block;
    width: 433px;
    height: 69px;
    border: 1px solid #C8C8C8;
    border-radius: 34.5px;
    margin-bottom: 30px;
    position: relative;
}

.right .label input::-webkit-input-placeholder {
    color: #c3c3c3;
}

/*获取验证码文字*/
#yzm_text {
    position: absolute;
    line-height: 20px;
    top: 0;
    right: 0;
    text-align: center;
    cursor: pointer;
    width: 80px;
    padding-left: 16px;
    height: 22px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #006AFF;
    letter-spacing: 0;
    border-left: 1px solid #E5E7E9;
    margin-top: 16px;
}

.right .label #yzm_pic {
    position: absolute;
    height: 49px;
    top: 10px;
    right: 32px;
}

.dis {
    color: #999 !important;
}

.layui-input {
    border: none !important;
    background: none;
    font-size: 15px;
    height: 54px;
}

/*图片验证码*/
.verify-input-width {
    width: 232px !important;
}

/*输入框高度*/
.layui-input-inline {
    height: 54px;
}

.phoneItem {
    margin-top: 23px;
}

.mt23 {
    margin-top: 23px;
}

.mt28 {
    margin-top: 28px;
}

.mr10 {
    margin-right: 10px;
}

/*验证码、手机号图标*/
.yzm_pic, .phone_pic {
    padding-top: 7px !important;
}

/*手机号数据行*/
.phoneLine {
    width: 290px !important;
}

/*图片验证码输入框*/
#imgCode {
    padding-left: 15px;
}