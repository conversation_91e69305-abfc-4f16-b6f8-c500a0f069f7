/*!
 * avgrund 0.1
 * http://lab.hakim.se/avgrund
 * MIT licensed
 *
 * Created by <PERSON><PERSON>, http://hakim.se
 */

.avgrund-active body {
	-webkit-transform: scale( 0.9 );
	   -moz-transform: scale( 0.9 );
	    -ms-transform: scale( 0.9 );
	     -o-transform: scale( 0.9 );
	        transform: scale( 0.9 );
}

.avgrund-cover {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 1;
	visibility: hidden;
	opacity: 0;
	background: rgba( 0, 0, 0, 0.5 );
}
	.avgrund-active .avgrund-cover {
		visibility: visible;
		opacity: 1;
	}

.avgrund-contents {
	position: relative;
	padding: 20px;
	max-width: 400px;
	height: 100%;
	margin: auto;
}
	.avgrund-active .avgrund-contents {
		-webkit-filter: blur(2px);
		   -moz-filter: blur(2px);
		    -ms-filter: blur(2px);
		     -o-filter: blur(2px);
		        filter: blur(2px);
	}
	.no-blur.avgrund-active .avgrund-contents {
		-webkit-filter: none;
		   -moz-filter: none;
		    -ms-filter: none;
		     -o-filter: none;
		        filter: none;
	}

.avgrund-popup {
	position: absolute;
	width: 340px;
	height: 180px;
	left: 50%;
	top: 50%;
	margin: -130px 0 0 -190px;
	visibility: hidden;
	opacity: 0;
	z-index: 2;
	padding: 20px;

	background: white;
	box-shadow: 0px 0px 20px rgba( 0, 0, 0, 0.6 );
	border-radius: 3px;

	-webkit-transform: scale( 0.8 );
	   -moz-transform: scale( 0.8 );
	    -ms-transform: scale( 0.8 );
	     -o-transform: scale( 0.8 );
	        transform: scale( 0.8 );
}
	.avgrund-active .avgrund-popup-animate {
		visibility: visible;
		opacity: 1;

		-webkit-transform: scale( 1.1 );
		   -moz-transform: scale( 1.1 );
		    -ms-transform: scale( 1.1 );
		     -o-transform: scale( 1.1 );
		        transform: scale( 1.1 );
	}
	.avgrund-popup.stack {
		-webkit-transform: scale( 1.5 );
		   -moz-transform: scale( 1.5 );
		    -ms-transform: scale( 1.5 );
		     -o-transform: scale( 1.5 );
		        transform: scale( 1.5 );
	}
	.avgrund-active .avgrund-popup.stack {
		-webkit-transform: scale( 1.1 );
		   -moz-transform: scale( 1.1 );
		    -ms-transform: scale( 1.1 );
		     -o-transform: scale( 1.1 );
		        transform: scale( 1.1 );
	}


.avgrund-ready body,
.avgrund-ready .avgrund-contents,
.avgrund-ready .avgrund-popup,
.avgrund-ready .avgrund-cover {
	-webkit-transform-origin: 50% 50%;
	   -moz-transform-origin: 50% 50%;
	    -ms-transform-origin: 50% 50%;
	     -o-transform-origin: 50% 50%;
	        transform-origin: 50% 50%;

	-webkit-transition: 0.3s all cubic-bezier(0.250, 0.460, 0.450, 0.940);
	   -moz-transition: 0.3s all cubic-bezier(0.250, 0.460, 0.450, 0.940);
	    -ms-transition: 0.3s all cubic-bezier(0.250, 0.460, 0.450, 0.940);
	     -o-transition: 0.3s all cubic-bezier(0.250, 0.460, 0.450, 0.940);
	        transition: 0.3s all cubic-bezier(0.250, 0.460, 0.450, 0.940);
}
.avgrund-ready .avgrund-popup.no-transition {
	-webkit-transition: none;
	   -moz-transition: none;
	    -ms-transition: none;
	     -o-transition: none;
	        transition: none;
}

