@charset "utf-8";

html{height:100%}
body{min-height: 100%;position: relative;font-size:14px;color:#333; background-color:#fff}
a{color:#333}a:hover,a:focus,.maincolor,.maincolor a{color:#06c}
.bg-1{ background-color:#f5fafe}
h4{line-height:30px}
/*页面框架*/
.Hui-header{top:0; right:0; left:0;height:50px;z-index:999; background:url(../images/loginbg.jpg) no-repeat center top; background-size: cover; color: #fff; height: 50px;}
.Hui-aside{position: absolute;top:50px;bottom:0;left:0;padding-top:10px;width:199px;z-index:99;overflow:auto;background: url(../images/loginbg.jpg) no-repeat center bottom; background-size: cover;border-top: 1px solid #ccc;border-right: 1px solid #ccc}
.Hui-article-box{position: absolute;top:50px;right:0;bottom: 0;left:199px; overflow:hidden; z-index:1; background-color:#fff;border-top: 1px solid #ccc;}
.Hui-article{position: absolute;top:35px;bottom:0;left:0; right:0;overflow:auto;z-index:1}
.Hui-container{ padding:45px 0 0}
.Hui-aside,.Hui-article-box,.dislpayArrow{-moz-transition: all 0.2s ease 0s;-webkit-transition: all 0.2s ease 0s;-ms-transition: all 0.2s ease 0s;-o-transition: all 0.2s ease 0s;transition: all 0.2s ease 0s}
.big-page .Hui-article-box,.big-page .dislpayArrow,.big-page .breadcrumb{left:0px}
.big-page .Hui-aside{ left:-200px}

/*组件*/
/*logo*/
.Hui-logo,.Hui-logo-m{height:44px; margin-left:20px;cursor:pointer;font-size:26px; font-weight:400; line-height:44px}
.Hui-logo{display:inline-block;padding-right:5px; position: absolute; color: #333;text-shadow:5px 5px 5px #ccc;}
.Hui-logo:hover,.Hui-logo-m:hover{text-decoration:none; color: #333;}
.Hui-logo-m{display:none;width:45px;margin-left:10px}
.Hui-subtitle{ font-size:12px; color:#eee; padding-top:15px}
.viewhome{ line-height:44px; padding-right:15px}
.viewhome a{ color:#FFF; padding-left:20px; background:url(../images/icon_link.png) no-repeat 0 center}
	
/*导航*/
#Hui-nav{ margin-left:200px; margin-right:260px;}
#Hui-nav > ul > li{ font-weight:normal;color: #fff;}
#Hui-nav > ul > li > a{padding:0 20px; color: #fff;}
#Hui-nav > ul > li,#Hui-nav > ul > li > a{height:44px; line-height:44px}
#Hui-nav > ul > li > a:hover,#Hui-nav > ul > li.current > a{ background-color:#E5F3E6;}
.Hui-userbar{ position:absolute; top:0px; right:20px}
.Hui-userbar > li{ float:left; display:inline-block; position:relative;line-height:50px}
.Hui-userbar > li > a{ display:inline-block; padding:0 10px;height:50px; line-height:50px;}
.Hui-userbar > li > a:hover,Hui-userbar > li.current > a{ text-decoration:none;background-color:#E5F3E6;}
#Hui-msg .badge{ position:absolute; left:19px; top:4px; font-size:12px; font-weight:normal; padding:1px 5px}

/*左侧菜单*/
.Hui-aside .menu_dropdown dl{ margin-bottom:0}
.Hui-aside .menu_dropdown dt{display:block;line-height:45px;padding-left:25px;cursor:pointer;position:relative;font-weight:normal; border-bottom: 1px solid #ccc; color:#333;}
.Hui-aside .menu_dropdown dt:hover [class^="icon-"]{ color:#7e8795}
.Hui-aside .menu_dropdown dt a{ color:#FAFBFC; text-decoration: none;}
.Hui-aside .menu_dropdown dt .menu_dropdown-arrow{ position:absolute;overflow: hidden; top:0; right:15px;transition-duration:0.3s ;transition-property:all; color: #333;}
.Hui-aside .menu_dropdown dd{ display:none}
.Hui-aside .menu_dropdown dt.selected .menu_dropdown-arrow{transform: rotate(180deg)}
.Hui-aside .menu_dropdown dd.selected{display:block; margin-left:0px}
.Hui-aside .menu_dropdown ul{padding:0px}
.Hui-aside .menu_dropdown li{line-height:40px;overflow:hidden;zoom:1}
.Hui-aside .menu_dropdown li a{display:block;font-weight: bold;color:#666;}
.Hui-aside .menu_dropdown li a:hover{color:#148cf1; background-color:#F5FAFE; text-decoration: none;}
.Hui-aside .menu_dropdown li a i{ font-weight: normal}
.Hui-aside .menu_dropdown dd li a{line-height:40px;padding-left:50px; font-weight:normal}
.Hui-aside .menu_dropdown li.current a,.menu_dropdown li.current a:hover{background-color:#E5F3E6;}
.Hui-aside .menu_dropdown dt .Hui-iconfont{margin-right: 10px;}
	/*菜单收缩切换*/
.dislpayArrow{position: absolute;top: 0;bottom: 0;left:200px;width:0px; height:100%;z-index:10}
.dislpayArrow a{ position:absolute; display:block; width:17px; height:61px;top:50%; margin-top:-30px;outline:none;background:url(../images/icon_arrow.png) no-repeat 0 0}
.dislpayArrow a.open{ background-position:0 -61px}
.dislpayArrow a:hover{ text-decoration:none; background-position:right 0}
.dislpayArrow a.open:hover{background-position:right -61px}

	/*选项卡导航*/
	.Hui-tabNav{height:35px; padding-right:75px;overflow:hidden; position:relative;background:#efeef0 url(../images/acrossTab-2.png) repeat-x 0 -175px;}
	.Hui-tabNav-wp{position:relative; height:35px;overflow:hidden}
	.Hui-tabNav .acrossTab{ position:absolute; height:26px; line-height:26px; background:none; top:8px; left:0;padding-top:0}
	.Hui-tabNav .acrossTab li,.Hui-tabNav .acrossTab li em{background-image:url(../images/acrossTab-2.png)}
	.Hui-tabNav .acrossTab li{height:26px;line-height:26px;}
	.Hui-tabNav .acrossTab li em{ right:-16px; height: 26px; width: 16px}
	.loading {background:url(../images/loading.gif) no-repeat center; height:100px}
	.show_iframe iframe {position: absolute;bottom: 0;height: 100%;width: 100%}
	.Hui-tabNav-more {position: absolute;right:0px;width:70px;top:4px;display: none}

	/*面包屑导航*/
	.breadcrumb{background-color:#F5F5F5}

	/*页脚
		Name:			mod_footer
		Level:			Global
		Sample:			<footer class="footer"><p><a target="_blank" href="/feedback.html">意见反馈</a><cite>|</cite><a target="_blank" href="/about">关于XX</a><cite>|</cite><a target="_blank" href="/jobs.html">诚聘英才</a><br/>Copyright &copy;2012 guojunhui.com 郭俊辉版权所有 京ICP备10046298-4号</p></footer>
		Explain:页脚
		Last Modify:	jackying
	*/
	.footer{border-top:1px solid #E8E8E8;margin-top:20px;font-family:tahoma,Arial;font-size:12px;color:#999;line-height:22px;text-align:center}
	.footer p{padding-top:15px}
	.footer a,.footer a:hover{color:#999}

/*==============以下是业务相关的样式====================*/
/*权限*/
.permission-list{ border:solid 1px #eee;}
.permission-list > dt{ background-color:#efefef;padding:5px 10px}
.permission-list > dd{ padding:10px; padding-left:30px}
.permission-list > dd > dl{ border-bottom:solid 1px #eee; padding:5px 0}
.permission-list > dd > dl > dt{ display:inline-block;float:left;white-space:nowrap;width:100px}
.permission-list > dd > dl > dd{ margin-left:100px;}
.permission-list > dd > dl > dd > label{ padding-right:10px}

/*图片预览*/
.portfolio-area{ margin-right: -20px;}
.portfolio-area li{position: relative; float: left; margin-right: 20px; width:162px; height:162px;margin-top: 20px;}
.portfolio-area li.hover{ z-index:9}
.portfolio-area li .portfoliobox{ position: absolute; top: 0; left: 0; width: 152px; height: 152px;padding:5px;border: solid 1px #eee; background-color: #fff;}
.portfolio-area li .checkbox{position: absolute; top: 10px; right: 5px; cursor:pointer}
.portfolio-area li.hover .portfoliobox{ height:auto;padding-bottom:10px;box-shadow:0 1px 3px rgba(68, 68, 68,0.3);-moz-box-shadow:0 1px 3px rgba(68, 68, 68,0.3);-webkit-box-shadow:0 1px 3px rgba(68, 68, 68,0.3)}
.portfolio-area li .picbox{width: 150px; height: 150px;overflow: hidden;text-align: center;vertical-align:middle;display:table-cell; line-height:150px;}
.portfolio-area li .picbox img{max-width:150px; max-height:150px;vertical-align:middle;_margin-top: expression_r(( 150 - this.height ) / 2);}
.portfolio-area li .textbox{ display: none; margin-top: 5px;}
.portfolio-area li.hover .textbox{ display: block;}
.portfolio-area li label{ display:block; cursor:pointer}