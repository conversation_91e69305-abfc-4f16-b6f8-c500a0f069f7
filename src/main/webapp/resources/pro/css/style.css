@charset "utf-8";
/* 你自己的样式 */
input{ background: transparent;}
input:-webkit-autofill { 
	-webkit-box-shadow: 0 0 0px 1000px #DBDCE3 inset !important; 
}
input[type='text'],select,button{-webkit-appearance: none;}
select{ background: url(../images/row.png) no-repeat right center; background-size: 12px;}
textarea{overflow: auto !important;}
input,.select-box,textarea{ max-width: 300px;}
p{ margin: 0;}
.textarea{ height: 150px; overflow: hidden;}
.block{display: block;}
.bg-blue{ background:#5A98DE; color: #fff;}
.blue{ color:#5A98DE;}
.grey{ color: #999;}
.red{ color:#F1786A;}
.red:hover{ color:#F15745;}
.bt-red{border-top: 2px solid #F1786A;}
.bt-success{ border-top: 2px solid #5EB95E;}
.bt-primary{ border-top: 2px solid #5A98DE;}
.bb-primary{ border-bottom: 2px solid #5A98DE;}
.border-grey{ border: 1px solid #E6E6E6;}
.bb-grey{ border-bottom: 1px solid #eee;}

.Todylist{  width: 100%;line-height: 30px;height: 30px;background-color: #f3f5f7;}
.Todylist li{ float: left; padding:0 10px; margin-right: 25px;}
.col_tit{ font-size: 16px; line-height: 35px; height: 35px; background-color: #eceff2; display: block; border-left:1px solid #eee; position: relative;}
.col_tit p{ display: block; background-color: #fff;padding: 0px 15px; line-height: 31px; position: absolute; top: 0; left: 0;}
.fm_box{ height: 350px; line-height: 35px; overflow: hidden;}
.fm_box a{max-width: 450px; overflow: hidden; text-overflow: ellipsis; display: block; white-space: nowrap; float: left;}
.col-6{min-width: 600px;}
.minWP{min-width: 1200px;}
.table{width: 100% !important;}

/*用户中心-修改手机号*/
#userEdit input[id='phoneYzm']{max-width: 250px;}

#userInfo{ background:#F5F5F5 url(../images/yybg.jpg) no-repeat center bottom; background-size: contain; }
#userInfo h3{ letter-spacing: 2px;}
