@charset "utf-8";
body{font-size:16px; font-family: "Microsoft YaHei","simsun","Helvetica Neue"}
input{border: none !important;background: none;outline: none !important; box-shadow: none !important; width: 200px !important;}
.header,.footer{ position:absolute; left:0; right:0; width:100%; z-index:99}
.title{font-size: 37px; letter-spacing:10px; text-shadow:4px 4px 8px #ccc;}
.loginWraper{ position:absolute; width:100%; left:0; top:0; bottom:0; right:0;z-index:3; background: url(../images/loginbg.jpg) no-repeat center;background-size: contain;}
.loginBox{ position:absolute; width:380px; height:280px; left:50%; top:45%; margin-left:-190px; margin-top:-140px; padding-top:33px; border-radius: 10px;}
.loginBox{ background: rgba(0,0,0,0.05); text-align: center;}
.regbox .loginBox{ padding-bottom: 35px; top:38%;}
.phone,.yzmbox{ font-size:20px; width:230px; margin:20px auto; line-height: 40px;}
.bg-grey{background:rgba(255,255,255,0.8); border-radius: 10px; text-align: center;}
.formControls a{ color: #5a98de;}
.formControls a:hover{text-decoration: underline;}
#checkbox{ width:16px !important; height: 16px;}
.btn{ width: 200px !important; border-image: url(zTreeStyle/img/diy/1_open.png) 30 ;}
.loginBox .yzmBtn{ width: 95px !important; float: right; height: 38px; line-height: 30px; margin-top: 4px;}
.loginBox .yzmInput{ width:125px !important;}
.footer{background-color:  rgba(0,0,0,0.1);padding: 15px 0; bottom:0; text-align:center; color:#666; font-size:14px;}
