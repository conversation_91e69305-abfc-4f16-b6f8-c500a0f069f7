@charset "utf-8";

*{word-wrap:break-word}
html,body,h1,h2,h3,h4,h5,h6,hr,p,iframe,dl,dt,dd,ul,ol,li,pre,form,button,input,textarea,th,td,fieldset{margin:0;padding:0}
ul,ol,dl{list-style-type:none}
html,body{*position:static}
html{font-family: sans-serif;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}
address,caption,cite,code,dfn,em,th,var{font-style:normal;font-weight:400}
input,button,textarea,select,optgroup,option{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit}
input,button{overflow: visible;vertical-align:middle;outline:none}
input[type="submit"],input[type="reset"],input[type="button"],input[type="text"],input[type="password"]{-webkit-appearance:none;outline:none}
body,th,td,button,input,select,textarea{font-family:"Microsoft Yahei","Hiragino Sans GB","Helvetica Neue",Helvetica,tahoma,arial,"WenQuanYi Micro Hei",Verdana,sans-serif,"\5B8B\4F53";font-size:12px;color: #333;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing:grayscale}
body{line-height:1.6}
h1,h2,h3,h4,h5,h6{font-size:100%}
a,area{outline:none;blr:expression(this.onFocus=this.blur())}
a{text-decoration:none;cursor: pointer}
a:hover{text-decoration:underline;outline:none}
a.ie6:hover{zoom:1}
a:focus{outline:none}
a:hover,a:active{outline:none}:focus{outline:none}
sub,sup{vertical-align:baseline}
button,input[type="button"], input[type="submit"] {line-height:normal !important}
/*img*/
img{border:0;vertical-align:middle}
a img,img{-ms-interpolation-mode:bicubic}
@media (max-width:767px){
	.img-responsive{max-width:100%!important;height: auto!important}
}

/*IE下a:hover 背景闪烁*/
*html{overflow:-moz-scrollbars-vertical;zoom:expression(function(ele){ele.style.zoom = "1";document.execCommand("BackgroundImageCache",false,true)}(this))}

/*HTML5 reset*/
header,footer,section,aside,details,menu,article,section,nav,address,hgroup,figure,figcaption,legend{display:block;margin:0;padding:0}time{display:inline}
audio,canvas,video{display:inline-block;*display:inline;*zoom:1}
audio:not([controls]){display:none}
legend{width:100%;margin-bottom:20px;font-size:21px;line-height:40px;border:0;border-bottom:1px solid #e5e5e5}
legend small{font-size:15px;color:#999}
svg:not(:root) {overflow: hidden}
fieldset {border-width:0;padding: 0.35em 0.625em 0.75em;margin: 0 2px;border: 1px solid #c0c0c0}
input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button {height: auto}
input[type="search"] {-webkit-appearance: textfield; /* 1 */}
input[type="search"]::-webkit-search-cancel-button,input[type="search"]::-webkit-search-decoration {-webkit-appearance: none}
/*清楚浮动
	Name:			style_clearfix
	Example:		class="clearfix|cl"
	Explain:		clearfix（简写cl）避免因子元素浮动而导致的父元素高度缺失能问题
*/
.cl:after,.clearfix:after{content:"\20";display:block;height:0;clear:both;visibility:hidden}.cl,.clearfix{zoom:1}

/*2.1 响应式栅格系统*/
.row.cl{}
.row{ box-sizing:border-box}
[class^="col-"],[class*=" col-"]{box-sizing:border-box; float:left;min-height: 1px;position: relative}
.col-1{width:8.33333%}
.col-2{width:16.66667%}
.col-3{width:25%}
.col-4{width:33.33333%}
.col-5{width:41.66667%}
.col-6{width:50%}
.col-7{width:58.33333%}
.col-8{width:66.66667%}
.col-9{width:75%}
.col-10{width:83.33333%}
.col-11{width:91.66667%}
.col-12{width:100%}

.col-offset-0{margin-left:0}
.col-offset-1{margin-left:8.33333%}
.col-offset-2{margin-left:16.66667%}
.col-offset-3{margin-left:25%}
.col-offset-4{margin-left:33.33333%}
.col-offset-5{margin-left:41.66667%}
.col-offset-6{margin-left:50%}
.col-offset-7{margin-left:58.33333%}
.col-offset-8{margin-left:66.66667%}
.col-offset-9{margin-left:75%}
.col-offset-10{margin-left:83.33333%}
.col-offset-11{margin-left:91.66667%}

.col-push-0{position:relative;left:0;right:auto}
.col-pull-0{right:0;left:auto}
.col-push-1{left:8.33333%;right:auto}
.col-pull-1{right:8.33333%;left:auto}
.col-push-2{left:16.66667%;right:auto}
.col-pull-2{right:16.66667%;left:auto}
.col-push-3{left:25%;right:auto}
.col-pull-3{right:25%;left:auto}
.col-push-4{left:33.33333%;right:auto}
.col-pull-4{right:33.33333%;left:auto}
.col-push-5{left:41.66667%;right:auto}
.col-pull-5{right:41.66667%;left:auto}
.col-push-6{left:50%;right:auto}
.col-pull-6{right:50%;left:auto}
.col-push-7{left:58.33333%;right:auto}
.col-pull-7{right:58.33333%;left:auto}
.col-push-8{left:66.66667%;right:auto}
.col-pull-8{right:66.66667%;left:auto}
.col-push-9{left:75%;right:auto}
.col-pull-9{right:75%;left:auto}
.col-push-10{left:83.33333%;right:auto}
.col-pull-10{right:83.33333%;left:auto}
.col-push-11{left:91.66667%;right:auto}
.col-pull-11{right:91.66667%;left:auto}
/*局部模块平分*/
.col-1-1{ width:100%}
.col-2-1{ width:50%}
.col-3-1{ width:33.333333%}
.col-3-2{ width:66.666667%}
.col-4-1{ width:25%}
.col-4-3{ width:75%}
.col-5-1{ width:20%}
.col-5-2{ width:40%}
.col-5-3{ width:60%}
.col-5-4{ width:80%}
.col-6-1{ width:16.666667%}
.col-6-5{ width:83.333333%}

@media (max-width:767px){
	.responsive [class^="col-"],.responsive [class*=" col-"]{float:none!important;width:auto!important}
	.responsive [class^="col-offset-"],.responsive [class*=" col-offset-"]{ margin-left:0px!important}
}
@media(min-width:768px){
	.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9{float:left;}
	.col-sm-12{width:100%;}
	.col-sm-11{width:91.66666666666666%;}
	.col-sm-10{width:83.33333333333334%;}
	.col-sm-9{width:75%;}
	.col-sm-8{width:66.66666666666666%;}
	.col-sm-7{width:58.333333333333336%;}
	.col-sm-6{width:50%;}
	.col-sm-5{width:41.66666666666667%;}
	.col-sm-4{width:33.33333333333333%;}
	.col-sm-3{width:25%;}
	.col-sm-2{width:16.666666666666664%;}
	.col-sm-1{width:8.333333333333332%;}
	.col-sm-pull-12{right:100%;}
	.col-sm-pull-11{right:91.66666666666666%;}
	.col-sm-pull-10{right:83.33333333333334%;}
	.col-sm-pull-9{right:75%;}
	.col-sm-pull-8{right:66.66666666666666%;}
	.col-sm-pull-7{right:58.333333333333336%;}
	.col-sm-pull-6{right:50%;}
	.col-sm-pull-5{right:41.66666666666667%;}
	.col-sm-pull-4{right:33.33333333333333%;}
	.col-sm-pull-3{right:25%;}
	.col-sm-pull-2{right:16.666666666666664%;}
	.col-sm-pull-1{right:8.333333333333332%;}
	.col-sm-pull-0{right:0;}
	.col-sm-push-12{left:100%;}
	.col-sm-push-11{left:91.66666666666666%;}
	.col-sm-push-10{left:83.33333333333334%;}
	.col-sm-push-9{left:75%;}
	.col-sm-push-8{left:66.66666666666666%;}
	.col-sm-push-7{left:58.333333333333336%;}
	.col-sm-push-6{left:50%;}
	.col-sm-push-5{left:41.66666666666667%;}
	.col-sm-push-4{left:33.33333333333333%;}
	.col-sm-push-3{left:25%;}
	.col-sm-push-2{left:16.666666666666664%;}
	.col-sm-push-1{left:8.333333333333332%;}
	.col-sm-push-0{left:0;}
	.col-sm-offset-12{margin-left:100%;}
	.col-sm-offset-11{margin-left:91.66666666666666%;}
	.col-sm-offset-10{margin-left:83.33333333333334%;}
	.col-sm-offset-9{margin-left:75%;}
	.col-sm-offset-8{margin-left:66.66666666666666%;}
	.col-sm-offset-7{margin-left:58.333333333333336%;}
	.col-sm-offset-6{margin-left:50%;}
	.col-sm-offset-5{margin-left:41.66666666666667%;}
	.col-sm-offset-4{margin-left:33.33333333333333%;}
	.col-sm-offset-3{margin-left:25%;}
	.col-sm-offset-2{margin-left:16.666666666666664%;}
	.col-sm-offset-1{margin-left:8.333333333333332%;}
	.col-sm-offset-0{margin-left:0;}
}
@media(min-width:992px){
	.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9{float:left;}
	.col-md-12{width:100%;}
	.col-md-11{width:91.66666666666666%;}
	.col-md-10{width:83.33333333333334%;}
	.col-md-9{width:75%;}
	.col-md-8{width:66.66666666666666%;}
	.col-md-7{width:58.333333333333336%;}
	.col-md-6{width:50%;}
	.col-md-5{width:41.66666666666667%;}
	.col-md-4{width:33.33333333333333%;}
	.col-md-3{width:25%;}
	.col-md-2{width:16.666666666666664%;}
	.col-md-1{width:8.333333333333332%;}
	.col-md-pull-12{right:100%;}
	.col-md-pull-11{right:91.66666666666666%;}
	.col-md-pull-10{right:83.33333333333334%;}
	.col-md-pull-9{right:75%;}
	.col-md-pull-8{right:66.66666666666666%;}
	.col-md-pull-7{right:58.333333333333336%;}
	.col-md-pull-6{right:50%;}
	.col-md-pull-5{right:41.66666666666667%;}
	.col-md-pull-4{right:33.33333333333333%;}
	.col-md-pull-3{right:25%;}
	.col-md-pull-2{right:16.666666666666664%;}
	.col-md-pull-1{right:8.333333333333332%;}
	.col-md-pull-0{right:0;}
	.col-md-push-12{left:100%;}
	.col-md-push-11{left:91.66666666666666%;}
	.col-md-push-10{left:83.33333333333334%;}
	.col-md-push-9{left:75%;}
	.col-md-push-8{left:66.66666666666666%;}
	.col-md-push-7{left:58.333333333333336%;}
	.col-md-push-6{left:50%;}
	.col-md-push-5{left:41.66666666666667%;}
	.col-md-push-4{left:33.33333333333333%;}
	.col-md-push-3{left:25%;}
	.col-md-push-2{left:16.666666666666664%;}
	.col-md-push-1{left:8.333333333333332%;}
	.col-md-push-0{left:0;}
	.col-md-offset-12{margin-left:100%;}
	.col-md-offset-11{margin-left:91.66666666666666%;}
	.col-md-offset-10{margin-left:83.33333333333334%;}
	.col-md-offset-9{margin-left:75%;}
	.col-md-offset-8{margin-left:66.66666666666666%;}
	.col-md-offset-7{margin-left:58.333333333333336%;}
	.col-md-offset-6{margin-left:50%;}
	.col-md-offset-5{margin-left:41.66666666666667%;}
	.col-md-offset-4{margin-left:33.33333333333333%;}
	.col-md-offset-3{margin-left:25%;}
	.col-md-offset-2{margin-left:16.666666666666664%;}
	.col-md-offset-1{margin-left:8.333333333333332%;}
	.col-md-offset-0{margin-left:0;}
}
@media(min-width:1200px){
	.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9{float:left;}
	.col-lg-12{width:100%;}
	.col-lg-11{width:91.66666666666666%;}
	.col-lg-10{width:83.33333333333334%;}
	.col-lg-9{width:75%;}
	.col-lg-8{width:66.66666666666666%;}
	.col-lg-7{width:58.333333333333336%;}
	.col-lg-6{width:50%;}
	.col-lg-5{width:41.66666666666667%;}
	.col-lg-4{width:33.33333333333333%;}
	.col-lg-3{width:25%;}
	.col-lg-2{width:16.666666666666664%;}
	.col-lg-1{width:8.333333333333332%;}
	.col-lg-pull-12{right:100%;}
	.col-lg-pull-11{right:91.66666666666666%;}
	.col-lg-pull-10{right:83.33333333333334%;}
	.col-lg-pull-9{right:75%;}
	.col-lg-pull-8{right:66.66666666666666%;}
	.col-lg-pull-7{right:58.333333333333336%;}
	.col-lg-pull-6{right:50%;}
	.col-lg-pull-5{right:41.66666666666667%;}
	.col-lg-pull-4{right:33.33333333333333%;}
	.col-lg-pull-3{right:25%;}
	.col-lg-pull-2{right:16.666666666666664%;}
	.col-lg-pull-1{right:8.333333333333332%;}
	.col-lg-pull-0{right:0;}
	.col-lg-push-12{left:100%;}
	.col-lg-push-11{left:91.66666666666666%;}
	.col-lg-push-10{left:83.33333333333334%;}
	.col-lg-push-9{left:75%;}
	.col-lg-push-8{left:66.66666666666666%;}
	.col-lg-push-7{left:58.333333333333336%;}
	.col-lg-push-6{left:50%;}
	.col-lg-push-5{left:41.66666666666667%;}
	.col-lg-push-4{left:33.33333333333333%;}
	.col-lg-push-3{left:25%;}
	.col-lg-push-2{left:16.666666666666664%;}
	.col-lg-push-1{left:8.333333333333332%;}
	.col-lg-push-0{left:0;}
	.col-lg-offset-12{margin-left:100%;}
	.col-lg-offset-11{margin-left:91.66666666666666%;}
	.col-lg-offset-10{margin-left:83.33333333333334%;}
	.col-lg-offset-9{margin-left:75%;}
	.col-lg-offset-8{margin-left:66.66666666666666%;}
	.col-lg-offset-7{margin-left:58.333333333333336%;}
	.col-lg-offset-6{margin-left:50%;}
	.col-lg-offset-5{margin-left:41.66666666666667%;}
	.col-lg-offset-4{margin-left:33.33333333333333%;}
	.col-lg-offset-3{margin-left:25%;}
	.col-lg-offset-2{margin-left:16.666666666666664%;}
	.col-lg-offset-1{margin-left:8.333333333333332%;}
	.col-lg-offset-0{margin-left:0;}
}
/*2.2 布局
	Name:			style_Layout
	Explain:		左右两栏|左中右三栏|上中下
	Last Modify:	guojunhui
*/
.Hui-wraper,.wp,.container{margin-left:auto;margin-right:auto;text-align:left}
.container-fluid{ width:100%}
.minWP{}
.sd{float:right}
.ct2 .mn{float:left}
.ct2 .sd{float:right}
.ct2_r .leftmenu{float:left}
.ct2_r .mn{float:right}
.ct3 .app{float:left;margin-right:20px}
.ct3 .mn{float:left;border-left:solid 1px #eee}
.ct3 .sd{float:right}
.w-1000{width:1000px}
.w-980{width:980px}
.w-300{width:300px}
.w-200{width:200px}
@media (max-width: 1000px) {
	.Hui-wraper,.wp,.container{ width:auto!important;padding:0 10px!important}
}

@media print{
	.Hui-wraper,.container{width:auto}
}


/*3.0 基础样式*/
/*3.1 排版*/
/*3.1.1 标题
	Example：
	<h1>h1. 大标题<small>小标题</small></h1>
    <h2>h2. 大标题<small>小标题</small></h2>
    <h3>h3. 大标题<small>小标题</small></h3>
    <h4>h4. 大标题<small>小标题</small></h4>
    <h5>h5. 大标题<small>小标题</small></h5>
    <h6>h6. 大标题<small>小标题</small></h6>
*/
h1,h2,h3,h4,h5,h6{font-weight:500;line-height:1.1;color:inherit}
h1 small,h2 small,h3 small,h4 small,h5 small,h6 small,h1 .small,h2 .small,h3 .small,h4 .small,h5 .small,h6 .small{font-weight:400;line-height:1;color:#999}
h1,h2,h3{padding-top:20px;padding-bottom:10px}
h1 small,h2 small,h3 small,h1 .small,h2 .small,h3 .small{font-size:65%}
h4,h5,h6{margin-top:10px;margin-bottom:10px}
h4 small,h5 small,h6 small,h4 .small,h5 .small,h6 .small{font-size:75%}
h1{font-size:36px}
h2{font-size:30px}
h3{font-size:24px}
h4{font-size:18px}
h5{font-size:14px}
h6{font-size:12px}

/*3.1.2 强调
	Example:
	<p>这是段落，向下10像素间距</p>
	<smail>小型文本，是父容器字体大小的85%</smail>
	<strong>重要文本，加粗显示</strong>
	<em>被强调的文本，斜体显示</em>
	<u>带下划线的文本</u>
	<cite>引用</cite>
	<mark>突出显示文本</mark>
	<del>带删除线的文本</del>
	<pre class="prettyprint linenums">预格式化的文本</pre>
*/
p{margin-bottom:10px}	/*段落*/
small{font-size:85%}	/*小型文本*/
b,strong {font-weight: bold}	/*重要的文本，加粗*/
em{font-style:italic}	/*被强调的文本*/
i{}	/*斜体*/
u{}	/*加下划线*/
cite{font-style:normal}	/*引用*/
mark{color:#000;background:#ff0}/*突出显示文本*/
var{}	/*变量*/
kbd{}	/*键盘文本*/
code{}	/*计算机代码文本*/
dfn{font-style: italic}	/*一个定义项目*/
del{font-family:Simsun}	/*删除线*/
code,kbd,pre,samp {font-family: monospace, serif;font-size: 1em}
pre{white-space: pre-wrap}	/*预格式化的文本*/
.uppercase{text-transform:uppercase} /*文字大写*/
.lowercase{text-transform:lowercase} /*文字小写*/
.capitalize{text-transform:capitalize} /*首字母大写*/
.en{font-family:Arial!important}
/*3.1.3 对齐
	Name:			style_text-align
	Example:		class="text-l|text-r|text-c|va-t|va-m|va-b"
	Explain:		.text-水平对齐 （.text-l左对齐|.text-r右对齐|.text-c居中对齐）
					.va-上下对齐 （.va-t 居上对齐|.va-m 居中对齐|.va-b 居下对齐）
*/
.text-l{text-align:left}.text-r{text-align:right}.text-c{text-align:center}
.va *{vertical-align:sub!important;*vertical-align:middle!important;_vertical-align:middle!important}
.va-t{vertical-align:top!important}.va-m{vertical-align:middle!important}.va-b{vertical-align:bottom!important}
/*3.1.4 定位
	Name:			style_position
	Example:		class="pos-r|pos-a|pos-f"
	Explain:		.pos-r 相对定位|.pos-a 绝对定位|.pos-f 固定
*/
.pos-r{position:relative}.pos-a{position:absolute}.pos-f{position:fixed}
/*3.1.5 浮动
	Name:			style_float
	Example:		class="l|r"
	Explain:		.l 左浮动|.r 右浮动
*/
.l,.f-l{float:left!important;_display:inline}
.r,.f-r{float:right!important;_display:inline}

[class*="span"].r,
[class*="span"].f-r{float:right}

/*控制元素对定位的位置：居左|居右|j居上|居下*/
.left{ left:0; right:auto}
.right{ right:0; left:auto}
.top{ top:auto; bottom:0}
.bottom{ top:0; bottom:auto}

/*3.1.6 文字单行溢出省略号
	Name:			style_text-overflow
	Example:		class="text-overflow"
*/
.text-overflow{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
/*3.1.7 线条
	Name:			style_line
	Example:		class="line"
*/
.line{font-size: 0px; line-height: 0px; border-top: solid 1px #eee; float: none}
/*3.1.8 外边距
	Name:			style_margin
	Example:		class="mt-5|mt-10..."
	Explain:		.mt表示上边距|.mb表示下边距|.ml表示左边距|.mr表示右边距
*/
.mt-5{margin-top:5px}.mt-10{margin-top:10px}.mt-15{margin-top:15px}.mt-20{margin-top:20px}.mt-25{margin-top:25px}.mt-30{margin-top:30px}.mt-35{margin-top:35px}.mt-40{margin-top:40px}.mt-50{margin-top:50px}
.mb-5{margin-bottom:5px}.mb-10{margin-bottom:10px}.mb-15{margin-bottom:15px}.mb-20{margin-bottom:20px}.mb-30{margin-bottom:30px}.mb-40{margin-bottom:40px}.mb-50{margin-bottom:50px}
.ml-5{margin-left:5px}.ml-10{margin-left:10px}.ml-15{margin-left:15px}.ml-20{margin-left:20px}.ml-30{margin-left:30px}.ml-40{margin-left:40px}.ml-50{margin-left:50px}
.mr-5{margin-right:5px}.mr-10{margin-right:10px}.mr-15{margin-right:15px}.mr-20{margin-right:20px}.mr-30{margin-right:30px}.mr-40{margin-right:40px}.mr-50{margin-right:50px}
/*3.1.9 内填充
	Name:			style_padding
	Example:		class="pt-5|pt-10|……"
	Explain:		.pt表示上填充|.pb表示下填充|.pl表示左填充|.pr表示右填充
*/
.pt-5{padding-top:5px}.pt-10{padding-top:10px}.pt-20{padding-top:20px}
.pb-5{padding-bottom:5px}.pb-10{padding-bottom:10px}.pb-20{padding-bottom:20px}
.pl-5{padding-left:5px}.pl-10{padding-left:10px}.pl-20{padding-left:20px}
.pr-5{padding-right:5px}.pr-10{padding-right:10px}.pr-20{padding-right:20px}
.pd-5{padding:5px}.pd-10{padding:10px}.pd-15{padding:15px}.pd-20{padding:20px}.pd-30{padding:30px}.pd-40{padding:40px}
/*3.1.10 边框，css3圆角
	Name:			style-border
	Example:		class="bk_gray radius"
	Explain:		.bk_gray 边框|radius 圆角|round 椭圆 | circle 圆形
*/
.bk-gray{border:solid 1px #eee}
.radius{border-radius:4px; overflow:hidden;}
.size-MINI.radius{ border-radius:3px}
.size-L.radius{ border-radius:5px}
.size-XL.radius{ border-radius:6px}
.round{border-radius:1000px; overflow:hidden;}
.circle{ border-radius:50%; overflow:hidden;}
/*3.1.11 css3阴影
	Name:			style_shadow
	Example:		class="box_shadow|text-shadow"
	Explain:		box_shadow 块级元素阴影，全局样式，可用在表格，文本框，文本域，div等块级元素上。
					text-shadow 文字阴影
*/
.box-shadow{-webkit-box-shadow:0px 0px 10px #ddd;box-shadow:0px 0px 10px #ddd;}
.text-shadow{-webkit-text-shadow:3px -3px 8px #eee;text-shadow:3px -3px 8px #eee;}
/*3.1.12 行内分割竖线
	Name:			style_pipe
	Example:		<span class="pipe">|</span>
*/
.pipe{margin:0 5px;color:#CCC;font-size:10px!important}
/*3.1.13 文字尺寸
	Name:			style_font-size
	Example:		class="f-12|f-14|f-16|f-18|f-20|f-24|f-26|f-28|f-30"
	Explain:		12px字体|14px字体|16px字体|18px字体|20px字体|24px字体|26px字体|28px字体|30px字体
*/
.f-12{font-size:12px}.f-14{font-size:14px}.f-16{font-size:16px}.f-18{font-size:18px}.f-20{font-size:20px}.f-24{font-size:24px}.f-26{font-size:26px}.f-28{font-size:28px}.f-30{font-size:30px}
/*3.1.14 文字行距
	Name:			mod_line-height
	Example:		class="lh-16|lh-18|lh-20|lh-22|lh-24|lh-26|lh-28|lh-30"
	Explain:		16px行高|18px行高|20px行高|22px行高|24px行高|26px行高|30px行高
*/
.lh-16{line-height:16px}.lh-18{line-height:18px}.lh-20{line-height:20px}.lh-22{line-height:22px}.lh-24{line-height:24px}.lh-26{line-height:26px}.lh-28{line-height:28px}.lh-30{line-height:30px}
/*2.0以前的兼容版本*/
.l16{line-height:16px}.l18{line-height:18px}.l20{line-height:20px}.l22{line-height:22px}.l-24{line-height:24px}.l-26{line-height:26px}.l-28{line-height:28px}.l-30{line-height:30px}

/*3.1.15 文字颜色
	Name:			style_color
	Example:		class="c-primary|c-sub|c-success|c-danger|c-warning|c-333|c-666|c-999|c-red|c-green|c-blue|c-white|c-black|c-orange"
	Explain:		主要颜色|次主色|强调色—成功|强调色—危险|强调色—警告色|强调色—错误色|次主色—浅黑|辅助色—灰色|标准色—红色|标准色—绿色|标准色—蓝色|标准色—白色|标准色—黑色|标准色—橙色
*/
/*全局默认链接颜色*/
body{ background-color:#fff; color:#333}
.bg-fff{ background-color:#fff}
a{color:#333}
a:hover{color:#06c}

/*主要颜色*/
.c-primary,.c-primary a,a.c-primary{color:#5a98de}
.c-primary a:hover,a.c-primary:hover{ color:#5a98de}
/*次主色*/
.c-secondary,.c-secondary a,a.c-secondary{color:#555}
.c-secondary a:hover,a.c-secondary:hover{ color:#555}

/*强调色—成功*/
.c-success,.c-success a,a.c-success{color:#5eb95e}
.c-success a:hover,a.c-success:hover{ color:#5eb95e}

/*强调色—危险*/
.c-danger,.c-danger a,a.c-danger{color:#dd514c}
.c-danger a:hover,a.c-danger:hover{ color:#dd514c}

/*强调色—警告*/
.c-warning,.c-warning a,a.c-warning{color:#f37b1d}
.c-warning a:hover,a.c-warning:hover{ color:#f37b1d}

/*强调色—错误*/
.c-error,.c-error a,a.c-error{color:#c00}
.c-error a:hover,a.c-error:hover{ color:#c00}

/*辅助色—浅黑*/
.c-333,.c-333 a,a.c-333{color:#333}
.c-333 a:hover,a.c-333:hover{ color:#333}

/*辅助色—灰色*/
.c-666,.c-666 a,a.c-666{color:#666}
.c-666 a:hover,a.c-666:hover{ color:#666}
.c-999,.c-999 a,a.c-999{color:#999}
.c-999 a:hover,a.c-999:hover{color:#999}

/*标准色—红色*/
.c-red,.c-red a,a.c-red{color:red}
.c-red a:hover,a.c-red:hover{ color:#red}
/*标准色—绿色*/
.c-green,.c-green a,a.c-green{color:green}
.c-red a:hover,a.c-red:hover{color:green}
/*标准色—蓝色*/
.c-blue,.c-blue a,a.c-blue{color:blue}
.c-blue a:hover,a.c-blue:hover{color:blue}
/*标准色—白色*/
.c-white,.c-white a,a.c-white{color:white}
.c-white a:hover,a.c-white:hover{color:white}
/*标准色—黑色*/
.c-black,.c-black a{color:black}
.c-black a:hover,a.c-black:hover{color:black}
/*标准色—橙色*/
.c-orange,.c-orange a,a.c-orange{color:orange}
.c-orange a:hover,a.c-orange:hover{color:orange}

/*3.1.16 文字颜色强调	从2.0起废弃3.1.16 文字强调，字体颜色全部放入到3.1.15
	Example:		class="text-muted|text-primary|text-warning|text-error|text-danger|text-success|text-info"
	Explain:		柔和|重要|警告|错误|危险|成功|信息
*/


/*3.1.17 缩略语
	Example:		<abbr title="User Interface" class="initialism">UI</abbr>
	Explain:		
*/
abbr[title],abbr[data-original-title]{cursor:help;border-bottom:1px dotted #999}
abbr.initialism{font-size:90%;text-transform:uppercase}
/*3.1.18 地址
	Example:		<address>北京市海淀区上地……</address>
	Explain:		
*/
address{display:block;margin-bottom:20px;font-style:normal;line-height:20px}
/*3.1.19 引用
	Example:		<blockquote>这是引用的内容</blockquote>
	Explain:		
*/
blockquote{padding:0 0 0 15px;margin:0 0 20px;border-left:5px solid #eee}
blockquote p{margin-bottom:0;font-size:17.5px;font-weight:300;line-height:1.25}
blockquote small{display:block;line-height:20px;color:#999}
blockquote small:before{content:'\2014 \00A0'}
blockquote.text-r{padding-right:15px;padding-left:0;border-right:5px solid #eee;border-left:0}
blockquote.text-r p,blockquote.text-r small{text-align:right}
blockquote.text-r small:before{content:''}
blockquote.text-r small:after{content:'\00A0 \2014'}
q:before,q:after,blockquote:before,blockquote:after{content:""}
q {/*短的引用*/quotes: "\201C" "\201D" "\2018" "\2019"}
/*3.1.20 上标，下标
	Example:		<sup>2</sup>	<sub>2<sub>
	Explain:		上标|下标
*/
sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}
sup{top:-0.5em}sub{bottom:-0.25em}
/*3.1.21 内容样式
	Name:			style_content
	Example:		<div class="content"><p>……</p></div>
	Explain:		内容样式
*/
.content{position:relative;font-size:14px;line-height:1.6;overflow:hidden;text-align:left}
.content h3{margin-top:20px;font-size:16px}
.content p{margin-top:20px}
	.indent{ text-indent:2em}
.content img{max-width:100%}
.content ul{text-indent:2em}
@media (max-width: 480px) {
	.content img{max-width:100%!important; height:auto!important;width:expression(this.width > 320 ? "320px" : this.width)!important}
	*html .content img{width:expression(this.width>320&&this.width>this.height?320:auto)}
}
/*3.1.22 列表
	Name:			style_ulolli
	Example:		<ul class="tlist"><li>无序列表</li><li>无序列表</li><li>无序列表</li></ul>
					<ol class="linenums"><li>有序列表</li><li>有序列表</li><li>有序列表</li></ol>
	Explain:		
*/
ul.unstyled,ol.unstyled{margin-left:0;list-style:none}
ul.inline,ol.inline{margin-left:0;list-style:none}
ul.inline>li,ol.inline>li{display:inline-block;*display:inline;padding-right:5px;padding-left:5px;*zoom:1}
.tlist li{line-height:22px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}/*禁止换行*/
	.dot-1{padding-left:10px;background:url(../images/dot_1.gif) no-repeat 3px center}/*圆点*/
	.box-1{padding-left:10px;background:url(../images/box_1.gif) no-repeat 3px center}/*小方块*/
	.jt-1{padding-left:18px;background:url(../images/jt_1.gif) no-repeat 3px center}/*箭头1*/
	.jt-2{padding-left:18px;background:url(../images/jt_2.gif) no-repeat 3px center}/*箭头2*/
	.jt-3{padding-left:18px;background:url(../images/jt_3.gif) no-repeat 3px center}/*箭头3*/
/*排行榜*/
/*<ol class="top-list"><li class="top"><em>1</em><a href="#">排行榜列表</a><span class="date">12</span></li></ol>*/
.top-list li{height: 20px;line-height: 20px;margin-bottom: 15px;overflow: hidden;padding-left: 30px;position: relative;text-overflow: ellipsis;vertical-align: bottom;white-space: nowrap}
.top-list em {background-color: #F8F8F8;border: 1px solid #EBEBEB;color: #333;height: 18px;left: 0;line-height: 18px;position: absolute;text-align: center;top: 0;width: 20px; font-style:normal}
.top-list .top em{background-color:#390;border:none;color: #fff;height: 20px;line-height: 20px;width: 22px}
.top-list .date {color: #999;font-size: 12px;position: absolute;right: 0;top: 0}
.tlist .time{font-size:12px; font-family:Arial; color:#999}

.listview{}
/*横向手机 竖向平板*/
@media (max-width: 767px) {
	.tlist li{position:relative; font-size:18px;border-bottom: 1px solid #eee}
	.tlist li a{display:block;padding:0.7em 30px 0.7em 12px}
	.dot_1{padding-left:0px}
	.night .tlist li{border-bottom: 1px solid #1F1F1F}
	.tlist .time{display:none}
	.tlist i {background:url(../images/article/arrow.png) no-repeat 0 0; background-size:9px auto; height: 15px;margin-top: -7px;position: absolute;right:10px;top: 50%;width: 9px}
	.night .tlist i{background-position:0 -75px}
	
}
/*3.1.23 描述
	Name:			style_dldtdd
	Example:		<dl class="dl-horizontal cl"><dt>H-ui</dt><dd>基于Bootstrap框架的改进扩展的前端框架</dd></dl>
	Explain:		.dl-horizontal 水平描述，默认不加为垂直模式。
*/
.dl-horizontal.cl{}
.dl-horizontal dt{float:left;width:160px;overflow:hidden;clear:left;text-align:right;text-overflow:ellipsis;white-space:nowrap}
.dl-horizontal dd{margin-left:180px}
/*3.1.24 隐藏 显示
  Name:				style_display
  Example:    <div class="hide">隐藏的内容</div> <div class="show">显示的内容</div>
  Explain:    		.hide 隐藏 / .show 显示
*/
.hide{display:none}[hidden]{display: none}
.hidden{display:none!important;visibility:hidden!important}
.f-hide{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}
[class*="span"].hide,.row-fluid [class*="span"].hide{display:none}
.show{display:block}
.invisible{visibility:hidden}

/*3.1.25 尺寸	新增尺寸全局类名，用于表单、按钮、表格、头像、标签等元素上，要与元素一起配合使用，方能看到效果*/
.size-MINI{}/*迷你*/
.size-S{}/*小*/
.size-M{}/*中 缺省默认尺寸，可以不写，可以理解为：均码*/
.size-L{}/*L*/
.size-XL{}/*大*/
.size-XXL{}/*加大*/
.size-XXXL{}/*超大*/

.input-text,.btn,.input-text.size-M,.btn.size-M{ font-size:14px; height:31px;line-height:1.42857;padding:4px}/*默认为M，可以不写，可以理解为：均码*/
a.btn,a.btn.size-M,span.btn,span.btn.size-M{ line-height:21px}
.btn,.btn.size-M{ padding:4px 12px}

.input-text.size-MINI,.btn.size-MINI{font-size:12px; height:23px;padding:1px 2px;line-height:1.42857}/*迷你*/
a.btn.size-MINI,span.btn.size-MINI{ line-height:21px}
.btn.size-MINI{ padding:1px 4px}

.input-text.size-S,.btn.size-S{font-size:12px; height:27px;padding:3px;line-height:1.42857}/*小*/
a.btn.size-S,span.btn.size-S{ line-height:19px}
.btn.size-S{ padding:3px 8px}

.input-text.size-L,.btn.size-L{font-size:16px; height:41px; padding:8px}/*大*/
a.btn.size-L,span.btn.size-L{ line-height:23px}
.btn.size-L{ padding:8px 16px}

.input-text.size-XL,.btn.size-XL{font-size:18px; height:48px; padding:10px}/*特大*/
a.btn.size-XL,span.btn.size-XL{ line-height:26px}
.btn.size-XL{ padding:10px 24px}

@media (max-width: 767px) {
	.responsive .input-text.size-MINI,.responsive .btn.size-MINI{height:24px}
	.responsive .input-text.size-S,.responsive .btn.size-S{ font-size:14px; height:30px}
	.responsive .input-text,.btn,.responsive .input-text.size-M,.responsive .btn.size-M{ font-size:16px; height:36px}
	.responsive .input-text.size-L,.responsive .btn.size-L{ font-size:18px; height:42px}
	.responsive .input-text.size-XL,.responsive .btn.size-XL{ font-size:20px; height:48px}
}

/*3.2 代码
	Name:			style_pre

	Example:		<code></code>,<pre class="prettyprint linenums">转义过的代码</pre>
	Explain:		code：行内代码，pre：基本代码块;包装代码片段，.prettyprint颜色增强/.linenums显示行号
*/
code{padding:0 3px 2px;font-family:Monaco,Menlo,Consolas,"Courier New",monospace}
pre .title,pre .keyword,pre .body,pre .des{color:#333}/*关键词*/

pre{display:block;font-family:Monaco,Menlo,Consolas,"Courier New",monospace;padding:9.5px;margin-bottom:10px;font-size:12px;line-height:20px;word-break:break-all;word-wrap:break-word;white-space:pre;white-space:pre-wrap;background-color:#f5f5f5;border:1px solid #ccc;border-radius:4px;color:#333}
.prettyprint{margin-bottom:20px;padding:8px;background-color:#f7f7f9;border:1px solid #e1e1e8}
.prettyprint .com { color: #998;font-style:italic }/*注释*/
.prettyprint .tag{color:#007}/*标签*/
.prettyprint .lit { color: #195f91}
.prettyprint .pun,.prettyprint .opn,.prettyprint .clo { color: #93a1a1}/*等于*/
.prettyprint .fun { color: #dc322f}
.prettyprint .str,.prettyprint .atv { color: #D14}/*值*/
.prettyprint .kwd,.prettyprint .prettyprint .tag { color: #1e347b}
.prettyprint .typ,.prettyprint .atn,.prettyprint .dec,.prettyprint .var { color: teal}/*文档声明，属性*/
.prettyprint .pln { color: #48484c}

.prettyprint.linenums{box-shadow:inset 40px 0 0 #fbfbfc,inset 41px 0 0 #ececf0}
.pre-scrollable{max-height:340px;overflow-y:scroll}
ol.linenums{list-style:decimal outside none; margin-left:20px}
ol.linenums li{ line-height:28px; padding-left:10px}
pre ol.linenums{margin:0 0 0 33px}
pre ol.linenums li{padding-left:12px;color:#bbb;line-height:18px;text-shadow:0 1px 0 #fff}

@media (max-width: 767px) {
	pre ol.linenums{ margin-left:0; list-style:none}
}

/*3.3 表格
	Name:			style_table
	Example:		<table class="table table-bordered table-striped table-condensed"><thead><tr><th>…</th></tr></thead><tbody><tr><td>…</td></tr></tbody></table>
	Explain:		表格，None无样式，仅仅有列和行|.table行与行之间以水平线相隔|.table-bordered表格外围均有外边框|.table-striped奇数行背景设为浅灰色|.table-condensed竖直方向padding缩减一半，从8px变为4px，所有的 td 和 th 元素都受影响
*/
/*默认table*/
table{width:100%;empty-cells:show;background-color:transparent;border-collapse:collapse;border-spacing:0}
table th{text-align:center; font-weight:400}
/*带水平线*/
.table th{font-weight:bold;}
.table th,.table td{padding:8px;line-height:20px}
.table td{text-align:center}
.table tbody tr.success > td{background-color:#dff0d8}
.table tbody tr.error > td{background-color:#f2dede}
.table tbody tr.warning > td{background-color:#fcf8e3}
.table tbody tr.info > td{background-color:#d9edf7}
.table tbody + tbody{border-top:2px solid #eee}
.table .table{background-color:#fff}

/*带横向分割线*/
.table-border{border-top:1px solid #eee; border-radius: 5px;}
/*.table-border th,.table-border td{border-bottom:1px solid #eee;}*/

/*th带背景*/
.table-bg thead th{background-color:#EDEDEE; border-bottom: 1px solid #ddd; border-top: 1px solid #ddd;}
/*带外边框*/
.table-bordered{border:1px solid #eee;border-collapse:separate;*border-collapse:collapse; border-radius: 5px;}
/*.table-bordered th,.table-bordered td{border-left:1px solid #eee;border-radius: 5px;}*/
/*.table-border.table-bordered{border-bottom:0}*/

/*奇数行背景设为浅灰色*/
.table-striped tbody > tr:nth-child(odd) > td,.table-striped tbody > tr:nth-child(odd) > th{background-color:#f9f9f9}
/*竖直方向padding缩减一半*/
.table-condensed th,.table-condensed td{padding:4px 5px}
/*鼠标悬停样式*/
.table-hover tbody tr:hover td,.table-hover tbody tr:hover th{background-color: #f5f5f5}
/*鼠标选择整行样式*/
.table tbody tr.selected td{background-color:#F3F3F3}
/*定义颜色*/
/*悬停在行*/
.table tbody tr.active,.table tbody tr.active>td,.table tbody tr.active>th,.table tbody tr .active{background-color:#F5F5F5!important}
/*成功或积极*/
.table tbody tr.success,.table tbody tr.success>td,.table tbody tr.success>th,.table tbody tr .success{background-color:#DFF0D8!important}

/*警告或出错*/
.table tbody tr.warning,.table tbody tr.warning>td,.table tbody tr.warning>th,.table tbody tr .warning{background-color:#FCF8E3!important}
/*危险*/
.table tbody tr.danger,.table tbody tr.danger>td,.table tbody tr.danger>th,.table tbody tr .danger{background-color:#F2DEDE!important}

/*表格文字对齐方式，默认是居左对齐*/
.table .text-c th,.table .text-c td{text-align:center}/*整行居中*/
.table .text-r th,.table .text-r td{text-align:right}/*整行居右*/
.table th.text-l,.table td.text-l{text-align:left!important}/*单独列居左*/
.table th.text-c,.table td.text-c{text-align:center!important}/*单独列居中*/
.table th.text-r,.table td.text-r{text-align:right!important}/*单独列居右*/

/*datatable*/
table.dataTable {border-collapse: separate;border-spacing: 0;clear: both}
table.dataTable thead .sorting_asc, table.dataTable thead .sorting_desc, table.dataTable thead .sorting {cursor: pointer; background-repeat:no-repeat;background-position:right center}
table.dataTable thead .sorting{background-image:url(../images/sort_both.png)}
table.dataTable thead .sorting_asc {background-image:url(../images/sort_asc.png)}
table.dataTable thead .sorting_desc {background-image:url(../images/sort_desc.png)}
.dataTable td.sorting_1 {background-color: #f5fafe}
.dataTables_wrapper .dataTables_length {float: left;padding-bottom:20px}
.dataTables_wrapper .dataTables_length .select{ width:50px}
.dataTables_wrapper .dataTables_filter {float: right;text-align: right}
.dataTables_wrapper .dataTables_filter .input-text { width:auto}
.dataTables_wrapper .dataTables_info {clear: both;float: left;padding-top:10px;font-size:14px; color:#666}
.dataTables_wrapper .dataTables_paginate {float: right;padding-top:10px;text-align: right}
.dataTables_wrapper .dataTables_paginate .paginate_button {border: 1px solid #eee;cursor: pointer;display: inline-block;margin-left: 2px;text-align: center;text-decoration: none;color: #666;height: 26px;line-height: 26px;text-decoration: none;margin: 0 0px 6px 6px;padding: 0 10px;font-size:14px}
.dataTables_wrapper .dataTables_paginate .paginate_button:hover,.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {background:#5a98de;color:#fff}

/*表格尺寸*/
.table.size-MINI{}/*迷你*/
.table.size-S{}/*小*/
.table.size-M{}/*中*/
.table.size-L{}/*默认为L，可以不写，可以理解为：均码*/
.table.size-XL{}/*大*/
.table.size-XXL{}/*加大*/
.table.size-XXXL{}/*超大*/

/*让表格支持响应式*/
/*
<div class="table-responsive">
  <table class="table">
    ...
  </table>
</div>
*/
@media (max-width: 767px) {
  table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-x: scroll;
    overflow-y: hidden;
    border: 1px solid #eeeeee;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    -webkit-overflow-scrolling: touch}
  table-responsive > .table {
    margin-bottom: 0}
  table-responsive > .table > thead > tr > th,
  table-responsive > .table > tbody > tr > th,
  table-responsive > .table > tfoot > tr > th,
  table-responsive > .table > thead > tr > td,
  table-responsive > .table > tbody > tr > td,
  table-responsive > .table > tfoot > tr > td {
    white-space: nowrap}
  table-responsive > .table-bordered {
    border: 0}
  table-responsive > .table-bordered > thead > tr > th:first-child,
  table-responsive > .table-bordered > tbody > tr > th:first-child,
  table-responsive > .table-bordered > tfoot > tr > th:first-child,
  table-responsive > .table-bordered > thead > tr > td:first-child,
  table-responsive > .table-bordered > tbody > tr > td:first-child,
  table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0}
  table-responsive > .table-bordered > thead > tr > th:last-child,
  table-responsive > .table-bordered > tbody > tr > th:last-child,
  table-responsive > .table-bordered > tfoot > tr > th:last-child,
  table-responsive > .table-bordered > thead > tr > td:last-child,
  table-responsive > .table-bordered > tbody > tr > td:last-child,
  table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0}
  table-responsive > .table-bordered > tbody > tr:last-child > th,
  table-responsive > .table-bordered > tfoot > tr:last-child > th,
  table-responsive > .table-bordered > tbody > tr:last-child > td,
  table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0}
}

/*3.4 表单
	Name:			style_form
	Example:
	Explain:

*/
/*3.4.1 input,textarea 文本域 文本区域*/
/*默认状态*/
.input-text,.textarea{box-sizing:border-box;border:solid 1px #DEDEDE;width:100%;
    -webkit-transition:all 0.2s linear 0s;
       -moz-transition:all 0.2s linear 0s;
         -o-transition:all 0.2s linear 0s;
            transition:all 0.2s linear 0s}
.textarea{ height:100px; resize:none; font-size:14px; padding:4px}
	.textarea-numberbar{ position:absolute; right:20px; bottom:5px; z-index:1; margin-bottom:0}
	.textarea-length{ font-style:normal}
.input-text:hover,.textarea:hover{border: solid 1px #3bb4f2}
/*得到焦点后*/
.input-text.focus,textarea.focus{border:solid 1px #0f9ae0 \9;border-color:rgba(82,168,236,0.8);box-shadow:0 1px 1px rgba(0, 0, 0, 0.075) inset, 0 0 8px rgba(102, 175, 233, 0.6)}
/*不可点击*/
.input-text.disabled,.textarea.disabled,.input-text.disabled.focus,.textarea.disabled.focus{background-color:#ededed; cursor:default;border-color: #eee;
	-webkit-box-shadow:inset 0 2px 2px #e8e7e7;
       -moz-box-shadow:inset 0 2px 2px #e8e7e7;
            box-shadow:inset 0 2px 2px #e8e7e7}
/*只读状态*/
.input-text.disabled,.textarea.disabled{background-color:#e6e6e6; cursor:default}
/*阴影*/
.input-text.box-shadow,.textarea.box-shadow{-ms-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}
/*行内元素*/
.input-text.inline{ display:inline-block; width:auto}

/*3.4.2 checkbox radio  单选 多选
	Example:
<div class="skin-minimal">
  <div class="check-box">
    <input type="checkbox" id="checkbox-1">
    <label for="checkbox-1">复选框</label>
  </div>
</div>

<div class="skin-minimal">
  <div class="radio-box">
    <input type="radio" id="minimal-radio-1" name="demo-radio1">
    <label for="minimal-radio-1">单选按钮</label>
  </div>
</div>
	Explain: 
*/
input[type="radio"],input[type="checkbox"] {line-height: normal; margin-top:-4px}
.check-box,.radio-box{ display:inline-block; box-sizing:border-box; cursor:pointer; position:relative; padding-left:30px; padding-right:20px}
.icheckbox,
.icheckbox-red,
.icheckbox-green,
.icheckbox-blue,
.icheckbox-aero,
.icheckbox-grey,
.icheckbox-orange,
.icheckbox-yellow,
.icheckbox-pink,
.icheckbox-purple,
.iradio,
.iradio-red,
.iradio-green,
.iradio-blue,
.iradio-aero,
.iradio-grey,
.iradio-orange,
.iradio-yellow,
.iradio-pink,
.iradio-purple {position: absolute;top:4px;left: 0}
@media (max-width: 767px) {
	.responsive .check-box,.responsive .radio-box{ display:block}
}

/*3.4.3 select 下拉框
<span class="select-box">
  <select class="select" size="1" name="demo">
    <option value="1" selected>默认</option>
    <option value="2">菜单二</option>
    <option value="3">菜单三</option>
  </select>
</span>
	Explain:	
	select 是表单元素中最难美化的一个，有两种美化方式：
		1、修改源生的，修改有限，只能修改个边框，背景，字体。优点：程序方便操作；缺点：丑，浏览器之间存在很大差异。
		2、将源生的隐藏掉，用其他元素（如div li）通过js模拟下拉交互，然后再传值给源生的select。优点：好看，兼容好；缺点：代码冗余，依赖JS，不方便操作
*/
/*方法一、修改源生*/
.select-box{border:solid 1px #eee;box-sizing:border-box;vertical-align:middle; width:100%; display:inline-block}
	.select{border:solid 1px #eee;box-sizing:border-box;cursor: pointer;line-height:normal;font-weight: normal;width:100%}
.select-box .select{ border:none}
.select-box.inline,.select-box.inline .select{ width:auto}

.select-box,
.select-box.size-M{height:31px;padding:4px 5px}
	.select,.size-M .select{font-size: 14px}

.select-box.size-MINI{height:23px;padding:0px 5px}
	.size-MINI .select{font-size: 12px}
		
.select-box.size-S{height:27px;padding:3px 5px}
	.size-S .select{font-size: 12px}
	
.select-box.size-L{height:41px;padding:8px 5px}
	.size-L .select{font-size: 16px}

.select-box.size-XL{height:48px;padding:10px 5px}
	.size-XL .select{font-size: 18px}

@media (max-width: 767px) {
	.responsive .select-box{ border:none}
	.responsive .select-box .select,.responsive .select{border:solid 1px #eee; padding:10px;font-size:16px}
	.responsive .select-box,.responsive .select-box.size-M,.responsive .select-box.size-MINI,.responsive .select-box.size-S,.responsive .select-box.size-L,.responsive .select-box.size-XL{ height:auto; padding:0}
}

/*方法二、JS模拟
<select name="demo" data-enabled="false">
  <option value="1" selected>默认</option>
  <option value="2">菜单二</option>
  <option value="3">菜单三</option>
</select>
需要引用2个js文件
<script type="text/javascript" src="lib/squid.js"></script> 
<script type="text/javascript" src="lib/jselect-1.0.js"></script>
页面调用方法 
<script type="text/javascript">
squid.swing.jselect()
</script>
*/
.select-wrapper {position:relative; display:inline-block;font-size:14px;cursor:default}
.select-default{zoom: 1;display:block; padding-left:10px; padding-right:30px;background-color:#fff;border:solid 1px #d0d0d0;height:34px;line-height:34px}
.jsselect.radius{ overflow:visible}
.jsselect.radius .select-default{ border-radius:4px}
.jsselect.radius .select-list{border-radius:4px;border-top-left-radius:0;border-top-right-radius:0}
.select-icon {position: absolute;height:8px;width:12px;right:10px;top:50%; margin-top:-4px;background: url(../images/iconpic-arrow-down2.png) no-repeat center}
.select-icon:hover {}
.unselectable {-moz-user-select: none;-khtml-user-select: none;-webkit-user-select: none;-o-user-select: none;user-select: none}
.select-list {position:absolute;left:0; right:0;top:100%;z-index:1;line-height:34px;max-height:320px; overflow:auto;_height:expression(this.scrollHeight > 319 ? "320px" : "auto");background-color:#fff;background-clip: padding-box;-moz-box-shadow:0 1px 2px rgba(0, 1, 1, 0.2);-webkit-box-shadow:0 1px 2px rgba(0, 1, 1, 0.2);box-shadow:0 1px 2px rgba(0, 1, 1, 0.2);box-sizing:border-box;border:solid 1px #d0d0d0;behavior: url(../lib/ie-css3.htc)}
.select-item {margin: 0;padding: 0}
.select-option {background:#fff;line-height:34px;text-align:left;white-space:nowrap; cursor:pointer; border-bottom:1px solid #f2f2f2; padding:0 10px}
.select-item .selected {background-color:#148cf1;color:#fff}

/*3.4.4 input-file 文件上传
	Example: 
<span class="btn-upload">
  <a style="z-index:2;" href="javascript:void();" class="btn btn-primary radius"><i class="iconfont">&#xf0020;</i> 浏览文件<input type="file" multiple name="file_0" class="input-file"></a>
</span>
	Explain：把文件选择域设为透明,通过绝对定位覆盖在长的好看的按钮上面。
*/
.btn-upload{position: relative; display:inline-block;height:31px; *display:inline;overflow:hidden;vertical-align:middle;cursor:pointer}
	.upload-url{cursor: pointer; width:300px}
	.input-file{position:absolute; right:0; top:0; cursor: pointer; z-index:1; font-size:30em;opacity:0;filter: alpha(opacity=0)}
.form-group .upload-btn{ margin-left:-1px}
.btn-upload .icon-add,.btn-upload .icon-minus{cursor: pointer;display: inline-block;font-family: Arial;font-size: 30px;height: 31px;line-height: 31px;text-align: center;vertical-align: middle;width: 36px}
@media (max-width: 767px) {
	/*.btn-upload{ position:static}
		.input-file{ position:static; opacity:1; font-size:16px}*/
		.upload-btn{ display:none}
		.upload-url{ display:none}
}

/*数字表单*/
.numberControlBox{display:inline-block;overflow:hidden;vertical-align: middle}
.ncb-up,.ncb-down{font-size:0px;display:block;height:10px;background-color:#f4f4f4;background:-moz-linear-gradient(top,rgb(255,255,255) 0%,rgb(230,230,230) 50%,rgb(255,255,255) 100%);width:24px;border:1px solid #d1d1d1;cursor:pointer}
.ncb-up{margin-bottom:1px}
.numberControlBox .ncb_ico{display:block;height:10px;background-image:url(../img/icon-arrow.png);background-repeat:no-repeat}
.ncb-up .ncb_ico{background-position: -22px center}
.ncb-down .ncb_ico{background-position: 1px center}
.ncb_btn_hover{border:1px solid #9dc7e7;background-color:#dff2fc;background:-moz-linear-gradient(top,rgb(255,255,255) 0%,rgb(210,237,250) 50%,rgb(255,255,255) 100%)}
.ncb_btn_selected{border:1px solid #6198c2;background-color:#aee1fb;background:-moz-linear-gradient(top,rgb(255,255,255) 0%,rgb(174,225,251) 50%,rgb(255,255,255) 100%)}
.input-text[type="number"]{width:80px}

/*3.4.5 表单布局*/
label,.placeholder{font-size:14px}
.form legend{font-size:20px}/*表单名称*/
.form .row{margin-top:15px}/*表单行*/
		.form-label{display:block; color:#555}/*表单标题*/
		.formControls{position:relative}/*表单控制区*/
		.formControls > *{vertical-align:middle}
			.placeholder{position:absolute; left:4px; top:4px;color:#c6c6c6; cursor:text}/*表单默认值*/	
.form-horizontal .row{display:table;width:100%}
.form-horizontal .form-label{margin-top:3px;cursor:text;text-align:right;padding-right:10px}
.form-horizontal .formControls{ padding-right:10px}
.form-horizontal .Validform_checktip{ margin-top:5px}
/*设置placeholder颜色*/
::-webkit-input-placeholder {color:#b3b3b3}/* WebKit browsers */
:-moz-placeholder {color:#b3b3b3}/* Mozilla Firefox 4 to 18 */
::-moz-placeholder {color:#b3b3b3}/* Mozilla Firefox 19+ */
:-ms-input-placeholder {color:#b3b3b3}/* Internet Explorer 10+ */
.placeholder{color:#adb0be; position:absolute; z-index:9}/*不兼容placeholder属性的浏览器，可使用<span class="placeholder">表单默认值</span>*/
@media (max-width: 767px) {
	.responsive .form-label{ text-align:left;padding-right:0; font-size:16px; float:none}
	.responsive .formControls{ padding-right:0; float:none; margin-top:10px}
}
/*3.4.6 表单验证*/
/*文本框的错误状态*/
.Validform_error{background-color:#fbe2e2; border-color:#c66161; color:#c00}
.Validform_wrong,.Validform_right,.Validform_warning{display:inline-block;height:20px;font-size:12px;vertical-align:middle; padding-left:25px}
/*错误*/
.Validform_wrong{background:url(../images/icon_error_s.png) no-repeat 0 center;color:#ef392b}
/*正确*/
.Validform_right{background:url(../images/icon_right_s.png) no-repeat 0 center}
/*警告*/
.Validform_warning{background:url(../images/icon_warning_s.png) no-repeat 0 center;color:#777}

/*密码等级*/
.passwordStrength b{font-weight:400}
.passwordStrength b,.passwordStrength span{display:inline-block; vertical-align:middle;line-height:16px;line-height:18px\9;height:16px}
.passwordStrength span{width:57px;text-align:center;background-color:#d0d0d0;	border-right:1px solid #fff}
.passwordStrength .last{border-right:none}
.passwordStrength .bgStrength{color:#fff;background-color:#fcc900}
/*Validform对话框*/
#Validform_msg{font-size:14px;width:300px; -webkit-box-shadow:2px 2px 3px #aaa; -moz-box-shadow:2px 2px 3px #aaa; background:#fff; position:absolute; top:0px; right:50px; z-index:99999; display:none;filter: progid:DXImageTransform.Microsoft.Shadow(Strength=3, Direction=135, Color='#999999'); box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1)}
#Validform_msg .iframe{position:absolute; left:0px; top:-1px; z-index:-1}
#Validform_msg .Validform_title{font-size:20px; padding:10px;text-align:left;color:#fff; position:relative; background-color:#fcc900}
#Validform_msg a.Validform_close:link,#Validform_msg a.Validform_close:visited{position:absolute; right:8px; top:6px; color:#fff; text-decoration:none; font-family:Verdana}
#Validform_msg a.Validform_close:hover{color:#fff}
#Validform_msg .Validform_info{padding:10px;border:1px solid #bbb; border-top:none; text-align:left}
@media (max-width: 767px) {
	.responsive .Validform_checktip{margin-top:10px}
}
/*3.5 按钮
	Name:			style_button
	Example:		<button class="btn radius radius btn-primary|btn-info|btn-success|btn-warning|btn-danger|btn-inverse|btn-link" type="button">按钮</button>
	Explain:		btn-primary：主要|btn-info：信息|btn-success：成功|btn-warning：警告|btn-danger：危险|btn-inverse：反向|btn-link：链接

*/
/*关闭*/
.close{font-size:20px;color: #000;text-shadow: 0 1px 0 #fff;opacity: 0.2;filter: alpha(opacity=20)}
.close:hover,.close:focus{color: #000;text-decoration: none;cursor: pointer;opacity: 0.4;filter: alpha(opacity=40)}

button.close{padding:0;cursor:pointer;background:transparent;border: 0;-webkit-appearance: none}
/*按钮*/
.btn{display:inline-block;box-sizing:border-box;cursor:pointer;text-align:center;font-weight:400;white-space:nowrap;vertical-align: middle;-moz-padding-start:npx; -moz-padding-end:npx;border:solid 1px #eee; background-color:#fff; width:auto;*zoom:1;*overflow:visible;
	-webkit-transition:background-color .1s linear;
		-moz-transition:background-color .1s linear;
		-o-transition:background-color .1s linear;
			transition:background-color .1s linear}
a.btn:hover,a.btn:focus,a.btn:active,a.btn.active,a.btn.disabled,a.btn[disabled]{text-decoration:none}
.btn:active,.btn.active{background-color:#ccc}
.btn:first-child{*margin-left:0}
.btn.active,.btn:active{-moz-box-shadow:0 1px 8px rgba(0, 0, 0, 0.125) inset;-webkit-box-shadow:0 1px 8px rgba(0, 0, 0, 0.125) inset; box-shadow:0 1px 8px rgba(0, 0, 0, 0.125) inset}

/*默认——灰色	通常用于取消*/
.btn-default{background-color:#e6e6e6;border-color:#e6e6e6}
.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active{color:#333;background-color:#c7c7c7;border-color:#c7c7c7}

/*主要——主色	通常用于确定、提交、购买、支付等*/
.btn-primary{color:#fff;background-color:#5a98de; border-color:#5a98de}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active{color:#fff;background-color:#0a6999;border-color:#0a6999}

/*次要按钮*/
.btn-secondary{color:#fff;background-color:#3bb4f2; border-color:#3bb4f2}
.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active,
.btn-secondary.active{color:#fff;background-color:#0f9ae0;border-color:#0f9ae0}

/*成功*/
.btn-success{color:#fff;background-color:#5eb95e; border-color:#5eb95e}
.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active{color:#fff;background-color:#429842;border-color:#429842}

/*警告*/
.btn-warning{color:#fff;background-color:#f37b1d; border-color:#f37b1d}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active{color:#fff;background-color:#c85e0b;border-color:#c85e0b}

/*危险*/
.btn-danger{color:#fff;background-color:#dd514c; border-color:#dd514c}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active{color:#fff;background-color:#c62b26;border-color:#c62b26}

/*链接*/
.btn-link{color:#0e90d2;cursor:pointer;border-color:transparent;background-color:transparent}
.btn-link:hover,
.btn-link:focus,
.btn-link:active,
.btn-link.active{color:#095f8a;text-decoration:underline;background-color:transparent}

/*禁用状态*/
.btn.disabled{cursor:not-allowed;background-image:none;opacity:.65;filter:alpha(opacity=65);box-shadow:none; pointer-events:none}

/*块级按钮*/
.btn-block {-moz-box-sizing: border-box;display: block;padding-left: 0;padding-right: 0;width: 100%}

/*表单组*/
.form-group{ display:inline-block;vertical-align:middle}
.form-group .input-text{ position:relative; vertical-align:top}
.form-group.radius .input-text{border-radius:4px 0 0 4px}
.form-group.round .input-text{border-radius:1000px 0 0 1000px}
.form-group .btn{position:relative;margin-left:-1px}
.form-group.radius .btn{ border-radius:0 4px 4px 0}
.form-group.round .btn{ border-radius:0 1000px 1000px 0}

/*3.6 图片*/
.duang-opacity img{-webkit-transition:opacity .1s linear;-moz-transition:opacity .1s linear;-o-transition:opacity .1s linear;transition:opacity .1s linear}
.duang-opacity a:hover img,a:hover .opacity img{opacity:0.85;filter: alpha(opacity=85)}
/*3.6.1 图集效果
	Name:			modal_album
	Example:
<div class="album-item">
	<div class="album-img">
		<img src="">
	</div>
	<div class="album-title"> 《仙剑奇侠传》赵灵儿<span class="c-999">(20张)</span></div>
	<div class="album-bg">
		<div class="album-bg-Fir"></div>
		<div class="album-bg-Sec"></div>
	</div>
</div>*/
.album-item{}
	.album-img{ border:1px solid #e0e0e0}
	.album-img img{ display:block}
	.album-title{display:block;text-align:left;padding:7px 5px;line-height:18px;color:#555;text-decoration:none;font-size:12px;border:solid 1px #e0e0e0;border-top:0}
	.album-bg-Fir,.album-bg-Sec{border:1px solid #e6e6e6;border-top:1px solid #f5f5f5;height:1px;margin:0 auto;overflow:hidden}
	.album-bg-Fir{ margin:0 3px}
	.album-bg-Sec{ margin:0 6px}

/*3.6.2 头像
	Name:			mod_avatar
	Example:		<i class="avatar avatar-L radius"><img src="static/h-ui/images/avatar-default-S.gif"></i>
*/
.avatar{display:inline-block;position:relative; overflow:hidden}
.avatar img{ display:block}
.avatar.radius,.avatar.radius img{border-radius:50%}
.avatar,.avatar img{width:32px; height:32px}
.avatar.size-MINI{ width:16px;height:16px}
.avatar.size-S,.avatar.size-S img{width:24px; height:24px}
.avatar.size-M,.avatar.size-M img{width:32px; height:32px}/*默认为中，可以不写，可以理解为：均码*/
.avatar.size-L,.avatar.size-L img{width:40px; height:40px}
.avatar.size-XL,.avatar.size-XL img{width:64px; height:64px}
.avatar.size-XXL,.avatar.size-XXL img{width:100px; height:100px}
.avatar.size-XXXL,.avatar.size-XXXL img{width:128px; height:128px}

/*3.7 图标
H-ui采用Font Awesome 3.2.1的整套图标，因为是图标字体，所以可以像控制字体那样随心所欲改变这些图标的颜色、大小、阴影以及任何CSS能控制的属性
*/
.iconpic{display:inline-block; vertical-align:sub;*vertical-align:middle;_vertical-align:middle; width:16px; height:16px; background-position:center; background-repeat:no-repeat}
/*3.8 效果
	Name:			style_animation
	Example:		<input class="btn hui-animation" val="淡入" type="button" data-tra="hui-fadein" />
*/
/* duang 加特效 */
.hui-bounce,.hui-flip,.hui-flash,.hui-shake,.hui-swing,.hui-wobble,.hui-ring{-webkit-animation:1s ease;-moz-animation:1s ease;-ms-animation:1s ease;animation:1s ease}
.hui-fadein,.hui-fadeinT,.hui-fadeinR,.hui-fadeinB,.hui-fadeinL,.hui-bouncein,.hui-bounceinT,.hui-bounceinR,.hui-bounceinB,.hui-bounceinL,.hui-rotatein,.hui-rotateinLT,.hui-rotateinLB,.hui-rotateinRT,.hui-rotateinRB,.hui-flipin,.hui-flipinX,.hui-flipinY{-webkit-animation:1s ease-out backwards;-moz-animation:1s ease-out backwards;-ms-animation:1s ease-out backwards;animation:1s ease-out backwards}
.hui-fadeout,.hui-fadeoutT,.hui-fadeoutR,.hui-fadeoutB,.hui-fadeoutL,.hui-bounceout,.hui-bounceoutT,.hui-bounceoutR,.hui-bounceoutB,.hui-bounceoutL,.hui-rotateout,.hui-rotateoutLT,.hui-rotateoutLB,.hui-rotateoutRT,.hui-rotateoutRB,.hui-flipout,.hui-flipoutX,.hui-flipoutY{-webkit-animation:1s ease-in forwards;-moz-animation:1s ease-in forwards;-ms-animation:1s ease-in forwards;animation:1s ease-in forwards}

/* 淡入 */
.hui-fadein{-webkit-animation-name:fadein;-moz-animation-name:fadein;-ms-animation-name:fadein;animation-name:fadein}
/* 淡入-从上 */
.hui-fadeinT{-webkit-animation-name:fadeinT;-moz-animation-name:fadeinT;-ms-animation-name:fadeinT;animation-name:fadeinT}
/* 淡入-从右 */
.hui-fadeinR{-webkit-animation-name:fadeinR;-moz-animation-name:fadeinR;-ms-animation-name:fadeinR;animation-name:fadeinR}
/* 淡入-从下 */
.hui-fadeinB{-webkit-animation-name:fadeinB;-moz-animation-name:fadeinB;-ms-animation-name:fadeinB;animation-name:fadeinB}
/* 淡入-从左 */
.hui-fadeinL{-webkit-animation-name:fadeinL;-moz-animation-name:fadeinL;-ms-animation-name:fadeinL;animation-name:fadeinL}
/* 淡出 */
.hui-fadeout{-webkit-animation-name:fadeout;-moz-animation-name:fadeout;-ms-animation-name:fadeout;animation-name:fadeout}

/* 淡出-向上 */
.hui-fadeoutT{-webkit-animation-name:fadeoutT;-moz-animation-name:fadeoutT;-ms-animation-name:fadeoutT;animation-name:fadeoutT}
/* 淡出-向右 */
.hui-fadeoutR{-webkit-animation-name:fadeoutR;-moz-animation-name:fadeoutR;-ms-animation-name:fadeoutR;animation-name:fadeoutR}
/* 淡出-向下 */
.hui-fadeoutB{-webkit-animation-name:fadeoutB;-moz-animation-name:fadeoutB;-ms-animation-name:fadeoutB;animation-name:fadeoutB}
/* 淡出-向左 */
.hui-fadeoutL{-webkit-animation-name:fadeoutL;-moz-animation-name:fadeoutL;-ms-animation-name:fadeoutL;animation-name:fadeoutL}

/* 弹跳 */
.hui-bounce{-webkit-animation-name:bounce;-moz-animation-name:bounce;-ms-animation-name:bounce;animation-name:bounce}

/* 弹入 */
.hui-bouncein{-webkit-animation-name:bouncein;-moz-animation-name:bouncein;-ms-animation-name:bouncein;animation-name:bouncein}
/* 弹入-从上 */
.hui-bounceinT{-webkit-animation-name:bounceinT;-moz-animation-name:bounceinT;-ms-animation-name:bounceinT;animation-name:bounceinT}
/* 弹入-从右 */
.hui-bounceinR{-webkit-animation-name:bounceinR;-moz-animation-name:bounceinR;-ms-animation-name:bounceinR;animation-name:bounceinR}
/* 弹入-从下 */
.hui-bounceinB{-webkit-animation-name:bounceinB;-moz-animation-name:bounceinB;-ms-animation-name:bounceinB;animation-name:bounceinB}
/* 弹入-从左 */
.hui-bounceinL{-webkit-animation-name:bounceinL;-moz-animation-name:bounceinL;-ms-animation-name:bounceinL;animation-name:bounceinL}

/* 弹出 */
.hui-bounceout{-webkit-animation-name:bounceout;-moz-animation-name:bounceout;-ms-animation-name:bounceout;animation-name:bounceout}
/* 弹出-向上 */
.hui-bounceoutT{-webkit-animation-name:bounceoutT;-moz-animation-name:bounceoutT;-ms-animation-name:bounceoutT;animation-name:bounceoutT}
/* 弹出-向右 */
.hui-bounceoutR{-webkit-animation-name:bounceoutR;-moz-animation-name:bounceoutR;-ms-animation-name:bounceoutR;animation-name:bounceoutR}
/* 弹出-向下 */
.hui-bounceoutB{-webkit-animation-name:bounceoutB;-moz-animation-name:bounceoutB;-ms-animation-name:bounceoutB;animation-name:bounceoutB}
/* 弹出-向左 */
.hui-bounceoutL{-webkit-animation-name:bounceoutL;-moz-animation-name:bounceoutL;-ms-animation-name:bounceoutL;animation-name:bounceoutL}

/* 转入 */
.hui-rotatein{-webkit-animation-name:rotatein;-moz-animation-name:rotatein;-ms-animation-name:rotatein;animation-name:rotatein}
/* 转入-从左上 */
.hui-rotateinLT{-webkit-animation-name:rotateinLT;-moz-animation-name:rotateinLT;-ms-animation-name:rotateinLT;animation-name:rotateinLT}
/* 转入-从左下 */
.hui-rotateinLB{-webkit-animation-name:rotateinLB;-moz-animation-name:rotateinLB;-ms-animation-name:rotateinLB;animation-name:rotateinLB}
/* 转入-从右上 */
.hui-rotateinRT{-webkit-animation-name:rotateinRT;-moz-animation-name:rotateinRT;-ms-animation-name:rotateinRT;animation-name:rotateinRT}
/* 转入-从右下*/
.hui-rotateinRB{-webkit-animation-name:rotateinRB;-moz-animation-name:rotateinRB;-ms-animation-name:rotateinRB;animation-name:rotateinRB}

/* 转出 */
.hui-rotateout{-webkit-animation-name:rotateout;-moz-animation-name:rotateout;-ms-animation-name:rotateout;animation-name:rotateout}
/* 转出-向左上 */
.hui-rotateoutLT{-webkit-animation-name:rotateoutLT;-moz-animation-name:rotateoutLT;-ms-animation-name:rotateoutLT;animation-name:rotateoutLT}
/* 转出-向左下 */
.hui-rotateoutLB{-webkit-animation-name:rotateoutLB;-moz-animation-name:rotateoutLB;-ms-animation-name:rotateoutLB;animation-name:rotateoutLB}
/* 转出-向右上 */
.hui-rotateoutRT{-webkit-animation-name:rotateoutRT;-moz-animation-name:rotateoutRT;-ms-animation-name:rotateoutRT;animation-name:rotateoutRT}
/* 转出-向右下 */
.hui-rotateoutRB{-webkit-animation-name:rotateoutRB;-moz-animation-name:rotateoutRB;-ms-animation-name:rotateoutRB;animation-name:rotateoutRB}

/* 翻转 */
.hui-flip{-webkit-animation-name:flip;-moz-animation-name:flip;-ms-animation-name:flip;animation-name:flip}
/* 翻入-X轴 */
.hui-flipinX{-webkit-animation-name:flipinX;-moz-animation-name:flipinX;-ms-animation-name:flipinX;animation-name:flipinX}
/* 翻入-Y轴 */
.hui-flipin,.hui-flipinY{-webkit-animation-name:flipinY;-moz-animation-name:flipinY;-ms-animation-name:flipinY;animation-name:flipinY}
/* 翻出-X轴 */
.hui-flipoutX{-webkit-animation-name:flipoutX;-moz-animation-name:flipoutX;-ms-animation-name:flipoutX;animation-name:flipoutX}
/* 翻出-Y轴 */
.hui-flipout,.hui-flipoutY{-webkit-animation-name:flipoutY;-moz-animation-name:flipoutY;-ms-animation-name:flipoutY;animation-name:flipoutY}

/* 闪烁 */
.hui-flash{-webkit-animation-name:flash;-moz-animation-name:flash;-ms-animation-name:flash;animation-name:flash}
/* 震颤 */
.hui-shake{-webkit-animation-name:shake;-moz-animation-name:shake;-ms-animation-name:shake;animation-name:shake}
/* 摇摆 */
.hui-swing{-webkit-animation-name:swing;-moz-animation-name:swing;-ms-animation-name:swing;animation-name:swing}
/* 摇晃 */
.hui-wobble{-webkit-animation-name:wobble;-moz-animation-name:wobble;-ms-animation-name:wobble;animation-name:wobble}
/* 震铃 */
.hui-ring{-webkit-animation-name:ring;-moz-animation-name:ring;-ms-animation-name:ring;animation-name:ring}
/* define */
/* 淡入 */
@-webkit-keyframes fadein{
    0%{opacity:0}
    100%{opacity:1}
}
@-moz-keyframes fadein{
    0%{opacity:0}
    100%{opacity:1}
}
@-ms-keyframes fadein{
    0%{opacity:0}
    100%{opacity:1}
}
@keyframes fadein{
    0%{opacity:0}
    100%{opacity:1}
}
/* 淡入-从上 */
@-webkit-keyframes fadeinT{
    0%{opacity:0;-webkit-transform:translateY(-100px)}
    100%{opacity:1;-webkit-transform:translateY(0)}
}
@-moz-keyframes fadeinT{
    0%{opacity:0;-moz-transform:translateY(-100px)}
    100%{opacity:1;-moz-transform:translateY(0)}
}
@-ms-keyframes fadeinT{
    0%{opacity:0;-ms-transform:translateY(-100px)}
    100%{opacity:1;-ms-transform:translateY(0)}
}
@keyframes fadeinT{
    0%{opacity:0;transform:translateY(-100px)}
    100%{opacity:1;transform:translateY(0)}
}
/* 淡入-从右 */
@-webkit-keyframes fadeinR{
    0%{opacity:0;-webkit-transform:translateX(100px)}
    100%{opacity:1;-webkit-transform:translateX(0)}
}
@-moz-keyframes fadeinR{
    0%{opacity:0;-moz-transform:translateX(100px)}
    100%{opacity:1;-moz-transform:translateX(0)}
}
@-ms-keyframes fadeinR{
    0%{opacity:0;-ms-transform:translateX(100px)}
    100%{opacity:1;-ms-transform:translateX(0)}
}
@keyframes fadeinR{
    0%{opacity:0;transform:translateX(100px)}
    100%{opacity:1;transform:translateX(0)}
}
/* 淡入-从下 */
@-webkit-keyframes fadeinB{
    0%{opacity:0;-webkit-transform:translateY(100px)}
    100%{opacity:1;-webkit-transform:translateY(0)}
}
@-moz-keyframes fadeinB{
    0%{opacity:0;-moz-transform:translateY(100px)}
    100%{opacity:1;-moz-transform:translateY(0)}
}
@-ms-keyframes fadeinB{
    0%{opacity:0;-ms-transform:translateY(100px)}
    100%{opacity:1;-ms-transform:translateY(0)}
}
@keyframes fadeinB{
    0%{opacity:0;transform:translateY(100px)}
    100%{opacity:1;transform:translateY(0)}
}
/* 淡入-从左 */
@-webkit-keyframes fadeinL{
    0%{opacity:0;-webkit-transform:translateX(-100px)}
    100%{opacity:1;-webkit-transform:translateX(0)}
}
@-moz-keyframes fadeinL{
    0%{opacity:0;-moz-transform:translateX(-100px)}
    100%{opacity:1;-moz-transform:translateX(0)}
}
@-ms-keyframes fadeinL{
    0%{opacity:0;-ms-transform:translateX(-100px)}
    100%{opacity:1;-ms-transform:translateX(0)}
}
@keyframes fadeinL{
    0%{opacity:0;transform:translateX(-100px)}
    100%{opacity:1;transform:translateX(0)}
}
/* 淡出 */
@-webkit-keyframes fadeout{
    0%{opacity:1}
    100%{opacity:0}
}
@-moz-keyframes fadeout{
    0%{opacity:1}
    100%{opacity:0}
}
@-ms-keyframes fadeout{
    0%{opacity:1}
    100%{opacity:0}
}
@keyframes fadeout{
    0%{opacity:1}
    100%{opacity:0}
}
/* 淡出-向上 */
@-webkit-keyframes fadeoutT{
    0%{opacity:1;-webkit-transform:translateY(0)}
    100%{opacity:0;-webkit-transform:translateY(-100px)}
}
@-moz-keyframes fadeoutT{
    0%{opacity:1;-moz-transform:translateY(0)}
    100%{opacity:0;-moz-transform:translateY(-100px)}
}
@-ms-keyframes fadeoutT{
    0%{opacity:1;-ms-transform:translateY(0)}
    100%{opacity:0;-ms-transform:translateY(-100px)}
}
@keyframes fadeoutT{
    0%{opacity:1;transform:translateY(0)}
    100%{opacity:0;transform:translateY(-100px)}
}
/* 淡出-向右 */
@-webkit-keyframes fadeoutR{
    0%{opacity:1;-webkit-transform:translateX(0)}
    100%{opacity:0;-webkit-transform:translateX(100px)}
}
@-moz-keyframes fadeoutR{
    0%{opacity:1;-moz-transform:translateX(0)}
    100%{opacity:0;-moz-transform:translateX(100px)}
}
@-ms-keyframes fadeoutR{
    0%{opacity:1;-ms-transform:translateX(0)}
    100%{opacity:0;-ms-transform:translateX(100px)}
}
@keyframes fadeoutR{
    0%{opacity:1;transform:translateX(0)}
    100%{opacity:0;transform:translateX(100px)}
}
/* 淡出-向下 */
@-webkit-keyframes fadeoutB{
    0%{opacity:1;-webkit-transform:translateY(0)}
    100%{opacity:0;-webkit-transform:translateY(100px)}
}
@-moz-keyframes fadeoutB{
    0%{opacity:1;-moz-transform:translateY(0)}
    100%{opacity:0;-moz-transform:translateY(100px)}
}
@-ms-keyframes fadeoutB{
    0%{opacity:1;-ms-transform:translateY(0)}
    100%{opacity:0;-ms-transform:translateY(100px)}
}
@keyframes fadeoutB{
    0%{opacity:1;transform:translateY(0)}
    100%{opacity:0;transform:translateY(100px)}
}
/* 淡出-向左 */
@-webkit-keyframes fadeoutL{
    0%{opacity:1;-webkit-transform:translateX(0)}
    100%{opacity:0;-webkit-transform:translateX(-100px)}
}
@-moz-keyframes fadeoutL{
    0%{opacity:1;-moz-transform:translateX(0)}
    100%{opacity:0;-moz-transform:translateX(-100px)}
}
@-ms-keyframes fadeoutL{
    0%{opacity:1;-ms-transform:translateX(0)}
    100%{opacity:0;-ms-transform:translateX(-100px)}
}
@keyframes fadeoutL{
    0%{opacity:1;transform:translateX(0)}
    100%{opacity:0;transform:translateX(-100px)}
}
/* 弹跳 */
@-webkit-keyframes bounce{
    0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
    40%{-webkit-transform:translateY(-30px)}
    60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes bounce{
    0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
    40%{-moz-transform:translateY(-30px)}
    60%{-moz-transform:translateY(-15px)}
}
@-ms-keyframes bounce{
    0%,20%,50%,80%,100%{-ms-transform:translateY(0)}
    40%{-ms-transform:translateY(-30px)}
    60%{-ms-transform:translateY(-15px)}
}
@keyframes bounce{
    0%,20%,50%,80%,100%{transform:translateY(0)}
    40%{transform:translateY(-30px)}
    60%{transform:translateY(-15px)}
}
/* 弹入 */
@-webkit-keyframes bouncein{
    0%{opacity:0;-webkit-transform:scale(0.3)}
    50%{opacity:1;-webkit-transform:scale(1.05)}
    70%{-webkit-transform:scale(0.9)}
    100%{-webkit-transform:scale(1)}
}
@-moz-keyframes bouncein{
    0%{opacity:0;-moz-transform:scale(0.3)}
    50%{opacity:1;-moz-transform:scale(1.05)}
    70%{-moz-transform:scale(0.9)}
    100%{-moz-transform:scale(1)}
}
@-ms-keyframes bouncein{
    0%{opacity:0;-ms-transform:scale(0.3)}
    50%{opacity:1;-ms-transform:scale(1.05)}
    70%{-ms-transform:scale(0.9)}
    100%{-ms-transform:scale(1)}
}
@keyframes bouncein{
    0%{opacity:0;transform:scale(0.3)}
    50%{opacity:1;transform:scale(1.05)}
    70%{transform:scale(0.9)}
    100%{transform:scale(1)}
}
/* 弹入-从上 */
@-webkit-keyframes bounceinT{
    0%{opacity:0;-webkit-transform:translateY(-100px)}
    60%{opacity:1;-webkit-transform:translateY(30px)}
    80%{-webkit-transform:translateY(-10px)}
    100%{-webkit-transform:translateY(0)}
}
@-moz-keyframes bounceinT{
    0%{opacity:0;-moz-transform:translateY(-100px)}
    60%{opacity:1;-moz-transform:translateY(30px)}
    80%{-moz-transform:translateY(-10px)}
    100%{-moz-transform:translateY(0)}
}
@-ms-keyframes bounceinT{
    0%{opacity:0;-ms-transform:translateY(-100px)}
    60%{opacity:1;-ms-transform:translateY(30px)}
    80%{-ms-transform:translateY(-10px)}
    100%{-ms-transform:translateY(0)}
}
@keyframes bounceinT{
    0%{opacity:0;transform:translateY(-100px)}
    60%{opacity:1;transform:translateY(30px)}
    80%{transform:translateY(-10px)}
    100%{transform:translateY(0)}
}
/* 弹入-从右 */
@-webkit-keyframes bounceinR{
    0%{opacity:0;-webkit-transform:translateX(100px)}
    60%{opacity:1;-webkit-transform:translateX(-30px)}
    80%{-webkit-transform:translateX(10px)}
    100%{-webkit-transform:translateX(0)}
}
@-moz-keyframes bounceinR{
    0%{opacity:0;-moz-transform:translateX(100px)}
    60%{opacity:1;-moz-transform:translateX(-30px)}
    80%{-moz-transform:translateX(10px)}
    100%{-moz-transform:translateX(0)}
}
@-ms-keyframes bounceinR{
    0%{opacity:0;-ms-transform:translateX(100px)}
    60%{opacity:1;-ms-transform:translateX(-30px)}
    80%{-ms-transform:translateX(10px)}
    100%{-ms-transform:translateX(0)}
}
@keyframes bounceinR{
    0%{opacity:0;transform:translateX(100px)}
    60%{opacity:1;transform:translateX(-30px)}
    80%{transform:translateX(10px)}
    100%{transform:translateX(0)}
}
/* 弹入-从下 */
@-webkit-keyframes bounceinB{
    0%{opacity:0;-webkit-transform:translateY(100px)}
    60%{opacity:1;-webkit-transform:translateY(-30px)}
    80%{-webkit-transform:translateY(10px)}
    100%{-webkit-transform:translateY(0)}
}
@-moz-keyframes bounceinB{
    0%{opacity:0;-moz-transform:translateY(100px)}
    60%{opacity:1;-moz-transform:translateY(-30px)}
    80%{-moz-transform:translateY(10px)}
    100%{-moz-transform:translateY(0)}
}
@-ms-keyframes bounceinB{
    0%{opacity:0;-ms-transform:translateY(100px)}
    60%{opacity:1;-ms-transform:translateY(-30px)}
    80%{-ms-transform:translateY(10px)}
    100%{-ms-transform:translateY(0)}
}
@keyframes bounceinB{
    0%{opacity:0;transform:translateY(100px)}
    60%{opacity:1;transform:translateY(-30px)}
    80%{transform:translateY(10px)}
    100%{transform:translateY(0)}
}
/* 弹入-从左 */
@-webkit-keyframes bounceinL{
    0%{opacity:0;-webkit-transform:translateX(-100px)}
    60%{opacity:1;-webkit-transform:translateX(30px)}
    80%{-webkit-transform:translateX(-10px)}
    100%{-webkit-transform:translateX(0)}
}
@-moz-keyframes bounceinL{
    0%{opacity:0;-moz-transform:translateX(-100px)}
    60%{opacity:1;-moz-transform:translateX(30px)}
    80%{-moz-transform:translateX(-10px)}
    100%{-moz-transform:translateX(0)}
}
@-ms-keyframes bounceinL{
    0%{opacity:0;-ms-transform:translateX(-100px)}
    60%{opacity:1;-ms-transform:translateX(30px)}
    80%{-ms-transform:translateX(-10px)}
    100%{-ms-transform:translateX(0)}
}
@keyframes bounceinL{
    0%{opacity:0;transform:translateX(-100px)}
    60%{opacity:1;transform:translateX(30px)}
    80%{transform:translateX(-10px)}
    100%{transform:translateX(0)}
}
/* 弹出 */
@-webkit-keyframes bounceout{
    0%{-webkit-transform:scale(1)}
    25%{-webkit-transform:scale(0.95)}
    50%{opacity:1;-webkit-transform:scale(1.1)}
    100%{opacity:0;-webkit-transform:scale(0.3)}
}
@-moz-keyframes bounceout{
    0%{-moz-transform:scale(1)}
    25%{-moz-transform:scale(0.95)}
    50%{opacity:1;-moz-transform:scale(1.1)}
    100%{opacity:0;-moz-transform:scale(0.3)}
}
@-ms-keyframes bounceout{
    0%{-ms-transform:scale(1)}
    25%{-ms-transform:scale(0.95)}
    50%{opacity:1;-ms-transform:scale(1.1)}
    100%{opacity:0;-ms-transform:scale(0.3)}
}
@keyframes bounceout{
    0%{transform:scale(1)}
    25%{transform:scale(0.95)}
    50%{opacity:1;transform:scale(1.1)}
    100%{opacity:0;transform:scale(0.3)}
}
/* 弹出-向上*/
@-webkit-keyframes bounceoutT{
    0%{-webkit-transform:translateY(0)}
    20%{opacity:1;-webkit-transform:translateY(20px)}
    100%{opacity:0;-webkit-transform:translateY(-100px)}
}
@-moz-keyframes bounceoutT{
    0%{-moz-transform:translateY(0)}
    20%{opacity:1;-moz-transform:translateY(20px)}
    100%{opacity:0;-moz-transform:translateY(-100px)}
}
@-ms-keyframes bounceoutT{
    0%{-ms-transform:translateY(0)}
    20%{opacity:1;-ms-transform:translateY(20px)}
    100%{opacity:0;-ms-transform:translateY(-100px)}
}
@keyframes bounceoutT{
    0%{transform:translateY(0)}
    20%{opacity:1;transform:translateY(20px)}
    100%{opacity:0;transform:translateY(-100px)}
}
/* 弹出-向右*/
@-webkit-keyframes bounceoutR{
    0%{-webkit-transform:translateX(0)}
    20%{opacity:1;-webkit-transform:translateX(-20px)}
    100%{opacity:0;-webkit-transform:translateX(100px)}
}
@-moz-keyframes bounceoutR{
    0%{-moz-transform:translateX(0)}
    20%{opacity:1;-moz-transform:translateX(-20px)}
    100%{opacity:0;-moz-transform:translateX(100px)}
}
@-ms-keyframes bounceoutR{
    0%{-ms-transform:translateX(0)}
    20%{opacity:1;-ms-transform:translateX(-20px)}
    100%{opacity:0;-ms-transform:translateX(100px)}
}
@keyframes bounceoutR{
    0%{transform:translateX(0)}
    20%{opacity:1;transform:translateX(-20px)}
    100%{opacity:0;transform:translateX(100px)}
}
/* 弹出-向下 */
@-webkit-keyframes bounceoutB{
    0%{-webkit-transform:translateY(0)}
    20%{opacity:1;-webkit-transform:translateY(-20px)}
    100%{opacity:0;-webkit-transform:translateY(100px)}
}
@-moz-keyframes bounceoutB{
    0%{-moz-transform:translateY(0)}
    20%{opacity:1;-moz-transform:translateY(-20px)}
    100%{opacity:0;-moz-transform:translateY(100px)}
}
@-ms-keyframes bounceoutB{
    0%{-ms-transform:translateY(0)}
    20%{opacity:1;-ms-transform:translateY(-20px)}
    100%{opacity:0;-ms-transform:translateY(100px)}
}
@keyframes bounceoutB{
    0%{transform:translateY(0)}
    20%{opacity:1;transform:translateY(-20px)}
    100%{opacity:0;transform:translateY(100px)}
}
/* 弹出-向左 */
@-webkit-keyframes bounceoutL{
    0%{-webkit-transform:translateX(0)}
    20%{opacity:1;-webkit-transform:translateX(20px)}
    100%{opacity:0;-webkit-transform:translateX(-100px)}
}
@-moz-keyframes bounceoutL{
    0%{-moz-transform:translateX(0)}
    20%{opacity:1;-moz-transform:translateX(20px)}
    100%{opacity:0;-moz-transform:translateX(-100px)}
}
@-ms-keyframes bounceoutL{
    0%{-ms-transform:translateX(0)}
    20%{opacity:1;-ms-transform:translateX(20px)}
    100%{opacity:0;-ms-transform:translateX(-100px)}
}
@keyframes bounceoutL{
    0%{transform:translateX(0)}
    20%{opacity:1;transform:translateX(20px)}
    100%{opacity:0;transform:translateX(-200px)}
}
/* 转入 */
@-webkit-keyframes rotatein{
    0%{opacity:0;-webkit-transform:rotate(-200deg)}
    100%{opacity:1;-webkit-transform:rotate(0)}
}
@-moz-keyframes rotatein{
    0%{opacity:0;-moz-transform:rotate(-200deg)}
    100%{opacity:1;-moz-transform:rotate(0)}
}
@-ms-keyframes rotatein{
    0%{opacity:0;-ms-transform:rotate(-200deg)}
    100%{opacity:1;-ms-transform:rotate(0)}
}
@keyframes rotatein{
    0%{opacity:0;transform:rotate(-200deg)}
    100%{opacity:1;transform:rotate(0)}
}
/* 转入-从左上 */
@-webkit-keyframes rotateinLT{
    0%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(-90deg);opacity:0}
    100%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(0);opacity:1}
}
@-moz-keyframes rotateinLT{
    0%{-moz-transform-origin:left bottom;-moz-transform:rotate(-90deg);opacity:0}
    100%{-moz-transform-origin:left bottom;-moz-transform:rotate(0);opacity:1}
}
@-ms-keyframes rotateinLT{
    0%{-ms-transform-origin:left bottom;-ms-transform:rotate(-90deg);opacity:0}
    100%{-ms-transform-origin:left bottom;-ms-transform:rotate(0);opacity:1}
}
@keyframes rotateinLT{
    0%{transform-origin:left bottom;transform:rotate(-90deg);opacity:0}
    100%{transform-origin:left bottom;transform:rotate(0);opacity:1}
}
/* 转入-从左下 */
@-webkit-keyframes rotateineftB{
    0%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(90deg);opacity:0}
    100%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(0);opacity:1}
}
@-moz-keyframes rotateineftB{
    0%{-moz-transform-origin:left bottom;-moz-transform:rotate(90deg);opacity:0}
    100%{-moz-transform-origin:left bottom;-moz-transform:rotate(0);opacity:1}
}
@-ms-keyframes rotateineftB{
    0%{-ms-transform-origin:left bottom;-ms-transform:rotate(90deg);opacity:0}
    100%{-ms-transform-origin:left bottom;-ms-transform:rotate(0);opacity:1}
}
@keyframes rotateineftB{
    0%{transform-origin:left bottom;transform:rotate(90deg);opacity:0}
    100%{transform-origin:left bottom;transform:rotate(0);opacity:1}
}
/* 转入-从右上 */
@-webkit-keyframes rotateinRT{
    0%{-webkit-transform-origin:right bottom;-webkit-transform:rotate(90deg);opacity:0}
    100%{-webkit-transform-origin:right bottom;-webkit-transform:rotate(0);opacity:1}
}
@-moz-keyframes rotateinRT{
    0%{-moz-transform-origin:right bottom;-moz-transform:rotate(90deg);opacity:0}
    100%{-moz-transform-origin:right bottom;-moz-transform:rotate(0);opacity:1}
}
@-ms-keyframes rotateinRT{
    0%{-ms-transform-origin:right bottom;-ms-transform:rotate(90deg);opacity:0}
    100%{-ms-transform-origin:right bottom;-ms-transform:rotate(0);opacity:1}
}
@keyframes rotateinRT{
    0%{transform-origin:right bottom;transform:rotate(90deg);opacity:0}
    100%{transform-origin:right bottom;transform:rotate(0);opacity:1}
}
/* 转入-从右下*/
@-webkit-keyframes rotateinRB{
    0%{-webkit-transform-origin:right bottom;-webkit-transform:rotate(-90deg);opacity:0}
    100%{-webkit-transform-origin:right bottom;-webkit-transform:rotate(0);opacity:1}
}
@-moz-keyframes rotateinRB{
    0%{-moz-transform-origin:right bottom;-moz-transform:rotate(-90deg);opacity:0}
    100%{-moz-transform-origin:right bottom;-moz-transform:rotate(0);opacity:1}
}
@-ms-keyframes rotateinRB{
    0%{-ms-transform-origin:right bottom;-ms-transform:rotate(-90deg);opacity:0}
    100%{-ms-transform-origin:right bottom;-ms-transform:rotate(0);opacity:1}
}
@keyframes rotateinRB{
    0%{transform-origin:right bottom;transform:rotate(-90deg);opacity:0}
    100%{transform-origin:right bottom;transform:rotate(0);opacity:1}
}
/* 转出 */
@-webkit-keyframes rotateout{
    0%{-webkit-transform-origin:center center;-webkit-transform:rotate(0);opacity:1}
    100%{-webkit-transform-origin:center center;-webkit-transform:rotate(200deg);opacity:0}
}
@-moz-keyframes rotateout{
    0%{-moz-transform-origin:center center;-moz-transform:rotate(0);opacity:1}
    100%{-moz-transform-origin:center center;-moz-transform:rotate(200deg);opacity:0}
}
@-ms-keyframes rotateout{
    0%{-ms-transform-origin:center center;-ms-transform:rotate(0);opacity:1}
    100%{-ms-transform-origin:center center;-ms-transform:rotate(200deg);opacity:0}
}
@keyframes rotateout{
    0%{transform-origin:center center;transform:rotate(0);opacity:1}
    100%{transform-origin:center center;transform:rotate(200deg);opacity:0}
}
/* 转出-向左上 */
@-webkit-keyframes rotateoutLT{
    0%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(0);opacity:1}
    100%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(-90deg);opacity:0}
}
@-moz-keyframes rotateoutLT{
    0%{-moz-transform-origin:left bottom;-moz-transform:rotate(0);opacity:1}
    100%{-moz-transform-origin:left bottom;-moz-transform:rotate(-90deg);opacity:0}
}
@-ms-keyframes rotateoutLT{
    0%{-ms-transform-origin:left bottom;-ms-transform:rotate(0);opacity:1}
    100%{-ms-transform-origin:left bottom;-ms-transform:rotate(-90deg);opacity:0}
}
@keyframes rotateoutLT{
    0%{transform-origin:left bottom;transform:rotate(0);opacity:1}
    100%{transform-origin:left bottom;transform:rotate(-90deg);opacity:0}
}
/* 转出-向左下 */
@-webkit-keyframes rotateoutLB{
    0%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(0);opacity:1}
    100%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(90deg);opacity:0}
}
@-moz-keyframes rotateoutLB{
    0%{-moz-transform-origin:left bottom;-moz-transform:rotate(0);opacity:1}
    100%{-moz-transform-origin:left bottom;-moz-transform:rotate(90deg);opacity:0}
}
@-ms-keyframes rotateoutLB{
    0%{-ms-transform-origin:left bottom;-ms-transform:rotate(0);opacity:1}
    100%{-ms-transform-origin:left bottom;-ms-transform:rotate(90deg);opacity:0}
}
@keyframes rotateoutLB{
    0%{transform-origin:left bottom;transform:rotate(0);opacity:1}
    100%{transform-origin:left bottom;transform:rotate(90deg);opacity:0}
}
/* 转出-向右上 */
@-webkit-keyframes rotateoutRT{
    0%{-webkit-transform-origin:right bottom;-webkit-transform:rotate(0);opacity:1}
    100%{-webkit-transform-origin:right bottom;-webkit-transform:rotate(90deg);opacity:0}
}
@-moz-keyframes rotateoutRT{
    0%{-moz-transform-origin:right bottom;-moz-transform:rotate(0);opacity:1}
    100%{-moz-transform-origin:right bottom;-moz-transform:rotate(90deg);opacity:0}
}
@-ms-keyframes rotateoutRT{
    0%{-ms-transform-origin:right bottom;-ms-transform:rotate(0);opacity:1}
    100%{-ms-transform-origin:right bottom;-ms-transform:rotate(90deg);opacity:0}
}
@keyframes rotateoutRT{
    0%{transform-origin:right bottom;transform:rotate(0);opacity:1}
    100%{transform-origin:right bottom;transform:rotate(90deg);opacity:0}
}
/* 转出-向右下 */
@-webkit-keyframes rotateoutBR{
    0%{-webkit-transform-origin:right bottom;-webkit-transform:rotate(0);opacity:1}
    100%{-webkit-transform-origin:right bottom;-webkit-transform:rotate(-90deg);opacity:0}
}
@-moz-keyframes rotateoutBR{
    0%{-moz-transform-origin:right bottom;-moz-transform:rotate(0);opacity:1}
    100%{-moz-transform-origin:right bottom;-moz-transform:rotate(-90deg);opacity:0}
}
@-ms-keyframes rotateoutBR{
    0%{-ms-transform-origin:right bottom;-ms-transform:rotate(0);opacity:1}
    100%{-ms-transform-origin:right bottom;-ms-transform:rotate(-90deg);opacity:0}
}
@keyframes rotateoutBR{
    0%{transform-origin:right bottom;transform:rotate(0);opacity:1}
    100%{transform-origin:right bottom;transform:rotate(-90deg);opacity:0}
}
/* 翻转 */
@-webkit-keyframes flip{
    0%{-webkit-transform:perspective(400px) rotateY(0);-webkit-animation-timing-function:ease-out}
    40%{-webkit-transform:perspective(400px) translateZ(150px) rotateY(170deg);-webkit-animation-timing-function:ease-out}
    50%{-webkit-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);-webkit-animation-timing-function:ease-in}
    80%{-webkit-transform:perspective(400px) rotateY(360deg) scale(0.95);-webkit-animation-timing-function:ease-in}
    100%{-webkit-transform:perspective(400px) scale(1);-webkit-animation-timing-function:ease-in}
}
@-moz-keyframes flip{
    0%{-moz-transform:perspective(400px) rotateY(0);-moz-animation-timing-function:ease-out}
    40%{-moz-transform:perspective(400px) translateZ(150px) rotateY(170deg);-moz-animation-timing-function:ease-out}
    50%{-moz-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);-moz-animation-timing-function:ease-in}
    80%{-moz-transform:perspective(400px) rotateY(360deg) scale(0.95);-moz-animation-timing-function:ease-in}
    100%{-moz-transform:perspective(400px) scale(1);-moz-animation-timing-function:ease-in}
}
@-ms-keyframes flip{
    0%{-ms-transform:perspective(400px) rotateY(0);-ms-animation-timing-function:ease-out}
    40%{-ms-transform:perspective(400px) translateZ(150px) rotateY(170deg);-ms-animation-timing-function:ease-out}
    50%{-ms-transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);-ms-animation-timing-function:ease-in}
    80%{-ms-transform:perspective(400px) rotateY(360deg) scale(0.95);-ms-animation-timing-function:ease-in}
    100%{-ms-transform:perspective(400px) scale(1);-ms-animation-timing-function:ease-in}
}
@keyframes flip{
    0%{transform:perspective(400px) rotateY(0);animation-timing-function:ease-out}
    40%{transform:perspective(400px) translateZ(150px) rotateY(170deg);animation-timing-function:ease-out}
    50%{transform:perspective(400px) translateZ(150px) rotateY(190deg) scale(1);animation-timing-function:ease-in}
    80%{transform:perspective(400px) rotateY(360deg) scale(0.95);animation-timing-function:ease-in}
    100%{transform:perspective(400px) scale(1);animation-timing-function:ease-in}
}
/* 翻入-X轴 */
@-webkit-keyframes flipinX{
    0%{-webkit-transform:perspective(400px) rotateX(90deg);opacity:0}
    40%{-webkit-transform:perspective(400px) rotateX(-10deg)}
    70%{-webkit-transform:perspective(400px) rotateX(10deg)}
    100%{-webkit-transform:perspective(400px) rotateX(0);opacity:1}
}
@-moz-keyframes flipinX{
    0%{-moz-transform:perspective(400px) rotateX(90deg);opacity:0}
    40%{-moz-transform:perspective(400px) rotateX(-10deg)}
    70%{-moz-transform:perspective(400px) rotateX(10deg)}
    100%{-moz-transform:perspective(400px) rotateX(0);opacity:1}
}
@-ms-keyframes flipinX{
    0%{-ms-transform:perspective(400px) rotateX(90deg);opacity:0}
    40%{-ms-transform:perspective(400px) rotateX(-10deg)}
    70%{-ms-transform:perspective(400px) rotateX(10deg)}
    100%{-ms-transform:perspective(400px) rotateX(0);opacity:1}
}
@keyframes flipinX{
    0%{transform:perspective(400px) rotateX(90deg);opacity:0}
    40%{transform:perspective(400px) rotateX(-10deg)}
    70%{transform:perspective(400px) rotateX(10deg)}
    100%{transform:perspective(400px) rotateX(0);opacity:1}
}
/* 翻入-Y轴 */
@-webkit-keyframes flipinY{
    0%{-webkit-transform:perspective(400px) rotateY(90deg);opacity:0}
    40%{-webkit-transform:perspective(400px) rotateY(-10deg)}
    70%{-webkit-transform:perspective(400px) rotateY(10deg)}
    100%{-webkit-transform:perspective(400px) rotateY(0);opacity:1}
}
@-moz-keyframes flipinY{
    0%{-moz-transform:perspective(400px) rotateY(90deg);opacity:0}
    40%{-moz-transform:perspective(400px) rotateY(-10deg)}
    70%{-moz-transform:perspective(400px) rotateY(10deg)}
    100%{-moz-transform:perspective(400px) rotateY(0);opacity:1}
}
@-ms-keyframes flipinY{
    0%{-ms-transform:perspective(400px) rotateY(90deg);opacity:0}
    40%{-ms-transform:perspective(400px) rotateY(-10deg)}
    70%{-ms-transform:perspective(400px) rotateY(10deg)}
    100%{-ms-transform:perspective(400px) rotateY(0);opacity:1}
}
@keyframes flipinY{
    0%{transform:perspective(400px) rotateY(90deg);opacity:0}
    40%{transform:perspective(400px) rotateY(-10deg)}
    70%{transform:perspective(400px) rotateY(10deg)}
    100%{transform:perspective(400px) rotateY(0);opacity:1}
}
/* 翻出-X轴 */
@-webkit-keyframes flipoutX{
    0%{-webkit-transform:perspective(400px) rotateX(0);opacity:1}
    100%{-webkit-transform:perspective(400px) rotateX(90deg);opacity:0}
}
@-moz-keyframes flipoutX{
    0%{-moz-transform:perspective(400px) rotateX(0);opacity:1}
    100%{-moz-transform:perspective(400px) rotateX(90deg);opacity:0}
}
@-ms-keyframes flipoutX{
    0%{-ms-transform:perspective(400px) rotateX(0);opacity:1}
    100%{-ms-transform:perspective(400px) rotateX(90deg);opacity:0}
}
@keyframes flipoutX{
    0%{transform:perspective(400px) rotateX(0);opacity:1}
    100%{transform:perspective(400px) rotateX(90deg);opacity:0}
}
/* 翻出-Y轴 */
@-webkit-keyframes flipoutY{
    0%{-webkit-transform:perspective(400px) rotateY(0);opacity:1}
    100%{-webkit-transform:perspective(400px) rotateY(90deg);opacity:0}
}
@-moz-keyframes flipoutY{
    0%{-moz-transform:perspective(400px) rotateY(0);opacity:1}
    100%{-moz-transform:perspective(400px) rotateY(90deg);opacity:0}
}
@-ms-keyframes flipoutY{
    0%{-ms-transform:perspective(400px) rotateY(0);opacity:1}
    100%{-ms-transform:perspective(400px) rotateY(90deg);opacity:0}
}
@keyframes flipoutY{
    0%{transform:perspective(400px) rotateY(0);opacity:1}
    100%{transform:perspective(400px) rotateY(90deg);opacity:0}
}
/* 闪烁 */
@-webkit-keyframes flash{
    0%,50%,100%{opacity:1}
    25%,75%{opacity:0}
}
@-moz-keyframes flash{
    0%,50%,100%{opacity:1}
    25%,75%{opacity:0}
}
@-ms-keyframes flash{
    0%,50%,100%{opacity:1}
    25%,75%{opacity:0}
}
@keyframes flash{
    0%,50%,100%{opacity:1}
    25%,75%{opacity:0}
}
/* 震颤 */
@-webkit-keyframes shake{
    0%,100%{-webkit-transform:translateX(0)}
    10%,30%,50%,70%,90%{-webkit-transform:translateX(-10px)}
    20%,40%,60%,80%{-webkit-transform:translateX(10px)}
}
@-moz-keyframes shake{
    0%,100%{-moz-transform:translateX(0)}
    10%,30%,50%,70%,90%{-moz-transform:translateX(-10px)}
    20%,40%,60%,80%{-moz-transform:translateX(10px)}
}
@-ms-keyframes shake{
    0%,100%{-ms-transform:translateX(0)}
    10%,30%,50%,70%,90%{-ms-transform:translateX(-10px)}
    20%,40%,60%,80%{-ms-transform:translateX(10px)}
}
@keyframes shake{
    0%,100%{transform:translateX(0)}
    10%,30%,50%,70%,90%{transform:translateX(-10px)}
    20%,40%,60%,80%{transform:translateX(10px)}
}
/* 摇摆 */
@-webkit-keyframes swing{
    20%{-webkit-transform:rotate(15deg)}
    40%{-webkit-transform:rotate(-10deg)}
    60%{-webkit-transform:rotate(5deg)}
    80%{-webkit-transform:rotate(-5deg)}
    100%{-webkit-transform:rotate(0)}
}
@-moz-keyframes swing{
    20%{-moz-transform:rotate(15deg)}
    40%{-moz-transform:rotate(-10deg)}
    60%{-moz-transform:rotate(5deg)}
    80%{-moz-transform:rotate(-5deg)}
    100%{-moz-transform:rotate(0)}
}
@-ms-keyframes swing{
    20%{-ms-transform:rotate(15deg)}
    40%{-ms-transform:rotate(-10deg)}
    60%{-ms-transform:rotate(5deg)}
    80%{-ms-transform:rotate(-5deg)}
    100%{-ms-transform:rotate(0)}
}
@keyframes swing{
    20%{transform:rotate(15deg)}
    40%{transform:rotate(-10deg)}
    60%{transform:rotate(5deg)}
    80%{transform:rotate(-5deg)}
    100%{transform:rotate(0)}
}
/* 摇晃 */
@-webkit-keyframes wobble{
    0%{-webkit-transform:translateX(0)}
    15%{-webkit-transform:translateX(-100px) rotate(-5deg)}
    30%{-webkit-transform:translateX(80px) rotate(3deg)}
    45%{-webkit-transform:translateX(-65px) rotate(-3deg)}
    60%{-webkit-transform:translateX(40px) rotate(2deg)}
    75%{-webkit-transform:translateX(-20px) rotate(-1deg)}
    100%{-webkit-transform:translateX(0)}
}
@-moz-keyframes wobble{
    0%{-moz-transform:translateX(0)}
    15%{-moz-transform:translateX(-100px) rotate(-5deg)}
    30%{-moz-transform:translateX(80px) rotate(3deg)}
    45%{-moz-transform:translateX(-65px) rotate(-3deg)}
    60%{-moz-transform:translateX(40px) rotate(2deg)}
    75%{-moz-transform:translateX(-20px) rotate(-1deg)}
    100%{-moz-transform:translateX(0)}
}
@-ms-keyframes wobble{
    0%{-ms-transform:translateX(0)}
    15%{-ms-transform:translateX(-100px) rotate(-5deg)}
    30%{-ms-transform:translateX(80px) rotate(3deg)}
    45%{-ms-transform:translateX(-65px) rotate(-3deg)}
    60%{-ms-transform:translateX(40px) rotate(2deg)}
    75%{-ms-transform:translateX(-20px) rotate(-1deg)}
    100%{-ms-transform:translateX(0)}
}
@keyframes wobble{
    0%{transform:translateX(0)}
    15%{transform:translateX(-100px) rotate(-5deg)}
    30%{transform:translateX(80px) rotate(3deg)}
    45%{transform:translateX(-65px) rotate(-3deg)}
    60%{transform:translateX(40px) rotate(2deg)}
    75%{transform:translateX(-20px) rotate(-1deg)}
    100%{transform:translateX(0)}
}
/* 震铃 */
@-webkit-keyframes ring{
    0%{-webkit-transform:scale(1)}
    10%,20%{-webkit-transform:scale(0.9) rotate(-3deg)}
    30%,50%,70%,90%{-webkit-transform:scale(1.1) rotate(3deg)}
    40%,60%,80%{-webkit-transform:scale(1.1) rotate(-3deg)}
    100%{-webkit-transform:scale(1) rotate(0)}
}
@-moz-keyframes ring{
    0%{-moz-transform:scale(1)}
    10%,20%{-moz-transform:scale(0.9) rotate(-3deg)}
    30%,50%,70%,90%{-moz-transform:scale(1.1) rotate(3deg)}
    40%,60%,80%{-moz-transform:scale(1.1) rotate(-3deg)}
    100%{-moz-transform:scale(1) rotate(0)}
}
@-ms-keyframes ring{
    0%{-ms-transform:scale(1)}
    10%,20%{-ms-transform:scale(0.9) rotate(-3deg)}
    30%,50%,70%,90%{-ms-transform:scale(1.1) rotate(3deg)}
    40%,60%,80%{-ms-transform:scale(1.1) rotate(-3deg)}
    100%{-ms-transform:scale(1) rotate(0)}
}
@keyframes ring{
    0%{transform:scale(1)}
    10%,20%{transform:scale(0.9) rotate(-3deg)}
    30%,50%,70%,90%{transform:scale(1.1) rotate(3deg)}
    40%,60%,80%{transform:scale(1.1) rotate(-3deg)}
    100%{transform:scale(1) rotate(0)}
}
/*4.1 按钮组
  Name:     mod_btn-group
  Example:  
<div class="btn-group">
  <span class="btn btn-primary radius">左边按钮</span>
  <span class="btn btn-default radius">中间按钮</span>
  <span class="btn btn-default radius">中间按钮</span>
  <span class="btn btn-default radius">右边按钮</span>
</div>

  Explain:
*/
.btn-group{ font-size:0}
.btn-group .btn{ margin-left:-1px}
.btn-group .btn:not(:first-child):not(:last-child):not(.dropdown-toggle){ border-radius:0}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle){border-bottom-right-radius: 0;border-top-right-radius: 0}
.btn-group > .btn:last-child:not(:first-child),.btn-group > .dropdown-toggle:not(:first-child) {border-bottom-left-radius: 0;border-top-left-radius: 0}

/*4.2.1 导航条
	Name:			mod_mainnav
	Example:		
	<nav class="mainnav">
	  <ul class="cl">
	    <li class="current"><a href="/">首页</a></li>
		<li><a href="#">核心</a></li>
		<li><a href="#">扩展</a></li>
		<li><a href="#">案例</a></li>
		<li><a href="#">联系我们</a></li>
	  </ul>
    </nav>
	Explain: 鼠标经过状态a:hover，当前选中状态li:current。		
*/
.mainnav{z-index:1;background-color:#222}/*导航条背景*/
.mainnav > ul > li,.mainnav > ul > li > a{height:40px;line-height:40px}/*导航条高度*/
.mainnav > ul > li{display:inline-block;color:#fff;font-size:14px;font-weight:bold}/*设置字体*/
.mainnav > ul > li > a{display:inline-block;padding:0 18px;color:#fff;text-align:center}/*链接颜色*/
.mainnav > ul > li > a:hover,.mainnav > ul > li.current > a{color:#fff;text-decoration:none; background-color:#000;-webkit-transition: background-color 0.3s ease 0s; -moz-transition: background-color 0.3s ease 0s; -o-transition: background-color 0.3s ease 0s; -ms-transition: background-color 0.3s ease 0s;transition: background-color 0.3s ease 0s}/*交互颜色*/

.Hui-nav-toggle{ display:none; position:absolute; top:0px; right:10px; padding:9px 10px; -moz-border-radius:4px;-webkit-border-radius:4px;-ms-border-radius:4px;border-radius:4px; background-color:rgba(0,0,0,0.5)}
@media (max-width: 767px) {
	.mainnav > ul > li{font-size:1.125em}
	.Hui-nav-toggle{ display:block}
}
@media (max-width: 480px) {
	.mainnav > ul > li{text-align:center}
	.mainnav > ul > li{width:20%}
	.mainnav > ul > li > a{padding:0; width:100%}
}
/*4.2.2 面包屑导航
	Name:			mod_breadcrumb
	Example:		
	<nav class="breadcrumb"><i class="iconfont">&#xf012b;</i><a href="/" class="maincolor">首页</a><span class="c-666 en">&gt;</span><a href="#">组件</a><span class="c-666 en">&gt;</span><span class="c-666">当前页面</span></nav>
*/
.breadcrumb{border-bottom: 1px solid #E5E5E5;line-height: 39px; height:39px;padding-left:20px;overflow:hidden}
.breadcrumb span{padding:0 5px}
/*4.2.3 翻页导航
	Name:			mod_pageNav
	Example:		<div class="pageNav" id="pageNav"></div>
	Explain:		需要调用pagenav.cn.js

*/
.pageNav{float:none;clear:both;font-size:0px;font-family:Arial,Helvetica,sans-serif;padding:18px 0px;text-align:center}
.pageNav span,.pageNav a,.pageNav b{font-size:14px;margin-right:5px;overflow:hidden;padding:3px 8px}
.pageNav a{border:1px solid #CCDBE4;cursor:pointer}
.pageNav b{color:#000}
.pageNav .mor{padding:3px;font-weight:bold}

/*4.2.4 顶部导航
	Name:			mod_topnav
	Sample:
	<div class="topnav"><div class="cl"><div class="l">您好，欢迎来到Hui！</div><div class="r"><span class="r_nav">[ <a rel="nofollow" href="javascript:login();">登录</a> ]</span><span class="pipe">|</span><span class="r_nav">[ <a href="javascript:register();" rel="nofollow">注册</a> ]</span><span class="pipe">|</span><span class="r_nav"><a title="收藏" href="javascript:addFavorite();">收藏本站</a></span><span class="pipe">|</span><span class="r_nav"><a href="javascript:void(0)" onclick="setHome(this);" title="设为首页">设为首页</a></span></div></div></div>
	
*/
.topnav{height:30px;line-height:30px;background:url(../images/bg_topnav.gif) repeat-x 0 0;border-bottom:1px solid #EBEBEB; font-size:12px}
.topbar{background-color: #ECECEC;border-bottom:1px solid #eee}
.topbar a{margin-right:5px}
.r_nav{display:inline-block; color:#999}

/*4.2.5 向导
	Name:			mod_steps
	Sample:
	<div class="four steps">
	  <span class="step">第一步</span>
	  <span class="active step">第二步</span>
	  <span class="disabled step">第三步</span>
	  <span class="disabled step">第四步</span>
	</div>
*/
.steps,.step{display:inline-block;position:relative;padding:1em 2em 1em 3em;vertical-align:top;background-color:#FFF;color:#888;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;-ms-box-sizing:border-box;box-sizing:border-box}
.step:after,.steps .step:after{position:absolute;z-index:2;content:'';top:0;right:-1.45em;border-bottom:1.5em solid transparent;border-left:1.5em solid #FFF;border-top:1.5em solid transparent;width:0;height:0}
.step,.steps .step,.steps .step:after{-webkit-transition:opacity .1s ease,color .1s ease,-webkit-box-shadow .1s ease;transition:opacity .1s ease,color .1s ease,box-shadow .1s ease}
.steps{cursor:pointer;display:inline-block;font-size:0;box-shadow:0 0 0 1px rgba(0,0,0,.1);line-height:1;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;-ms-box-sizing:border-box;box-sizing:border-box;border-radius:.3125rem}
.steps .step:first-child{padding-left:1.35em;border-radius:.3125em 0 0 .3125em}
.steps .step:last-child{border-radius:0 .3125em .3125em 0}
.steps .step:only-child{border-radius:.3125em}
.steps .step:last-child{margin-right:0}
.steps .step:last-child:after{display:none}
.step:hover,.step.hover{background-color:#F7F7F7;color:rgba(0,0,0,.8)}
.steps .step.hover:after,.steps .step:hover:after,.step:hover,.step.hover::after{border-left-color:#F7F7F7}
.steps .step.down,.steps .step:active,.step.down,.step:active{background-color:#F0F0F0}
.steps .step.down:after,.steps .step:active:after,.steps.down::after,.steps:active::after{border-left-color:#F0F0F0}
.steps .step.active,.active.step{cursor:auto;background-color:#428BCA;color:#FFF;font-weight:700}.steps .step.active:after,.active.steps:after{border-left-color:#428BCA}
.steps .disabled.step,.disabled.step{cursor:auto;background-color:#FFF;color:#CBCBCB}
.disabled.step:after{border:0;background-color:#FFF;top:.42em;right:-1em;width:2.15em;height:2.15em;-webkit-transform:rotate(-45deg);-ms-transform:rotate(-45deg);transform:rotate(-45deg);box-shadow:-1px -1px 0 0 rgba(0,0,0,.1) inset}
.attached.steps{margin:0;border-radius:.3125em .3125em 0 0}
.attached.steps .step:first-child{border-radius:.3125em 0 0}
.attached.steps .step:last-child{border-radius:0 .3125em 0 0}
.bottom.attached.steps{margin-top:-1px;border-radius:0 0 .3125em .3125em}
.bottom.attached.steps .step:first-child{border-radius:0 0 0 .3125em}
.bottom.attached.steps .step:last-child{border-radius:0 0 .3125em}

/*向导数量*/
.one.steps,.two.steps,.three.steps,.four.steps,.five.steps,.six.steps,.seven.steps,.eight.steps{display:block}
.one.steps>.step{width:100%}
.two.steps>.step{width:50%}
.three.steps>.step{width:33.333%}
.four.steps>.step{width:25%}
.five.steps>.step{width:20%}
.six.steps>.step{width:16.666%}
.seven.steps>.step{width:14.285%}
.eight.steps>.step{width:12.5%}

/*向导尺寸*/
.small.step,.small.steps .step{font-size:.8rem}/*小*/
.step,.steps .step{font-size:1rem}/*默认*/
.large.step,.large.steps .step{font-size:1.25rem}/*大*/

/*兼容写法
	Sample:
	<div class="steps-ie cl">
	  <a class="step-ie active" href="#">第一步<em class="arrow"></em></a>
	  <a class="step-ie" href="#">第二步<em class="arrow"></em></a>
	  <a class="step-ie" href="#">第三步<em class="arrow"></em></a>
	</div>
*/
.steps-ie,.step-ie,.step-ie .arrow{height:44px; line-height:44px}
.steps-ie{background-color:#eaf4fd; border:solid 1px #afcfcc}
.step-ie{position:relative;display:inline-block; float:left; cursor:pointer; padding:0 20px 0 40px; background:url(../images/step_bg.png) repeat-x 0 center}
.step-ie .arrow{position:absolute;right:-21px; top:0; width:21px; height:44px; display:block; cursor:pointer; background:url(../images/step_arrow.png) no-repeat 0 center;z-index:50}
.step-ie.active{background-image:url(../images/step_bg-active.png); color:#fff; z-index:100}
.step-ie.active .arrow{background-image:url(../images/step_arrow-active.png)}

/*4.2.6 竖向导向tab导航
	Sample:
	<div class="verticalTab">
	  <a class="" href="#">导航一<em></em></a>
	  <a class="active" href="#">导航二<em></em></a>
	  <a href="#">导航三<em></em></a>
	  <a href="#">导航四<em></em></a>
	</div>
*/
.verticalTab{background:#fff url(../images/tab_bg.png) repeat-y 0 0; width:38px}
.verticalTab a{position:relative; display:block; width:18px; height:auto; text-align:center; position:relative; padding:26px 10px 6px 10px; background:url(../images/tabNav.png) no-repeat 0 0}
.verticalTab a em{position:absolute; left:0; bottom:-20px; width:38px; height:20px; background:url(../images/tabNav_right.png) no-repeat 0 0; z-index:50}
.verticalTab a.active{background-image:url(../images/tabNav-active.png); color:#fff; z-index:99}
.verticalTab a.active em{background-image:url(../images/tabNav_right-active.png)}

/*4.2.6 横向导向tab导航
	Sample:
	<ul class="acrossTab">
	  <li>导航一<i></i><em></em></li>
	  <li class="active">导航二<i></i><em></em></li>
	  <li>导航三<i></i><em></em></li>
	</ul>
*/
.acrossTab{height:29px; background:url(../images/acrossTab-bg.png) repeat-x 0 0; padding-top:1px}
.acrossTab li,.acrossTab li em{background:url(../images/acrossTab.png) no-repeat 0 0}
.acrossTab li{position:relative;float:left; display:inline-block; height:29px; line-height:29px; font-size:12px;cursor:pointer;padding:0 30px; white-space:nowrap;color:#282828; background-position:0 0}
.acrossTab li em{position:absolute; width:23px;height:29px;right:-20px; top:0;z-index:50; background-position:right -30px}
.acrossTab li:hover{background-position:0 -60px}
.acrossTab li:hover em{background-position:right -90px}
.acrossTab li.active{background-position:0 -120px; z-index:99}
.acrossTab li.active em{background-position:right -150px}
.acrossTab li i{position:absolute; display:block; width:13px; height:13px; top:50%; margin-top:-6px; right:5px; font-size:0; line-height:0; cursor:pointer; background:url(../images/acrossTab-close.png) no-repeat 0 0}
.acrossTab li i:hover{background-position:0 bottom}

/*4.3 下拉菜单
	Name:			mod_dropDown
	Example:		<span class="dropDown "><a class="dropDown_A" href="#">下拉菜单</a><span class="dropDown-menu box-shadow"><ul><li><a href="#">菜单一</a></li><li><a href="#">菜单二</a></li></ul></span>span>
	Explain:		如需要在不同的地方显示不同的样式，请在<span class="dropDown"> 追加class，采用覆盖的方式重写默认样式。

*/
.dropDown{display:inline-block}
.dropDown_A{display:inline-block}
.dropDown_A i{transition-duration:0.3s ;transition-property:all}
.dropDown-menu{ display:none;transition: all 0.3s ease 0s}
.dropDown:focus,.dropDown-menu:focus {outline:0}
.dropDown-menu li.arrow{ position:absolute;display:block; width:12px; height:8px; margin-top:-13px; margin-left:20%; line-height:0;background:url(../images/icon-jt.png) no-repeat 0 0}
/*鼠标经过*/
.dropDown.hover .dropDown_A,.dropDown.open .dropDown_A{text-decoration:none;background-color:rgba(255,255,255,0.2)}
.dropDown.hover .dropDown_A i,.dropDown.open .dropDown_A i{transform: rotate(180deg);transition-duration:0.3s ;transition-property:all;_background-position:0 0}
/*打开菜单*/
.dropDown.open{position:relative;z-index:990}
.dropDown.open .dropDown-menu{position:absolute;z-index:1000;display:inline-block;top:100%;left:-1px;min-width:100%;background-color:#fff;border:solid 1px #f2f2f2}/*默认左对齐*/
.dropDown.open .dropDown-menu.box-shadow{ border-top-left-radius:0;border-top-right-radius:0}
.dropDown.open .dropDown-menu.radius{ border-top:none}
/*右对齐*/
.dropDown.open.right .dropDown-menu{right:-1px!important;left:auto!important}
.dropDown.open .dropDown-menu li{float:none;display:inline}
.dropDown-menu li a{display:block;clear: both;border-bottom:solid 1px #f2f2f2;margin-bottom:-1px;padding:0px 20px; height:30px; line-height:30px; font-size:12px;text-align:left;font-weight: normal;white-space:nowrap}
.dropDown-menu li:last-child a{ border-bottom:none}
.dropDown-menu li a:hover,.dropDown-menu li a:focus{background-color:#f0f1f2;text-decoration:none}
/*禁用菜单*/
.dropDown.open .dropDown-menu .disabled a,.dropDown.open .dropdown-menu .disabled a:hover,.dropDown.open .dropdown-menu .disabled a:focus{color:#999;text-decoration:none; cursor:no-drop; background-color:transparent}
.dropDown.open .dropDown-menu li.divider{ display:block;height:0px; line-height:0px;margin:9px 0;overflow:hidden; border-top:solid 1px #eee}

/*4.4 幻灯片
	Name:			module_slider
	Sample:
  <section class="slider cl has-dots">
		<a href="javascript:void(0)" class="unslider-arrow prev"></a>
		<a href="javascript:void(0)" class="unslider-arrow next"></a>
    <ul class="tabcon">
      <li><a href="#" target="_blank"><img name="" src="pic/1.png" width="300" height="150" alt=""></a></li>
      <li><a href="#" target="_blank"><img name="" src="pic/2.png" width="300" height="150" alt=""></a></li>
      <li><a href="#" target="_blank"><img name="" src="pic/3.png" width="300" height="150" alt=""></a></li>
    </ul>
  </section>
	<script type="text/javascript" src="Lib/unslider.min.js"></script> 
	<script type="text/javascript">
	$(function() {
		$('.slider').unslider({deplay:3000,fluid: true,dots: true});
		var unslider = $('.slider').unslider();
    	$('.unslider-arrow').click(function() {
        	var fn = this.className.split(' ')[1];
			unslider.data('unslider')[fn]()})});
	</script>
	Explain: 默认dots为圆点，在slider后面追加numSlider样式就变成带数字的方框，boxSlider为不带数字长方条
*/
.slider{position:relative;text-align:center; margin:0 auto}
.slider .tabcon li{float: left;overflow:hidden}
.dots{position:absolute; left:0;right:0px;bottom:10px;text-align:center}
.dots span{display:inline-block;text-align:center;width:8px; height:8px; font-size:0px;line-height:0px;margin-right:8px;cursor:pointer; background-color:#C2C2C2;border-radius:8px}
.dots span.active{background-color:#222}
.numSlider .dots span{width:20px; height:20px; line-height:20px; font-size:13px; margin-right:10px;line-height:20px;background-color:#fff;font-family:Arial;font-weight:bold; text-indent:inherit;border-radius:0px}
.numSlider .dots span.active{background-color:#222;color:#fff}
.boxSlider .dots span{width:40px; height:10px;border-radius:0px}
.boxRadius .dots span{width:40px}

.unslider-arrow{display:block; position:absolute; top:50%; margin-top:-25px;height: 50px;width: 50px; z-index:3;opacity: 0.7;filter: alpha(opacity=70); background-color:#303030; background-image:url(../images/unslider-arrow.png); background-repeat:no-repeat}
.unslider-arrow:hover{opacity: 1;filter: alpha(opacity=100)}
.prev{left:0px; background-position:0 0}
.next{right:0px;background-position:right 0}
/*4.5 选项卡
	Name:			module_slider
	Sample:
	<div id="tab_demo" class="HuiTab">
      <div class="tabBar cl"><span>选项卡一</span><span>选项卡二</span><span>自适应宽度</span></div>
      <div class="tabCon">内容一</div>
      <div class="tabCon">内容二</div>
      <div class="tabCon">内容三</div>
    </div>	
*/
.tabBar {border-bottom: 2px solid #222}
.tabBar span {background-color: #e8e8e8;cursor: pointer;display: inline-block;float: left;
font-weight: bold;height: 30px;line-height: 30px;padding: 0 15px}
.tabBar span.current{background-color: #222;color: #fff}
.tabCon {display: none}
/*4.6 标签与标号
	Name:			style_label
	Example:		<span class="label label-default|label-primary|label-secondary|label-success|label-warning|label-danger">默认</span>
	Explain:		.label-default 默认|.label-primary 主要|.label-secondary 次要|.label-success 成功|.label-warning 警告|.label-danger 危险
	
	Name:			style_badge
	Example:		<span class="badge badge-default|label-primary|badge-secondary|badge-success|badge-warning|badge-danger">默认</span>
	Explain:		.badge-default 默认|.badge-primary 主要|.badge-secondary 次要|.badge-success 成功|.badge-warning 警告|.badge-danger 危险

*/
.label, .badge{display: inline-block;padding:2px 4px;font-size: 11.844px;font-weight: bold;line-height:14px;color: #fff;white-space: nowrap;vertical-align:middle;background-color: #999}
/*圆角*/
.label.radius{border-radius: 3px}
.badge{padding-right:9px;padding-left:9px;border-radius:9px}
.label:empty, .badge:empty{display: none}
a.label:hover, a.label:focus, a.badge:hover, a.badge:focus{color: #fff;text-decoration: none;cursor: pointer}

/*默认*/
.label-default, .badge-default{background-color: #e6e6e6; color:#333}
.label-default[href], .badge-default[href]{background-color: #e6e6e6;color:#333}

/*主要*/
.label-primary, .badge-primary{background-color: #5a98de}
.label-primary[href], .badge-primary[href]{background-color: #5a98de}

/*主要*/
.label-secondary, .badge-secondary{background-color: #3bb4f2}
.label-secondary[href], .badge-secondary[href]{background-color: #3bb4f2}

/*成功*/
.label-success, .badge-success{background-color:#5eb95e}
.label-success[href], .badge-success[href]{background-color: #5eb95e}

/*警告*/
.label-warning, .badge-warning{background-color: #f37b1d}
.label-warning[href], .badge-warning[href]{background-color: #f37b1d}

/*危险*/
.label-danger, .badge-danger{background-color: #dd514c}
.label-danger[href], .badge-danger[href]{background-color: #dd514c}

/*4.7 缩略图
	Name:       style_img
	Example:		
	Explain:		缩略图
*/
.imgbox{}
.img{}
.img-circle{border-radius: 50%}
@media (max-width: 480px) {
	.responsive img,.thumbImage img{max-width:100%!important; height:auto!important;width:expression(this.width > 320 ? "320px" : this.width)!important}
	.thumbImage {margin: auto;width: 320px}
	*html .content img,*html .thumbImage img{width:expression(this.width>320&&this.width>this.height?320:auto)}
}

/*4.8 警告
	Name:			mod_Hui-alert
	Example:		
  <div class="Huialert Huialert-success/Huialert-danger/Huialert-error/Huialert-info/Huialert-block"><i class="Hui-iconfont">&#xe6a6;</i>警告内容<ul><li>……</li></ul></div>
	Explain:		警告,使用警告框jQuery插件
*/
.Huialert{position:relative;padding:8px 35px 8px 14px;margin-bottom: 20px;text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);background-color: #fcf8e3;border: 1px solid #fbeed5}
.Huialert, .Huialert h4{color: #c09853}
.Huialert h4{margin: 0}
.Huialert .Hui-iconfont{position:absolute;top:9px;right:10px;line-height: 20px;cursor:pointer; color:#000; opacity:0.2;_color:#666}
.Huialert .Hui-iconfont.hover{color:#000;opacity:0.8}
.Huialert-success{color: #468847;background-color: #dff0d8;border-color: #d6e9c6}
.Huialert-success h4{color: #468847}
.Huialert-danger{color: #b94a48;background-color: #f2dede;border-color: #eed3d7}
.Huialert-danger h4{color: #b94a48}
.Huialert-error{color: #fff;background-color: #f37b1d;border-color: #e56c0c}
.Huialert-error h4{color: #fff}
.Huialert-info{color: #31708f;background-color: #d9edf7;border-color: #bce8f1}
.Huialert-info h4{color:#31708f}
.Huialert-block{padding-top: 14px;padding-bottom: 14px}
.Huialert-block > p, .Huialert-block > ul{margin-bottom: 0}
.Huialert-block p + p{margin-top: 5px}

/*4.9 进度条 loading
	Name:			mod_progress
	Example:
	<div class="progress radius"><div class="progress-bar"><span class="sr-only"></span></div></div>
	Explain:		警告,使用警告框jQuery插件
*/
.progress,.progress-bar,.sr-only{height:10px; font-size:0;line-height:0}
.progress{overflow:hidden; width:400px}
.progress.radius{}/*自定义圆角*/
.progress-bar{width:100%;background-color:#efefef}
.sr-only{display:inline-block; background-color:#58b957}

/*4.10 对话框
	Name:			mod_modal
	Example:		
*/
.loading{height: 50px; background: url(../images/loading_072.gif) no-repeat center}
.mask{position:fixed;top:0;left:0;z-index:999;width:100%;height:100%;background:url(../images/backdrop.png) repeat 0 0;-moz-transition:all 0.3s ease-in;-webkit-transition:all 0.3s ease-in;-o-transition:all 0.3s ease-in;transition:all 0.3s ease-in}

* html .mask{position:absolute;left:expression(documentElement.scrollLeft + documentElement.clientWidth - this.offsetWidth);top:expression(documentElement.scrollTop + documentElement.clientHeight - this.offsetHeight)}

/*Huimodal*/
.modal-open{overflow:hidden}/*当弹出层出现时，隐藏body右侧滚动条*/
.page-overflow{margin-right:16px}/*隐藏body滚动条时，页面偏移*/
.modal-scrollable{position:fixed;top:0;right:0;bottom:0;left:0;overflow: auto}/*弹出层的最外层，全屏，用户响应点击和滚动*/
.modal-overflow .modal-scrollable{overflow-y:scroll}/*弹窗超出屏幕高度时，弹出层替代body右侧滚动条，实现弹出层的整体滚动*/
.modal-backdrop {position:fixed;top:0;right:0;bottom:0;left:0;width:100%;height:100%;z-index:1040;background-color:#000}/*默认遮罩层，全屏黑色*/
* html .modal-backdrop{position:absolute;left:expression(documentElement.scrollLeft + documentElement.clientWidth - this.offsetWidth);top:expression(documentElement.scrollTop + documentElement.clientHeight - this.offsetHeight)}/*解决IE下 遮罩层兼容性*/
.modal-backdrop.fade {opacity:0;filter: alpha(opacity=0)}/*遮罩层失去时，透明度为0*/
.modal-backdrop,.modal-backdrop.fade.in {opacity: 0.7;filter: alpha(opacity=70)}/*遮罩层进入 0.7透明度*/
.modal {position:absolute;top:50%;left:50%; width:500px;margin-left:-250px;z-index:1050;overflow: visible;background-color: #fff;background-clip: padding-box;
    box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
    -webkit-background-clip:padding-box;
    -khtml-background-clip:padding-box;
    -moz-background-clip:padding-box;
    -ms-background-clip:padding-box;
    -o-background-clip:padding-box;
    background-clip:padding-box;
    border-radius:6px;
    border:1px solid rgba(0, 0, 0, 0.3);*border:1px solid #999}/*弹出层，绝对定位，默认【宽度500px，高度自适应，背景白色，6px圆角，带阴影】，自定义宽度可在data-width中设置参数*/
.fade {opacity: 0;filter: alpha(opacity=0);-webkit-transition: opacity 0.2s linear;-moz-transition: opacity 0.2s linear;-o-transition: opacity 0.2s linear;transition: opacity 0.2s linear}
.fade.in {opacity: 1;filter: alpha(opacity=100)}
.modal.fade {top: -100%}/*弹出层进入动画效果*/
.modal.fade.in{top:50%}/*弹出层进入后居顶距离，配合margin-top负值，实现上下自动居中*/
.modal.modal-overflow.fade.in{top:0px}/*如果弹出层高度大于窗口，弹出窗口距顶0像素*/
.loading-spinner {position: absolute;top: 50%;left: 50%;margin: -12px 0 0 -12px}/*Ajax加载数据时loading*/
  /*弹出层header区*/
  .modal-header {padding: 9px 15px;border-bottom: 1px solid #eee; position:relative}/*弹出层头部*/
  .modal-header .close{position:absolute; right:10px; top:10px}/*关闭按钮，请调用4.1.1 按钮 .close*/
  .modal-header h3{margin:0}/*标题*/
  .modal-body {overflow-y:visible;padding: 15px} /*内容不限高度，内填充15px，不满意可自行修改*/
  .modal-form {margin-bottom: 0} /*弹出层表单*/
  /*弹出层footer区，放按钮*/
  .modal-footer {padding: 14px 15px 15px;margin-bottom: 0;text-align: right;background-color: #f5f5f5;border-top: 1px solid #eee;*zoom: 1;border-radius: 0 0 6px 6px;box-shadow: inset 0 1px 0 #fff}
  .modal-footer:before,.modal-footer:after {display: table;content: ""}
  .modal-footer:after {clear: both}
  .modal-footer .btn + .btn {margin-left: 5px;margin-bottom: 0}
  .modal-footer .btn-group .btn + .btn {margin-left: -1px}
.modal-alert{position:fixed; width:300px;margin-left:-150px; margin-top:-30px; z-index:9999}
.modal-alert-info{padding:30px; text-align:center; font-size:14px; background-color:#fff}

.mask_box{background-image:none;display:none;z-index:99}
.hover .mask_box{position:absolute;bottom:0px; left:0px;display:block;background:url(../images/backdrop.png) repeat 0 0;text-align:left}
.easyDialog_wrapper{width:320px; color:#444; border:3px solid rgba(0,0,0,0);border-radius:5px;box-shadow:0 0 10px rgba(0,0,0,0.4); display:none}
.easyDialog_wrapper .easyDialog_content{border-radius:4px; background:#fff; border:1px solid #e5e5e5}
.easyDialog_wrapper .easyDialog_title{height:30px; line-height:30px; overflow:hidden; color:#666; padding:0 10px; font-size:14px; border-bottom:1px solid #e5e5e5; background:#f7f7f7; border-radius:4px 4px 0 0; margin:0 }
.easyDialog_wrapper .close_btn{font-family:arial; font-size:18px; _font-size:12px; font-weight:700; color:#999; text-decoration:none; float:right}
.easyDialog_wrapper .close_btn:hover{color:#333}
.easyDialog_wrapper .easyDialog_text{padding:25px 10px; font-size:13px; line-height:22px}
.easyDialog_wrapper .easyDialog_footer{padding:10px; text-align:right; *zoom:1}
.easyDialog_wrapper .easyDialog_footer:after{content:''; display:block; height:0; overflow:hidden; visibility:hidden; clear:both}
.easyDialog_wrapper .btn_highlight,.easyDialog_wrapper .btn_normal{border:1px solid; border-radius:2px; cursor:pointer;float:right; font-size:12px; padding:0 12px; height:24px; line-height:24px; margin-bottom:10px}
.easyDialog_wrapper .btn_highlight{background:#4787ed; background:-webkit-gradient(linear,center bottom,center top,from(#4787ed),to(#4d90fe)); background:-moz-linear-gradient(90deg, #4787ed, #4d90fe); border-color:#3079ed; color:#fff}
.easyDialog_wrapper .btn_normal{margin-left:10px; border-color:#c3c3c3; background:#ececec; color:#333; background:-webkit-gradient(linear,center bottom,center top,from(#ececec),to(#f4f4f4)); background:-moz-linear-gradient(90deg,#ececec,#f4f4f4)}

.modal-open .dropdown-menu {z-index: 2050}
.modal-open .dropdown.open {*z-index: 2050}
.modal-open .popover {z-index: 2060}
.modal-open .tooltip {z-index: 2070}

/*4.12 返回顶部
	Name:			mod_totop
	Level:			Global
	Example:		<a href="javascript:void(0)" class="toTop" title=backToTopTxt alt=backToTopTxt></a>
	Explain:		返回顶部
*/
.toTop{position:fixed;bottom:10px;right:20px; z-index:999;display:none; font-size:16px;cursor: pointer;overflow:hidden;visibility:visible; background-color:#fff;border: 1px solid #d9d9d9;color: #9c9c9c;font-size: 16px;width: 38px;height: 38px;line-height: 38px;text-align: center; text-decoration:none;_position:absolute;_top:expression(documentElement.scrollTop + documentElement.clientHeight-this.offsetHeight)}
.toTop:hover{ color:#fff; text-decoration:none; background-color:#999}

/*4.13 分享到
	Name:			mod_share
	Example:
<section class="share cl">
<!-- Baidu Button BEGIN -->
<div id="bdshare" class="bdshare_t bds_tools get-codes-bdshare">
<span class="bds_more">分享到：</span>
<a class="bds_tsina"></a>
<a class="bds_qzone"></a>
<a class="bds_tqq"></a>
<a class="bds_renren"></a>
<a class="bds_sqq"></a>
</div>
<script type="text/javascript" id="bdshare_js" data="type=tools&amp;uid=0" ></script>
<script type="text/javascript" id="bdshell_js"></script>
<script type="text/javascript">
document.getElementById("bdshell_js").src = "http://bdimg.share.baidu.com/static/js/shell_v2.js?cdnversion=" + Math.ceil(new Date()/3600000)
</script>
<!-- Baidu Button END -->
</section>
	Explain:		分享到 直接调用百度分享，http://share.baidu.com/code

*/
/*4.14 Panel 面板
	Name:			mod_panel
	Sample:	
	<div class="panel panel-default">
		<div class="panel-body">默认面板</div>
	</div>
*/
.panel{ background-color:#fff; border:solid 1px transparent}
	.panel-header{ border-bottom:solid 1px transparent; padding:8px 15px; font-size:14px; font-weight:700}/*面板标题*/
	.panel-body{ padding:15px}/*面板内容*/
	.panel-footer{background-color: #f5f5f5;border-top: 1px solid #eee;padding:5px 20px}/*面板页脚*/
/*默认面板*/
.panel-default{border-color:#eee;}
.panel-default > .panel-header{ border-color:#eee; background-color:#f5f5f5; color:#444}

/*主要面板*/
.panel-primary{border-color:#5a98de;}
.panel-primary > .panel-header{ border-color:#5a98de; background-color:#5a98de; color:#fff}

/*次要面板*/
.panel-secondary{border-color:#3bb4f2;}
.panel-secondary > .panel-header{ border-color:#3bb4f2; background-color:#3bb4f2; color:#fff}

/*成功面板*/
.panel-success{border-color:#5eb95e;}
.panel-success > .panel-header{ border-color:#5eb95e; background-color:#5eb95e; color:#fff}

/*警告面板*/
.panel-warning{border-color:#f37b1d;}
.panel-warning > .panel-header{ border-color:#f37b1d; background-color:#f37b1d; color:#fff}

/*危险面板*/
.panel-danger{border-color:#dd514c;}
.panel-danger > .panel-header{ border-color:#dd514c; background-color:#dd514c; color:#fff}

/*4.15 案例
	Name:			mod_docs-example
	Example:		<div class="docs-example tooltip-demo">……</div>
*/
.docs-example{position:relative;*position:static;*padding-top: 19px;margin: 15px 0;padding: 39px 19px 14px;background-color: #fff;border: 1px solid #eee;border-radius: 4px}
.docs-example:after{content: "Example";position: absolute;top: -1px;left: -1px;*position:static;padding: 3px 7px;font-size: 12px;font-weight: bold;background-color: #f5f5f5;border: 1px solid #eee;color: #9da0a4;border-radius: 4px 0 4px 0}
/*4.16 滚动
	Name:			mod_rolling
	Example:		<div class="rollpicshow"><ul><li>……</li></ul></div>
*/
.marquee{height:22px;overflow:hidden;line-height:22px}
.rollpic .prev,.rollpic .next{display:block; height:38px; width:38px; cursor:pointer; float:left; background:url(../images/unslider-arrow2.png) no-repeat 0 0; margin-top:70px}
.rollpic .prev{background-position:0 0; margin-right:5px}
.rollpic .prev:hover{background-position:0 -38px}
.rollpic .next{background-position:0 -76px;margin-left:5px}
.rollpic .next:hover{background-position:0 -114px}
.rollpicshow{float:left; border:solid 1px #eee}
.rollpicshow li{padding:10px}
/*4.17 搜索条
	Name:			mod_searchBar
	Sample:
<div class="searchBar">
  <form class="form-search" method="post" action="">
    <input id="searchKeyword" name="searchKeyword" value="请输入搜索关键词" class="searchTxt">
    <input id="searchBtn" name="searchBtn" type="submit" value="搜索" class="searchBtn" onclick="b_onclick()">
  </form>
</div>
*/
.searchBar{z-index: 100; width:460px;height: 33px;background:url(../images/search.gif) no-repeat 0 -48px;position:relative}
	.form-search{margin-bottom:0px}
	.searchTxt{float:left;width:350px;height:23px;line-height:23px;font-size:14px;color:#9a9a9a;margin-left:33px;margin-top:5px;border:none;background-color:#fff}
	.searchTxt.focus{color:#333}
	.searchBtn{float:right;display:block;border:none;width:73px;height:33px;cursor:pointer;text-indent:-9999px;background:url(../images/search.gif) no-repeat right -81px}
	.searchBtn:hover{background-position:right -114px}
	/*搜索下拉提示*/
	.ac_results{position:absolute;width:386px; height:auto;border:solid 1px #890800;background-color:#fff;top:32px; left:0px; display:none}
	.ac_results ul{width:100%;list-style-position:outside;list-style:none;padding:0;margin:0}
	.ac_results li p{float:left;margin:0;padding:0;overflow:hidden}
	.ac_results li p tt{color:#666}
	.ac_results li span{margin:0;padding:0;display:inline;float:right;color:#f93;width:90px; text-align:right; overflow:hidden}
	.ac_results ul li{padding-left:5px;padding-right:5px;display:block;height:22px;line-height:22px;cursor:pointer;color:#000;font:宋体;font-size:12px}
	.ac_results ul li.hover{background-color:#fef8ec;color:#000}
		.ac_loading{background:#fff url(../images/loading_s.gif) right center no-repeat}

.AD{text-align:center}
/*4.18.1 对联广告
	Name:			mod_AD_dual
	Sample:
	<div class="dual dual_l"><div class="dual_con">对联的内容</div><a href="#" class="dual_close">X关闭</a></div>
	<div class="dual dual_r"><div class="dual_con">对联的内容</div><a href="#" class="dual_close">X关闭</a></div>
*/
.dual{top:260px;position:absolute; width:102px; overflow:hidden; display:none; z-index:100}
.dual_l{left:6px}
.dual_r{right:6px}
.dual_con{border:#CCC solid 1px;width:100px; height:300px; overflow:hidden; background-color:#0C9}
.dual_close{width:100%;height:24px; line-height:24px; text-align:center; display:block; font-size:13px; color:#555; text-decoration:none}

/*4.19 标签*/
/*4.19.1 标签输入效果
	Name:			mod_Hui-tags
	Sample:			
	<div class="Hui-tags">
	  <div class="Hui-tags-editor cl"><i class="Hui-tags-icon"></i>
		<span class="Hui-tags-token">Hui前端框架</span>
		<span class="Hui-tags-token">CSS3</span>
		<span class="Hui-tags-token">HTML5</span>
		<div class="Hui-tags-iptwrap"><input type="text" class="Hui-tags-input" maxlength="20" value=""><label class="Hui-tags-label">添加相关标签，用空格或回车分隔</label></div>
	  </div>
	  <div class="Hui-tags-list">
	    <div class="Hui-notag">暂无常用标签</div>
		<div class="Hui-tags-has"><span>前端框架</span> <span>前端开发</span> <span>H-ui</span></div>
	  </div>
	  <input type="hidden" class="Hui-tags-val" name="" value="">
	</div>
*/
.Hui-tags{border:solid 1px #dedede; padding:0 10px}
	.Hui-tags-editor{position:relative; padding:10px 0 10px 24px}
	.Hui-tags-editor .Hui-tags-icon{position:absolute; left:0; top:11px; font-size:14px; color:#666}
		.Hui-tags-token{color:#aaa; float:left; font-size:12px; height:20px; line-height:20px; margin-right:8px; padding:0 1px; white-space:nowrap; cursor:pointer}
		.Hui-tags-token:before{content:"#"}
		.Hui-tags-token:hover{text-decoration:line-through}
		.Hui-tags-iptwrap{position:relative; float:left}
			.Hui-tags-input{position:relative;height:20px;min-width:60px; border:0 none; vertical-align:top;line-height:20px; color:#333;z-index:2;background: url(../images/empty.png) repeat scroll 0 0; display:inline-block; width:100%}
			.Hui-tags-label{position:absolute; top:0; left:2px; width:240px; height:20px; line-height:20px; font-size:14px; overflow:hidden; z-index:1; color:#ccc}
		.Hui-tags-list{padding:0 0 10px 0; display:none}
	.Hui-notag{font-size:12px}
	.Hui-tags-has span{cursor:pointer; font-size:12px; white-space:nowrap; margin-right:10px}

/*4.19.2 标签混排效果
	Name:			mod_tags
	Sample:			<div class="pd-10 tags"><a href="http://www.h-ui.net/">H-ui前端框架</a>……</div>
	
*/
.tags a{height:26px; line-height:26px;padding-right:6px}
	.tags0{}
	.tags1{color:#C00; font-size:24px}
	.tags2{color:#030; font-size:16px}
	.tags3{color:#00F}
	.tags4{font-size:16px}
	.tags5{color:#C00; font-size:20px}
	.tags6{color:#F06 font-size:20px}
	.tags7{color:#030; font-weight:bold; font-size:18px}
	.tags8{color:#F06; font-weight:bold}
	.tags9{color:#C00; font-weight:bold;font-size:16px}
.tags a:hover{color:#F00; text-decoration:underline}

/*4.19.3 tag云标签*/
#tagyun {position:relative}
#tagyun a {position:absolute;top:0px;left:0px;font-weight:bold;text-decoration:none;padding:3px 6px}

/*4.20 折叠
	Name:			mod_Huifold
	Sample:	
	<ul id="" class="Huifold">
	  <li class="item"><h4>标题<b>+</b></h4><div class="info">内容 </div></li>
	  ……
	</ul>
*/
.Huifold .item{position:relative}
.Huifold .item h4{margin:0;font-weight:bold;position:relative;border-top: 1px solid #fff;font-size:15px;line-height:22px;padding:7px 10px;background-color:#eee;cursor:pointer;padding-right:30px}
.Huifold .item h4 b{position:absolute;display: block; cursor:pointer;right:10px;top:7px;width:16px;height:16px; text-align:center; color:#666}
.Huifold .item .info{display:none;padding:10px}

/*4.21 遮罩条
	Name:			mod_maskBar
*/
.maskBar{position:absolute;width:100%; height:auto;left:0; bottom:-60px; right:0;background-color:#000;z-index:2; color:#fff!important;opacity:0.6;filter:alpha(opacity=60)}
.maskWraper{position:relative; overflow:hidden}
.maskWraper.hover .maskBar{bottom:0px;
  transition: bottom 200ms;
  -moz-transition: bottom 200ms; /* Firefox 4 */
  -webkit-transition: bottom 200ms; /* Safari 和 Chrome */
  -o-transition: bottom 200ms; /* Opera */
}
.maskBox{position:absolute;width:100%; height:100%;top:0;left:0; bottom:0;right:0;z-index:2;background-color:#000; color:#fff!important;opacity:0;filter:alpha(opacity=0)}
.maskWraper.hover .maskBox{opacity:0.2;filter:alpha(opacity=20);
  transition: opacity 500ms;
  -moz-transition: opacity 500ms; /* Firefox 4 */
  -webkit-transition: opacity 500ms; /* Safari 和 Chrome */
  -o-transition: opacity 500ms; /* Opera */
}

/*4.22 评论列表
	Name:			mod_comment
	Sample:		
	<ul class="commentList">
	  <li class="item cl">
	    <a href="#"><i class="avatar avatar-L radius"><img alt="" src="static/h-ui/images/avatar-default-S.gif"></i></a>
		<div class="comment-main">
		  <header class="comment-header">
		    <div class="comment-meta"><a class="comment-author" href="#">辉哥</a> 评论于 <time title="2014年8月31日 下午3:20" datetime="2014-08-31T03:54:20">2014-8-31 15:20</time></div>
	      </header>
		  <div class="comment-body"><p><a href="#">@某人</a> 你是猴子派来的救兵吗？</p></div></div>
	  </li>
	</ul>
*/
.commentList .item{list-style: none outside none;margin: 1.6rem 0 0}
.commentList .avatar{border: 1px solid transparent;float: left}
	.comment-main{position:relative;margin-left:64px;border:1px solid #dedede;border-radius:2px}
.comment-main:before,.comment-main:after{position:absolute;top:11px;left:-16px;right:100%;width:0;height:0;display:block;content:"";border-color:transparent;border-style:solid solid outset;pointer-events:none}
	.comment-main:before{border-right-color:#dedede;border-width:8px}
	.comment-main:after{border-width:7px;border-right-color:#f8f8f8;margin-top:1px;margin-left:2px}
		.comment-header{padding:10px 15px;background:#f8f8f8;border-bottom:1px solid #eee}
		.comment-title{margin:0 0 8px 0;font-size:1.6rem;line-height:1.2}
		.comment-meta{font-size:13px;color:#999;line-height:1.2}
		.comment-meta a{color:#999}
		.comment-author{font-weight:700;color:#999}
		.comment-body{padding:15px;overflow:hidden}
		.comment-body>:last-child{margin-bottom:0}
.commentList .comment-flip .avatar {float: right}
  .comment-flip .comment-main{margin-left: 0; margin-right: 64px}
  .comment-flip .comment-main:before {border-left-color: #dedede;border-right-color: transparent}
  .comment-flip .comment-main:before, .comment-flip .comment-main:after {left: 100%;position: absolute;right: -16px}
  .comment-flip .comment-main:after {border-left-color: #f8f8f8;border-right-color: transparent;margin-left: auto;margin-right: 2px}

/*5.1 错误页-404*/
.page-404{ color:#afb5bf; padding-top:60px}
.page-404 .error-title{font-size:80px}
.page-404 .error-title .iconfont{font-size:80px}
.page-404 .error-description{font-size:24px}
.page-404 .error-info{font-size:14px}
#page-404 .footer{ position:absolute; bottom:0; left:0; right:0}