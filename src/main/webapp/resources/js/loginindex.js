layui.use(['form', 'layer'], function () {
    var form = layui.form,
      layer = layui.layer;
    // 监听提交
    form.on('submit(submit)', function (data) {
      event.preventDefault();
      var loading = layer.load(1, {
        shade: [0.1, '#fff'] //0.1透明度的白色背景
      });
      $.ajax({
        type: 'POST',
        url: '',
        data: data.field,
        success: function (data) {
          layer.close(loading);
          if (data.result == 200) {
            $("#btn_sub").attr("disabled", true);
            layer.msg(data.obj, {
              time: 1000
            }, function () {
              var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
              parent.layer.close(index);
            });
          } else if (data.result == 500) {
            layer.msg(data.msg, {
              time: 1000
            }, function () {

            });
          }
          return false;
        }
      })
    });
    // 监听取消
    form.on('submit(cancle)', function (data) {
      event.preventDefault();
      var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
      parent.layer.close(index);
      return false;
    })
  })