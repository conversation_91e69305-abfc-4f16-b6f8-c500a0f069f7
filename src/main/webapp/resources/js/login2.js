layui.use(['layer', 'element'], function () {
    var layer = layui.layer;
    var element = layui.element;

    function myBrowser() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1;
        if (isOpera) {
            return "Opera"
        }
        ; //判断是否Opera浏览器
        if (userAgent.indexOf("Firefox") > -1) {
            return "FF";
        } //判断是否Firefox浏览器
        if (userAgent.indexOf("Chrome") > -1) {
            return "Chrome";
        }
        if (userAgent.indexOf("Safari") > -1) {
            return "Safari";
        } //判断是否Safari浏览器
        if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
            var b_version = navigator.appVersion;
            var version = b_version.split(";");
            if (version[1]) {
                var trim_Version = version[1].replace(/[ ]/g, "");
                if (trim_Version == "MSIE6.0" || trim_Version == "MSIE7.0" || trim_Version == "MSIE8.0") {
                    return "IE";
                }
            }
        }
        ; //判断是否IE浏览器
    }

    var mb = myBrowser();
    if ("IE" == mb) {
        layer.confirm('系统检测到您当前的浏览器版本太低需要升级！否则部分功能将无法使用！请点击这里下载并安装Chrome浏览器！', {
            title: '温馨提示',
            btn: ['去下载'],
            btnAlign: 'c',
            closeBtn: 0
        }, function () {
            window.location.href = 'https://gj.dahe.cn/browser/Chrome.zip';
        });
    }

    var login2 = {
        init: function () {
            var self = this;
            self.verifyPhone();
            self.verifySms();
            self.getVerify();
            self.submit();
            $('#username').focus();
            self.canGetVerify = false;
            self.timer = null;
        },
        verifyPhone: function () { //验证手机号
            var self = this;
            $('#username').keyup(function () {
                var re = /^1[2|3|4|5|6|7|8|9]\d{9}$/;
                var _value = $(this).val();
                if (_value.length === 11) {
                    if (re.test(_value)) {
                        if (!self.timer) {
                            $('.get-verify').addClass('active');
                            self.canGetVerify = true;
                        }
                    } else {
                        layer.msg("注：手机号不合规范！", {
                            time: 3000
                        });
                        $('.get-verify').removeClass('active');
                        self.canGetVerify = false;
                    }
                } else if (_value.length > 11) {
                    layer.msg("您的手机号超过11位了", {
                        time: 1000
                    });
                    $(this).val(_value.substr(0, 11));
                    $('.get-verify').removeClass('active');
                    self.canGetVerify = false;
                } else {
                    $('.get-verify').removeClass('active');
                    self.canGetVerify = false;
                }
            })
        },
        verifySms: function () { //验证短信验证码
            var self = this;
            $('#password').keyup(function () {
                var _value = $(this).val();
                if (_value.length > 6) {
                    $(this).val(_value.substr(0, 6));
                }
            });
        },
        djs: function () { //验证码倒计时
            var time = 60,
                re = /^1[2|3|4|5|6|7|8|9]\d{9}$/,
                self = this;
            this.timer = setInterval(function () {
                time--;
                if (time === 0) {
                    clearInterval(self.timer);
                    self.timer = null;
                    if (re.test($('#username').val())) {
                        $('.get-verify').addClass('active');
                        self.canGetVerify = true;
                    }
                    $('.get-verify').html('重新获取');
                } else {
                    $('.get-verify').html(time + 's后重发');
                }
            }, 1000);
        },
        getVerify: function () { //获取验证码
            var re = /^1[2|3|4|5|6|7|8|9]\d{9}$/,
                self = this;
            $('.get-verify').click(function (e) {
                var _value = $('#username').val();
                if (re.test(_value)) {
                    if (!self.timer) {
                        $('.get-verify').addClass('active');
                    }
                } else {
                    layer.msg("注：手机号不合规范！", {
                        time: 3000
                    });
                    $('.get-verify').removeClass('active');
                }
                if (self.canGetVerify) {
                    $('.get-verify').removeClass('active');
                    self.djs();
                    self.canGetVerify = false;
                    $.post("/dahe/common/getsms", {
                        "phone": _value,
                        "type": 3
                    }, function (data) {
                        layer.msg(data.msg);
                    });
                }
                e.preventDefault();
            })
        },
        submit: function () { //确认提交验证
            var self = this,
                _login = function () {
                    var phoneNumber = $('#username').val(),
                        smsCode = $('#password').val(),
                        imageCode = $('#imageCode').val(),
                        service = $('input[name="service"]').val();

                    if (!phoneNumber || phoneNumber == '' || !/^1[3-9]\d{9}$/.test(phoneNumber)) {
                        layer.msg('手机号不能为空或格式不正确！', {
                            time: 2000
                        });
                        return;
                    }
                    if (!smsCode || smsCode == '') {
                        layer.msg('必须输入短信验证码！', {
                            time: 2000
                        });
                        return;
                    }
                    if (!imageCode || imageCode == '') {
                        layer.msg('必须输入图形验证码！', {
                            time: 2000
                        });
                        return;
                    }

                    // 显示加载状态
                    $("#loginBtn").text('登录中...').attr('disabled', true);

                    // Ajax提交到Login2Controller
                    $.ajax({
                        url: '/login2',
                        type: 'POST',
                        data: {
                            username: phoneNumber,
                            password: smsCode,
                            imageCode: imageCode
                        },
                        success: function(result) {
                            const isBusinessSuccess = result.success && result.obj !== false;
                            if (isBusinessSuccess === true) {
                                // 登录成功
                                layer.msg('登录成功，正在跳转...', {
                                    icon: 1,
                                    time: 1000
                                }, function() {
                                    // 跳转到service指定的地址或默认首页
                                    window.location.href = '/';
                                });
                            } else {
                                // 登录失败
                                var errorMsg = result.msg || '登录失败';
                                layer.msg(errorMsg, {
                                    icon: 2,
                                    time: 3000
                                });
                                // 清空短信验证码和图形验证码
                                $('#password').val('');
                                $('#imageCode').val('');
                                // 刷新图形验证码
                                $('#yzm_pic').attr('src', '/dahe/pub/getVerifyCodeImage?i=' + Math.random());
                                // 恢复按钮状态
                                $("#loginBtn").text('登　　录').attr('disabled', false);
                            }
                        },
                        error: function(xhr, status, error) {
                            var errorMsg = '网络错误，请稍后重试';
                            if (xhr.responseJSON && xhr.responseJSON.msg) {
                                errorMsg = xhr.responseJSON.msg;
                            }
                            layer.msg(errorMsg, {
                                icon: 2,
                                time: 3000
                            });
                            // 清空短信验证码和图形验证码
                            $('#password').val('');
                            $('#imageCode').val('');
                            // 刷新图形验证码
                            $('#yzm_pic').attr('src', '/dahe/pub/getVerifyCodeImage?i=' + Math.random());
                            // 恢复按钮状态
                            $("#loginBtn").text('登　　录').attr('disabled', false);
                        }
                    });
                };

            // 图形验证码点击刷新
            $('.changeImg').click(function () {
                $(this).attr('src', '/dahe/pub/getVerifyCodeImage?i=' + Math.random())
            });

            $(document).keydown(function (e) {
                if (e.keyCode === 13) {
                    _login()
                }
            });
            $('#loginBtn').click(function () {
                _login()
            })
        }
    }.init();

    if ($(".errors").text().length > 0) {
        layer.msg($(".errors").text(), {
            time: 2000
        });
    }
})
