
layui.use(['layer','element'], function () {
    var layer = layui.layer;
    var element = layui.element;
    window.processLoginResult = function (code,url) {
        if(code==0){
            //code为0代表登录成功，url为需要跳转的地址
            window.location.href = url;
        }else if(code==1){
            //code为1代表微信还未进行账号绑定，需要按正常流程登录后进行绑定
            getTabIndex(1);
            layer.msg('账号未绑定，请登陆后进行绑定！',{icon:5,time:2000},function(){
                $('#login_container').html('<div class="nobind">账号未绑定，请登陆后进行绑定！</div>');
            });
        }else {
            layer.msg('未知错误！',{icon:5,time:2000});
        }
    };

    function myBrowser() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1;
        if (isOpera) {
            return "Opera"
        }; //判断是否Opera浏览器
        if (userAgent.indexOf("Firefox") > -1) {
            return "FF";
        } //判断是否Firefox浏览器
        if (userAgent.indexOf("Chrome") > -1) {
            return "Chrome";
        }
        if (userAgent.indexOf("Safari") > -1) {
            return "Safari";
        } //判断是否Safari浏览器
        if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
            var b_version = navigator.appVersion;
            var version = b_version.split(";");
            if (version[1]) {
                var trim_Version = version[1].replace(/[ ]/g, "");
                if (trim_Version == "MSIE6.0" || trim_Version == "MSIE7.0" || trim_Version == "MSIE8.0") {
                    return "IE";
                }
            }

        }; //判断是否IE浏览器
    }

    var str = $('#mark');
    if (str.val() == 1) {
        layer.msg("您已在其他地方登陆，此处被下线", {
            time: 3000
        });
    }

    var mb = myBrowser();
    if ("IE" == mb) {
        layer.confirm('系统检测到您当前的浏览器版本太低需要升级！否则部分功能将无法使用！请点击这里下载并安装Chrome浏览器！', {
            title: '温馨提示',
            btn: ['去下载'],
            btnAlign: 'c',
            closeBtn: 0
        }, function () {
            window.location.href = 'https://gj.dahe.cn/browser/Chrome.zip';
        });
    }


    var interval;
    var login = {
        init: function () {
            var self = this;
            // self.getewm();
            self.verifyPhone();
            self.verifySms();
            self.getVerify();
            self.submit();
            $('#username')[0].focus();
            self.canGetVerify = false;
            self.timer = null;
        },
        // 获取二维码
        getewm: function () {
            var redirect_uri = "https://id.henan.gov.cn/login";
            var search = window.location.search.replace(/^\?/, '').split('&');
            search = search ? search : [];
            for (var i = 0; i < search.length; i++) {
                var query = search[i].split("=");
                //确保不报错
                if (!query || query.length != 2) {
                    continue;
                }
                if (query[0] == "service") {
                    redirect_uri = redirect_uri + "?service=" + query[1];
                }
            }
            redirect_uri = redirect_uri+"&jsCallback=processLoginResult";
            // var obj = new WxLogin({
            //     self_redirect: true,
            //     id: "login_container",
            //     appid: "wx319f7ab18f45e18a",
            //     scope: "snsapi_login",
            //     redirect_uri: encodeURIComponent(redirect_uri),
            //     state: "",
            //     style: "",
            //     href: "https://id.henan.gov.cn/resources/css/resetcode.css"
            // });
        },
        verifyPhone: function () { //验证手机号
            var self = this;
            $('#username').keyup(function () {
                var re = /^1[2|3|4|5|6|7|8|9]\d{9}$/;
                var _value = $(this).val();
                if (_value.length === 11) {
                    if (re.test(_value)) {
                        if (!self.timer) {
                            $('.get-verify').addClass('active');
                            self.canGetVerify = true;
                        }
                    } else {
                        layer.msg("注：手机号包含非法字符！", {
                            time: 1500
                        });
                        $('.get-verify').removeClass('active');
                        self.canGetVerify = false;
                    }
                } else if (_value.length > 11) {
                    layer.msg("您的手机号超过11位了", {
                        time: 1000
                    });
                    $(this).val(_value.substr(0,11));
                    $('.get-verify').removeClass('active');
                    self.canGetVerify = false;
                } else {
                    $('.get-verify').removeClass('active');
                    self.canGetVerify = false;
                }
            })
        },
        verifySms: function () { //验证手机号
            var self = this;
            $('.yzm-input input').keyup(function () {
                var _value = $(this).val();
                if (_value.length > 6) {
                    $(this).val(_value.substr(0,6));
                }
            });
        },
        djs: function () { //验证码倒计时
            var time = 60,
                re = /^1[2|3|4|5|6|7|8|9]\d{9}$/,
                self = this;
            this.timer = setInterval(function () {
                time--;
                if (time === 0) {
                    clearInterval(self.timer);
                    self.timer = null;
                    if (re.test($('#username').val())) {
                        $('.get-verify').addClass('active');
                        self.canGetVerify = true;
                    }
                    $('.get-verify').html('重新获取');
                } else {
                    $('.get-verify').html(time + 's后重发');
                }
            }, 1000);

        },
        getVerify: function () { //获取验证码
            var re = /^1[2|3|4|5|6|7|8|9]\d{9}$/,
                self = this;
            $('.get-verify').click(function (e) {
                var _value = $('#username').val();
                if (re.test(_value)) {
                    if (!self.timer) {
                        $('.get-verify').addClass('active');
                    }
                } else {
                    layer.msg("注：手机号不合规范！", {
                        time: 3000
                    });
                    $('.get-verify').removeClass('active');
                }
                if (self.canGetVerify) {
                    $('.get-verify').removeClass('active');
                    self.djs();
                    self.canGetVerify = false;
                    $.post("/dahe/common/getsms", {
                        "phone": _value,
                        "type": 3
                    }, function (data) {
                        layer.msg(data.msg);
                    });
                }
                e.preventDefault();
            })
        },
        submit: function (phoneNumber, verifyCode) { //确认提交验证
            var self = this,
                _login = function () {
                    var phoneNumber = $('#username').val(),
                        verifyCode = $('.verify input').val();
                    if (!phoneNumber || phoneNumber == '') {
                        $("#loginBtn").attr("type", "button");
                        layer.msg('手机号不能为空！', {
                            time: 1000
                        });
                        return;
                    }
                    if (!verifyCode || verifyCode == '') {
                        $("#loginBtn").attr("type", "button");
                        layer.msg('必须输入验证码！', {
                            time: 1000
                        });
                        return;
                    }
                    $.post("/dahe/login/matchPhone", {
                        "phone": $('#username').val(),
                        "smsCode": $('.verify input').val(),
                        "imgCode": $('.imageCode').val()
                    }, function (data) {
                        document.getElementById("yzm_pic").src="/dahe/pub/getVerifyCodeImage?"+new Date().getMilliseconds();
                        if (data.status === 1) {
                            window.location.href=data.obj;
                            return;
                        }
                        layer.msg(data.msg);
                    });
                };
            $('.changeImg').click(function () {
                $(this).attr('src', '/dahe/pub/getVerifyCodeImage')
            });
            $(document).keydown(function (e) {
                if (e.keyCode === 13) {
                    _login()
                }
            });
            $('#loginBtn').click(function () {
                _login();
            })
        }
    }.init();

})