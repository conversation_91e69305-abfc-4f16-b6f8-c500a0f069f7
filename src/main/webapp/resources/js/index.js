// 应用页切换
$(".appArea").slide({
    mainCell: ".appPage",
    effect: "left",
    autoPlay: false,
    delayTime: 600,
    trigger: "click"
});
var operation = {
    // 头像裁切的变量
    txdsumary:'',
    jcrop_api:'',
    // table切换
    tableSwitch: function (elem, _this) {
        $(elem).show().siblings(".rightBox").hide();
        $(_this).addClass("on").siblings("li").removeClass("on");
    },
    //我的消息
    myMessge: function () {
        $.ajax({
            url: '/home/<USER>',
            type: 'post',
            data: {
                pageNumber: 1,
                pageSize: 5
            },
            success: function (data) {
                var messagehtml = '';
                if (data.status == 1) {
                    var list=data.obj.obj.datas;
                    if(list.length > 0){
                        $.each(list,function(index, item){
                            var allmessage=$("#allmessage").val();
                            messagehtml += '<li class="cl msg_t"><a href="'+allmessage+'" target="_blank" class="newsTitle fl">'+item.title+'</a>' +
                                '<span class="date">'+item.createTime+'</span></li>'
                        });
                    }else {
                        return "暂无数据！！！"
                    }
                }
                $('.myMsg .newsList').html(messagehtml);
            }
        })
    },
    // 最新稿件
    mynews: function () {
        $.ajax({
            url: '/home/<USER>',
            type: 'post',
            data: {
                length: 5
            },
            success: function (data) {
                // console.log(data);
                var newshtml = '';
                if (data.status == 1) {
                    var list=data.obj.obj;
                    if(list.length > 0) {
                        $.each(list, function (index, item) {
                            //去掉标题中的br标签
                            var title=item.title;
                            title=title.replace('<br>','');
                            newshtml += '<li class="cl"><a href="'+item.selfUrl+'" target="_blank" class="newsTitle fl">'+title+'</a>' +
                                '<span class="date fl">'+item.createDate+'</span></li>';
                        });
                    }else {
                        return "暂无数据！！！"
                    }
                }
                $('.newPaper .newsList').html(newshtml);
            }
        })
    },
    //天气
    weather:function(){
      var self = this;
        $.ajax({
            type:"get",
            url:"/dahe/cy/weather",
            success:function(data){
                if(data.status == 1){
                    var object =data.obj.result;
                    self.initWeather(object);
                    // console.log(object);
                }else{
                    // layer.msg(data.msg);
                }
            }
        });
    },
    initWeather:function (data) {
      var weather = $('.weather');
      weather.find('.city').html(data.today.city);
      weather.find('.weatherimg').html('<img src="/resources/img/weathercn/' + data.today.weather_id.fb + '.png">');
      weather.find('.temperature').html(data.today.temperature);
      weather.find('.winddirect').html(data.sk.wind_direction);
      weather.find('.windpower').html(data.sk.wind_strength);
      weather.find('.windspeed').html(data.today.weather);
    },
    // 头像裁切
    editicon: function () {
        var imgsrc = $('.editicon img').attr('src');
        layer.open({
            type: 1,
            title: '修改头像',
            area: '250px',
            content: '<div id="imgBox"><div class="editimg"><img src="'+imgsrc+'" class="userIcon"></div><div class="btns"><button type="button" class="layui-btn layui-btn-sm mr-10" id="uploadtx">上传图片</button><button type="button" class="sure layui-btn layui-btn-sm layui-btn-normal" disabled onclick="operation.saveIcon()">确定裁剪</button></div></div>'
        });
        layui.use(['upload'], function () {
            var upload = layui.upload;
            //执行实例
            upload.render({
                elem: '#uploadtx',
                field: 'attach',
                url: '/upload/upload-img',
                done: function (res) {
                    if (res.success) {
                        $(".editimg").html('<img src="'+res.obj+'" id="newIcon">');
                        $('.sure').attr('disabled',false)
                        // 裁切头像
                        $('#newIcon').Jcrop({
                            allowSelect:false,
                            minSize:[100,100],
                            onSelect: operation.showCoords
                        }, function () {
                            operation.jcrop_api = this;
                            operation.jcrop_api.setSelect([0, 0, 100, 100]);
                        });
                    } else {
                        layer.msg(res.msg);
                    }
                },
                error: function (err) {
                    console.log(err)
                }
            });

        })

    },
    // 裁切图片的六个点
    showCoords:function () {
        operation.txdsumary = operation.jcrop_api.tellSelect()
        // console.log(operation.txdsumary)
    },
    //确定裁切头像
    saveIcon:function () {
        $.ajax({
            url: '/upload/img-cut',
            type: 'post',
            data: {
                // x轴
                x: operation.txdsumary.x,
                // y轴
                y: operation.txdsumary.y,
                // 截图框宽
                // width: operation.txdsumary.x2,
                width:$('.editimg').width(),
                // 截图宽
                cutWidth:operation.txdsumary.w,
                // 截图框高
                // height: operation.txdsumary.y2,
                height: $('.editimg').height(),
                // 截图高
                cutHeight:operation.txdsumary.h,
                // 图片路径
                imgePath: $("#newIcon").attr('src')
            },
            success: function (data) {
                layer.msg('成功', {
                    time: 1000
                }, function (index) {
                    $.ajax({
                        url:'/home/<USER>',
                        type:'post',
                        data:{
                            uid:$('#uid').val(),
                            icon:data.obj
                        },
                        success:function (res) {
                            // window.location.reload()
                            layer.closeAll();
                            $('.editicon img').attr('src',data.obj);
                            $('#imgBox .editimg').html('<img src="'+ data.obj +'" class="userIcon">')
                        }
                    })
                });
            }
        })
    },
    //时间
    nowTime:function () {
        var d = new Date();
        var week;
        switch (d.getDay()){
            case 1: week="星期一"; break;
            case 2: week="星期二"; break;
            case 3: week="星期三"; break;
            case 4: week="星期四"; break;
            case 5: week="星期五"; break;
            case 6: week="星期六"; break;
            default: week="星期天";
        }
        var str = d.getFullYear()+"年"+(d.getMonth()+1)+"月"+d.getDate()+"日"+" "+week;
        $('.nowtime').html(str);
    },
    //时间戳转换成时间格式
    format:function(time, format) {
        if(typeof(time) === "string") {
            time = time.replace(/\-/g, "/");
        } // 鍏煎Safari
        time = new Date(time);
        var year = time.getFullYear(),
            month = time.getMonth() + 1,
            day = time.getDate(),
            hour = time.getHours(),
            minute = time.getMinutes(),
            second = time.getSeconds();
        month = month > 9 ? month : "0" + month;
        day = day > 9 ? day : "0" + day;
        hour = hour > 9 ? hour : "0" + hour;
        minute = minute > 9 ? minute : "0" + minute;
        second = second > 9 ? second : "0" + second;
        time =
            year +
            "-" +
            month +
            "-" +
            day +
            " " +
            hour +
            ":" +
            minute +
            ":" +
            second;
        switch (format) {
            case "ymd":
                time = time.split(" ")[0];
                break;
            case "md":
                time = time.split(" ")[0].slice(5, 10);
                break;
            case "hms":
                time = time.split(" ")[1];
                break;
            case "hm":
                time = time.split(" ")[1].slice(0, 5);
                break;
            default:
                time = time;
        }
        return time;
    }
}
operation.nowTime();
operation.myMessge();
operation.mynews();
operation.weather();
// 提交
layui.use(['form', 'layer'], function () {
    var form = layui.form,
        layer = layui.layer;
    // 监听提交基本信息
    form.on('submit(submit)', function (data) {
        event.preventDefault();
        var loading = layer.load(1, {
            shade: [0.1, '#fff'] //0.1透明度的白色背景
        });
        $.ajax({
            type: 'POST',
            url: '/home/<USER>',
            data: data.field,
            success: function (data) {
                layer.close(loading);
                if (data.status == 1) {
                    $("#btn_sub").attr("disabled", true);
                    layer.msg(data.msg, {
                        time: 1000
                    }, function () {
                        // 成功之后的操作
                        $.ajax({
                            type: 'POST',
                            url: '/home/<USER>',
                            success: function (data) {
                                $(".userName").html(data.obj.truename);
                            }
                        });
                    });
                } else {
                    layer.msg(data.msg, {
                        time: 1000
                    }, function () {

                    });
                }
                return false;
            }
        })
    })
    // 修改手机号
    // 监听提交
    form.on('submit(submitPhone)', function (data) {
        event.preventDefault();
        var loading = layer.load(1, {
            shade: [0.1, '#fff'] //0.1透明度的白色背景
        });
        $.ajax({
            type: 'POST',
            url: '/home/<USER>',
            data: data.field,
            success: function (data) {
                layer.close(loading);
                if (data.status == 1) {
                    $("#btn_sub").attr("disabled", true);
                    layer.msg(data.msg, {
                        time: 1000
                    }, function () {
                        $.ajax({
                            type: 'POST',
                            url: '/home/<USER>',
                            success: function (data) {
                                $("#oldPhone").val(data.obj.phone);
                                $("input[name='phone']").val("")
                                $("input[name='code']").val("")
                            }
                        });
                        /*var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                         parent.layer.close(index);*/
                    });
                } else {
                    layer.msg(data.msg, {
                        time: 1000
                    }, function () {

                    });
                }
                return false;
            }
        })
    })
})
var timer;
// $('.getyzm').attr("disabled", true);
// $('#newphone').keyup(function () {
//     var re = /^1\d{10}$/;
//
//     var _value = $(this).val();
//     if (_value.length === 11) {
//
//         if (re.test(_value)) {
//             if (!timer) {
//                 $('.getyzm').attr("disabled", false);
//             }
//         } else {
//             layer.msg("注：手机号格式不正确！", {
//                 time: 1500
//             });
//             $('.getyzm').attr("disabled", true);
//         }
//
//     } else if (_value.length > 11) {
//         layer.msg("您的手机号超过11位了", {
//             time: 1000
//         });
//         $('.getyzm').attr("disabled", true);
//     } else {
//         $('.getyzm').attr("disabled", true);
//     }
//
// });

// 阻止默认行为
function preventDefa(e) {
    if (window.event) {
        //IE中阻止函数器默认动作的方式
        window.event.returnValue = false;
    } else {
        //阻止默认浏览器动作(W3C)
        e.preventDefault();
    }
}

// 验证码倒计时
$('.getyzm').click(function () { //验证码倒计时
    preventDefa();
    //验证手机号
    var re = /^1\d{10}$/;
    var _value = $('#newphone').val();
    if (_value.length !== 11) {
        layer.msg("请检查手机号是否正确！", {
                time: 1500
            });
        return false
    }else {
        if (re.test(_value)) {
            $('.getyzm').attr("disabled", true);
            var time = 60,
                re = /^1[3|5|7|8]\d{9}$/;
            $.ajax({
                type: 'POST',
                url: '/dahe/common/getsms',
                data: {
                    phone: $('#newphone').val(),
                    type: 4
                },
                success: function (data) {
                    if(data.success){
                        timer = setInterval(function () {
                            time--;
                            if (time === 0) {
                                clearInterval(timer);
                                timer = null;
                                $('.getyzm').attr("disabled", false);
                                $('.getyzm').html('重新获取');

                            } else {
                                $('.getyzm').html(time + 's后重发');
                            }
                        }, 1000);
                    }else {
                        $('.getyzm').attr("disabled", false);
                    }
                    layer.msg(data.msg, {
                        time: 1500
                    });
                }
            })
        } else {
            $('.getyzm').attr("disabled", false);
            layer.msg("手机号格式不正确！", {
                time: 1500
            });
        }
    }
});
