
		function my_view(question_id) {
			$('#view').dialog({
				title : '查看',
				width : 750,
				height : 450,
				closed : false,
				minimizable : true,
				maximizable : true,
				cache : false,
				href : '<%=request.getContextPath()%>/consult/view/' + question_id,
				modal : true
			});
		}

		function my_replay(consult_id) {
			$('#view').panel({
				'href' : '<%=request.getContextPath()%>/consult/replay/' + consult_id
			});
			// $('#view').dialog('refresh','<%=request.getContextPath()%>/consult/replay/'+consult_id);
		}

		function my_send_replay(consult_id) {
			var id = consult_id;
			var content = $('#content').val();

			if (content == '') {
				alert("请输入内容");
				return;
			}
			// alert(username);
			$.post("<%=request.getContextPath()%>/consult/replay", {
				'id' : id,
				'content' : content
			}, function(data) {
				// $('#view').panel({'content':'sdagsdfgfsd'});

				// alert(data);
				// if (data.success == false) {
				alert(data.msg);
				$('#view').panel('close');
				$('#deadlinkList').datagrid('reload');
				// } else {
				// $("#issend").attr("value", "1");
				// alert(data.msg);
				// }
			});

		}