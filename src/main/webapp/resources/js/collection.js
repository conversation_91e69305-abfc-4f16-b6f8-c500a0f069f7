$(function(){
	$(".share_box").mouseover(function(){
		$(this).children("#bdshare").css("display","block");
		//$(this).children("#bdsharebuttonbox").css("display","block");
		});
	$(".share_box").mouseleave(function(){
		$(this).children("#bdshare").css("display","none");
		//$(this).children("#bdsharebuttonbox").css("display","none");
	});
	var docuWidth;
	var docuHeight;
	function view() {
		documentWidth = document.documentElement.clientWidth;
		documentHeight = document.documentElement.clientHeight;
		document.body.style.height = documentHeight + "px";
		document.body.style.width = documentWidth + "px";
	}
	view();
	var w,h;
	if (documentWidth < 700) {
		w = '100%';
		h = '100%';
	}else{
		w = '600px';
		h = '450px';
	}
	$(".undatepwd").click(function(){
		layer.open({
			title:"修改密码",
		  	type: 2,
		  	area: [w, h],
		  	fix: false, //不固定
		  	maxmin: true,
		  	content: "/home/<USER>",
		});
	});
	$(".bindphone").click(function(){
		layer.open({
			title:"绑定手机号",
		  	type: 2,
		  	area: [w, h],
		  	fix: false, //不固定
		  	maxmin: true,
		  	content:'/home/<USER>',
		});
	});
		
		
	//获取当前日期
	var mydate = new Date();
	var month = (mydate.getMonth()+1);
	var day = mydate.getDate();
	var week=["星期日","星期一","星期二","星期三","星期四","星期五","星期六"];
	$("#wMonth").html(month+"月");
	$("#wDate").html(day+"日 ");
	$("#wDay").html(week[mydate.getDay()]);
	
	//请求天气数据
	$.ajax({
		type:"get",
		url:"/dahe/cy/weather",
		success:function(data){
			data = data.obj;
			$("#Temp").html(data.temp);
			$("#cityName").html(data.city);
			$("#Weather").html(data.weather);
			$("#WindDirection").html(data.windDiec);
			$("#WindLevel").html(data.windLevel);
		}
	});


 //

});
function layer_close(){
	var index = parent.layer.getFrameIndex(window.name);
	parent.layer.close(index);
}

//退出登录
function login_out(){
   	//delCookie('dahepp_un');
   	location.href='/home/<USER>';
}