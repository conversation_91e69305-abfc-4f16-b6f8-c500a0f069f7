    //写入cookie
		function SetCookie(name,value)
		{
			var Days = 10; //此 cookie 将被保存 10 天
			var exp = new Date();
			exp.setTime(exp.getTime() + Days*24*60*60*1000);
			//
			document.cookie = name + "="+ escape (value) + ";domain=dahe.cn;expires=" + exp.toGMTString()+";path=/";
		}
		///删除cookie
		function delCookie(name)
		{
			var exp = new Date();
			exp.setTime(exp.getTime() - 100000);
			var cval=getCookie(name);
			//alert(exp.toGMTString());
			if(cval!=null) document.cookie= name + "="+cval+";domain=dahe.cn;expires="+exp.toGMTString()+";path=/";
		}
		//读取cookie
		function getCookie(name)
		{
			var arr = document.cookie.match(new RegExp("(^| )"+name+"=([^;]*)(;|$)"));
			if(arr != null)
			return unescape(arr[2]);
			return null;
		}
	      