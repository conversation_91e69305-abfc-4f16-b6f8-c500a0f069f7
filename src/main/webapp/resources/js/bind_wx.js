layui.use(['layer'],function () {
  var layer = layui.layer;

    ;function myBrowser() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1;
        if (isOpera) {
            return "Opera"
        }
        ; //判断是否Opera浏览器
        if (userAgent.indexOf("Firefox") > -1) {
            return "FF";
        } //判断是否Firefox浏览器
        if (userAgent.indexOf("Chrome") > -1) {
            return "Chrome";
        }
        if (userAgent.indexOf("Safari") > -1) {
            return "Safari";
        } //判断是否Safari浏览器
        if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
            var b_version = navigator.appVersion;
            var version = b_version.split(";");
            if (version[1]) {
                var trim_Version = version[1].replace(/[ ]/g, "");
                if (trim_Version == "MSIE6.0" || trim_Version == "MSIE7.0" || trim_Version == "MSIE8.0") {
                    return "IE";
                }
            }

        }
        ; //判断是否IE浏览器
    }

    var str = $('#mark');
    if (str.val() == 1) {
        layer.msg("您已在其他地方登陆，此处被下线", {time: 3000});
    }

    var mb = myBrowser();
    if ("IE" == mb) {
        layer.confirm('系统检测到您当前的浏览器版本太低需要升级！否则部分功能将无法使用！请点击这里下载并安装Chrome浏览器！', {
            title: '温馨提示',
            btn: ['去下载'],
            btnAlign: 'c',
            closeBtn: 0
        }, function () {
            window.location.href = 'https://gj.dahe.cn/browser/Chrome.zip';
        });
    }


    var interval;
    var uuid = $("#uuid").text();

    var login = {
        init: function () {
            var self = this;

            this.initCode();
            this.verifyPhone();
            setTimeout(function () {
                self.initPhoneNum();
            },0);

            self.getVerify();
            this.remember();
            this.submit();
            this.phoneNum = null;
            this.ifRemember = true;
            this.canGetVerify = false;
            this.timer = null;

            $('.slideToQR').hover(function () {
                $('.QRnotice').fadeIn(100);
            }, function () {
                $('.QRnotice').fadeOut(100);
            })
        },
        initPhoneNum: function () {//初始化手机号
            var _user = window.localStorage.user,
                re = /^1[2|3|4|5|6|7|8|9]\d{9}$/;

            $('.phone input')[0].focus();

            if (_user) {
                $('.phone input').val(decodeURI(_user));

                if (re.test($('.phone input').val())) {
                    $('.get-verify').addClass('active');
                    this.canGetVerify = true;
                }

            }

        },
        initCode: function () {

            $('.QRcode > .ewm').attr('src', '/codeimg?uuid=' + uuid);
        },
        verifyPhone: function () {//验证手机号
            var self = this;

            $('.phone input').keyup(function () {
                var re = /^1[2|3|4|5|6|7|8|9]\d{9}$/;

                var _value = $(this).val();
                if (_value.length === 11) {

                    if (re.test(_value)) {

                        if(!self.timer){
                            $('.get-verify').addClass('active');
                            self.canGetVerify = true;
                        }

                    } else {
                        layer.msg("注：手机号包含非法字符！", {time: 1500});
                        $('.get-verify').removeClass('active');
                        self.canGetVerify = false;
                    }

                } else if (_value.length > 11) {
                    layer.msg("您的手机号超过11位了", {time: 1000});
                    $('.get-verify').removeClass('active');
                    self.canGetVerify = false;
                } else {
                    $('.get-verify').removeClass('active');
                    self.canGetVerify = false;
                }

            })
        },
        djs: function () {//验证码倒计时
            var time = 60,
                re = /^1[2|3|4|5|6|7|8|9]\d{9}$/,
                self = this;
            this.timer = setInterval(function () {
                time--;

                if (time === 0) {
                    clearInterval(self.timer);
                    self.timer = null;

                    if (re.test($('.phone input').val())) {
                        $('.get-verify').addClass('active');
                        self.canGetVerify = true;
                    }

                    $('.get-verify').html('重新获取');

                } else {
                    $('.get-verify').html(time + 's后重发');
                }
            }, 1000);

        },
        getVerify: function () {//获取验证码
            var re = /^1[2|3|4|5|6|7|8|9]\d{9}$/,
                self = this;

            $('.get-verify').click(function (e) {
                var _value = $('.phone input').val();


                if (self.canGetVerify) {
                    $('.get-verify').removeClass('active');
                    self.djs();
                    self.canGetVerify = false;
                    $.post("/dahe/common/getsms", {"phone": _value, "type":5}, function (data) {
                        layer.msg(data.msg);
                    });
                }
                e.preventDefault();
            })
        },
        storage: function (val) {//记住用户信息
            window.localStorage.user = encodeURI(val);
        },
        remember: function () {//是否记住用户
            var on = true,
                self = this;

            $('.remember').click(function () {
                if (self.ifRemember) {
                    $(this).find('.yes').removeClass('active');
                } else {
                    $(this).find('.yes').addClass('active');
                }
                self.ifRemember = !self.ifRemember;
            })
        },
        submit: function (phoneNumber, verifyCode) {//确认提交验证
            var self = this,
                _login = function () {
                    var phoneNumber = $('.phone input').val(),
                        verifyCode = $('.verify input').val();
                    if (!phoneNumber || phoneNumber=='') {
                        $("#loginBtn").attr("type","button");
                        layer.msg('手机号不能为空！', {time: 1000});
                        return;
                    }
                    if ( !verifyCode || verifyCode=='') {
                        $("#loginBtn").attr("type","button");
                        layer.msg('必须输入验证码！', {time: 1000});
                        return;
                    }
                    $("#loginBtn").attr("type","submit");
                };
            $('.changeImg').click(function () {
                $(this).attr('src','/dahe/pub/getVerifyCodeImage?i=' + Math.random())
            });
            $(document).keydown(function (e) {

                if (e.keyCode === 13) {
                    _login()
                }
            });
            $('#loginBtn').click(function () {
                _login()
            })
        }
    }.init();

})