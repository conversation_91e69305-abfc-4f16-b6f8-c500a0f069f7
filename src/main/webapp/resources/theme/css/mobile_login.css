html,
body {
	overflow-x: hidden;
}
.panel{
	background:#ffffff;
	height: 390px;
}
#login{
	margin-top: 50px;
	margin-right: 35px;
}
.mobile_show{
	display: none;
}
.navbar-head{
	margin-top: 10px;
}
.mobile_x5_fl{
	width: 50% !important;
	float: left;
}
.navbar-head a{
	display: block;
    width: 203px;
    height: 38px;
}
.form-x .form-button{
	margin-left: 0px !important;
}
.mobile_img{
	margin: auto;
	width: 100%;
	display: block !important;
}
@media screen and (max-width:700px) {
	h1{
		font-size: 16px !important;
	}
	.panel{
		height:auto !important;
	}
	.navbar-head{
		margin-top: 0px;
	}
	.mobile_head_blank{
		padding: 0px !important;
		margin: 0px !important;
	}
	.mobile_layout{
		height: auto !important;
    	background-size: 100% 200px !important;
	}
	.margin-large{
		margin: 0px !important;
	}
	.mobile_hiden{
		display: none !important;
	}
	.mobile_show {
		display: block;
	}
	.mobile_panel{
		height:auto !important;
	}
	.mobile_box{
		margin-top: 20px !important;
		margin-right: 0px !important;
	}
	.layui-input-block{
		margin-left:0px !important;
		width: 200px !important;
    	float: left;
	}
	.mobile_btn{
		float: none;
		margin: auto !important;
	}
	.mobile_msg_btn{
		margin-top: 0px !important;
		font-size: 13px !important;
    	line-height: 24px !important;
    	padding: 5px 10px !important;
    	height: 40px !important;
	}
	.mobile_fr{
		float: right;
		display: block;
	}
	.mobile_fl{
		float: left;
		display: block;
	}
	.mobile_foot{
		/*position: absolute;
	    bottom: 0px;*/
	    margin-bottom: 10px;
	}
	.mobile_nav{
		position: absolute;
		z-index: 999;
		background: rgba(0, 0, 0, 0) !important;
		border: none !important;	
	}
	.mobile_nav_ul{
		background-color:white;
		border: solid 1px rgba(0, 0, 0, .2) !important;
	}
	.mobile_lable{
		height: 40px !important;
	    line-height: 40px !important;
	    padding-bottom: 10px !important;
	    font-size: 13px !important;
	    text-align: right !important;
	}
	.mobile_input{
		padding: 10px !important;
	    font-size: 13px !important;
	    line-height: 0px !important;
	    height: 40px !important;
	}
	.mobile_mr_20{
		margin-right: 20px !important;
	}
	.mobile_mt_20{
		margin-top: .
		
		;
	}
	.mobile_100{
		width: 100% !important;
	}
	.mobile_x4{
		width : 33.33333333% !important;
	}
	.mobile_x3{
		width : 25% !important;
	}
	.mobile_x8{
		width: 66.66666667% !important;
	}
	.zhucexuzhi{
		margin-top: 10px;
    	text-align: center;
	}
	.zhucexuzhi a{
		display: block;
    	width: 100px;
    	margin: auto !important;
	}
	.mobile_img{
		width: 100px;
	}
}
@media screen and (max-width:360px) {
	.layui-input-block{
		width: 170px !important;
	}
}}
}