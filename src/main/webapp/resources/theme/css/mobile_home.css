html,
body {
	overflow-x: hidden;
}
.navbar-body{
	float: left;
	width: auto;
}
#siteSearch .btn1{
	line-height: 35px;
    height: 35px;
}
.navbar-head a{
	display: block;
    width: 203px;
    height: 38px;
}
.navbtn{
	margin-top: 5px;
	margin-left: 5px;
	padding: 4px 12px;
    font-size: 16px;
    float: right;
    display: none;
}
.mobile_show_line{
	display: none !important;
}
.mobile_show{
	display: none !important;
}


@media screen and (max-width:700px) {
	
	h1{
		font-size: 16px;
	}
	.navbar-body{
		float: right;
    	width: inherit;
	}
	.navbar-body .nav li{
		border: none;
	}
	.mobile_search{
		margin: 10px auto;
		float: none !important;
	}
	.mobile_nav{
		position: absolute;
		z-index: 999;
		background: rgba(0, 0, 0, 0) !important;
		border: none !important;	
	}
	.mobile_nav_ul{
		background-color:white;
		border: solid 1px rgba(0, 0, 0, .2) !important;
	}
	.mobile_hiden{
		display: none !important;
	}
	.mobile_show {
		display: block !important;
	}
	.mobile_show_line{
		display: inline-block;
	}
	.pt-15{
		padding-top: 5px;
	}
	.pb-10{
		padding-bottom: 5px;
	}
	.mobile_gear_nav{
		position: fixed;
	    background-color: white;
	    right: 5px;
	    top: 55px;
	    width: 60px;
	    z-index: 999;
	    padding: initial !important;
	}
	.mobile_gear_nav a{
		cursor: pointer;
	    line-height: 30px;
	    height: 30px;
		border-radius: 0px !important; 
	    margin:auto !important; 
	    font-size: 13px !important;
	    border-color: initial !important;
	    overflow: hidden;
	}
	.yh_center{
		margin-right: 0px;
	}
	.yh_center img{
		display: block;
    	margin: auto;
    	width: 60px;
	    height: 60px;
	    border-radius: 60px;
	}
	.yh_center span{
    	margin: auto;
    	font-size: 16px;
	}
	.weather_mod{
		padding-top: 0px !important;
	}
	.mobile_80{
		width: 80%;
	}
	.mobile_50{
		width: 50%;
	}
	.mobile_100{
		width: 100% !important;
	}
	.list-info{
		height: initial;
		min-height: 130px;
	}
	#footer{
		padding: 20px 0;
	    background-color: #333333;
	    color: #fff;
	    text-align: center;
	    font-size: 14px;
	    margin-top: 10px;
	    display: none;
	}
	#footer p{
		margin-bottom: 0px;
	}
	.weather_mod .fl{
		float: none;
		margin: auto;
		text-align:center;
	}
	.yanyu_luntan div,
	.yanyu_luntan img{
		width: 60px !important;
    	height: 60px !important;
    	margin: 0px !important;
    }
    .yanyu_luntan div{
    	margin-left: 10px !important;
    }
    .yanyu_luntan p{
    	font-size: 12px;
    	line-height: 12px;
    	height: 12px;
    }
    .yanyu,
    .luntan{
    	margin: 0px !important;
    }
    .daluntan_img img{
    	width: 60px !important;
   	 	height: 60px !important;
    }
    .f-18{
    	font-size: 16px;
    }
}

@media screen and (max-width:400px){
	#myForm input
	{
		width: 200px;
	}
	#myForm input[type="submit"], #myForm input[type="button"]{
		width: 200px;
	}
	#phone,
	#myForm button{
		width: 100px !important;
		margin: 0px !important;
	}
}