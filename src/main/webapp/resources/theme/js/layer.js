$('#closelayer').on('click', function(){
	var index = parent.layer.getFrameIndex(window.name);
    parent.layer.msg('关闭成功！' , 1);
    parent.layer.close(index);
});

function layers(classid,id,burl,w,h){
var url=burl + "?classid=" + classid + "&id=" + id;
layer.open({
    type: 2,
    title: false,
    shadeClose: true,
    shade: 0.8,
    area:  [w,h],
    content: url //iframe的url
}); 
}

function layerqr(burl,w,h){
var url="http://qr.liantu.com/api.php?text=http://m.dahebaba.com" + burl;
layer.open({
    type: 2,
    title: false,
    shadeClose: true,
    shade: 0.8,
    area:  [w,h],
    content: url //iframe的url
}); 
}

function layermsg(str,time,icon){
layer.msg(str, time, icon);
}

function iframe(url,w,h){
	if(!arguments[2]) w = "800px";
	if(!arguments[3]) h = "460px";
var url=url;
    layer.open({
    type: 2,
    title: false,
    shadeClose: true,
    shade: 0.8,
    area:  [w,h],
    content: url //iframe的url
}); 
}


$('#signin-loader').on('click', function(){
    $.layer({
        type: 2,
        title: false,
        maxmin: false,
        shadeClose: true, //开启点击遮罩关闭层
        area : ['800px' , '460px'],
        offset : ['100px', ''],
        iframe: {src: '/e/member/login/'},
        close: function(index){
        location.reload(); //自动关闭后可做一些刷新页面等操作
    }
    });
})
$('#signup-loader').on('click', function(){
    $.layer({
        type: 2,
        title: false,
        maxmin: false,
        shadeClose: true, //开启点击遮罩关闭层
        area : ['800px' , '460px'],
        offset : ['100px', ''],
        iframe: {src: '/e/member/register/'},
    });
})

$('#loginout').on('click', function(){
$.layer({
    shade: [0],
    area: ['auto','auto'],
    dialog: {
        msg: '您是否要退出登录呢？',
        btns: 2,                    
        type: 4,
        btn: ['确定','取消'],
        yes: function(){
    $.layer({
        type: 2,
        title: false,
        maxmin: false,
        shadeClose: true, //开启点击遮罩关闭层
        area : ['800px' , '460px'],
        offset : ['100px', ''],
        iframe: {src: '/e/member/doaction.php?enews=exit&ecmsfrom=9'},
        time:2
    });
        layer.msg('退出成功', 2, function(){
    location.reload(); //自动关闭后可做一些刷新页面等操作
});
        }, no: function(){
            layer.msg('取消成功', 1, 13);
        }
    }

});
})
$('#loginoutpl').on('click', function(){
$.layer({
    shade: [0],
    area: ['auto','auto'],
    dialog: {
        msg: '您是否要退出登录呢？',
        btns: 2,                    
        type: 4,
        btn: ['确定','取消'],
        yes: function(){
    $.layer({
        type: 2,
        title: false,
        maxmin: false,
        shadeClose: true, //开启点击遮罩关闭层
        area : ['800px' , '460px'],
        offset : ['100px', ''],
        iframe: {src: '/e/member/doaction.php?enews=exit&ecmsfrom=9'},
        time:2
    });
        layer.msg('退出成功', 2, function(){
    location.reload(); //自动关闭后可做一些刷新页面等操作
});
        }, no: function(){
            layer.msg('取消成功', 1, 13);
        }
    }

});
})