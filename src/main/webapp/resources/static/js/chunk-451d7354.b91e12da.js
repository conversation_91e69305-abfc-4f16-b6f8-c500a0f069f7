(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-451d7354"],{"0bac":function(v,_,i){},"101f":function(v,_,i){"use strict";i.r(_);var d=function(){var v=this,_=v.$createElement,i=v._self._c||_;return i("div",{staticClass:"components-container"},[i("code",[v._v("页面滚动到指定位置会在右下角出现返回顶部按钮")]),v._v(" "),i("code",[v._v("可自定义按钮的样式、show/hide临界点、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素 ")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("div",[v._v("我是占位")]),v._v(" "),i("el-tooltip",{attrs:{placement:"top",content:"文字提示"}},[i("back-to-top",{attrs:{transitionName:"fade",customStyle:v.myBackToTopStyle,visibilityHeight:300,backPosition:50}})],1)],1)},t=[],e=function(){var v=this,_=v.$createElement,i=v._self._c||_;return i("transition",{attrs:{name:v.transitionName}},[i("div",{directives:[{name:"show",rawName:"v-show",value:v.visible,expression:"visible"}],staticClass:"back-to-ceiling",style:v.customStyle,on:{click:v.backToTop}},[i("svg",{staticClass:"Icon Icon--backToTopArrow",staticStyle:{height:"16px",width:"16px"},attrs:{width:"16",height:"16",viewBox:"0 0 17 17",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true"}},[i("title",[v._v("回到顶部")]),v._v(" "),i("g",[i("path",{attrs:{d:"M12.036 15.59c0 .55-.453.995-.997.995H5.032c-.55 0-.997-.445-.997-.996V8.584H1.03c-1.1 0-1.36-.633-.578-1.416L7.33.29c.39-.39 1.026-.385 1.412 0l6.878 6.88c.782.78.523 1.415-.58 1.415h-3.004v7.004z","fill-rule":"evenodd"}})])])])])},n=[],o=(i("c5f6"),{name:"BackToTop",props:{visibilityHeight:{type:Number,default:400},backPosition:{type:Number,default:0},customStyle:{type:Object,default:{right:"50px",bottom:"50px",width:"40px",height:"40px","border-radius":"4px","line-height":"45px",background:"#e7eaf1"}},transitionName:{type:String,default:"fade"}},data:function(){return{visible:!1,interval:null}},mounted:function(){window.addEventListener("scroll",this.handleScroll)},beforeDestroy:function(){window.removeEventListener("scroll",this.handleScroll),this.interval&&clearInterval(this.interval)},methods:{handleScroll:function(){this.visible=window.pageYOffset>this.visibilityHeight},backToTop:function(){var v=this,_=window.pageYOffset,i=0;this.interval=setInterval((function(){var d=Math.floor(v.easeInOutQuad(10*i,_,-_,500));d<=v.backPosition?(window.scrollTo(0,v.backPosition),clearInterval(v.interval)):window.scrollTo(0,d),i++}),16.7)},easeInOutQuad:function(v,_,i,d){return(v/=d/2)<1?i/2*v*v+_:-i/2*(--v*(v-2)-1)+_}}}),a=o,l=(i("77d9"),i("2877")),s=Object(l["a"])(a,e,n,!1,null,"8609375e",null),r=s.exports,c={components:{BackToTop:r},data:function(){return{myBackToTopStyle:{right:"50px",bottom:"50px",width:"40px",height:"40px","border-radius":"4px","line-height":"45px",background:"#e7eaf1"}}}},u=c,h=Object(l["a"])(u,d,t,!1,null,null,null);_["default"]=h.exports},"77d9":function(v,_,i){"use strict";i("0bac")}}]);