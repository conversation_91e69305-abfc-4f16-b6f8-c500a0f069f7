(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7e11b727"],{"7cf8":function(t,e,n){"use strict";n.d(e,"g",(function(){return c})),n.d(e,"e",(function(){return a})),n.d(e,"f",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"b",(function(){return u})),n.d(e,"d",(function(){return d})),n.d(e,"a",(function(){return f})),n.d(e,"h",(function(){return l}));var r=n("1c1e");function c(t){return Object(r["a"])({url:"monitor/memory",method:"get",params:{page:t}})}function a(){return Object(r["a"])({url:"cache",method:"get"})}function o(t,e){return Object(r["a"])({url:"cache/listlockeduser",method:"post",params:{page:t,size:e}})}function i(t){return Object(r["a"])({url:"cache/dellockeduser",method:"get",params:{uid:t}})}function u(t){return Object(r["a"])({url:"cache/delete/".concat(t),method:"get"})}function d(t,e){return Object(r["a"])({url:"cache/delete/".concat(t,"/").concat(e),method:"get"})}function f(t){return Object(r["a"])({url:"cache/".concat(t),method:"get"})}function l(t,e,n){return Object(r["a"])({url:"monitor/sms",method:"get",params:{phone:t,page:e,size:n}})}},cd87:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("v-chart",{attrs:{"force-fit":!0,height:t.height,data:t.data,"data-pre":t.dataPre,scale:t.scale}},[n("v-tooltip",{attrs:{"show-title":!1,"data-key":"item*percent"}}),t._v(" "),n("v-axis"),t._v(" "),n("v-legend",{attrs:{"data-key":"item"}}),t._v(" "),n("v-pie",{attrs:{position:"percent",color:"item","v-style":t.pieStyle,label:t.labelConfig}}),t._v(" "),n("v-coord",{attrs:{type:"theta"}})],1)],1)},c=[],a=n("7cf8"),o=[{item:"可用内存",count:0},{item:"总内存",count:0}],i={transform:[{type:"percent",field:"count",dimension:"item",as:"percent"}]},u=[{dataKey:"percent",min:0,formatter:".0%"}],d={data:function(){return{data:o,dataPre:i,scale:u,height:500,pieStyle:{stroke:"#fff",lineWidth:1},labelConfig:["percent",{formatter:function(t,e){return e.point.item+": "+t}}]}},mounted:function(){this.getData()},methods:{getData:function(){var t=this;Object(a["g"])().then((function(e){var n=e.obj,r=n.freeMemory,c=n.totalMemory;t.data=[{item:"可用内存",count:r},{item:"总内存",count:c}]})).catch((function(t){}))}}},f=d,l=n("2877"),s=Object(l["a"])(f,r,c,!1,null,null,null);e["default"]=s.exports}}]);