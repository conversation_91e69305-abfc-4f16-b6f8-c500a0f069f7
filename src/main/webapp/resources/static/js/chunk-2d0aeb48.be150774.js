(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0aeb48"],{"0acd":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},s=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"components-container"},[n("code",[t._v("这里暂时列出了自己在项目中用到的组件和一些自己封装的组件，如有补充可以提"),n("a",{attrs:{target:"_blank",href:"https://github.com/PanJiaChen/vue-element-admin/issues"}},[t._v(" issue ")]),n("br"),t._v("\n  我个人崇尚自己封装组件，因为很多组件会和业务后高度的耦合，而且第三方封装的组件灵活性可控性都不高，如有需要可以看楼主之前写过的一篇"),n("a",{attrs:{href:"https://segmentfault.com/a/1190000009090836",target:"_blank"}},[t._v("文章")])])])}],r=n("2877"),c={},u=Object(r["a"])(c,a,s,!1,null,null,null);e["default"]=u.exports}}]);