(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d38d1"],{"5cf3":function(t,a,e){"use strict";e.r(a);var n=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("el-upload",{attrs:{action:"https://upload.qbox.me",data:t.dataObj,drag:"",multiple:!0,"before-upload":t.beforeUpload}},[e("i",{staticClass:"el-icon-upload"}),t._v(" "),e("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),e("em",[t._v("点击上传")])])])},o=[],i=e("1c1e");function u(){return Object(i["a"])({url:"/qiniu/upload/token",method:"get"})}var d={data:function(){return{dataObj:{token:"",key:""},image_uri:[],fileList:[]}},methods:{beforeUpload:function(){var t=this;return new Promise((function(a,e){u().then((function(e){var n=e.data.qiniu_key,o=e.data.qiniu_token;t._data.dataObj.token=o,t._data.dataObj.key=n,a(!0)})).catch((function(t){console.log(t),e(!1)}))}))}}},l=d,c=e("2877"),r=Object(c["a"])(l,n,o,!1,null,null,null);a["default"]=r.exports}}]);