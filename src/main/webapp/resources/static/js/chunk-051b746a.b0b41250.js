(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-051b746a"],{"28a0":function(t,e){"function"===typeof Object.create?t.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}},3022:function(t,e,r){(function(t){var n=Object.getOwnPropertyDescriptors||function(t){for(var e=Object.keys(t),r={},n=0;n<e.length;n++)r[e[n]]=Object.getOwnPropertyDescriptor(t,e[n]);return r},o=/%[sdj%]/g;e.format=function(t){if(!w(t)){for(var e=[],r=0;r<arguments.length;r++)e.push(a(arguments[r]));return e.join(" ")}r=1;for(var n=arguments,i=n.length,u=String(t).replace(o,(function(t){if("%%"===t)return"%";if(r>=i)return t;switch(t){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(e){return"[Circular]"}default:return t}})),c=n[r];r<i;c=n[++r])O(c)||!_(c)?u+=" "+c:u+=" "+a(c);return u},e.deprecate=function(r,n){if("undefined"!==typeof t&&!0===t.noDeprecation)return r;if("undefined"===typeof t)return function(){return e.deprecate(r,n).apply(this,arguments)};var o=!1;function i(){if(!o){if(t.throwDeprecation)throw new Error(n);t.traceDeprecation?console.trace(n):console.error(n),o=!0}return r.apply(this,arguments)}return i};var i,u={};function a(t,r){var n={seen:[],stylize:s};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),b(r)?n.showHidden=r:r&&e._extend(n,r),S(n.showHidden)&&(n.showHidden=!1),S(n.depth)&&(n.depth=2),S(n.colors)&&(n.colors=!1),S(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=c),p(n,t,n.depth)}function c(t,e){var r=a.styles[e];return r?"["+a.colors[r][0]+"m"+t+"["+a.colors[r][1]+"m":t}function s(t,e){return t}function f(t){var e={};return t.forEach((function(t,r){e[t]=!0})),e}function p(t,r,n){if(t.customInspect&&r&&P(r.inspect)&&r.inspect!==e.inspect&&(!r.constructor||r.constructor.prototype!==r)){var o=r.inspect(n,t);return w(o)||(o=p(t,o,n)),o}var i=l(t,r);if(i)return i;var u=Object.keys(r),a=f(u);if(t.showHidden&&(u=Object.getOwnPropertyNames(r)),A(r)&&(u.indexOf("message")>=0||u.indexOf("description")>=0))return d(r);if(0===u.length){if(P(r)){var c=r.name?": "+r.name:"";return t.stylize("[Function"+c+"]","special")}if(D(r))return t.stylize(RegExp.prototype.toString.call(r),"regexp");if(E(r))return t.stylize(Date.prototype.toString.call(r),"date");if(A(r))return d(r)}var s,b="",O=!1,j=["{","}"];if(y(r)&&(O=!0,j=["[","]"]),P(r)){var v=r.name?": "+r.name:"";b=" [Function"+v+"]"}return D(r)&&(b=" "+RegExp.prototype.toString.call(r)),E(r)&&(b=" "+Date.prototype.toUTCString.call(r)),A(r)&&(b=" "+d(r)),0!==u.length||O&&0!=r.length?n<0?D(r)?t.stylize(RegExp.prototype.toString.call(r),"regexp"):t.stylize("[Object]","special"):(t.seen.push(r),s=O?h(t,r,n,a,u):u.map((function(e){return m(t,r,n,a,e,O)})),t.seen.pop(),g(s,b,j)):j[0]+b+j[1]}function l(t,e){if(S(e))return t.stylize("undefined","undefined");if(w(e)){var r="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(r,"string")}return v(e)?t.stylize(""+e,"number"):b(e)?t.stylize(""+e,"boolean"):O(e)?t.stylize("null","null"):void 0}function d(t){return"["+Error.prototype.toString.call(t)+"]"}function h(t,e,r,n,o){for(var i=[],u=0,a=e.length;u<a;++u)J(e,String(u))?i.push(m(t,e,r,n,String(u),!0)):i.push("");return o.forEach((function(o){o.match(/^\d+$/)||i.push(m(t,e,r,n,o,!0))})),i}function m(t,e,r,n,o,i){var u,a,c;if(c=Object.getOwnPropertyDescriptor(e,o)||{value:e[o]},c.get?a=c.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):c.set&&(a=t.stylize("[Setter]","special")),J(n,o)||(u="["+o+"]"),a||(t.seen.indexOf(c.value)<0?(a=O(r)?p(t,c.value,null):p(t,c.value,r-1),a.indexOf("\n")>-1&&(a=i?a.split("\n").map((function(t){return"  "+t})).join("\n").substr(2):"\n"+a.split("\n").map((function(t){return"   "+t})).join("\n"))):a=t.stylize("[Circular]","special")),S(u)){if(i&&o.match(/^\d+$/))return a;u=JSON.stringify(""+o),u.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(u=u.substr(1,u.length-2),u=t.stylize(u,"name")):(u=u.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),u=t.stylize(u,"string"))}return u+": "+a}function g(t,e,r){var n=t.reduce((function(t,e){return e.indexOf("\n")>=0&&0,t+e.replace(/\u001b\[\d\d?m/g,"").length+1}),0);return n>60?r[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+r[1]:r[0]+e+" "+t.join(", ")+" "+r[1]}function y(t){return Array.isArray(t)}function b(t){return"boolean"===typeof t}function O(t){return null===t}function j(t){return null==t}function v(t){return"number"===typeof t}function w(t){return"string"===typeof t}function x(t){return"symbol"===typeof t}function S(t){return void 0===t}function D(t){return _(t)&&"[object RegExp]"===z(t)}function _(t){return"object"===typeof t&&null!==t}function E(t){return _(t)&&"[object Date]"===z(t)}function A(t){return _(t)&&("[object Error]"===z(t)||t instanceof Error)}function P(t){return"function"===typeof t}function T(t){return null===t||"boolean"===typeof t||"number"===typeof t||"string"===typeof t||"symbol"===typeof t||"undefined"===typeof t}function z(t){return Object.prototype.toString.call(t)}function N(t){return t<10?"0"+t.toString(10):t.toString(10)}e.debuglog=function(r){if(S(i)&&(i=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/admin",VUE_APP_NAME:"sso-admin",VUE_APP_TITLE:"舆情系统管理",BASE_URL:"/"}).NODE_DEBUG||""),r=r.toUpperCase(),!u[r])if(new RegExp("\\b"+r+"\\b","i").test(i)){var n=t.pid;u[r]=function(){var t=e.format.apply(e,arguments);console.error("%s %d: %s",r,n,t)}}else u[r]=function(){};return u[r]},e.inspect=a,a.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},a.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},e.isArray=y,e.isBoolean=b,e.isNull=O,e.isNullOrUndefined=j,e.isNumber=v,e.isString=w,e.isSymbol=x,e.isUndefined=S,e.isRegExp=D,e.isObject=_,e.isDate=E,e.isError=A,e.isFunction=P,e.isPrimitive=T,e.isBuffer=r("d60a");var k=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function C(){var t=new Date,e=[N(t.getHours()),N(t.getMinutes()),N(t.getSeconds())].join(":");return[t.getDate(),k[t.getMonth()],e].join(" ")}function J(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.log=function(){console.log("%s - %s",C(),e.format.apply(e,arguments))},e.inherits=r("28a0"),e._extend=function(t,e){if(!e||!_(e))return t;var r=Object.keys(e),n=r.length;while(n--)t[r[n]]=e[r[n]];return t};var R="undefined"!==typeof Symbol?Symbol("util.promisify.custom"):void 0;function F(t,e){if(!t){var r=new Error("Promise was rejected with a falsy value");r.reason=t,t=r}return e(t)}function M(e){if("function"!==typeof e)throw new TypeError('The "original" argument must be of type Function');function r(){for(var r=[],n=0;n<arguments.length;n++)r.push(arguments[n]);var o=r.pop();if("function"!==typeof o)throw new TypeError("The last argument must be of type Function");var i=this,u=function(){return o.apply(i,arguments)};e.apply(this,r).then((function(e){t.nextTick(u,null,e)}),(function(e){t.nextTick(F,e,u)}))}return Object.setPrototypeOf(r,Object.getPrototypeOf(e)),Object.defineProperties(r,n(e)),r}e.promisify=function(t){if("function"!==typeof t)throw new TypeError('The "original" argument must be of type Function');if(R&&t[R]){var e=t[R];if("function"!==typeof e)throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(e,R,{value:e,enumerable:!1,writable:!1,configurable:!0}),e}function e(){for(var e,r,n=new Promise((function(t,n){e=t,r=n})),o=[],i=0;i<arguments.length;i++)o.push(arguments[i]);o.push((function(t,n){t?r(t):e(n)}));try{t.apply(this,o)}catch(u){r(u)}return n}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),R&&Object.defineProperty(e,R,{value:e,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(e,n(t))},e.promisify.custom=R,e.callbackify=M}).call(this,r("4362"))},"6bf6":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container calendar-list-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:8}},[r("ve-ring",{attrs:{data:t.charOfSex.chartData,settings:t.charOfSex.chartSettings}}),t._v(" "),r("p",{staticClass:"chart-description"},[t._v("性别比例")])],1),t._v(" "),r("el-col",{attrs:{span:8}},[r("ve-ring",{attrs:{data:t.charOfJob.chartData,settings:t.charOfJob.chartSettings}}),t._v(" "),r("p",{staticClass:"chart-description"},[t._v("职务比例")])],1),t._v(" "),r("el-col",{attrs:{span:8}},[r("ve-ring",{attrs:{data:t.charOfArea.chartData,settings:t.charOfArea.chartSettings}}),t._v(" "),r("p",{staticClass:"chart-description"},[t._v("区划人员排行")])],1)],1),t._v(" "),r("ve-bar",{attrs:{data:t.charOfSite.chartData,settings:t.charOfSite.chartSettings}}),t._v(" "),r("ve-line",{attrs:{data:t.charOfRegister.chartData,settings:t.charOfRegister.chartSettings}})],1)},o=[],i=r("c24f");r("3022"),r("4be7");var u={data:function(){return this.typeArr=["line","histogram","pie","ring"],{resourceOptions:[],periods:[],params:{type:void 0,period:void 0},charOfSex:{index:0,chartData:{columns:["sex","count"],rows:[]},chartSettings:{labelMap:{count:"性别"},dimension:["sex"],dataOrder:{date:"desc"},metrics:["count"],legendName:{"登录数量":"性别"},type:this.typeArr[this.index]}},charOfArea:{index:0,chartData:{columns:["area","count"],rows:[]},chartSettings:{labelMap:{count:"区划"},dimension:["area"],dataOrder:{date:"desc"},metrics:["count"],legendName:{"登录数量":"区划"},type:this.typeArr[this.index]}},charOfJob:{index:0,chartData:{columns:["job","count"],rows:[]},chartSettings:{limitShowNum:10,roseType:"radius",labelMap:{count:"接口调用数量"},dimension:["job"],metrics:["count"],legendName:{"登录数量":"接口调用数量"},type:this.typeArr[this.index]}},charOfRegister:{index:0,chartData:{columns:["date","count"],rows:[]},chartSettings:{labelMap:{count:"注册时间分布"},dimension:["date"],metrics:["count"],legendName:{"登录数量":"注册时间分布"},type:this.typeArr[this.index]}},charOfSite:{index:0,chartData:{columns:["site","count"],rows:[]},chartSettings:{roseType:"radius",labelMap:{count:"站点人员数量排行"},dataOrder:{label:"count",order:"desc"},dimension:["site"],metrics:["count"],legendName:{"登录数量":"站点人员数量"},type:this.typeArr[this.index]}},pickerOptions:{shortcuts:[{text:"一天",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-864e5),t.$emit("pick",[r,e])}},{text:"最近一周",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-6048e5),t.$emit("pick",[r,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-2592e6),t.$emit("pick",[r,e])}}]},period:[]}},computed:{sumField:function(){return this.resourceOptions.map((function(t){return t}))}},mounted:function(){this.getData()},methods:{getData:function(){var t=this;Object(i["x"])().then((function(e){t.charOfSex.chartData.rows=e.obj.sex,t.charOfJob.chartData.rows=e.obj.job,t.charOfRegister.chartData.rows=e.obj.register_date,t.charOfSite.chartData.rows=e.obj.site.slice(0,10),t.charOfArea.chartData.rows=e.obj.area})).catch((function(t){console.log(t)}))},changeChartType:function(){this.index++,this.index>=this.typeArr.length&&(this.index=0),this.chartSettings={type:this.typeArr[this.index]}},handleFilter:function(){this.getData(this.params)}}},a=u,c=(r("6f0e"),r("2877")),s=Object(c["a"])(a,n,o,!1,null,"10e5bb2f",null);e["default"]=s.exports},"6f0e":function(t,e,r){"use strict";r("cf7b")},c24f:function(t,e,r){"use strict";r.d(e,"b",(function(){return o})),r.d(e,"i",(function(){return i})),r.d(e,"l",(function(){return u})),r.d(e,"u",(function(){return a})),r.d(e,"g",(function(){return c})),r.d(e,"r",(function(){return s})),r.d(e,"k",(function(){return f})),r.d(e,"q",(function(){return p})),r.d(e,"a",(function(){return l})),r.d(e,"n",(function(){return d})),r.d(e,"d",(function(){return h})),r.d(e,"s",(function(){return m})),r.d(e,"p",(function(){return g})),r.d(e,"c",(function(){return y})),r.d(e,"j",(function(){return b})),r.d(e,"h",(function(){return O})),r.d(e,"w",(function(){return j})),r.d(e,"t",(function(){return v})),r.d(e,"o",(function(){return w})),r.d(e,"x",(function(){return x})),r.d(e,"m",(function(){return S})),r.d(e,"v",(function(){return D})),r.d(e,"f",(function(){return _})),r.d(e,"e",(function(){return E}));var n=r("1c1e");function o(t){return Object(n["a"])({url:"user/add",method:"post",params:t})}function i(t){return Object(n["a"])({url:"user/edit",method:"post",params:t})}function u(t){return Object(n["a"])({url:"user/bind",method:"post",params:t})}function a(t){return Object(n["a"])({url:"user",method:"post",params:t})}function c(t){return Object(n["a"])({url:"user/del",method:"post",params:{id:t}})}function s(t){return Object(n["a"])({url:"role",method:"post",params:{page:t}})}function f(t,e){return Object(n["a"])({url:"userrole/edit_user_role",method:"post",params:{userId:t,roleIds:e}})}function p(t){return Object(n["a"])({url:"resource/page",method:"post",params:t})}function l(t){return Object(n["a"])({url:"role/add",method:"post",params:t})}function d(t){return Object(n["a"])({url:"user/forbid",method:"post",params:{uid:t}})}function h(t){return Object(n["a"])({url:"user/allow",method:"post",params:{uid:t}})}function m(){return Object(n["a"])({url:"tree/gettree",method:"post"})}function g(t){return Object(n["a"])({url:"tree/getdepartment",method:"post",params:{sid:t}})}function y(t){return Object(n["a"])({url:"userjob/add",method:"post",params:t})}function b(t){return Object(n["a"])({url:"userjob/edit",method:"post",params:t})}function O(t){return Object(n["a"])({url:"userjob/delete",method:"post",params:{id:t}})}function j(t){return Object(n["a"])({url:"userjob/page",method:"post",params:t})}function v(){return Object(n["a"])({url:"userjob/list",method:"post"})}function w(){return Object(n["a"])({url:"user/areas",method:"get"})}function x(){return Object(n["a"])({url:"user/statistics"})}function S(){return Object(n["a"])({url:"user/export/profile"})}function D(t){return Object(n["a"])({url:"role/list-site-role",method:"post",params:{userId:t}})}function _(t){return Object(n["a"])({url:"userlog/page",method:"post",params:t})}function E(){return Object(n["a"])({url:"userlog/type",method:"get"})}},cf7b:function(t,e,r){},d60a:function(t,e){t.exports=function(t){return t&&"object"===typeof t&&"function"===typeof t.copy&&"function"===typeof t.fill&&"function"===typeof t.readUInt8}}}]);