(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-73470ebc"],{"1ef7":function(e,t,i){(function(e){e(i("56b3"))})((function(e){"use strict";var t=/^(\s*)(>[> ]*|[*+-] \[[x ]\]\s|[*+-]\s|(\d+)([.)]))(\s*)/,i=/^(\s*)(>[> ]*|[*+-] \[[x ]\]|[*+-]|(\d+)[.)])(\s*)$/,n=/[*+-]\s/;e.commands.newlineAndIndentContinueMarkdownList=function(r){if(r.getOption("disableInput"))return e.Pass;for(var s=r.listSelections(),o=[],l=0;l<s.length;l++){var a=s[l].head,h=r.getStateAfter(a.line),c=!1!==h.list,u=0!==h.quote,d=r.getLine(a.line),f=t.exec(d);if(!s[l].empty()||!c&&!u||!f)return void r.execCommand("newlineAndIndent");if(i.test(d))/>\s*$/.test(d)||r.replaceRange("",{line:a.line,ch:0},{line:a.line,ch:a.ch+1}),o[l]="\n";else{var p=f[1],m=f[5],g=n.test(f[2])||f[2].indexOf(">")>=0?f[2].replace("x"," "):parseInt(f[3],10)+1+f[4];o[l]="\n"+p+g+m}}r.replaceSelections(o)}}))},"44a0":function(e,t,i){(function(e){e(i("56b3"),i("959b"),i("9eb9"))})((function(e){"use strict";var t=/^((?:(?:aaas?|about|acap|adiumxtra|af[ps]|aim|apt|attachment|aw|beshare|bitcoin|bolo|callto|cap|chrome(?:-extension)?|cid|coap|com-eventbrite-attendee|content|crid|cvs|data|dav|dict|dlna-(?:playcontainer|playsingle)|dns|doi|dtn|dvb|ed2k|facetime|feed|file|finger|fish|ftp|geo|gg|git|gizmoproject|go|gopher|gtalk|h323|hcp|https?|iax|icap|icon|im|imap|info|ipn|ipp|irc[6s]?|iris(?:\.beep|\.lwz|\.xpc|\.xpcs)?|itms|jar|javascript|jms|keyparc|lastfm|ldaps?|magnet|mailto|maps|market|message|mid|mms|ms-help|msnim|msrps?|mtqp|mumble|mupdate|mvn|news|nfs|nih?|nntp|notes|oid|opaquelocktoken|palm|paparazzi|platform|pop|pres|proxy|psyc|query|res(?:ource)?|rmi|rsync|rtmp|rtsp|secondlife|service|session|sftp|sgn|shttp|sieve|sips?|skype|sm[bs]|snmp|soap\.beeps?|soldat|spotify|ssh|steam|svn|tag|teamspeak|tel(?:net)?|tftp|things|thismessage|tip|tn3270|tv|udp|unreal|urn|ut2004|vemmi|ventrilo|view-source|webcal|wss?|wtai|wyciwyg|xcon(?:-userid)?|xfire|xmlrpc\.beeps?|xmpp|xri|ymsgr|z39\.50[rs]?):(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]|\([^\s()<>]*\))+(?:\([^\s()<>]*\)|[^\s`*!()\[\]{};:'".,<>?«»“”‘’]))/i;e.defineMode("gfm",(function(i,n){var r=0;function s(e){return e.code=!1,null}var o={startState:function(){return{code:!1,codeBlock:!1,ateSpace:!1}},copyState:function(e){return{code:e.code,codeBlock:e.codeBlock,ateSpace:e.ateSpace}},token:function(e,i){if(i.combineTokens=null,i.codeBlock)return e.match(/^```+/)?(i.codeBlock=!1,null):(e.skipToEnd(),null);if(e.sol()&&(i.code=!1),e.sol()&&e.match(/^```+/))return e.skipToEnd(),i.codeBlock=!0,null;if("`"===e.peek()){e.next();var s=e.pos;e.eatWhile("`");var o=1+e.pos-s;return i.code?o===r&&(i.code=!1):(r=o,i.code=!0),null}if(i.code)return e.next(),null;if(e.eatSpace())return i.ateSpace=!0,null;if((e.sol()||i.ateSpace)&&(i.ateSpace=!1,!1!==n.gitHubSpice)){if(e.match(/^(?:[a-zA-Z0-9\-_]+\/)?(?:[a-zA-Z0-9\-_]+@)?(?:[a-f0-9]{7,40}\b)/))return i.combineTokens=!0,"link";if(e.match(/^(?:[a-zA-Z0-9\-_]+\/)?(?:[a-zA-Z0-9\-_]+)?#[0-9]+\b/))return i.combineTokens=!0,"link"}return e.match(t)&&"]("!=e.string.slice(e.start-2,e.start)&&(0==e.start||/\W/.test(e.string.charAt(e.start-1)))?(i.combineTokens=!0,"link"):(e.next(),null)},blankLine:s},l={taskLists:!0,fencedCodeBlocks:"```",strikethrough:!0};for(var a in n)l[a]=n[a];return l.name="markdown",e.overlayMode(e.getMode(i,l),o)}),"markdown"),e.defineMIME("text/x-gfm","gfm")}))},"4a5d":function(e,t,i){(function(t){var n;(function(){"use strict";n=function(e,i,n,r){r=r||{},this.dictionary=null,this.rules={},this.dictionaryTable={},this.compoundRules=[],this.compoundRuleCodes={},this.replacementTable=[],this.flags=r.flags||{},this.memoized={},this.loaded=!1;var s,o,l,a,h,c=this;function u(e,t){var i=c._readFile(e,null,r.asyncLoad);r.asyncLoad?i.then((function(e){t(e)})):t(i)}function d(e){i=e,n&&p()}function f(e){n=e,i&&p()}function p(){for(c.rules=c._parseAFF(i),c.compoundRuleCodes={},o=0,a=c.compoundRules.length;o<a;o++){var e=c.compoundRules[o];for(l=0,h=e.length;l<h;l++)c.compoundRuleCodes[e[l]]=[]}for(o in"ONLYINCOMPOUND"in c.flags&&(c.compoundRuleCodes[c.flags.ONLYINCOMPOUND]=[]),c.dictionaryTable=c._parseDIC(n),c.compoundRuleCodes)0===c.compoundRuleCodes[o].length&&delete c.compoundRuleCodes[o];for(o=0,a=c.compoundRules.length;o<a;o++){var t=c.compoundRules[o],s="";for(l=0,h=t.length;l<h;l++){var u=t[l];u in c.compoundRuleCodes?s+="("+c.compoundRuleCodes[u].join("|")+")":s+=u}c.compoundRules[o]=new RegExp(s,"i")}c.loaded=!0,r.asyncLoad&&r.loadedCallback&&r.loadedCallback(c)}return e&&(c.dictionary=e,i&&n?p():"undefined"!==typeof window&&"chrome"in window&&"extension"in window.chrome&&"getURL"in window.chrome.extension?(s=r.dictionaryPath?r.dictionaryPath:"typo/dictionaries",i||u(chrome.extension.getURL(s+"/"+e+"/"+e+".aff"),d),n||u(chrome.extension.getURL(s+"/"+e+"/"+e+".dic"),f)):(s=r.dictionaryPath?r.dictionaryPath:t+"/dictionaries",i||u(s+"/"+e+"/"+e+".aff",d),n||u(s+"/"+e+"/"+e+".dic",f))),this},n.prototype={load:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);return this},_readFile:function(e,t,n){if(t=t||"utf8","undefined"!==typeof XMLHttpRequest){var r,s=new XMLHttpRequest;return s.open("GET",e,n),n&&(r=new Promise((function(e,t){s.onload=function(){200===s.status?e(s.responseText):t(s.statusText)},s.onerror=function(){t(s.statusText)}}))),s.overrideMimeType&&s.overrideMimeType("text/plain; charset="+t),s.send(null),n?r:s.responseText}var o=i(3);try{if(o.existsSync(e))return o.readFileSync(e,t);console.log("Path "+e+" does not exist.")}catch(l){return console.log(l),""}},_parseAFF:function(e){var t,i,n,r,s,o,l,a,h={},c=e.split(/\r?\n/);for(s=0,l=c.length;s<l;s++)if(t=this._removeAffixComments(c[s]),t=t.trim(),t){var u=t.split(/\s+/),d=u[0];if("PFX"==d||"SFX"==d){var f=u[1],p=u[2];n=parseInt(u[3],10);var m=[];for(o=s+1,a=s+1+n;o<a;o++){i=c[o],r=i.split(/\s+/);var g=r[2],v=r[3].split("/"),x=v[0];"0"===x&&(x="");var w=this.parseRuleCodes(v[1]),b=r[4],y={};y.add=x,w.length>0&&(y.continuationClasses=w),"."!==b&&(y.match="SFX"===d?new RegExp(b+"$"):new RegExp("^"+b)),"0"!=g&&(y.remove="SFX"===d?new RegExp(g+"$"):g),m.push(y)}h[f]={type:d,combineable:"Y"==p,entries:m},s+=n}else if("COMPOUNDRULE"===d){for(n=parseInt(u[1],10),o=s+1,a=s+1+n;o<a;o++)t=c[o],r=t.split(/\s+/),this.compoundRules.push(r[1]);s+=n}else"REP"===d?(r=t.split(/\s+/),3===r.length&&this.replacementTable.push([r[1],r[2]])):this.flags[d]=u[1]}return h},_removeAffixComments:function(e){return e.match(/^\s*#/,"")?"":e},_parseDIC:function(e){e=this._removeDicComments(e);var t=e.split(/\r?\n/),i={};function n(e,t){i.hasOwnProperty(e)||(i[e]=null),t.length>0&&(null===i[e]&&(i[e]=[]),i[e].push(t))}for(var r=1,s=t.length;r<s;r++){var o=t[r];if(o){var l=o.split("/",2),a=l[0];if(l.length>1){var h=this.parseRuleCodes(l[1]);"NEEDAFFIX"in this.flags&&-1!=h.indexOf(this.flags.NEEDAFFIX)||n(a,h);for(var c=0,u=h.length;c<u;c++){var d=h[c],f=this.rules[d];if(f)for(var p=this._applyRule(a,f),m=0,g=p.length;m<g;m++){var v=p[m];if(n(v,[]),f.combineable)for(var x=c+1;x<u;x++){var w=h[x],b=this.rules[w];if(b&&b.combineable&&f.type!=b.type)for(var y=this._applyRule(v,b),k=0,D=y.length;k<D;k++){var C=y[k];n(C,[])}}}d in this.compoundRuleCodes&&this.compoundRuleCodes[d].push(a)}}else n(a.trim(),[])}}return i},_removeDicComments:function(e){return e=e.replace(/^\t.*$/gm,""),e},parseRuleCodes:function(e){if(!e)return[];if(!("FLAG"in this.flags))return e.split("");if("long"===this.flags.FLAG){for(var t=[],i=0,n=e.length;i<n;i+=2)t.push(e.substr(i,2));return t}return"num"===this.flags.FLAG?e.split(","):void 0},_applyRule:function(e,t){for(var i=t.entries,n=[],r=0,s=i.length;r<s;r++){var o=i[r];if(!o.match||e.match(o.match)){var l=e;if(o.remove&&(l=l.replace(o.remove,"")),"SFX"===t.type?l+=o.add:l=o.add+l,n.push(l),"continuationClasses"in o)for(var a=0,h=o.continuationClasses.length;a<h;a++){var c=this.rules[o.continuationClasses[a]];c&&(n=n.concat(this._applyRule(l,c)))}}}return n},check:function(e){if(!this.loaded)throw"Dictionary not loaded.";var t=e.replace(/^\s\s*/,"").replace(/\s\s*$/,"");if(this.checkExact(t))return!0;if(t.toUpperCase()===t){var i=t[0]+t.substring(1).toLowerCase();if(this.hasFlag(i,"KEEPCASE"))return!1;if(this.checkExact(i))return!0;if(this.checkExact(t.toLowerCase()))return!0}var n=t[0].toLowerCase()+t.substring(1);if(n!==t){if(this.hasFlag(n,"KEEPCASE"))return!1;if(this.checkExact(n))return!0}return!1},checkExact:function(e){if(!this.loaded)throw"Dictionary not loaded.";var t,i,n=this.dictionaryTable[e];if("undefined"===typeof n){if("COMPOUNDMIN"in this.flags&&e.length>=this.flags.COMPOUNDMIN)for(t=0,i=this.compoundRules.length;t<i;t++)if(e.match(this.compoundRules[t]))return!0}else{if(null===n)return!0;if("object"===typeof n)for(t=0,i=n.length;t<i;t++)if(!this.hasFlag(e,"ONLYINCOMPOUND",n[t]))return!0}return!1},hasFlag:function(e,t,i){if(!this.loaded)throw"Dictionary not loaded.";return!(!(t in this.flags)||("undefined"===typeof i&&(i=Array.prototype.concat.apply([],this.dictionaryTable[e])),!i||-1===i.indexOf(this.flags[t])))},alphabet:"",suggest:function(e,t){if(!this.loaded)throw"Dictionary not loaded.";if(t=t||5,this.memoized.hasOwnProperty(e)){var i=this.memoized[e]["limit"];if(t<=i||this.memoized[e]["suggestions"].length<i)return this.memoized[e]["suggestions"].slice(0,t)}if(this.check(e))return[];for(var n=0,r=this.replacementTable.length;n<r;n++){var s=this.replacementTable[n];if(-1!==e.indexOf(s[0])){var o=e.replace(s[0],s[1]);if(this.check(o))return[o]}}var l=this;function a(e,t){var i,n,r,s,o={},a=l.alphabet.length;if("string"==typeof e){var h=e;e={},e[h]=!0}for(var h in e)for(i=0,r=h.length+1;i<r;i++){var c=[h.substring(0,i),h.substring(i)];if(c[1]&&(s=c[0]+c[1].substring(1),t&&!l.check(s)||(s in o?o[s]+=1:o[s]=1)),c[1].length>1&&c[1][1]!==c[1][0]&&(s=c[0]+c[1][1]+c[1][0]+c[1].substring(2),t&&!l.check(s)||(s in o?o[s]+=1:o[s]=1)),c[1]){var u=c[1].substring(0,1).toUpperCase()===c[1].substring(0,1)?"uppercase":"lowercase";for(n=0;n<a;n++){var d=l.alphabet[n];"uppercase"===u&&(d=d.toUpperCase()),d!=c[1].substring(0,1)&&(s=c[0]+d+c[1].substring(1),t&&!l.check(s)||(s in o?o[s]+=1:o[s]=1))}}if(c[1])for(n=0;n<a;n++){u=c[0].substring(-1).toUpperCase()===c[0].substring(-1)&&c[1].substring(0,1).toUpperCase()===c[1].substring(0,1)?"uppercase":"lowercase",d=l.alphabet[n];"uppercase"===u&&(d=d.toUpperCase()),s=c[0]+d+c[1],t&&!l.check(s)||(s in o?o[s]+=1:o[s]=1)}}return o}function h(e){var i,n=a(e),r=a(n,!0),s=r;for(var o in n)l.check(o)&&(o in s?s[o]+=n[o]:s[o]=n[o]);var h=[];for(i in s)s.hasOwnProperty(i)&&h.push([i,s[i]]);function c(e,t){var i=e[1],n=t[1];return i<n?-1:i>n?1:t[0].localeCompare(e[0])}h.sort(c).reverse();var u=[],d="lowercase";e.toUpperCase()===e?d="uppercase":e.substr(0,1).toUpperCase()+e.substr(1).toLowerCase()===e&&(d="capitalized");var f=t;for(i=0;i<Math.min(f,h.length);i++)"uppercase"===d?h[i][0]=h[i][0].toUpperCase():"capitalized"===d&&(h[i][0]=h[i][0].substr(0,1).toUpperCase()+h[i][0].substr(1)),l.hasFlag(h[i][0],"NOSUGGEST")||-1!=u.indexOf(h[i][0])?f++:u.push(h[i][0]);return u}return l.alphabet="abcdefghijklmnopqrstuvwxyz",this.memoized[e]={suggestions:h(e),limit:t},this.memoized[e]["suggestions"]}}})(),e.exports=n}).call(this,"/")},"6d78":function(e,t,i){(function(e){e(i("56b3"))})((function(e){"use strict";function t(e){var t=e.getWrapperElement();e.state.fullScreenRestore={scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,width:t.style.width,height:t.style.height},t.style.width="",t.style.height="auto",t.className+=" CodeMirror-fullscreen",document.documentElement.style.overflow="hidden",e.refresh()}function i(e){var t=e.getWrapperElement();t.className=t.className.replace(/\s*CodeMirror-fullscreen\b/,""),document.documentElement.style.overflow="";var i=e.state.fullScreenRestore;t.style.width=i.width,t.style.height=i.height,window.scrollTo(i.scrollLeft,i.scrollTop),e.refresh()}e.defineOption("fullScreen",!1,(function(n,r,s){s==e.Init&&(s=!1),!s!=!r&&(r?t(n):i(n))}))}))},"7c5c":function(e,t,i){(function(e,i){i(t)})(0,(function(e){"use strict";function t(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function n(e,t){if(e){if("string"===typeof e)return r(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?r(e,t):void 0}}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function s(e,t){var i="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(i)return(i=i.call(e)).next.bind(i);if(Array.isArray(e)||(i=n(e))||t&&e&&"number"===typeof e.length){i&&(e=i);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function o(){return{baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}function l(t){e.defaults=t}e.defaults=o();var a=/[&<>"']/,h=/[&<>"']/g,c=/[<>"']|&(?!#?\w+;)/,u=/[<>"']|&(?!#?\w+;)/g,d={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},f=function(e){return d[e]};function p(e,t){if(t){if(a.test(e))return e.replace(h,f)}else if(c.test(e))return e.replace(u,f);return e}var m=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function g(e){return e.replace(m,(function(e,t){return t=t.toLowerCase(),"colon"===t?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""}))}var v=/(^|[^\[])\^/g;function x(e,t){e="string"===typeof e?e:e.source,t=t||"";var i={replace:function(t,n){return n=n.source||n,n=n.replace(v,"$1"),e=e.replace(t,n),i},getRegex:function(){return new RegExp(e,t)}};return i}var w=/[^\w:]/g,b=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function y(e,t,i){if(e){var n;try{n=decodeURIComponent(g(i)).replace(w,"").toLowerCase()}catch(r){return null}if(0===n.indexOf("javascript:")||0===n.indexOf("vbscript:")||0===n.indexOf("data:"))return null}t&&!b.test(i)&&(i=A(t,i));try{i=encodeURI(i).replace(/%25/g,"%")}catch(r){return null}return i}var k={},D=/^[^:]+:\/*[^/]*$/,C=/^([^:]+:)[\s\S]*$/,S=/^([^:]+:\/*[^/]*)[\s\S]*$/;function A(e,t){k[" "+e]||(D.test(e)?k[" "+e]=e+"/":k[" "+e]=F(e,"/",!0)),e=k[" "+e];var i=-1===e.indexOf(":");return"//"===t.substring(0,2)?i?t:e.replace(C,"$1")+t:"/"===t.charAt(0)?i?t:e.replace(S,"$1")+t:e+t}var E={exec:function(){}};function M(e){for(var t,i,n=1;n<arguments.length;n++)for(i in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}function B(e,t){var i=e.replace(/\|/g,(function(e,t,i){var n=!1,r=t;while(--r>=0&&"\\"===i[r])n=!n;return n?"|":" |"})),n=i.split(/ \|/),r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),n.length>t)n.splice(t);else while(n.length<t)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function F(e,t,i){var n=e.length;if(0===n)return"";var r=0;while(r<n){var s=e.charAt(n-r-1);if(s!==t||i){if(s===t||!i)break;r++}else r++}return e.slice(0,n-r)}function T(e,t){if(-1===e.indexOf(t[1]))return-1;for(var i=e.length,n=0,r=0;r<i;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return-1}function O(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function L(e,t){if(t<1)return"";var i="";while(t>1)1&t&&(i+=e),t>>=1,e+=e;return i+e}function R(e,t,i,n){var r=t.href,s=t.title?p(t.title):null,o=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){n.state.inLink=!0;var l={type:"link",raw:i,href:r,title:s,text:o,tokens:n.inlineTokens(o,[])};return n.state.inLink=!1,l}return{type:"image",raw:i,href:r,title:s,text:p(o)}}function I(e,t){var i=e.match(/^(\s+)(?:```)/);if(null===i)return t;var n=i[1];return t.split("\n").map((function(e){var t=e.match(/^\s+/);if(null===t)return e;var i=t[0];return i.length>=n.length?e.slice(n.length):e})).join("\n")}var N=function(){function t(t){this.options=t||e.defaults}var i=t.prototype;return i.space=function(e){var t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}},i.code=function(e){var t=this.rules.block.code.exec(e);if(t){var i=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?i:F(i,"\n")}}},i.fences=function(e){var t=this.rules.block.fences.exec(e);if(t){var i=t[0],n=I(i,t[3]||"");return{type:"code",raw:i,lang:t[2]?t[2].trim():t[2],text:n}}},i.heading=function(e){var t=this.rules.block.heading.exec(e);if(t){var i=t[2].trim();if(/#$/.test(i)){var n=F(i,"#");this.options.pedantic?i=n.trim():n&&!/ $/.test(n)||(i=n.trim())}var r={type:"heading",raw:t[0],depth:t[1].length,text:i,tokens:[]};return this.lexer.inline(r.text,r.tokens),r}},i.hr=function(e){var t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}},i.blockquote=function(e){var t=this.rules.block.blockquote.exec(e);if(t){var i=t[0].replace(/^ *>[ \t]?/gm,"");return{type:"blockquote",raw:t[0],tokens:this.lexer.blockTokens(i,[]),text:i}}},i.list=function(e){var t=this.rules.block.list.exec(e);if(t){var i,n,r,o,l,a,h,c,u,d,f,p,m=t[1].trim(),g=m.length>1,v={type:"list",raw:"",ordered:g,start:g?+m.slice(0,-1):"",loose:!1,items:[]};m=g?"\\d{1,9}\\"+m.slice(-1):"\\"+m,this.options.pedantic&&(m=g?m:"[*+-]");var x=new RegExp("^( {0,3}"+m+")((?:[\t ][^\\n]*)?(?:\\n|$))");while(e){if(p=!1,!(t=x.exec(e)))break;if(this.rules.block.hr.test(e))break;if(i=t[0],e=e.substring(i.length),c=t[2].split("\n",1)[0],u=e.split("\n",1)[0],this.options.pedantic?(o=2,f=c.trimLeft()):(o=t[2].search(/[^ ]/),o=o>4?1:o,f=c.slice(o),o+=t[1].length),a=!1,!c&&/^ *$/.test(u)&&(i+=u+"\n",e=e.substring(u.length+1),p=!0),!p){var w=new RegExp("^ {0,"+Math.min(3,o-1)+"}(?:[*+-]|\\d{1,9}[.)])((?: [^\\n]*)?(?:\\n|$))"),b=new RegExp("^ {0,"+Math.min(3,o-1)+"}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)"),y=new RegExp("^( {0,"+Math.min(3,o-1)+"})(```|~~~)");while(e){if(d=e.split("\n",1)[0],c=d,this.options.pedantic&&(c=c.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),y.test(c))break;if(this.rules.block.heading.test(c))break;if(w.test(c))break;if(b.test(e))break;if(c.search(/[^ ]/)>=o||!c.trim())f+="\n"+c.slice(o);else{if(a)break;f+="\n"+c}a||c.trim()||(a=!0),i+=d+"\n",e=e.substring(d.length+1)}}v.loose||(h?v.loose=!0:/\n *\n *$/.test(i)&&(h=!0)),this.options.gfm&&(n=/^\[[ xX]\] /.exec(f),n&&(r="[ ] "!==n[0],f=f.replace(/^\[[ xX]\] +/,""))),v.items.push({type:"list_item",raw:i,task:!!n,checked:r,loose:!1,text:f}),v.raw+=i}v.items[v.items.length-1].raw=i.trimRight(),v.items[v.items.length-1].text=f.trimRight(),v.raw=v.raw.trimRight();var k=v.items.length;for(l=0;l<k;l++){this.lexer.state.top=!1,v.items[l].tokens=this.lexer.blockTokens(v.items[l].text,[]);var D=v.items[l].tokens.filter((function(e){return"space"===e.type})),C=D.every((function(e){for(var t,i=e.raw.split(""),n=0,r=s(i);!(t=r()).done;){var o=t.value;if("\n"===o&&(n+=1),n>1)return!0}return!1}));!v.loose&&D.length&&C&&(v.loose=!0,v.items[l].loose=!0)}return v}},i.html=function(e){var t=this.rules.block.html.exec(e);if(t){var i={type:"html",raw:t[0],pre:!this.options.sanitizer&&("pre"===t[1]||"script"===t[1]||"style"===t[1]),text:t[0]};return this.options.sanitize&&(i.type="paragraph",i.text=this.options.sanitizer?this.options.sanitizer(t[0]):p(t[0]),i.tokens=[],this.lexer.inline(i.text,i.tokens)),i}},i.def=function(e){var t=this.rules.block.def.exec(e);if(t){t[3]&&(t[3]=t[3].substring(1,t[3].length-1));var i=t[1].toLowerCase().replace(/\s+/g," ");return{type:"def",tag:i,raw:t[0],href:t[2],title:t[3]}}},i.table=function(e){var t=this.rules.block.table.exec(e);if(t){var i={type:"table",header:B(t[1]).map((function(e){return{text:e}})),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(i.header.length===i.align.length){i.raw=t[0];var n,r,s,o,l=i.align.length;for(n=0;n<l;n++)/^ *-+: *$/.test(i.align[n])?i.align[n]="right":/^ *:-+: *$/.test(i.align[n])?i.align[n]="center":/^ *:-+ *$/.test(i.align[n])?i.align[n]="left":i.align[n]=null;for(l=i.rows.length,n=0;n<l;n++)i.rows[n]=B(i.rows[n],i.header.length).map((function(e){return{text:e}}));for(l=i.header.length,r=0;r<l;r++)i.header[r].tokens=[],this.lexer.inline(i.header[r].text,i.header[r].tokens);for(l=i.rows.length,r=0;r<l;r++)for(o=i.rows[r],s=0;s<o.length;s++)o[s].tokens=[],this.lexer.inline(o[s].text,o[s].tokens);return i}}},i.lheading=function(e){var t=this.rules.block.lheading.exec(e);if(t){var i={type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:[]};return this.lexer.inline(i.text,i.tokens),i}},i.paragraph=function(e){var t=this.rules.block.paragraph.exec(e);if(t){var i={type:"paragraph",raw:t[0],text:"\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1],tokens:[]};return this.lexer.inline(i.text,i.tokens),i}},i.text=function(e){var t=this.rules.block.text.exec(e);if(t){var i={type:"text",raw:t[0],text:t[0],tokens:[]};return this.lexer.inline(i.text,i.tokens),i}},i.escape=function(e){var t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:p(t[1])}},i.tag=function(e){var t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):p(t[0]):t[0]}},i.link=function(e){var t=this.rules.inline.link.exec(e);if(t){var i=t[2].trim();if(!this.options.pedantic&&/^</.test(i)){if(!/>$/.test(i))return;var n=F(i.slice(0,-1),"\\");if((i.length-n.length)%2===0)return}else{var r=T(t[2],"()");if(r>-1){var s=0===t[0].indexOf("!")?5:4,o=s+t[1].length+r;t[2]=t[2].substring(0,r),t[0]=t[0].substring(0,o).trim(),t[3]=""}}var l=t[2],a="";if(this.options.pedantic){var h=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(l);h&&(l=h[1],a=h[3])}else a=t[3]?t[3].slice(1,-1):"";return l=l.trim(),/^</.test(l)&&(l=this.options.pedantic&&!/>$/.test(i)?l.slice(1):l.slice(1,-1)),R(t,{href:l?l.replace(this.rules.inline._escapes,"$1"):l,title:a?a.replace(this.rules.inline._escapes,"$1"):a},t[0],this.lexer)}},i.reflink=function(e,t){var i;if((i=this.rules.inline.reflink.exec(e))||(i=this.rules.inline.nolink.exec(e))){var n=(i[2]||i[1]).replace(/\s+/g," ");if(n=t[n.toLowerCase()],!n||!n.href){var r=i[0].charAt(0);return{type:"text",raw:r,text:r}}return R(i,n,i[0],this.lexer)}},i.emStrong=function(e,t,i){void 0===i&&(i="");var n=this.rules.inline.emStrong.lDelim.exec(e);if(n&&(!n[3]||!i.match(/(?:[0-9A-Za-z\xAA\xB2\xB3\xB5\xB9\xBA\xBC-\xBE\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u0660-\u0669\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0966-\u096F\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09E6-\u09F1\u09F4-\u09F9\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AE6-\u0AEF\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B66-\u0B6F\u0B71-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0BE6-\u0BF2\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C66-\u0C6F\u0C78-\u0C7E\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CE6-\u0CEF\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D58-\u0D61\u0D66-\u0D78\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DE6-\u0DEF\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F20-\u0F33\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F-\u1049\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u1090-\u1099\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1369-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A16\u1A20-\u1A54\u1A80-\u1A89\u1A90-\u1A99\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B50-\u1B59\u1B83-\u1BA0\u1BAE-\u1BE5\u1C00-\u1C23\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2150-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2CFD\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3192-\u3195\u31A0-\u31BF\u31F0-\u31FF\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA830-\uA835\uA840-\uA873\uA882-\uA8B3\uA8D0-\uA8D9\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA900-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF-\uA9D9\uA9E0-\uA9E4\uA9E6-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA50-\uAA59\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDE80-\uDE9C\uDEA0-\uDED0\uDEE1-\uDEFB\uDF00-\uDF23\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC58-\uDC76\uDC79-\uDC9E\uDCA7-\uDCAF\uDCE0-\uDCF2\uDCF4\uDCF5\uDCFB-\uDD1B\uDD20-\uDD39\uDD80-\uDDB7\uDDBC-\uDDCF\uDDD2-\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE40-\uDE48\uDE60-\uDE7E\uDE80-\uDE9F\uDEC0-\uDEC7\uDEC9-\uDEE4\uDEEB-\uDEEF\uDF00-\uDF35\uDF40-\uDF55\uDF58-\uDF72\uDF78-\uDF91\uDFA9-\uDFAF]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDCFA-\uDD23\uDD30-\uDD39\uDE60-\uDE7E\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF27\uDF30-\uDF45\uDF51-\uDF54\uDF70-\uDF81\uDFB0-\uDFCB\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC52-\uDC6F\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD03-\uDD26\uDD36-\uDD3F\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDD0-\uDDDA\uDDDC\uDDE1-\uDDF4\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDEF0-\uDEF9\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC50-\uDC59\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE50-\uDE59\uDE80-\uDEAA\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF30-\uDF3B\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCF2\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC50-\uDC6C\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF2\uDFB0\uDFC0-\uDFD4]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF50-\uDF59\uDF5B-\uDF61\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE96\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD834[\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB\uDEF0-\uDEF9]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCC7-\uDCCF\uDD00-\uDD43\uDD4B\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])/))){var r=n[1]||n[2]||"";if(!r||r&&(""===i||this.rules.inline.punctuation.exec(i))){var s,o,l=n[0].length-1,a=l,h=0,c="*"===n[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;c.lastIndex=0,t=t.slice(-1*e.length+l);while(null!=(n=c.exec(t)))if(s=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],s)if(o=s.length,n[3]||n[4])a+=o;else if(!((n[5]||n[6])&&l%3)||(l+o)%3){if(a-=o,!(a>0)){if(o=Math.min(o,o+a+h),Math.min(l,o)%2){var u=e.slice(1,l+n.index+o);return{type:"em",raw:e.slice(0,l+n.index+o+1),text:u,tokens:this.lexer.inlineTokens(u,[])}}var d=e.slice(2,l+n.index+o-1);return{type:"strong",raw:e.slice(0,l+n.index+o+1),text:d,tokens:this.lexer.inlineTokens(d,[])}}}else h+=o}}},i.codespan=function(e){var t=this.rules.inline.code.exec(e);if(t){var i=t[2].replace(/\n/g," "),n=/[^ ]/.test(i),r=/^ /.test(i)&&/ $/.test(i);return n&&r&&(i=i.substring(1,i.length-1)),i=p(i,!0),{type:"codespan",raw:t[0],text:i}}},i.br=function(e){var t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}},i.del=function(e){var t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2],[])}},i.autolink=function(e,t){var i,n,r=this.rules.inline.autolink.exec(e);if(r)return"@"===r[2]?(i=p(this.options.mangle?t(r[1]):r[1]),n="mailto:"+i):(i=p(r[1]),n=i),{type:"link",raw:r[0],text:i,href:n,tokens:[{type:"text",raw:i,text:i}]}},i.url=function(e,t){var i;if(i=this.rules.inline.url.exec(e)){var n,r;if("@"===i[2])n=p(this.options.mangle?t(i[0]):i[0]),r="mailto:"+n;else{var s;do{s=i[0],i[0]=this.rules.inline._backpedal.exec(i[0])[0]}while(s!==i[0]);n=p(i[0]),r="www."===i[1]?"http://"+n:n}return{type:"link",raw:i[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}},i.inlineText=function(e,t){var i,n=this.rules.inline.text.exec(e);if(n)return i=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):p(n[0]):n[0]:p(this.options.smartypants?t(n[0]):n[0]),{type:"text",raw:n[0],text:i}},t}(),P={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?<?([^\s>]+)>?(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:E,lheading:/^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/};P.def=x(P.def).replace("label",P._label).replace("title",P._title).getRegex(),P.bullet=/(?:[*+-]|\d{1,9}[.)])/,P.listItemStart=x(/^( *)(bull) */).replace("bull",P.bullet).getRegex(),P.list=x(P.list).replace(/bull/g,P.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+P.def.source+")").getRegex(),P._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",P._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,P.html=x(P.html,"i").replace("comment",P._comment).replace("tag",P._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),P.paragraph=x(P._paragraph).replace("hr",P.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",P._tag).getRegex(),P.blockquote=x(P.blockquote).replace("paragraph",P.paragraph).getRegex(),P.normal=M({},P),P.gfm=M({},P.normal,{table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),P.gfm.table=x(P.gfm.table).replace("hr",P.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",P._tag).getRegex(),P.gfm.paragraph=x(P._paragraph).replace("hr",P.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",P.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",P._tag).getRegex(),P.pedantic=M({},P.normal,{html:x("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",P._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:E,paragraph:x(P.normal._paragraph).replace("hr",P.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",P.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()});var z={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:E,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^[^_*]*?\_\_[^_*]*?\*[^_*]*?(?=\_\_)|[^*]+(?=[^*])|[punct_](\*+)(?=[\s]|$)|[^punct*_\s](\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|[^punct*_\s](\*+)(?=[^punct*_\s])/,rDelimUnd:/^[^_*]*?\*\*[^_*]*?\_[^_*]*?(?=\*\*)|[^_]+(?=[^_])|[punct*](\_+)(?=[\s]|$)|[^punct*_\s](\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:E,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};function _(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function H(e){var t,i,n="",r=e.length;for(t=0;t<r;t++)i=e.charCodeAt(t),Math.random()>.5&&(i="x"+i.toString(16)),n+="&#"+i+";";return n}z._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",z.punctuation=x(z.punctuation).replace(/punctuation/g,z._punctuation).getRegex(),z.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,z.escapedEmSt=/\\\*|\\_/g,z._comment=x(P._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),z.emStrong.lDelim=x(z.emStrong.lDelim).replace(/punct/g,z._punctuation).getRegex(),z.emStrong.rDelimAst=x(z.emStrong.rDelimAst,"g").replace(/punct/g,z._punctuation).getRegex(),z.emStrong.rDelimUnd=x(z.emStrong.rDelimUnd,"g").replace(/punct/g,z._punctuation).getRegex(),z._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,z._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,z._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,z.autolink=x(z.autolink).replace("scheme",z._scheme).replace("email",z._email).getRegex(),z._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,z.tag=x(z.tag).replace("comment",z._comment).replace("attribute",z._attribute).getRegex(),z._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,z._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,z._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,z.link=x(z.link).replace("label",z._label).replace("href",z._href).replace("title",z._title).getRegex(),z.reflink=x(z.reflink).replace("label",z._label).replace("ref",P._label).getRegex(),z.nolink=x(z.nolink).replace("ref",P._label).getRegex(),z.reflinkSearch=x(z.reflinkSearch,"g").replace("reflink",z.reflink).replace("nolink",z.nolink).getRegex(),z.normal=M({},z),z.pedantic=M({},z.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:x(/^!?\[(label)\]\((.*?)\)/).replace("label",z._label).getRegex(),reflink:x(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",z._label).getRegex()}),z.gfm=M({},z.normal,{escape:x(z.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),z.gfm.url=x(z.gfm.url,"i").replace("email",z.gfm._extended_email).getRegex(),z.breaks=M({},z.gfm,{br:x(z.br).replace("{2,}","*").getRegex(),text:x(z.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()});var q=function(){function t(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||e.defaults,this.options.tokenizer=this.options.tokenizer||new N,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};var i={block:P.normal,inline:z.normal};this.options.pedantic?(i.block=P.pedantic,i.inline=z.pedantic):this.options.gfm&&(i.block=P.gfm,this.options.breaks?i.inline=z.breaks:i.inline=z.gfm),this.tokenizer.rules=i}t.lex=function(e,i){var n=new t(i);return n.lex(e)},t.lexInline=function(e,i){var n=new t(i);return n.inlineTokens(e)};var n=t.prototype;return n.lex=function(e){var t;e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);while(t=this.inlineQueue.shift())this.inlineTokens(t.src,t.tokens);return this.tokens},n.blockTokens=function(e,t){var i,n,r,s,o=this;void 0===t&&(t=[]),e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(function(e,t,i){return t+"    ".repeat(i.length)}));while(e)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((function(n){return!!(i=n.call({lexer:o},e,t))&&(e=e.substring(i.raw.length),t.push(i),!0)}))))if(i=this.tokenizer.space(e))e=e.substring(i.raw.length),1===i.raw.length&&t.length>0?t[t.length-1].raw+="\n":t.push(i);else if(i=this.tokenizer.code(e))e=e.substring(i.raw.length),n=t[t.length-1],!n||"paragraph"!==n.type&&"text"!==n.type?t.push(i):(n.raw+="\n"+i.raw,n.text+="\n"+i.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(i=this.tokenizer.fences(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.heading(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.hr(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.blockquote(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.list(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.html(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.def(e))e=e.substring(i.raw.length),n=t[t.length-1],!n||"paragraph"!==n.type&&"text"!==n.type?this.tokens.links[i.tag]||(this.tokens.links[i.tag]={href:i.href,title:i.title}):(n.raw+="\n"+i.raw,n.text+="\n"+i.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(i=this.tokenizer.table(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.lheading(e))e=e.substring(i.raw.length),t.push(i);else if(r=e,this.options.extensions&&this.options.extensions.startBlock&&function(){var t=1/0,i=e.slice(1),n=void 0;o.options.extensions.startBlock.forEach((function(e){n=e.call({lexer:this},i),"number"===typeof n&&n>=0&&(t=Math.min(t,n))})),t<1/0&&t>=0&&(r=e.substring(0,t+1))}(),this.state.top&&(i=this.tokenizer.paragraph(r)))n=t[t.length-1],s&&"paragraph"===n.type?(n.raw+="\n"+i.raw,n.text+="\n"+i.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):t.push(i),s=r.length!==e.length,e=e.substring(i.raw.length);else if(i=this.tokenizer.text(e))e=e.substring(i.raw.length),n=t[t.length-1],n&&"text"===n.type?(n.raw+="\n"+i.raw,n.text+="\n"+i.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):t.push(i);else if(e){var l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}return this.state.top=!0,t},n.inline=function(e,t){this.inlineQueue.push({src:e,tokens:t})},n.inlineTokens=function(e,t){var i,n,r,s=this;void 0===t&&(t=[]);var o,l,a,h=e;if(this.tokens.links){var c=Object.keys(this.tokens.links);if(c.length>0)while(null!=(o=this.tokenizer.rules.inline.reflinkSearch.exec(h)))c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(h=h.slice(0,o.index)+"["+L("a",o[0].length-2)+"]"+h.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}while(null!=(o=this.tokenizer.rules.inline.blockSkip.exec(h)))h=h.slice(0,o.index)+"["+L("a",o[0].length-2)+"]"+h.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);while(null!=(o=this.tokenizer.rules.inline.escapedEmSt.exec(h)))h=h.slice(0,o.index)+"++"+h.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex);while(e)if(l||(a=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((function(n){return!!(i=n.call({lexer:s},e,t))&&(e=e.substring(i.raw.length),t.push(i),!0)}))))if(i=this.tokenizer.escape(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.tag(e))e=e.substring(i.raw.length),n=t[t.length-1],n&&"text"===i.type&&"text"===n.type?(n.raw+=i.raw,n.text+=i.text):t.push(i);else if(i=this.tokenizer.link(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(i.raw.length),n=t[t.length-1],n&&"text"===i.type&&"text"===n.type?(n.raw+=i.raw,n.text+=i.text):t.push(i);else if(i=this.tokenizer.emStrong(e,h,a))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.codespan(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.br(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.del(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.autolink(e,H))e=e.substring(i.raw.length),t.push(i);else if(this.state.inLink||!(i=this.tokenizer.url(e,H))){if(r=e,this.options.extensions&&this.options.extensions.startInline&&function(){var t=1/0,i=e.slice(1),n=void 0;s.options.extensions.startInline.forEach((function(e){n=e.call({lexer:this},i),"number"===typeof n&&n>=0&&(t=Math.min(t,n))})),t<1/0&&t>=0&&(r=e.substring(0,t+1))}(),i=this.tokenizer.inlineText(r,_))e=e.substring(i.raw.length),"_"!==i.raw.slice(-1)&&(a=i.raw.slice(-1)),l=!0,n=t[t.length-1],n&&"text"===n.type?(n.raw+=i.raw,n.text+=i.text):t.push(i);else if(e){var u="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(u);break}throw new Error(u)}}else e=e.substring(i.raw.length),t.push(i);return t},i(t,null,[{key:"rules",get:function(){return{block:P,inline:z}}}]),t}(),V=function(){function t(t){this.options=t||e.defaults}var i=t.prototype;return i.code=function(e,t,i){var n=(t||"").match(/\S*/)[0];if(this.options.highlight){var r=this.options.highlight(e,n);null!=r&&r!==e&&(i=!0,e=r)}return e=e.replace(/\n$/,"")+"\n",n?'<pre><code class="'+this.options.langPrefix+p(n,!0)+'">'+(i?e:p(e,!0))+"</code></pre>\n":"<pre><code>"+(i?e:p(e,!0))+"</code></pre>\n"},i.blockquote=function(e){return"<blockquote>\n"+e+"</blockquote>\n"},i.html=function(e){return e},i.heading=function(e,t,i,n){if(this.options.headerIds){var r=this.options.headerPrefix+n.slug(i);return"<h"+t+' id="'+r+'">'+e+"</h"+t+">\n"}return"<h"+t+">"+e+"</h"+t+">\n"},i.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},i.list=function(e,t,i){var n=t?"ol":"ul",r=t&&1!==i?' start="'+i+'"':"";return"<"+n+r+">\n"+e+"</"+n+">\n"},i.listitem=function(e){return"<li>"+e+"</li>\n"},i.checkbox=function(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},i.paragraph=function(e){return"<p>"+e+"</p>\n"},i.table=function(e,t){return t&&(t="<tbody>"+t+"</tbody>"),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"},i.tablerow=function(e){return"<tr>\n"+e+"</tr>\n"},i.tablecell=function(e,t){var i=t.header?"th":"td",n=t.align?"<"+i+' align="'+t.align+'">':"<"+i+">";return n+e+"</"+i+">\n"},i.strong=function(e){return"<strong>"+e+"</strong>"},i.em=function(e){return"<em>"+e+"</em>"},i.codespan=function(e){return"<code>"+e+"</code>"},i.br=function(){return this.options.xhtml?"<br/>":"<br>"},i.del=function(e){return"<del>"+e+"</del>"},i.link=function(e,t,i){if(e=y(this.options.sanitize,this.options.baseUrl,e),null===e)return i;var n='<a href="'+p(e)+'"';return t&&(n+=' title="'+t+'"'),n+=">"+i+"</a>",n},i.image=function(e,t,i){if(e=y(this.options.sanitize,this.options.baseUrl,e),null===e)return i;var n='<img src="'+e+'" alt="'+i+'"';return t&&(n+=' title="'+t+'"'),n+=this.options.xhtml?"/>":">",n},i.text=function(e){return e},t}(),W=function(){function e(){}var t=e.prototype;return t.strong=function(e){return e},t.em=function(e){return e},t.codespan=function(e){return e},t.del=function(e){return e},t.html=function(e){return e},t.text=function(e){return e},t.link=function(e,t,i){return""+i},t.image=function(e,t,i){return""+i},t.br=function(){return""},e}(),j=function(){function e(){this.seen={}}var t=e.prototype;return t.serialize=function(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")},t.getNextSafeSlug=function(e,t){var i=e,n=0;if(this.seen.hasOwnProperty(i)){n=this.seen[e];do{n++,i=e+"-"+n}while(this.seen.hasOwnProperty(i))}return t||(this.seen[e]=n,this.seen[i]=0),i},t.slug=function(e,t){void 0===t&&(t={});var i=this.serialize(e);return this.getNextSafeSlug(i,t.dryrun)},e}(),$=function(){function t(t){this.options=t||e.defaults,this.options.renderer=this.options.renderer||new V,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new W,this.slugger=new j}t.parse=function(e,i){var n=new t(i);return n.parse(e)},t.parseInline=function(e,i){var n=new t(i);return n.parseInline(e)};var i=t.prototype;return i.parse=function(e,t){void 0===t&&(t=!0);var i,n,r,s,o,l,a,h,c,u,d,f,p,m,v,x,w,b,y,k="",D=e.length;for(i=0;i<D;i++)if(u=e[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[u.type]&&(y=this.options.extensions.renderers[u.type].call({parser:this},u),!1!==y||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(u.type)))k+=y||"";else switch(u.type){case"space":continue;case"hr":k+=this.renderer.hr();continue;case"heading":k+=this.renderer.heading(this.parseInline(u.tokens),u.depth,g(this.parseInline(u.tokens,this.textRenderer)),this.slugger);continue;case"code":k+=this.renderer.code(u.text,u.lang,u.escaped);continue;case"table":for(h="",a="",s=u.header.length,n=0;n<s;n++)a+=this.renderer.tablecell(this.parseInline(u.header[n].tokens),{header:!0,align:u.align[n]});for(h+=this.renderer.tablerow(a),c="",s=u.rows.length,n=0;n<s;n++){for(l=u.rows[n],a="",o=l.length,r=0;r<o;r++)a+=this.renderer.tablecell(this.parseInline(l[r].tokens),{header:!1,align:u.align[r]});c+=this.renderer.tablerow(a)}k+=this.renderer.table(h,c);continue;case"blockquote":c=this.parse(u.tokens),k+=this.renderer.blockquote(c);continue;case"list":for(d=u.ordered,f=u.start,p=u.loose,s=u.items.length,c="",n=0;n<s;n++)v=u.items[n],x=v.checked,w=v.task,m="",v.task&&(b=this.renderer.checkbox(x),p?v.tokens.length>0&&"paragraph"===v.tokens[0].type?(v.tokens[0].text=b+" "+v.tokens[0].text,v.tokens[0].tokens&&v.tokens[0].tokens.length>0&&"text"===v.tokens[0].tokens[0].type&&(v.tokens[0].tokens[0].text=b+" "+v.tokens[0].tokens[0].text)):v.tokens.unshift({type:"text",text:b}):m+=b),m+=this.parse(v.tokens,p),c+=this.renderer.listitem(m,w,x);k+=this.renderer.list(c,d,f);continue;case"html":k+=this.renderer.html(u.text);continue;case"paragraph":k+=this.renderer.paragraph(this.parseInline(u.tokens));continue;case"text":c=u.tokens?this.parseInline(u.tokens):u.text;while(i+1<D&&"text"===e[i+1].type)u=e[++i],c+="\n"+(u.tokens?this.parseInline(u.tokens):u.text);k+=t?this.renderer.paragraph(c):c;continue;default:var C='Token with "'+u.type+'" type was not found.';if(this.options.silent)return void console.error(C);throw new Error(C)}return k},i.parseInline=function(e,t){t=t||this.renderer;var i,n,r,s="",o=e.length;for(i=0;i<o;i++)if(n=e[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[n.type]&&(r=this.options.extensions.renderers[n.type].call({parser:this},n),!1!==r||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(n.type)))s+=r||"";else switch(n.type){case"escape":s+=t.text(n.text);break;case"html":s+=t.html(n.text);break;case"link":s+=t.link(n.href,n.title,this.parseInline(n.tokens,t));break;case"image":s+=t.image(n.href,n.title,n.text);break;case"strong":s+=t.strong(this.parseInline(n.tokens,t));break;case"em":s+=t.em(this.parseInline(n.tokens,t));break;case"codespan":s+=t.codespan(n.text);break;case"br":s+=t.br();break;case"del":s+=t.del(this.parseInline(n.tokens,t));break;case"text":s+=t.text(n.text);break;default:var l='Token with "'+n.type+'" type was not found.';if(this.options.silent)return void console.error(l);throw new Error(l)}return s},t}();function U(e,t,i){if("undefined"===typeof e||null===e)throw new Error("marked(): input parameter is undefined or null");if("string"!==typeof e)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");if("function"===typeof t&&(i=t,t=null),t=M({},U.defaults,t||{}),O(t),i){var n,r=t.highlight;try{n=q.lex(e,t)}catch(a){return i(a)}var s=function(e){var s;if(!e)try{t.walkTokens&&U.walkTokens(n,t.walkTokens),s=$.parse(n,t)}catch(a){e=a}return t.highlight=r,e?i(e):i(null,s)};if(!r||r.length<3)return s();if(delete t.highlight,!n.length)return s();var o=0;return U.walkTokens(n,(function(e){"code"===e.type&&(o++,setTimeout((function(){r(e.text,e.lang,(function(t,i){if(t)return s(t);null!=i&&i!==e.text&&(e.text=i,e.escaped=!0),o--,0===o&&s()}))}),0))})),void(0===o&&s())}try{var l=q.lex(e,t);return t.walkTokens&&U.walkTokens(l,t.walkTokens),$.parse(l,t)}catch(a){if(a.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+p(a.message+"",!0)+"</pre>";throw a}}U.options=U.setOptions=function(e){return M(U.defaults,e),l(U.defaults),U},U.getDefaults=o,U.defaults=e.defaults,U.use=function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var n,r=M.apply(void 0,[{}].concat(t)),s=U.defaults.extensions||{renderers:{},childTokens:{}};t.forEach((function(e){if(e.extensions&&(n=!0,e.extensions.forEach((function(e){if(!e.name)throw new Error("extension name required");if(e.renderer){var t=s.renderers?s.renderers[e.name]:null;s.renderers[e.name]=t?function(){for(var i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];var s=e.renderer.apply(this,n);return!1===s&&(s=t.apply(this,n)),s}:e.renderer}if(e.tokenizer){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");s[e.level]?s[e.level].unshift(e.tokenizer):s[e.level]=[e.tokenizer],e.start&&("block"===e.level?s.startBlock?s.startBlock.push(e.start):s.startBlock=[e.start]:"inline"===e.level&&(s.startInline?s.startInline.push(e.start):s.startInline=[e.start]))}e.childTokens&&(s.childTokens[e.name]=e.childTokens)}))),e.renderer&&function(){var t=U.defaults.renderer||new V,i=function(i){var n=t[i];t[i]=function(){for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];var l=e.renderer[i].apply(t,s);return!1===l&&(l=n.apply(t,s)),l}};for(var n in e.renderer)i(n);r.renderer=t}(),e.tokenizer&&function(){var t=U.defaults.tokenizer||new N,i=function(i){var n=t[i];t[i]=function(){for(var r=arguments.length,s=new Array(r),o=0;o<r;o++)s[o]=arguments[o];var l=e.tokenizer[i].apply(t,s);return!1===l&&(l=n.apply(t,s)),l}};for(var n in e.tokenizer)i(n);r.tokenizer=t}(),e.walkTokens){var t=U.defaults.walkTokens;r.walkTokens=function(i){e.walkTokens.call(this,i),t&&t.call(this,i)}}n&&(r.extensions=s),U.setOptions(r)}))},U.walkTokens=function(e,t){for(var i,n=function(){var e=i.value;switch(t.call(U,e),e.type){case"table":for(var n,r=s(e.header);!(n=r()).done;){var o=n.value;U.walkTokens(o.tokens,t)}for(var l,a=s(e.rows);!(l=a()).done;)for(var h,c=l.value,u=s(c);!(h=u()).done;){var d=h.value;U.walkTokens(d.tokens,t)}break;case"list":U.walkTokens(e.items,t);break;default:U.defaults.extensions&&U.defaults.extensions.childTokens&&U.defaults.extensions.childTokens[e.type]?U.defaults.extensions.childTokens[e.type].forEach((function(i){U.walkTokens(e[i],t)})):e.tokens&&U.walkTokens(e.tokens,t)}},r=s(e);!(i=r()).done;)n()},U.parseInline=function(e,t){if("undefined"===typeof e||null===e)throw new Error("marked.parseInline(): input parameter is undefined or null");if("string"!==typeof e)throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");t=M({},U.defaults,t||{}),O(t);try{var i=q.lexInline(e,t);return t.walkTokens&&U.walkTokens(i,t.walkTokens),$.parseInline(i,t)}catch(n){if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+p(n.message+"",!0)+"</pre>";throw n}},U.Parser=$,U.parser=$.parse,U.Renderer=V,U.TextRenderer=W,U.Lexer=q,U.lexer=q.lex,U.Tokenizer=N,U.Slugger=j,U.parse=U;var G=U.options,K=U.setOptions,J=U.use,X=U.walkTokens,Z=U.parseInline,Q=U,Y=$.parse,ee=q.lex;e.Lexer=q,e.Parser=$,e.Renderer=V,e.Slugger=j,e.TextRenderer=W,e.Tokenizer=N,e.getDefaults=o,e.lexer=ee,e.marked=U,e.options=G,e.parse=Q,e.parseInline=Z,e.parser=Y,e.setOptions=K,e.use=J,e.walkTokens=X,Object.defineProperty(e,"__esModule",{value:!0})}))},"959b":function(e,t,i){(function(e){e(i("56b3"),i("d5e0"),i("f040"))})((function(e){"use strict";e.defineMode("markdown",(function(t,i){var n=e.getMode(t,"text/html"),r="null"==n.name;function s(i){if(e.findModeByName){var n=e.findModeByName(i);n&&(i=n.mime||n.mimes[0])}var r=e.getMode(t,i);return"null"==r.name?null:r}void 0===i.highlightFormatting&&(i.highlightFormatting=!1),void 0===i.maxBlockquoteDepth&&(i.maxBlockquoteDepth=0),void 0===i.taskLists&&(i.taskLists=!1),void 0===i.strikethrough&&(i.strikethrough=!1),void 0===i.tokenTypeOverrides&&(i.tokenTypeOverrides={});var o={header:"header",code:"comment",quote:"quote",list1:"variable-2",list2:"variable-3",list3:"keyword",hr:"hr",image:"image",imageAltText:"image-alt-text",imageMarker:"image-marker",formatting:"formatting",linkInline:"link",linkEmail:"link",linkText:"link",linkHref:"string",em:"em",strong:"strong",strikethrough:"strikethrough"};for(var l in o)o.hasOwnProperty(l)&&i.tokenTypeOverrides[l]&&(o[l]=i.tokenTypeOverrides[l]);var a=/^([*\-_])(?:\s*\1){2,}\s*$/,h=/^(?:[*\-+]|^[0-9]+([.)]))\s+/,c=/^\[(x| )\](?=\s)/,u=i.allowAtxHeaderWithoutSpace?/^(#+)/:/^(#+)(?: |$)/,d=/^ *(?:\={1,}|-{1,})\s*$/,f=/^[^#!\[\]*_\\<>` "'(~]+/,p=new RegExp("^("+(!0===i.fencedCodeBlocks?"~~~+|```+":i.fencedCodeBlocks)+")[ \\t]*([\\w+#-]*)"),m=/[!\"#$%&\'()*+,\-\.\/:;<=>?@\[\\\]^_`{|}~—]/;function g(e,t,i){return t.f=t.inline=i,i(e,t)}function v(e,t,i){return t.f=t.block=i,i(e,t)}function x(e){return!e||!/\S/.test(e.string)}function w(e){return e.linkTitle=!1,e.em=!1,e.strong=!1,e.strikethrough=!1,e.quote=0,e.indentedCode=!1,e.f==y&&(e.f=S,e.block=b),e.trailingSpace=0,e.trailingSpaceNewLine=!1,e.prevLine=e.thisLine,e.thisLine=null,null}function b(t,n){var r=t.sol(),l=!1!==n.list,f=n.indentedCode;n.indentedCode=!1,l&&(n.indentationDiff>=0?(n.indentationDiff<4&&(n.indentation-=n.indentationDiff),n.list=null):n.indentation>0?n.list=null:n.list=!1);var m=null;if(n.indentationDiff>=4)return t.skipToEnd(),f||x(n.prevLine)?(n.indentation-=4,n.indentedCode=!0,o.code):null;if(t.eatSpace())return null;if((m=t.match(u))&&m[1].length<=6)return n.header=m[1].length,i.highlightFormatting&&(n.formatting="header"),n.f=n.inline,D(n);if(!(x(n.prevLine)||n.quote||l||f)&&(m=t.match(d)))return n.header="="==m[0].charAt(0)?1:2,i.highlightFormatting&&(n.formatting="header"),n.f=n.inline,D(n);if(t.eat(">"))return n.quote=r?1:n.quote+1,i.highlightFormatting&&(n.formatting="quote"),t.eatSpace(),D(n);if("["===t.peek())return g(t,n,F);if(t.match(a,!0))return n.hr=!0,o.hr;if(m=t.match(h)){var v=m[1]?"ol":"ul";n.indentation=t.column()+t.current().length,n.list=!0;while(n.listStack&&t.column()<n.listStack[n.listStack.length-1])n.listStack.pop();return n.listStack.push(n.indentation),i.taskLists&&t.match(c,!1)&&(n.taskList=!0),n.f=n.inline,i.highlightFormatting&&(n.formatting=["list","list-"+v]),D(n)}return i.fencedCodeBlocks&&(m=t.match(p,!0))?(n.fencedChars=m[1],n.localMode=s(m[2]),n.localMode&&(n.localState=e.startState(n.localMode)),n.f=n.block=k,i.highlightFormatting&&(n.formatting="code-block"),n.code=-1,D(n)):g(t,n,n.inline)}function y(t,i){var s=n.token(t,i.htmlState);if(!r){var o=e.innerMode(n,i.htmlState);("xml"==o.mode.name&&null===o.state.tagStart&&!o.state.context&&o.state.tokenize.isInText||i.md_inside&&t.current().indexOf(">")>-1)&&(i.f=S,i.block=b,i.htmlState=null)}return s}function k(e,t){if(t.fencedChars&&e.match(t.fencedChars)){i.highlightFormatting&&(t.formatting="code-block");var n=D(t);return t.localMode=t.localState=null,t.block=b,t.f=S,t.fencedChars=null,t.code=0,n}return t.fencedChars&&e.skipTo(t.fencedChars)?"comment":t.localMode?t.localMode.token(e,t.localState):(e.skipToEnd(),o.code)}function D(e){var t=[];if(e.formatting){t.push(o.formatting),"string"===typeof e.formatting&&(e.formatting=[e.formatting]);for(var n=0;n<e.formatting.length;n++)t.push(o.formatting+"-"+e.formatting[n]),"header"===e.formatting[n]&&t.push(o.formatting+"-"+e.formatting[n]+"-"+e.header),"quote"===e.formatting[n]&&(!i.maxBlockquoteDepth||i.maxBlockquoteDepth>=e.quote?t.push(o.formatting+"-"+e.formatting[n]+"-"+e.quote):t.push("error"))}if(e.taskOpen)return t.push("meta"),t.length?t.join(" "):null;if(e.taskClosed)return t.push("property"),t.length?t.join(" "):null;if(e.linkHref?t.push(o.linkHref,"url"):(e.strong&&t.push(o.strong),e.em&&t.push(o.em),e.strikethrough&&t.push(o.strikethrough),e.linkText&&t.push(o.linkText),e.code&&t.push(o.code),e.image&&t.push(o.image),e.imageAltText&&t.push(o.imageAltText,"link"),e.imageMarker&&t.push(o.imageMarker)),e.header&&t.push(o.header,o.header+"-"+e.header),e.quote&&(t.push(o.quote),!i.maxBlockquoteDepth||i.maxBlockquoteDepth>=e.quote?t.push(o.quote+"-"+e.quote):t.push(o.quote+"-"+i.maxBlockquoteDepth)),!1!==e.list){var r=(e.listStack.length-1)%3;r?1===r?t.push(o.list2):t.push(o.list3):t.push(o.list1)}return e.trailingSpaceNewLine?t.push("trailing-space-new-line"):e.trailingSpace&&t.push("trailing-space-"+(e.trailingSpace%2?"a":"b")),t.length?t.join(" "):null}function C(e,t){if(e.match(f,!0))return D(t)}function S(t,r){var s=r.text(t,r);if("undefined"!==typeof s)return s;if(r.list)return r.list=null,D(r);if(r.taskList){var l="x"!==t.match(c,!0)[1];return l?r.taskOpen=!0:r.taskClosed=!0,i.highlightFormatting&&(r.formatting="task"),r.taskList=!1,D(r)}if(r.taskOpen=!1,r.taskClosed=!1,r.header&&t.match(/^#+$/,!0))return i.highlightFormatting&&(r.formatting="header"),D(r);var a=t.next();if(r.linkTitle){r.linkTitle=!1;var h=a;"("===a&&(h=")"),h=(h+"").replace(/([.?*+^\[\]\\(){}|-])/g,"\\$1");var u="^\\s*(?:[^"+h+"\\\\]+|\\\\\\\\|\\\\.)"+h;if(t.match(new RegExp(u),!0))return o.linkHref}if("`"===a){var d=r.formatting;i.highlightFormatting&&(r.formatting="code"),t.eatWhile("`");var f=t.current().length;if(0==r.code)return r.code=f,D(r);if(f==r.code){var p=D(r);return r.code=0,p}return r.formatting=d,D(r)}if(r.code)return D(r);if("\\"===a&&(t.next(),i.highlightFormatting)){var g=D(r),x=o.formatting+"-escape";return g?g+" "+x:x}if("!"===a&&t.match(/\[[^\]]*\] ?(?:\(|\[)/,!1))return r.imageMarker=!0,r.image=!0,i.highlightFormatting&&(r.formatting="image"),D(r);if("["===a&&r.imageMarker&&t.match(/[^\]]*\](\(.*?\)| ?\[.*?\])/,!1))return r.imageMarker=!1,r.imageAltText=!0,i.highlightFormatting&&(r.formatting="image"),D(r);if("]"===a&&r.imageAltText){i.highlightFormatting&&(r.formatting="image");g=D(r);return r.imageAltText=!1,r.image=!1,r.inline=r.f=E,g}if("["===a&&!r.image)return r.linkText=!0,i.highlightFormatting&&(r.formatting="link"),D(r);if("]"===a&&r.linkText){i.highlightFormatting&&(r.formatting="link");g=D(r);return r.linkText=!1,r.inline=r.f=t.match(/\(.*?\)| ?\[.*?\]/,!1)?E:S,g}if("<"===a&&t.match(/^(https?|ftps?):\/\/(?:[^\\>]|\\.)+>/,!1)){r.f=r.inline=A,i.highlightFormatting&&(r.formatting="link");g=D(r);return g?g+=" ":g="",g+o.linkInline}if("<"===a&&t.match(/^[^> \\]+@(?:[^\\>]|\\.)+>/,!1)){r.f=r.inline=A,i.highlightFormatting&&(r.formatting="link");g=D(r);return g?g+=" ":g="",g+o.linkEmail}if("<"===a&&t.match(/^(!--|[a-z]+(?:\s+[a-z_:.\-]+(?:\s*=\s*[^ >]+)?)*\s*>)/i,!1)){var w=t.string.indexOf(">",t.pos);if(-1!=w){var b=t.string.substring(t.start,w);/markdown\s*=\s*('|"){0,1}1('|"){0,1}/.test(b)&&(r.md_inside=!0)}return t.backUp(1),r.htmlState=e.startState(n),v(t,r,y)}if("<"===a&&t.match(/^\/\w*?>/))return r.md_inside=!1,"tag";if("*"===a||"_"===a){var k=1,C=1==t.pos?" ":t.string.charAt(t.pos-2);while(k<3&&t.eat(a))k++;var M=t.peek()||" ",B=!/\s/.test(M)&&(!m.test(M)||/\s/.test(C)||m.test(C)),F=!/\s/.test(C)&&(!m.test(C)||/\s/.test(M)||m.test(M)),T=null,O=null;if(k%2&&(r.em||!B||"*"!==a&&F&&!m.test(C)?r.em!=a||!F||"*"!==a&&B&&!m.test(M)||(T=!1):T=!0),k>1&&(r.strong||!B||"*"!==a&&F&&!m.test(C)?r.strong!=a||!F||"*"!==a&&B&&!m.test(M)||(O=!1):O=!0),null!=O||null!=T){i.highlightFormatting&&(r.formatting=null==T?"strong":null==O?"em":"strong em"),!0===T&&(r.em=a),!0===O&&(r.strong=a);p=D(r);return!1===T&&(r.em=!1),!1===O&&(r.strong=!1),p}}else if(" "===a&&(t.eat("*")||t.eat("_"))){if(" "===t.peek())return D(r);t.backUp(1)}if(i.strikethrough)if("~"===a&&t.eatWhile(a)){if(r.strikethrough){i.highlightFormatting&&(r.formatting="strikethrough");p=D(r);return r.strikethrough=!1,p}if(t.match(/^[^\s]/,!1))return r.strikethrough=!0,i.highlightFormatting&&(r.formatting="strikethrough"),D(r)}else if(" "===a&&t.match(/^~~/,!0)){if(" "===t.peek())return D(r);t.backUp(2)}return" "===a&&(t.match(/ +$/,!1)?r.trailingSpace++:r.trailingSpace&&(r.trailingSpaceNewLine=!0)),D(r)}function A(e,t){var n=e.next();if(">"===n){t.f=t.inline=S,i.highlightFormatting&&(t.formatting="link");var r=D(t);return r?r+=" ":r="",r+o.linkInline}return e.match(/^[^>]+/,!0),o.linkInline}function E(e,t){if(e.eatSpace())return null;var n=e.next();return"("===n||"["===n?(t.f=t.inline=B("("===n?")":"]"),i.highlightFormatting&&(t.formatting="link-string"),t.linkHref=!0,D(t)):"error"}var M={")":/^(?:[^\\\(\)]|\\.|\((?:[^\\\(\)]|\\.)*\))*?(?=\))/,"]":/^(?:[^\\\[\]]|\\.|\[(?:[^\\\[\]]|\\.)*\])*?(?=\])/};function B(e){return function(t,n){var r=t.next();if(r===e){n.f=n.inline=S,i.highlightFormatting&&(n.formatting="link-string");var s=D(n);return n.linkHref=!1,s}return t.match(M[e]),n.linkHref=!0,D(n)}}function F(e,t){return e.match(/^([^\]\\]|\\.)*\]:/,!1)?(t.f=T,e.next(),i.highlightFormatting&&(t.formatting="link"),t.linkText=!0,D(t)):g(e,t,S)}function T(e,t){if(e.match(/^\]:/,!0)){t.f=t.inline=O,i.highlightFormatting&&(t.formatting="link");var n=D(t);return t.linkText=!1,n}return e.match(/^([^\]\\]|\\.)+/,!0),o.linkText}function O(e,t){return e.eatSpace()?null:(e.match(/^[^\s]+/,!0),void 0===e.peek()?t.linkTitle=!0:e.match(/^(?:\s+(?:"(?:[^"\\]|\\\\|\\.)+"|'(?:[^'\\]|\\\\|\\.)+'|\((?:[^)\\]|\\\\|\\.)+\)))?/,!0),t.f=t.inline=S,o.linkHref+" url")}var L={startState:function(){return{f:b,prevLine:null,thisLine:null,block:b,htmlState:null,indentation:0,inline:S,text:C,formatting:!1,linkText:!1,linkHref:!1,linkTitle:!1,code:0,em:!1,strong:!1,header:0,hr:!1,taskList:!1,list:!1,listStack:[],quote:0,trailingSpace:0,trailingSpaceNewLine:!1,strikethrough:!1,fencedChars:null}},copyState:function(t){return{f:t.f,prevLine:t.prevLine,thisLine:t.thisLine,block:t.block,htmlState:t.htmlState&&e.copyState(n,t.htmlState),indentation:t.indentation,localMode:t.localMode,localState:t.localMode?e.copyState(t.localMode,t.localState):null,inline:t.inline,text:t.text,formatting:!1,linkText:t.linkText,linkTitle:t.linkTitle,code:t.code,em:t.em,strong:t.strong,strikethrough:t.strikethrough,header:t.header,hr:t.hr,taskList:t.taskList,list:t.list,listStack:t.listStack.slice(0),quote:t.quote,indentedCode:t.indentedCode,trailingSpace:t.trailingSpace,trailingSpaceNewLine:t.trailingSpaceNewLine,md_inside:t.md_inside,fencedChars:t.fencedChars}},token:function(e,t){if(t.formatting=!1,e!=t.thisLine){var i=t.header||t.hr;if(t.header=0,t.hr=!1,e.match(/^\s*$/,!0)||i){if(w(t),!i)return null;t.prevLine=null}t.prevLine=t.thisLine,t.thisLine=e,t.taskList=!1,t.trailingSpace=0,t.trailingSpaceNewLine=!1,t.f=t.block;var n=e.match(/^\s*/,!0)[0].replace(/\t/g,"    ").length;if(t.indentationDiff=Math.min(n-t.indentation,4),t.indentation=t.indentation+t.indentationDiff,n>0)return null}return t.f(e,t)},innerMode:function(e){return e.block==y?{state:e.htmlState,mode:n}:e.localState?{state:e.localState,mode:e.localMode}:{state:e,mode:L}},blankLine:w,getType:D,closeBrackets:"()[]{}''\"\"``",fold:"markdown"};return L}),"xml"),e.defineMIME("text/x-markdown","markdown")}))},9948:function(e,t,i){(function(e){e(i("56b3"))})((function(e){"use strict";function t(e){e.state.markedSelection&&e.operation((function(){h(e)}))}function i(e){e.state.markedSelection&&e.state.markedSelection.length&&e.operation((function(){l(e)}))}e.defineOption("styleSelectedText",!1,(function(n,r,s){var o=s&&s!=e.Init;r&&!o?(n.state.markedSelection=[],n.state.markedSelectionStyle="string"==typeof r?r:"CodeMirror-selectedtext",a(n),n.on("cursorActivity",t),n.on("change",i)):!r&&o&&(n.off("cursorActivity",t),n.off("change",i),l(n),n.state.markedSelection=n.state.markedSelectionStyle=null)}));var n=8,r=e.Pos,s=e.cmpPos;function o(e,t,i,o){if(0!=s(t,i))for(var l=e.state.markedSelection,a=e.state.markedSelectionStyle,h=t.line;;){var c=h==t.line?t:r(h,0),u=h+n,d=u>=i.line,f=d?i:r(u,0),p=e.markText(c,f,{className:a});if(null==o?l.push(p):l.splice(o++,0,p),d)break;h=u}}function l(e){for(var t=e.state.markedSelection,i=0;i<t.length;++i)t[i].clear();t.length=0}function a(e){l(e);for(var t=e.listSelections(),i=0;i<t.length;i++)o(e,t[i].from(),t[i].to())}function h(e){if(!e.somethingSelected())return l(e);if(e.listSelections().length>1)return a(e);var t=e.getCursor("start"),i=e.getCursor("end"),r=e.state.markedSelection;if(!r.length)return o(e,t,i);var h=r[0].find(),c=r[r.length-1].find();if(!h||!c||i.line-t.line<n||s(t,c.to)>=0||s(i,h.from)<=0)return a(e);while(s(t,h.from)>0)r.shift().clear(),h=r[0].find();s(t,h.from)<0&&(h.to.line-t.line<n?(r.shift().clear(),o(e,t,h.to,0)):o(e,t,h.from,0));while(s(i,c.to)<0)r.pop().clear(),c=r[r.length-1].find();s(i,c.to)>0&&(i.line-c.from.line<n?(r.pop().clear(),o(e,c.from,i)):o(e,c.to,i))}}))},"9eb9":function(e,t,i){(function(e){e(i("56b3"))})((function(e){"use strict";e.overlayMode=function(t,i,n){return{startState:function(){return{base:e.startState(t),overlay:e.startState(i),basePos:0,baseCur:null,overlayPos:0,overlayCur:null,streamSeen:null}},copyState:function(n){return{base:e.copyState(t,n.base),overlay:e.copyState(i,n.overlay),basePos:n.basePos,baseCur:null,overlayPos:n.overlayPos,overlayCur:null}},token:function(e,r){return(e!=r.streamSeen||Math.min(r.basePos,r.overlayPos)<e.start)&&(r.streamSeen=e,r.basePos=r.overlayPos=e.start),e.start==r.basePos&&(r.baseCur=t.token(e,r.base),r.basePos=e.pos),e.start==r.overlayPos&&(e.pos=e.start,r.overlayCur=i.token(e,r.overlay),r.overlayPos=e.pos),e.pos=Math.min(r.basePos,r.overlayPos),null==r.overlayCur?r.baseCur:null!=r.baseCur&&r.overlay.combineTokens||n&&null==r.overlay.combineTokens?r.baseCur+" "+r.overlayCur:r.overlayCur},indent:t.indent&&function(e,i){return t.indent(e.base,i)},electricChars:t.electricChars,innerMode:function(e){return{state:e.base,mode:t}},blankLine:function(e){var r,s;return t.blankLine&&(r=t.blankLine(e.base)),i.blankLine&&(s=i.blankLine(e.overlay)),null==s?r:n&&null!=r?r+" "+s:s}}}}))},a2f9:function(e,t,i){"use strict";i.r(t);class n{constructor(){}lineAt(e){if(e<0||e>this.length)throw new RangeError(`Invalid position ${e} in document of length ${this.length}`);return this.lineInner(e,!1,1,0)}line(e){if(e<1||e>this.lines)throw new RangeError(`Invalid line number ${e} in ${this.lines}-line document`);return this.lineInner(e,!0,1,0)}replace(e,t,i){let n=[];return this.decompose(0,e,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(t,this.length,n,1),s.from(n,this.length-(t-e)+i.length)}append(e){return this.replace(this.length,this.length,e)}slice(e,t=this.length){let i=[];return this.decompose(e,t,i,0),s.from(i,t-e)}eq(e){if(e==this)return!0;if(e.length!=this.length||e.lines!=this.lines)return!1;let t=this.scanIdentical(e,1),i=this.length-this.scanIdentical(e,-1),n=new h(this),r=new h(e);for(let s=t,o=t;;){if(n.next(s),r.next(s),s=0,n.lineBreak!=r.lineBreak||n.done!=r.done||n.value!=r.value)return!1;if(o+=n.value.length,n.done||o>=i)return!0}}iter(e=1){return new h(this,e)}iterRange(e,t=this.length){return new c(this,e,t)}iterLines(e,t){let i;if(null==e)i=this.iter();else{null==t&&(t=this.lines+1);let n=this.line(e).from;i=this.iterRange(n,Math.max(n,t==this.lines+1?this.length:t<=1?0:this.line(t-1).to))}return new u(i)}toString(){return this.sliceString(0)}toJSON(){let e=[];return this.flatten(e),e}static of(e){if(0==e.length)throw new RangeError("A document must have at least one line");return 1!=e.length||e[0]?e.length<=32?new r(e):s.from(r.split(e,[])):n.empty}}class r extends n{constructor(e,t=o(e)){super(),this.text=e,this.length=t}get lines(){return this.text.length}get children(){return null}lineInner(e,t,i,n){for(let r=0;;r++){let s=this.text[r],o=n+s.length;if((t?i:o)>=e)return new d(n,o,i,s);n=o+1,i++}}decompose(e,t,i,n){let s=e<=0&&t>=this.length?this:new r(a(this.text,e,t),Math.min(t,this.length)-Math.max(0,e));if(1&n){let e=i.pop(),t=l(s.text,e.text.slice(),0,s.length);if(t.length<=32)i.push(new r(t,e.length+s.length));else{let e=t.length>>1;i.push(new r(t.slice(0,e)),new r(t.slice(e)))}}else i.push(s)}replace(e,t,i){if(!(i instanceof r))return super.replace(e,t,i);let n=l(this.text,l(i.text,a(this.text,0,e)),t),o=this.length+i.length-(t-e);return n.length<=32?new r(n,o):s.from(r.split(n,[]),o)}sliceString(e,t=this.length,i="\n"){let n="";for(let r=0,s=0;r<=t&&s<this.text.length;s++){let o=this.text[s],l=r+o.length;r>e&&s&&(n+=i),e<l&&t>r&&(n+=o.slice(Math.max(0,e-r),t-r)),r=l+1}return n}flatten(e){for(let t of this.text)e.push(t)}scanIdentical(){return 0}static split(e,t){let i=[],n=-1;for(let s of e)i.push(s),n+=s.length+1,32==i.length&&(t.push(new r(i,n)),i=[],n=-1);return n>-1&&t.push(new r(i,n)),t}}class s extends n{constructor(e,t){super(),this.children=e,this.length=t,this.lines=0;for(let i of e)this.lines+=i.lines}lineInner(e,t,i,n){for(let r=0;;r++){let s=this.children[r],o=n+s.length,l=i+s.lines-1;if((t?l:o)>=e)return s.lineInner(e,t,i,n);n=o+1,i=l+1}}decompose(e,t,i,n){for(let r=0,s=0;s<=t&&r<this.children.length;r++){let o=this.children[r],l=s+o.length;if(e<=l&&t>=s){let r=n&((s<=e?1:0)|(l>=t?2:0));s>=e&&l<=t&&!r?i.push(o):o.decompose(e-s,t-s,i,r)}s=l+1}}replace(e,t,i){if(i.lines<this.lines)for(let n=0,r=0;n<this.children.length;n++){let o=this.children[n],l=r+o.length;if(e>=r&&t<=l){let a=o.replace(e-r,t-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>4&&a.lines>h>>6){let r=this.children.slice();return r[n]=a,new s(r,this.length-(t-e)+i.length)}return super.replace(r,l,a)}r=l+1}return super.replace(e,t,i)}sliceString(e,t=this.length,i="\n"){let n="";for(let r=0,s=0;r<this.children.length&&s<=t;r++){let o=this.children[r],l=s+o.length;s>e&&r&&(n+=i),e<l&&t>s&&(n+=o.sliceString(e-s,t-s,i)),s=l+1}return n}flatten(e){for(let t of this.children)t.flatten(e)}scanIdentical(e,t){if(!(e instanceof s))return 0;let i=0,[n,r,o,l]=t>0?[0,0,this.children.length,e.children.length]:[this.children.length-1,e.children.length-1,-1,-1];for(;;n+=t,r+=t){if(n==o||r==l)return i;let s=this.children[n],a=e.children[r];if(s!=a)return i+s.scanIdentical(a,t);i+=s.length+1}}static from(e,t=e.reduce((e,t)=>e+t.length+1,-1)){let i=0;for(let r of e)i+=r.lines;if(i<32){let i=[];for(let t of e)t.flatten(i);return new r(i,t)}let n=Math.max(32,i>>5),o=n<<1,l=n>>1,a=[],h=0,c=-1,u=[];function d(e){let t;if(e.lines>o&&e instanceof s)for(let i of e.children)d(i);else e.lines>l&&(h>l||!h)?(f(),a.push(e)):e instanceof r&&h&&(t=u[u.length-1])instanceof r&&e.lines+t.lines<=32?(h+=e.lines,c+=e.length+1,u[u.length-1]=new r(t.text.concat(e.text),t.length+1+e.length)):(h+e.lines>n&&f(),h+=e.lines,c+=e.length+1,u.push(e))}function f(){0!=h&&(a.push(1==u.length?u[0]:s.from(u,c)),c=-1,h=u.length=0)}for(let r of e)d(r);return f(),1==a.length?a[0]:new s(a,t)}}function o(e){let t=-1;for(let i of e)t+=i.length+1;return t}function l(e,t,i=0,n=1e9){for(let r=0,s=0,o=!0;s<e.length&&r<=n;s++){let l=e[s],a=r+l.length;a>=i&&(a>n&&(l=l.slice(0,n-r)),r<i&&(l=l.slice(i-r)),o?(t[t.length-1]+=l,o=!1):t.push(l)),r=a+1}return t}function a(e,t,i){return l(e,[""],t,i)}n.empty=new r([""],0);class h{constructor(e,t=1){this.dir=t,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[e],this.offsets=[t>0?1:(e instanceof r?e.text.length:e.children.length)<<1]}nextInner(e,t){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],s=this.offsets[i],o=s>>1,l=n instanceof r?n.text.length:n.children.length;if(o==(t>0?l:0)){if(0==i)return this.done=!0,this.value="",this;t>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((1&s)==(t>0?0:1)){if(this.offsets[i]+=t,0==e)return this.lineBreak=!0,this.value="\n",this;e--}else if(n instanceof r){let r=n.text[o+(t<0?-1:0)];if(this.offsets[i]+=t,r.length>Math.max(0,e))return this.value=0==e?r:t>0?r.slice(e):r.slice(0,r.length-e),this;e-=r.length}else{let s=n.children[o+(t<0?-1:0)];e>s.length?(e-=s.length,this.offsets[i]+=t):(t<0&&this.offsets[i]--,this.nodes.push(s),this.offsets.push(t>0?1:(s instanceof r?s.text.length:s.children.length)<<1))}}}next(e=0){return e<0&&(this.nextInner(-e,-this.dir),e=this.value.length),this.nextInner(e,this.dir)}}class c{constructor(e,t,i){this.value="",this.done=!1,this.cursor=new h(e,t>i?-1:1),this.pos=t>i?e.length:0,this.from=Math.min(t,i),this.to=Math.max(t,i)}nextInner(e,t){if(t<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;e+=Math.max(0,t<0?this.pos-this.to:this.from-this.pos);let i=t<0?this.pos-this.from:this.to-this.pos;e>i&&(e=i),i-=e;let{value:n}=this.cursor.next(e);return this.pos+=(n.length+e)*t,this.value=n.length<=i?n:t<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(e=0){return e<0?e=Math.max(e,this.from-this.pos):e>0&&(e=Math.min(e,this.to-this.pos)),this.nextInner(e,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&""!=this.value}}class u{constructor(e){this.inner=e,this.afterBreak=!0,this.value="",this.done=!1}next(e=0){let{done:t,lineBreak:i,value:n}=this.inner.next(e);return t?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}"undefined"!=typeof Symbol&&(n.prototype[Symbol.iterator]=function(){return this.iter()},h.prototype[Symbol.iterator]=c.prototype[Symbol.iterator]=u.prototype[Symbol.iterator]=function(){return this});class d{constructor(e,t,i,n){this.from=e,this.to=t,this.number=i,this.text=n}get length(){return this.to-this.from}}let f="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(e=>e?parseInt(e,36):1);for(let xm=1;xm<f.length;xm++)f[xm]+=f[xm-1];function p(e){for(let t=1;t<f.length;t+=2)if(f[t]>e)return f[t-1]<=e;return!1}function m(e){return e>=127462&&e<=127487}const g=8205;function v(e,t,i=!0,n=!0){return(i?x:w)(e,t,n)}function x(e,t,i){if(t==e.length)return t;t&&b(e.charCodeAt(t))&&y(e.charCodeAt(t-1))&&t--;let n=k(e,t);t+=C(n);while(t<e.length){let r=k(e,t);if(n==g||r==g||i&&p(r))t+=C(r),n=r;else{if(!m(r))break;{let i=0,n=t-2;while(n>=0&&m(k(e,n)))i++,n-=2;if(i%2==0)break;t+=2}}}return t}function w(e,t,i){while(t>0){let n=x(e,t-2,i);if(n<t)return n;t--}return 0}function b(e){return e>=56320&&e<57344}function y(e){return e>=55296&&e<56320}function k(e,t){let i=e.charCodeAt(t);if(!y(i)||t+1==e.length)return i;let n=e.charCodeAt(t+1);return b(n)?n-56320+(i-55296<<10)+65536:i}function D(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}function C(e){return e<65536?1:2}const S=/\r\n?|\n/;var A=function(e){return e[e["Simple"]=0]="Simple",e[e["TrackDel"]=1]="TrackDel",e[e["TrackBefore"]=2]="TrackBefore",e[e["TrackAfter"]=3]="TrackAfter",e}(A||(A={}));class E{constructor(e){this.sections=e}get length(){let e=0;for(let t=0;t<this.sections.length;t+=2)e+=this.sections[t];return e}get newLength(){let e=0;for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t+1];e+=i<0?this.sections[t]:i}return e}get empty(){return 0==this.sections.length||2==this.sections.length&&this.sections[1]<0}iterGaps(e){for(let t=0,i=0,n=0;t<this.sections.length;){let r=this.sections[t++],s=this.sections[t++];s<0?(e(i,n,r),n+=r):n+=s,i+=r}}iterChangedRanges(e,t=!1){T(this,e,t)}get invertedDesc(){let e=[];for(let t=0;t<this.sections.length;){let i=this.sections[t++],n=this.sections[t++];n<0?e.push(i,n):e.push(n,i)}return new E(e)}composeDesc(e){return this.empty?e:e.empty?this:L(this,e)}mapDesc(e,t=!1){return e.empty?this:O(this,e,t)}mapPos(e,t=-1,i=A.Simple){let n=0,r=0;for(let s=0;s<this.sections.length;){let o=this.sections[s++],l=this.sections[s++],a=n+o;if(l<0){if(a>e)return r+(e-n);r+=o}else{if(i!=A.Simple&&a>=e&&(i==A.TrackDel&&n<e&&a>e||i==A.TrackBefore&&n<e||i==A.TrackAfter&&a>e))return null;if(a>e||a==e&&t<0&&!o)return e==n||t<0?r:r+l;r+=l}n=a}if(e>n)throw new RangeError(`Position ${e} is out of range for changeset of length ${n}`);return r}touchesRange(e,t=e){for(let i=0,n=0;i<this.sections.length&&n<=t;){let r=this.sections[i++],s=this.sections[i++],o=n+r;if(s>=0&&n<=t&&o>=e)return!(n<e&&o>t)||"cover";n=o}return!1}toString(){let e="";for(let t=0;t<this.sections.length;){let i=this.sections[t++],n=this.sections[t++];e+=(e?" ":"")+i+(n>=0?":"+n:"")}return e}toJSON(){return this.sections}static fromJSON(e){if(!Array.isArray(e)||e.length%2||e.some(e=>"number"!=typeof e))throw new RangeError("Invalid JSON representation of ChangeDesc");return new E(e)}static create(e){return new E(e)}}class M extends E{constructor(e,t){super(e),this.inserted=t}apply(e){if(this.length!=e.length)throw new RangeError("Applying change set to a document with the wrong length");return T(this,(t,i,n,r,s)=>e=e.replace(n,n+(i-t),s),!1),e}mapDesc(e,t=!1){return O(this,e,t,!0)}invert(e){let t=this.sections.slice(),i=[];for(let r=0,s=0;r<t.length;r+=2){let o=t[r],l=t[r+1];if(l>=0){t[r]=l,t[r+1]=o;let a=r>>1;while(i.length<a)i.push(n.empty);i.push(o?e.slice(s,s+o):n.empty)}s+=o}return new M(t,i)}compose(e){return this.empty?e:e.empty?this:L(this,e,!0)}map(e,t=!1){return e.empty?this:O(this,e,t,!0)}iterChanges(e,t=!1){T(this,e,t)}get desc(){return E.create(this.sections)}filter(e){let t=[],i=[],n=[],r=new R(this);e:for(let s=0,o=0;;){let l=s==e.length?1e9:e[s++];while(o<l||o==l&&0==r.len){if(r.done)break e;let e=Math.min(r.len,l-o);B(n,e,-1);let s=-1==r.ins?-1:0==r.off?r.ins:0;B(t,e,s),s>0&&F(i,t,r.text),r.forward(e),o+=e}let a=e[s++];while(o<a){if(r.done)break e;let e=Math.min(r.len,a-o);B(t,e,-1),B(n,e,-1==r.ins?-1:0==r.off?r.ins:0),r.forward(e),o+=e}}return{changes:new M(t,i),filtered:E.create(n)}}toJSON(){let e=[];for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t],n=this.sections[t+1];n<0?e.push(i):0==n?e.push([i]):e.push([i].concat(this.inserted[t>>1].toJSON()))}return e}static of(e,t,i){let r=[],s=[],o=0,l=null;function a(e=!1){if(!e&&!r.length)return;o<t&&B(r,t-o,-1);let i=new M(r,s);l=l?l.compose(i.map(l)):i,r=[],s=[],o=0}function h(e){if(Array.isArray(e))for(let t of e)h(t);else if(e instanceof M){if(e.length!=t)throw new RangeError(`Mismatched change set length (got ${e.length}, expected ${t})`);a(),l=l?l.compose(e.map(l)):e}else{let{from:l,to:h=l,insert:c}=e;if(l>h||l<0||h>t)throw new RangeError(`Invalid change range ${l} to ${h} (in doc of length ${t})`);let u=c?"string"==typeof c?n.of(c.split(i||S)):c:n.empty,d=u.length;if(l==h&&0==d)return;l<o&&a(),l>o&&B(r,l-o,-1),B(r,h-l,d),F(s,r,u),o=h}}return h(e),a(!l),l}static empty(e){return new M(e?[e,-1]:[],[])}static fromJSON(e){if(!Array.isArray(e))throw new RangeError("Invalid JSON representation of ChangeSet");let t=[],i=[];for(let r=0;r<e.length;r++){let s=e[r];if("number"==typeof s)t.push(s,-1);else{if(!Array.isArray(s)||"number"!=typeof s[0]||s.some((e,t)=>t&&"string"!=typeof e))throw new RangeError("Invalid JSON representation of ChangeSet");if(1==s.length)t.push(s[0],0);else{while(i.length<r)i.push(n.empty);i[r]=n.of(s.slice(1)),t.push(s[0],i[r].length)}}}return new M(t,i)}static createSet(e,t){return new M(e,t)}}function B(e,t,i,n=!1){if(0==t&&i<=0)return;let r=e.length-2;r>=0&&i<=0&&i==e[r+1]?e[r]+=t:0==t&&0==e[r]?e[r+1]+=i:n?(e[r]+=t,e[r+1]+=i):e.push(t,i)}function F(e,t,i){if(0==i.length)return;let r=t.length-2>>1;if(r<e.length)e[e.length-1]=e[e.length-1].append(i);else{while(e.length<r)e.push(n.empty);e.push(i)}}function T(e,t,i){let r=e.inserted;for(let s=0,o=0,l=0;l<e.sections.length;){let a=e.sections[l++],h=e.sections[l++];if(h<0)s+=a,o+=a;else{let c=s,u=o,d=n.empty;for(;;){if(c+=a,u+=h,h&&r&&(d=d.append(r[l-2>>1])),i||l==e.sections.length||e.sections[l+1]<0)break;a=e.sections[l++],h=e.sections[l++]}t(s,c,o,u,d),s=c,o=u}}}function O(e,t,i,n=!1){let r=[],s=n?[]:null,o=new R(e),l=new R(t);for(let a=0,h=0;;)if(-1==o.ins)a+=o.len,o.next();else if(-1==l.ins&&h<a){let e=Math.min(l.len,a-h);l.forward(e),B(r,e,-1),h+=e}else if(l.ins>=0&&(o.done||h<a||h==a&&(l.len<o.len||l.len==o.len&&!i))){B(r,l.ins,-1);while(a>h&&!o.done&&a+o.len<h+l.len)a+=o.len,o.next();h+=l.len,l.next()}else{if(!(o.ins>=0)){if(o.done&&l.done)return s?M.createSet(r,s):E.create(r);throw new Error("Mismatched change set lengths")}{let e=0,t=a+o.len;for(;;)if(l.ins>=0&&h>a&&h+l.len<t)e+=l.ins,h+=l.len,l.next();else{if(!(-1==l.ins&&h<t))break;{let i=Math.min(l.len,t-h);e+=i,l.forward(i),h+=i}}B(r,e,o.ins),s&&F(s,r,o.text),a=t,o.next()}}}function L(e,t,i=!1){let n=[],r=i?[]:null,s=new R(e),o=new R(t);for(let l=!1;;){if(s.done&&o.done)return r?M.createSet(n,r):E.create(n);if(0==s.ins)B(n,s.len,0,l),s.next();else if(0!=o.len||o.done){if(s.done||o.done)throw new Error("Mismatched change set lengths");{let e=Math.min(s.len2,o.len),t=n.length;if(-1==s.ins){let t=-1==o.ins?-1:o.off?0:o.ins;B(n,e,t,l),r&&t&&F(r,n,o.text)}else-1==o.ins?(B(n,s.off?0:s.len,e,l),r&&F(r,n,s.textBit(e))):(B(n,s.off?0:s.len,o.off?0:o.ins,l),r&&!o.off&&F(r,n,o.text));l=(s.ins>e||o.ins>=0&&o.len>e)&&(l||n.length>t),s.forward2(e),o.forward(e)}}else B(n,0,o.ins,l),r&&F(r,n,o.text),o.next()}}class R{constructor(e){this.set=e,this.i=0,this.next()}next(){let{sections:e}=this.set;this.i<e.length?(this.len=e[this.i++],this.ins=e[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return-2==this.ins}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:e}=this.set,t=this.i-2>>1;return t>=e.length?n.empty:e[t]}textBit(e){let{inserted:t}=this.set,i=this.i-2>>1;return i>=t.length&&!e?n.empty:t[i].slice(this.off,null==e?void 0:this.off+e)}forward(e){e==this.len?this.next():(this.len-=e,this.off+=e)}forward2(e){-1==this.ins?this.forward(e):e==this.ins?this.next():(this.ins-=e,this.off+=e)}}class I{constructor(e,t,i){this.from=e,this.to=t,this.flags=i}get anchor(){return 16&this.flags?this.to:this.from}get head(){return 16&this.flags?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return 4&this.flags?-1:8&this.flags?1:0}get bidiLevel(){let e=3&this.flags;return 3==e?null:e}get goalColumn(){let e=this.flags>>5;return 33554431==e?void 0:e}map(e,t=-1){let i,n;return this.empty?i=n=e.mapPos(this.from,t):(i=e.mapPos(this.from,1),n=e.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new I(i,n,this.flags)}extend(e,t=e){if(e<=this.anchor&&t>=this.anchor)return N.range(e,t);let i=Math.abs(e-this.anchor)>Math.abs(t-this.anchor)?e:t;return N.range(this.anchor,i)}eq(e){return this.anchor==e.anchor&&this.head==e.head}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(e){if(!e||"number"!=typeof e.anchor||"number"!=typeof e.head)throw new RangeError("Invalid JSON representation for SelectionRange");return N.range(e.anchor,e.head)}static create(e,t,i){return new I(e,t,i)}}class N{constructor(e,t){this.ranges=e,this.mainIndex=t}map(e,t=-1){return e.empty?this:N.create(this.ranges.map(i=>i.map(e,t)),this.mainIndex)}eq(e){if(this.ranges.length!=e.ranges.length||this.mainIndex!=e.mainIndex)return!1;for(let t=0;t<this.ranges.length;t++)if(!this.ranges[t].eq(e.ranges[t]))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return 1==this.ranges.length?this:new N([this.main],0)}addRange(e,t=!0){return N.create([e].concat(this.ranges),t?0:this.mainIndex+1)}replaceRange(e,t=this.mainIndex){let i=this.ranges.slice();return i[t]=e,N.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(e=>e.toJSON()),main:this.mainIndex}}static fromJSON(e){if(!e||!Array.isArray(e.ranges)||"number"!=typeof e.main||e.main>=e.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new N(e.ranges.map(e=>I.fromJSON(e)),e.main)}static single(e,t=e){return new N([N.range(e,t)],0)}static create(e,t=0){if(0==e.length)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<e.length;n++){let r=e[n];if(r.empty?r.from<=i:r.from<i)return N.normalized(e.slice(),t);i=r.to}return new N(e,t)}static cursor(e,t=0,i,n){return I.create(e,e,(0==t?0:t<0?4:8)|(null==i?3:Math.min(2,i))|(null!==n&&void 0!==n?n:33554431)<<5)}static range(e,t,i){let n=(null!==i&&void 0!==i?i:33554431)<<5;return t<e?I.create(t,e,24|n):I.create(e,t,n|(t>e?4:0))}static normalized(e,t=0){let i=e[t];e.sort((e,t)=>e.from-t.from),t=e.indexOf(i);for(let n=1;n<e.length;n++){let i=e[n],r=e[n-1];if(i.empty?i.from<=r.to:i.from<r.to){let s=r.from,o=Math.max(i.to,r.to);n<=t&&t--,e.splice(--n,2,i.anchor>i.head?N.range(o,s):N.range(s,o))}}return new N(e,t)}}function P(e,t){for(let i of e.ranges)if(i.to>t)throw new RangeError("Selection points outside of document")}let z=0;class _{constructor(e,t,i,n,r){this.combine=e,this.compareInput=t,this.compare=i,this.isStatic=n,this.extensions=r,this.id=z++,this.default=e([])}static define(e={}){return new _(e.combine||(e=>e),e.compareInput||((e,t)=>e===t),e.compare||(e.combine?(e,t)=>e===t:H),!!e.static,e.enables)}of(e){return new q([],this,0,e)}compute(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new q(e,this,1,t)}computeN(e,t){if(this.isStatic)throw new Error("Can't compute a static facet");return new q(e,this,2,t)}from(e,t){return t||(t=e=>e),this.compute([e],i=>t(i.field(e)))}}function H(e,t){return e==t||e.length==t.length&&e.every((e,i)=>e===t[i])}class q{constructor(e,t,i,n){this.dependencies=e,this.facet=t,this.type=i,this.value=n,this.id=z++}dynamicSlot(e){var t;let i=this.value,n=this.facet.compareInput,r=this.id,s=e[r]>>1,o=2==this.type,l=!1,a=!1,h=[];for(let c of this.dependencies)"doc"==c?l=!0:"selection"==c?a=!0:0==(1&(null!==(t=e[c.id])&&void 0!==t?t:1))&&h.push(e[c.id]);return{create(e){return e.values[s]=i(e),1},update(e,t){if(l&&t.docChanged||a&&(t.docChanged||t.selection)||W(e,h)){let t=i(e);if(o?!V(t,e.values[s],n):!n(t,e.values[s]))return e.values[s]=t,1}return 0},reconfigure:(e,t)=>{let l=i(e),a=t.config.address[r];if(null!=a){let i=ie(t,a);if(this.dependencies.every(i=>i instanceof _?t.facet(i)===e.facet(i):!(i instanceof U)||t.field(i,!1)==e.field(i,!1))||(o?V(l,i,n):n(l,i)))return e.values[s]=i,0}return e.values[s]=l,1}}}}function V(e,t,i){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!i(e[n],t[n]))return!1;return!0}function W(e,t){let i=!1;for(let n of t)1&te(e,n)&&(i=!0);return i}function j(e,t,i){let n=i.map(t=>e[t.id]),r=i.map(e=>e.type),s=n.filter(e=>!(1&e)),o=e[t.id]>>1;function l(e){let i=[];for(let t=0;t<n.length;t++){let s=ie(e,n[t]);if(2==r[t])for(let e of s)i.push(e);else i.push(s)}return t.combine(i)}return{create(e){for(let t of n)te(e,t);return e.values[o]=l(e),1},update(e,i){if(!W(e,s))return 0;let n=l(e);return t.compare(n,e.values[o])?0:(e.values[o]=n,1)},reconfigure(e,r){let s=W(e,n),a=r.config.facets[t.id],h=r.facet(t);if(a&&!s&&H(i,a))return e.values[o]=h,0;let c=l(e);return t.compare(c,h)?(e.values[o]=h,0):(e.values[o]=c,1)}}}const $=_.define({static:!0});class U{constructor(e,t,i,n,r){this.id=e,this.createF=t,this.updateF=i,this.compareF=n,this.spec=r,this.provides=void 0}static define(e){let t=new U(z++,e.create,e.update,e.compare||((e,t)=>e===t),e);return e.provide&&(t.provides=e.provide(t)),t}create(e){let t=e.facet($).find(e=>e.field==this);return((null===t||void 0===t?void 0:t.create)||this.createF)(e)}slot(e){let t=e[this.id]>>1;return{create:e=>(e.values[t]=this.create(e),1),update:(e,i)=>{let n=e.values[t],r=this.updateF(n,i);return this.compareF(n,r)?0:(e.values[t]=r,1)},reconfigure:(e,i)=>null!=i.config.address[this.id]?(e.values[t]=i.field(this),0):(e.values[t]=this.create(e),1)}}init(e){return[this,$.of({field:this,create:e})]}get extension(){return this}}const G={lowest:4,low:3,default:2,high:1,highest:0};function K(e){return t=>new X(t,e)}const J={highest:K(G.highest),high:K(G.high),default:K(G.default),low:K(G.low),lowest:K(G.lowest)};class X{constructor(e,t){this.inner=e,this.prec=t}}class Z{of(e){return new Q(this,e)}reconfigure(e){return Z.reconfigure.of({compartment:this,extension:e})}get(e){return e.config.compartments.get(this)}}class Q{constructor(e,t){this.compartment=e,this.inner=t}}class Y{constructor(e,t,i,n,r,s){this.base=e,this.compartments=t,this.dynamicSlots=i,this.address=n,this.staticValues=r,this.facets=s,this.statusTemplate=[];while(this.statusTemplate.length<i.length)this.statusTemplate.push(0)}staticFacet(e){let t=this.address[e.id];return null==t?e.default:this.staticValues[t>>1]}static resolve(e,t,i){let n=[],r=Object.create(null),s=new Map;for(let u of ee(e,t,s))u instanceof U?n.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let o=Object.create(null),l=[],a=[];for(let u of n)o[u.id]=a.length<<1,a.push(e=>u.slot(e));let h=null===i||void 0===i?void 0:i.config.facets;for(let u in r){let e=r[u],t=e[0].facet,n=h&&h[u]||[];if(e.every(e=>0==e.type))if(o[t.id]=l.length<<1|1,H(n,e))l.push(i.facet(t));else{let n=t.combine(e.map(e=>e.value));l.push(i&&t.compare(n,i.facet(t))?i.facet(t):n)}else{for(let t of e)0==t.type?(o[t.id]=l.length<<1|1,l.push(t.value)):(o[t.id]=a.length<<1,a.push(e=>t.dynamicSlot(e)));o[t.id]=a.length<<1,a.push(i=>j(i,t,e))}}let c=a.map(e=>e(o));return new Y(e,s,c,o,l,r)}}function ee(e,t,i){let n=[[],[],[],[],[]],r=new Map;function s(e,o){let l=r.get(e);if(null!=l){if(l<=o)return;let t=n[l].indexOf(e);t>-1&&n[l].splice(t,1),e instanceof Q&&i.delete(e.compartment)}if(r.set(e,o),Array.isArray(e))for(let t of e)s(t,o);else if(e instanceof Q){if(i.has(e.compartment))throw new RangeError("Duplicate use of compartment in extensions");let n=t.get(e.compartment)||e.inner;i.set(e.compartment,n),s(n,o)}else if(e instanceof X)s(e.inner,e.prec);else if(e instanceof U)n[o].push(e),e.provides&&s(e.provides,o);else if(e instanceof q)n[o].push(e),e.facet.extensions&&s(e.facet.extensions,o);else{let t=e.extension;if(!t)throw new Error(`Unrecognized extension value in extension set (${e}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);s(t,o)}}return s(e,G.default),n.reduce((e,t)=>e.concat(t))}function te(e,t){if(1&t)return 2;let i=t>>1,n=e.status[i];if(4==n)throw new Error("Cyclic dependency between fields and/or facets");if(2&n)return n;e.status[i]=4;let r=e.computeSlot(e,e.config.dynamicSlots[i]);return e.status[i]=2|r}function ie(e,t){return 1&t?e.config.staticValues[t>>1]:e.values[t>>1]}const ne=_.define(),re=_.define({combine:e=>e.some(e=>e),static:!0}),se=_.define({combine:e=>e.length?e[0]:void 0,static:!0}),oe=_.define(),le=_.define(),ae=_.define(),he=_.define({combine:e=>!!e.length&&e[0]});class ce{constructor(e,t){this.type=e,this.value=t}static define(){return new ue}}class ue{of(e){return new ce(this,e)}}class de{constructor(e){this.map=e}of(e){return new fe(this,e)}}class fe{constructor(e,t){this.type=e,this.value=t}map(e){let t=this.type.map(this.value,e);return void 0===t?void 0:t==this.value?this:new fe(this.type,t)}is(e){return this.type==e}static define(e={}){return new de(e.map||(e=>e))}static mapEffects(e,t){if(!e.length)return e;let i=[];for(let n of e){let e=n.map(t);e&&i.push(e)}return i}}fe.reconfigure=fe.define(),fe.appendConfig=fe.define();class pe{constructor(e,t,i,n,r,s){this.startState=e,this.changes=t,this.selection=i,this.effects=n,this.annotations=r,this.scrollIntoView=s,this._doc=null,this._state=null,i&&P(i,t.newLength),r.some(e=>e.type==pe.time)||(this.annotations=r.concat(pe.time.of(Date.now())))}static create(e,t,i,n,r,s){return new pe(e,t,i,n,r,s)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(e){for(let t of this.annotations)if(t.type==e)return t.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(e){let t=this.annotation(pe.userEvent);return!(!t||!(t==e||t.length>e.length&&t.slice(0,e.length)==e&&"."==t[e.length]))}}function me(e,t){let i=[];for(let n=0,r=0;;){let s,o;if(n<e.length&&(r==t.length||t[r]>=e[n]))s=e[n++],o=e[n++];else{if(!(r<t.length))return i;s=t[r++],o=t[r++]}!i.length||i[i.length-1]<s?i.push(s,o):i[i.length-1]<o&&(i[i.length-1]=o)}}function ge(e,t,i){var n;let r,s,o;return i?(r=t.changes,s=M.empty(t.changes.length),o=e.changes.compose(t.changes)):(r=t.changes.map(e.changes),s=e.changes.mapDesc(t.changes,!0),o=e.changes.compose(r)),{changes:o,selection:t.selection?t.selection.map(s):null===(n=e.selection)||void 0===n?void 0:n.map(r),effects:fe.mapEffects(e.effects,r).concat(fe.mapEffects(t.effects,s)),annotations:e.annotations.length?e.annotations.concat(t.annotations):t.annotations,scrollIntoView:e.scrollIntoView||t.scrollIntoView}}function ve(e,t,i){let n=t.selection,r=ke(t.annotations);return t.userEvent&&(r=r.concat(pe.userEvent.of(t.userEvent))),{changes:t.changes instanceof M?t.changes:M.of(t.changes||[],i,e.facet(se)),selection:n&&(n instanceof N?n:N.single(n.anchor,n.head)),effects:ke(t.effects),annotations:r,scrollIntoView:!!t.scrollIntoView}}function xe(e,t,i){let n=ve(e,t.length?t[0]:{},e.doc.length);t.length&&!1===t[0].filter&&(i=!1);for(let s=1;s<t.length;s++){!1===t[s].filter&&(i=!1);let r=!!t[s].sequential;n=ge(n,ve(e,t[s],r?n.changes.newLength:e.doc.length),r)}let r=pe.create(e,n.changes,n.selection,n.effects,n.annotations,n.scrollIntoView);return be(i?we(r):r)}function we(e){let t=e.startState,i=!0;for(let r of t.facet(oe)){let t=r(e);if(!1===t){i=!1;break}Array.isArray(t)&&(i=!0===i?t:me(i,t))}if(!0!==i){let n,r;if(!1===i)r=e.changes.invertedDesc,n=M.empty(t.doc.length);else{let t=e.changes.filter(i);n=t.changes,r=t.filtered.mapDesc(t.changes).invertedDesc}e=pe.create(t,n,e.selection&&e.selection.map(r),fe.mapEffects(e.effects,r),e.annotations,e.scrollIntoView)}let n=t.facet(le);for(let r=n.length-1;r>=0;r--){let i=n[r](e);e=i instanceof pe?i:Array.isArray(i)&&1==i.length&&i[0]instanceof pe?i[0]:xe(t,ke(i),!1)}return e}function be(e){let t=e.startState,i=t.facet(ae),n=e;for(let r=i.length-1;r>=0;r--){let s=i[r](e);s&&Object.keys(s).length&&(n=ge(e,ve(t,s,e.changes.newLength),!0))}return n==e?e:pe.create(t,e.changes,e.selection,n.effects,n.annotations,n.scrollIntoView)}pe.time=ce.define(),pe.userEvent=ce.define(),pe.addToHistory=ce.define(),pe.remote=ce.define();const ye=[];function ke(e){return null==e?ye:Array.isArray(e)?e:[e]}var De=function(e){return e[e["Word"]=0]="Word",e[e["Space"]=1]="Space",e[e["Other"]=2]="Other",e}(De||(De={}));const Ce=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let Se;try{Se=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch(vm){}function Ae(e){if(Se)return Se.test(e);for(let t=0;t<e.length;t++){let i=e[t];if(/\w/.test(i)||i>""&&(i.toUpperCase()!=i.toLowerCase()||Ce.test(i)))return!0}return!1}function Ee(e){return t=>{if(!/\S/.test(t))return De.Space;if(Ae(t))return De.Word;for(let i=0;i<e.length;i++)if(t.indexOf(e[i])>-1)return De.Word;return De.Other}}class Me{constructor(e,t,i,n,r,s){this.config=e,this.doc=t,this.selection=i,this.values=n,this.status=e.statusTemplate.slice(),this.computeSlot=r,s&&(s._state=this);for(let o=0;o<this.config.dynamicSlots.length;o++)te(this,o<<1);this.computeSlot=null}field(e,t=!0){let i=this.config.address[e.id];if(null!=i)return te(this,i),ie(this,i);if(t)throw new RangeError("Field is not present in this state")}update(...e){return xe(this,e,!0)}applyTransaction(e){let t,i=this.config,{base:n,compartments:r}=i;for(let s of e.effects)s.is(Z.reconfigure)?(i&&(r=new Map,i.compartments.forEach((e,t)=>r.set(t,e)),i=null),r.set(s.value.compartment,s.value.extension)):s.is(fe.reconfigure)?(i=null,n=s.value):s.is(fe.appendConfig)&&(i=null,n=ke(n).concat(s.value));if(i)t=e.startState.values.slice();else{i=Y.resolve(n,r,this);let e=new Me(i,this.doc,this.selection,i.dynamicSlots.map(()=>null),(e,t)=>t.reconfigure(e,this),null);t=e.values}new Me(i,e.newDoc,e.newSelection,t,(t,i)=>i.update(t,e),e)}replaceSelection(e){return"string"==typeof e&&(e=this.toText(e)),this.changeByRange(t=>({changes:{from:t.from,to:t.to,insert:e},range:N.cursor(t.from+e.length)}))}changeByRange(e){let t=this.selection,i=e(t.ranges[0]),n=this.changes(i.changes),r=[i.range],s=ke(i.effects);for(let o=1;o<t.ranges.length;o++){let i=e(t.ranges[o]),l=this.changes(i.changes),a=l.map(n);for(let e=0;e<o;e++)r[e]=r[e].map(a);let h=n.mapDesc(l,!0);r.push(i.range.map(h)),n=n.compose(a),s=fe.mapEffects(s,a).concat(fe.mapEffects(ke(i.effects),h))}return{changes:n,selection:N.create(r,t.mainIndex),effects:s}}changes(e=[]){return e instanceof M?e:M.of(e,this.doc.length,this.facet(Me.lineSeparator))}toText(e){return n.of(e.split(this.facet(Me.lineSeparator)||S))}sliceDoc(e=0,t=this.doc.length){return this.doc.sliceString(e,t,this.lineBreak)}facet(e){let t=this.config.address[e.id];return null==t?e.default:(te(this,t),ie(this,t))}toJSON(e){let t={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(e)for(let i in e){let n=e[i];n instanceof U&&(t[i]=n.spec.toJSON(this.field(e[i]),this))}return t}static fromJSON(e,t={},i){if(!e||"string"!=typeof e.doc)throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i)for(let r in i){let t=i[r],s=e[r];n.push(t.init(e=>t.spec.fromJSON(s,e)))}return Me.create({doc:e.doc,selection:N.fromJSON(e.selection),extensions:t.extensions?n.concat([t.extensions]):n})}static create(e={}){let t=Y.resolve(e.extensions||[],new Map),i=e.doc instanceof n?e.doc:n.of((e.doc||"").split(t.staticFacet(Me.lineSeparator)||S)),r=e.selection?e.selection instanceof N?e.selection:N.single(e.selection.anchor,e.selection.head):N.single(0);return P(r,i.length),t.staticFacet(re)||(r=r.asSingle()),new Me(t,i,r,t.dynamicSlots.map(()=>null),(e,t)=>t.create(e),null)}get tabSize(){return this.facet(Me.tabSize)}get lineBreak(){return this.facet(Me.lineSeparator)||"\n"}get readOnly(){return this.facet(he)}phrase(e,...t){for(let i of this.facet(Me.phrases))if(Object.prototype.hasOwnProperty.call(i,e)){e=i[e];break}return t.length&&(e=e.replace(/\$(\$|\d*)/g,(e,i)=>{if("$"==i)return"$";let n=+(i||1);return!n||n>t.length?e:t[n-1]})),e}languageDataAt(e,t,i=-1){let n=[];for(let r of this.facet(ne))for(let s of r(this,t,i))Object.prototype.hasOwnProperty.call(s,e)&&n.push(s[e]);return n}charCategorizer(e){return Ee(this.languageDataAt("wordChars",e).join(""))}wordAt(e){let{text:t,from:i,length:n}=this.doc.lineAt(e),r=this.charCategorizer(e),s=e-i,o=e-i;while(s>0){let e=v(t,s,!1);if(r(t.slice(e,s))!=De.Word)break;s=e}while(o<n){let e=v(t,o);if(r(t.slice(o,e))!=De.Word)break;o=e}return s==o?null:N.range(s+i,o+i)}}function Be(e,t,i={}){let n={};for(let r of e)for(let e of Object.keys(r)){let t=r[e],s=n[e];if(void 0===s)n[e]=t;else if(s===t||void 0===t);else{if(!Object.hasOwnProperty.call(i,e))throw new Error("Config merge conflict for field "+e);n[e]=i[e](s,t)}}for(let r in t)void 0===n[r]&&(n[r]=t[r]);return n}Me.allowMultipleSelections=re,Me.tabSize=_.define({combine:e=>e.length?e[0]:4}),Me.lineSeparator=se,Me.readOnly=he,Me.phrases=_.define({compare(e,t){let i=Object.keys(e),n=Object.keys(t);return i.length==n.length&&i.every(i=>e[i]==t[i])}}),Me.languageData=ne,Me.changeFilter=oe,Me.transactionFilter=le,Me.transactionExtender=ae,Z.reconfigure=fe.define();class Fe{eq(e){return this==e}range(e,t=e){return Te.create(e,t,this)}}Fe.prototype.startSide=Fe.prototype.endSide=0,Fe.prototype.point=!1,Fe.prototype.mapMode=A.TrackDel;class Te{constructor(e,t,i){this.from=e,this.to=t,this.value=i}static create(e,t,i){return new Te(e,t,i)}}function Oe(e,t){return e.from-t.from||e.value.startSide-t.value.startSide}class Le{constructor(e,t,i,n){this.from=e,this.to=t,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(e,t,i,n=0){let r=i?this.to:this.from;for(let s=n,o=r.length;;){if(s==o)return s;let n=s+o>>1,l=r[n]-e||(i?this.value[n].endSide:this.value[n].startSide)-t;if(n==s)return l>=0?s:o;l>=0?o=n:s=n+1}}between(e,t,i,n){for(let r=this.findIndex(t,-1e9,!0),s=this.findIndex(i,1e9,!1,r);r<s;r++)if(!1===n(this.from[r]+e,this.to[r]+e,this.value[r]))return!1}map(e,t){let i=[],n=[],r=[],s=-1,o=-1;for(let l=0;l<this.value.length;l++){let a,h,c=this.value[l],u=this.from[l]+e,d=this.to[l]+e;if(u==d){let e=t.mapPos(u,c.startSide,c.mapMode);if(null==e)continue;if(a=h=e,c.startSide!=c.endSide&&(h=t.mapPos(u,c.endSide),h<a))continue}else if(a=t.mapPos(u,c.startSide),h=t.mapPos(d,c.endSide),a>h||a==h&&c.startSide>0&&c.endSide<=0)continue;(h-a||c.endSide-c.startSide)<0||(s<0&&(s=a),c.point&&(o=Math.max(o,h-a)),i.push(c),n.push(a-s),r.push(h-s))}return{mapped:i.length?new Le(n,r,i,o):null,pos:s}}}class Re{constructor(e,t,i,n){this.chunkPos=e,this.chunk=t,this.nextLayer=i,this.maxPoint=n}static create(e,t,i,n){return new Re(e,t,i,n)}get length(){let e=this.chunk.length-1;return e<0?0:Math.max(this.chunkEnd(e),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let e=this.nextLayer.size;for(let t of this.chunk)e+=t.value.length;return e}chunkEnd(e){return this.chunkPos[e]+this.chunk[e].length}update(e){let{add:t=[],sort:i=!1,filterFrom:n=0,filterTo:r=this.length}=e,s=e.filter;if(0==t.length&&!s)return this;if(i&&(t=t.slice().sort(Oe)),this.isEmpty)return t.length?Re.of(t):this;let o=new ze(this,null,-1).goto(0),l=0,a=[],h=new Ne;while(o.value||l<t.length)if(l<t.length&&(o.from-t[l].from||o.startSide-t[l].value.startSide)>=0){let e=t[l++];h.addInner(e.from,e.to,e.value)||a.push(e)}else 1==o.rangeIndex&&o.chunkIndex<this.chunk.length&&(l==t.length||this.chunkEnd(o.chunkIndex)<t[l].from)&&(!s||n>this.chunkEnd(o.chunkIndex)||r<this.chunkPos[o.chunkIndex])&&h.addChunk(this.chunkPos[o.chunkIndex],this.chunk[o.chunkIndex])?o.nextChunk():((!s||n>o.to||r<o.from||s(o.from,o.to,o.value))&&(h.addInner(o.from,o.to,o.value)||a.push(Te.create(o.from,o.to,o.value))),o.next());return h.finishInner(this.nextLayer.isEmpty&&!a.length?Re.empty:this.nextLayer.update({add:a,filter:s,filterFrom:n,filterTo:r}))}map(e){if(e.empty||this.isEmpty)return this;let t=[],i=[],n=-1;for(let s=0;s<this.chunk.length;s++){let r=this.chunkPos[s],o=this.chunk[s],l=e.touchesRange(r,r+o.length);if(!1===l)n=Math.max(n,o.maxPoint),t.push(o),i.push(e.mapPos(r));else if(!0===l){let{mapped:s,pos:l}=o.map(r,e);s&&(n=Math.max(n,s.maxPoint),t.push(s),i.push(l))}}let r=this.nextLayer.map(e);return 0==t.length?r:new Re(i,t,r||Re.empty,n)}between(e,t,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let r=this.chunkPos[n],s=this.chunk[n];if(t>=r&&e<=r+s.length&&!1===s.between(r,e-r,t-r,i))return}this.nextLayer.between(e,t,i)}}iter(e=0){return _e.from([this]).goto(e)}get isEmpty(){return this.nextLayer==this}static iter(e,t=0){return _e.from(e).goto(t)}static compare(e,t,i,n,r=-1){let s=e.filter(e=>e.maxPoint>0||!e.isEmpty&&e.maxPoint>=r),o=t.filter(e=>e.maxPoint>0||!e.isEmpty&&e.maxPoint>=r),l=Pe(s,o,i),a=new qe(s,l,r),h=new qe(o,l,r);i.iterGaps((e,t,i)=>Ve(a,e,h,t,i,n)),i.empty&&0==i.length&&Ve(a,0,h,0,0,n)}static eq(e,t,i=0,n){null==n&&(n=1e9);let r=e.filter(e=>!e.isEmpty&&t.indexOf(e)<0),s=t.filter(t=>!t.isEmpty&&e.indexOf(t)<0);if(r.length!=s.length)return!1;if(!r.length)return!0;let o=Pe(r,s),l=new qe(r,o,0).goto(i),a=new qe(s,o,0).goto(i);for(;;){if(l.to!=a.to||!We(l.active,a.active)||l.point&&(!a.point||!l.point.eq(a.point)))return!1;if(l.to>n)return!0;l.next(),a.next()}}static spans(e,t,i,n,r=-1){let s=new qe(e,null,r).goto(t),o=t,l=s.openStart;for(;;){let e=Math.min(s.to,i);if(s.point?(n.point(o,e,s.point,s.activeForPoint(s.to),l,s.pointRank),l=s.openEnd(e)+(s.to>e?1:0)):e>o&&(n.span(o,e,s.active,l),l=s.openEnd(e)),s.to>i)break;o=s.to,s.next()}return l}static of(e,t=!1){let i=new Ne;for(let n of e instanceof Te?[e]:t?Ie(e):e)i.add(n.from,n.to,n.value);return i.finish()}}function Ie(e){if(e.length>1)for(let t=e[0],i=1;i<e.length;i++){let n=e[i];if(Oe(t,n)>0)return e.slice().sort(Oe);t=n}return e}Re.empty=new Re([],[],null,-1),Re.empty.nextLayer=Re.empty;class Ne{constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}finishChunk(e){this.chunks.push(new Le(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,e&&(this.from=[],this.to=[],this.value=[])}add(e,t,i){this.addInner(e,t,i)||(this.nextLayer||(this.nextLayer=new Ne)).add(e,t,i)}addInner(e,t,i){let n=e-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(e-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return!(n<0)&&(250==this.from.length&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=e),this.from.push(e-this.chunkStart),this.to.push(t-this.chunkStart),this.last=i,this.lastFrom=e,this.lastTo=t,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,t-e)),!0)}addChunk(e,t){if((e-this.lastTo||t.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,t.maxPoint),this.chunks.push(t),this.chunkPos.push(e);let i=t.value.length-1;return this.last=t.value[i],this.lastFrom=t.from[i]+e,this.lastTo=t.to[i]+e,!0}finish(){return this.finishInner(Re.empty)}finishInner(e){if(this.from.length&&this.finishChunk(!1),0==this.chunks.length)return e;let t=Re.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(e):e,this.setMaxPoint);return this.from=null,t}}function Pe(e,t,i){let n=new Map;for(let s of e)for(let e=0;e<s.chunk.length;e++)s.chunk[e].maxPoint<=0&&n.set(s.chunk[e],s.chunkPos[e]);let r=new Set;for(let s of t)for(let e=0;e<s.chunk.length;e++){let t=n.get(s.chunk[e]);null==t||(i?i.mapPos(t):t)!=s.chunkPos[e]||(null===i||void 0===i?void 0:i.touchesRange(t,t+s.chunk[e].length))||r.add(s.chunk[e])}return r}class ze{constructor(e,t,i,n=0){this.layer=e,this.skip=t,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(e,t=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(e,t,!1),this}gotoInner(e,t,i){while(this.chunkIndex<this.layer.chunk.length){let t=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(t)||this.layer.chunkEnd(this.chunkIndex)<e||t.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(e-this.layer.chunkPos[this.chunkIndex],t,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(e,t){(this.to-e||this.endSide-t)<0&&this.gotoInner(e,t,!0)}next(){for(;;){if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}{let e=this.layer.chunkPos[this.chunkIndex],t=this.layer.chunk[this.chunkIndex],i=e+t.from[this.rangeIndex];if(this.from=i,this.to=e+t.to[this.rangeIndex],this.value=t.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}}setRangeIndex(e){if(e==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)while(this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]))this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=e}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(e){return this.from-e.from||this.startSide-e.startSide||this.rank-e.rank||this.to-e.to||this.endSide-e.endSide}}class _e{constructor(e){this.heap=e}static from(e,t=null,i=-1){let n=[];for(let r=0;r<e.length;r++)for(let s=e[r];!s.isEmpty;s=s.nextLayer)s.maxPoint>=i&&n.push(new ze(s,t,i,r));return 1==n.length?n[0]:new _e(n)}get startSide(){return this.value?this.value.startSide:0}goto(e,t=-1e9){for(let i of this.heap)i.goto(e,t);for(let i=this.heap.length>>1;i>=0;i--)He(this.heap,i);return this.next(),this}forward(e,t){for(let i of this.heap)i.forward(e,t);for(let i=this.heap.length>>1;i>=0;i--)He(this.heap,i);(this.to-e||this.value.endSide-t)<0&&this.next()}next(){if(0==this.heap.length)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let e=this.heap[0];this.from=e.from,this.to=e.to,this.value=e.value,this.rank=e.rank,e.value&&e.next(),He(this.heap,0)}}}function He(e,t){for(let i=e[t];;){let n=1+(t<<1);if(n>=e.length)break;let r=e[n];if(n+1<e.length&&r.compare(e[n+1])>=0&&(r=e[n+1],n++),i.compare(r)<0)break;e[n]=i,e[t]=r,t=n}}class qe{constructor(e,t,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=_e.from(e,t,i)}goto(e,t=-1e9){return this.cursor.goto(e,t),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=e,this.endSide=t,this.openStart=-1,this.next(),this}forward(e,t){while(this.minActive>-1&&(this.activeTo[this.minActive]-e||this.active[this.minActive].endSide-t)<0)this.removeActive(this.minActive);this.cursor.forward(e,t)}removeActive(e){je(this.active,e),je(this.activeTo,e),je(this.activeRank,e),this.minActive=Ue(this.active,this.activeTo)}addActive(e){let t=0,{value:i,to:n,rank:r}=this.cursor;while(t<this.activeRank.length&&this.activeRank[t]<=r)t++;$e(this.active,t,i),$e(this.activeTo,t,n),$e(this.activeRank,t,r),e&&$e(e,t,this.cursor.from),this.minActive=Ue(this.active,this.activeTo)}next(){let e=this.to,t=this.point;this.point=null;let i=this.openStart<0?[]:null,n=0;for(;;){let r=this.minActive;if(r>-1&&(this.activeTo[r]-this.cursor.from||this.active[r].endSide-this.cursor.startSide)<0){if(this.activeTo[r]>e){this.to=this.activeTo[r],this.endSide=this.active[r].endSide;break}this.removeActive(r),i&&je(i,r)}else{if(!this.cursor.value){this.to=this.endSide=1e9;break}if(this.cursor.from>e){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}{let r=this.cursor.value;if(r.point){if(!(t&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)){this.point=r,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=r.endSide,this.cursor.from<e&&(n=1),this.cursor.next(),this.forward(this.to,this.endSide);break}this.cursor.next()}else this.addActive(i),this.cursor.next()}}}if(i){let t=0;while(t<i.length&&i[t]<e)t++;this.openStart=t+n}}activeForPoint(e){if(!this.active.length)return this.active;let t=[];for(let i=this.active.length-1;i>=0;i--){if(this.activeRank[i]<this.pointRank)break;(this.activeTo[i]>e||this.activeTo[i]==e&&this.active[i].endSide>=this.point.endSide)&&t.push(this.active[i])}return t.reverse()}openEnd(e){let t=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>e;i--)t++;return t}}function Ve(e,t,i,n,r,s){e.goto(t),i.goto(n);let o=n+r,l=n,a=n-t;for(;;){let t=e.to+a-i.to||e.endSide-i.endSide,n=t<0?e.to+a:i.to,r=Math.min(n,o);if(e.point||i.point?e.point&&i.point&&(e.point==i.point||e.point.eq(i.point))&&We(e.activeForPoint(e.to+a),i.activeForPoint(i.to))||s.comparePoint(l,r,e.point,i.point):r>l&&!We(e.active,i.active)&&s.compareRange(l,r,e.active,i.active),n>o)break;l=n,t<=0&&e.next(),t>=0&&i.next()}}function We(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++)if(e[i]!=t[i]&&!e[i].eq(t[i]))return!1;return!0}function je(e,t){for(let i=t,n=e.length-1;i<n;i++)e[i]=e[i+1];e.pop()}function $e(e,t,i){for(let n=e.length-1;n>=t;n--)e[n+1]=e[n];e[t]=i}function Ue(e,t){let i=-1,n=1e9;for(let r=0;r<t.length;r++)(t[r]-n||e[r].endSide-e[i].endSide)<0&&(i=r,n=t[r]);return i}function Ge(e,t,i=e.length){let n=0;for(let r=0;r<i;)9==e.charCodeAt(r)?(n+=t-n%t,r++):(n++,r=v(e,r));return n}function Ke(e,t,i,n){for(let r=0,s=0;;){if(s>=t)return r;if(r==e.length)break;s+=9==e.charCodeAt(r)?i-s%i:1,r=v(e,r)}return!0===n?-1:e.length}const Je="ͼ",Xe="undefined"==typeof Symbol?"__"+Je:Symbol.for(Je),Ze="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),Qe="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class Ye{constructor(e,t){this.rules=[];let{finish:i}=t||{};function n(e){return/^@/.test(e)?[e]:e.split(/,\s*/)}function r(e,t,s,o){let l=[],a=/^@(\w+)\b/.exec(e[0]),h=a&&"keyframes"==a[1];if(a&&null==t)return s.push(e[0]+";");for(let i in t){let o=t[i];if(/&/.test(i))r(i.split(/,\s*/).map(t=>e.map(e=>t.replace(/&/,e))).reduce((e,t)=>e.concat(t)),o,s);else if(o&&"object"==typeof o){if(!a)throw new RangeError("The value of a property ("+i+") should be a primitive value.");r(n(i),o,l,h)}else null!=o&&l.push(i.replace(/_.*/,"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())+": "+o+";")}(l.length||h)&&s.push((!i||a||o?e:e.map(i)).join(", ")+" {"+l.join(" ")+"}")}for(let s in e)r(n(s),e[s],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let e=Qe[Xe]||1;return Qe[Xe]=e+1,Je+e.toString(36)}static mount(e,t){(e[Ze]||new tt(e)).mount(Array.isArray(t)?t:[t])}}let et=null;class tt{constructor(e){if(!e.head&&e.adoptedStyleSheets&&"undefined"!=typeof CSSStyleSheet){if(et)return e.adoptedStyleSheets=[et.sheet].concat(e.adoptedStyleSheets),e[Ze]=et;this.sheet=new CSSStyleSheet,e.adoptedStyleSheets=[this.sheet].concat(e.adoptedStyleSheets),et=this}else{this.styleTag=(e.ownerDocument||e).createElement("style");let t=e.head||e;t.insertBefore(this.styleTag,t.firstChild)}this.modules=[],e[Ze]=this}mount(e){let t=this.sheet,i=0,n=0;for(let r=0;r<e.length;r++){let s=e[r],o=this.modules.indexOf(s);if(o<n&&o>-1&&(this.modules.splice(o,1),n--,o=-1),-1==o){if(this.modules.splice(n++,0,s),t)for(let e=0;e<s.rules.length;e++)t.insertRule(s.rules[e],i++)}else{while(n<o)i+=this.modules[n++].rules.length;i+=s.rules.length,n++}}if(!t){let e="";for(let t=0;t<this.modules.length;t++)e+=this.modules[t].getRules()+"\n";this.styleTag.textContent=e}}}for(var it={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",229:"q"},nt={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"',229:"Q"},rt="undefined"!=typeof navigator&&/Chrome\/(\d+)/.exec(navigator.userAgent),st="undefined"!=typeof navigator&&/Apple Computer/.test(navigator.vendor),ot="undefined"!=typeof navigator&&/Gecko\/\d+/.test(navigator.userAgent),lt="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),at="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),ht=rt&&(lt||+rt[1]<57)||ot&&lt,ct=0;ct<10;ct++)it[48+ct]=it[96+ct]=String(ct);for(ct=1;ct<=24;ct++)it[ct+111]="F"+ct;for(ct=65;ct<=90;ct++)it[ct]=String.fromCharCode(ct+32),nt[ct]=String.fromCharCode(ct);for(var ut in it)nt.hasOwnProperty(ut)||(nt[ut]=it[ut]);function dt(e){var t=ht&&(e.ctrlKey||e.altKey||e.metaKey)||(st||at)&&e.shiftKey&&e.key&&1==e.key.length,i=!t&&e.key||(e.shiftKey?nt:it)[e.keyCode]||e.key||"Unidentified";return"Esc"==i&&(i="Escape"),"Del"==i&&(i="Delete"),"Left"==i&&(i="ArrowLeft"),"Up"==i&&(i="ArrowUp"),"Right"==i&&(i="ArrowRight"),"Down"==i&&(i="ArrowDown"),i}function ft(e){let t;return t=11==e.nodeType?e.getSelection?e:e.ownerDocument:e,t.getSelection()}function pt(e,t){return!!t&&(e==t||e.contains(1!=t.nodeType?t.parentNode:t))}function mt(){let e=document.activeElement;while(e&&e.shadowRoot)e=e.shadowRoot.activeElement;return e}function gt(e,t){if(!t.anchorNode)return!1;try{return pt(e,t.anchorNode)}catch(vm){return!1}}function vt(e){return 3==e.nodeType?Ft(e,0,e.nodeValue.length).getClientRects():1==e.nodeType?e.getClientRects():[]}function xt(e,t,i,n){return!!i&&(bt(e,t,i,n,-1)||bt(e,t,i,n,1))}function wt(e){for(var t=0;;t++)if(e=e.previousSibling,!e)return t}function bt(e,t,i,n,r){for(;;){if(e==i&&t==n)return!0;if(t==(r<0?0:yt(e))){if("DIV"==e.nodeName)return!1;let i=e.parentNode;if(!i||1!=i.nodeType)return!1;t=wt(e)+(r<0?0:1),e=i}else{if(1!=e.nodeType)return!1;if(e=e.childNodes[t+(r<0?-1:0)],1==e.nodeType&&"false"==e.contentEditable)return!1;t=r<0?yt(e):0}}}function yt(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}const kt={left:0,right:0,top:0,bottom:0};function Dt(e,t){let i=t?e.left:e.right;return{left:i,right:i,top:e.top,bottom:e.bottom}}function Ct(e){return{left:0,right:e.innerWidth,top:0,bottom:e.innerHeight}}function St(e,t,i,n,r,s,o,l){let a=e.ownerDocument,h=a.defaultView;for(let c=e;c;)if(1==c.nodeType){let e,u=c==a.body;if(u)e=Ct(h);else{if(c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.parentNode;continue}let t=c.getBoundingClientRect();e={left:t.left,right:t.left+c.clientWidth,top:t.top,bottom:t.top+c.clientHeight}}let d=0,f=0;if("nearest"==r)t.top<e.top?(f=-(e.top-t.top+o),i>0&&t.bottom>e.bottom+f&&(f=t.bottom-e.bottom+f+o)):t.bottom>e.bottom&&(f=t.bottom-e.bottom+o,i<0&&t.top-f<e.top&&(f=-(e.top+f-t.top+o)));else{let n=t.bottom-t.top,s=e.bottom-e.top,l="center"==r&&n<=s?t.top+n/2-s/2:"start"==r||"center"==r&&i<0?t.top-o:t.bottom-s+o;f=l-e.top}if("nearest"==n)t.left<e.left?(d=-(e.left-t.left+s),i>0&&t.right>e.right+d&&(d=t.right-e.right+d+s)):t.right>e.right&&(d=t.right-e.right+s,i<0&&t.left<e.left+d&&(d=-(e.left+d-t.left+s)));else{let i="center"==n?t.left+(t.right-t.left)/2-(e.right-e.left)/2:"start"==n==l?t.left-s:t.right-(e.right-e.left)+s;d=i-e.left}if(d||f)if(u)h.scrollBy(d,f);else{if(f){let e=c.scrollTop;c.scrollTop+=f,f=c.scrollTop-e}if(d){let e=c.scrollLeft;c.scrollLeft+=d,d=c.scrollLeft-e}t={left:t.left-d,top:t.top-f,right:t.right-d,bottom:t.bottom-f}}if(u)break;c=c.assignedSlot||c.parentNode,n=r="nearest"}else{if(11!=c.nodeType)break;c=c.host}}class At{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(e){return this.anchorNode==e.anchorNode&&this.anchorOffset==e.anchorOffset&&this.focusNode==e.focusNode&&this.focusOffset==e.focusOffset}setRange(e){this.set(e.anchorNode,e.anchorOffset,e.focusNode,e.focusOffset)}set(e,t,i,n){this.anchorNode=e,this.anchorOffset=t,this.focusNode=i,this.focusOffset=n}}let Et,Mt=null;function Bt(e){if(e.setActive)return e.setActive();if(Mt)return e.focus(Mt);let t=[];for(let i=e;i;i=i.parentNode)if(t.push(i,i.scrollTop,i.scrollLeft),i==i.ownerDocument)break;if(e.focus(null==Mt?{get preventScroll(){return Mt={preventScroll:!0},!0}}:void 0),!Mt){Mt=!1;for(let e=0;e<t.length;){let i=t[e++],n=t[e++],r=t[e++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=r&&(i.scrollLeft=r)}}}function Ft(e,t,i=t){let n=Et||(Et=document.createRange());return n.setEnd(e,i),n.setStart(e,t),n}function Tt(e,t,i){let n={key:t,code:t,keyCode:i,which:i,cancelable:!0},r=new KeyboardEvent("keydown",n);r.synthetic=!0,e.dispatchEvent(r);let s=new KeyboardEvent("keyup",n);return s.synthetic=!0,e.dispatchEvent(s),r.defaultPrevented||s.defaultPrevented}function Ot(e){while(e){if(e&&(9==e.nodeType||11==e.nodeType&&e.host))return e;e=e.assignedSlot||e.parentNode}return null}function Lt(e){while(e.attributes.length)e.removeAttributeNode(e.attributes[0])}class Rt{constructor(e,t,i=!0){this.node=e,this.offset=t,this.precise=i}static before(e,t){return new Rt(e.parentNode,wt(e),t)}static after(e,t){return new Rt(e.parentNode,wt(e)+1,t)}}const It=[];class Nt{constructor(){this.parent=null,this.dom=null,this.dirty=2}get editorView(){if(!this.parent)throw new Error("Accessing view in orphan content view");return this.parent.editorView}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(e){let t=this.posAtStart;for(let i of this.children){if(i==e)return t;t+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(e){return this.posBefore(e)+e.length}coordsAt(e,t){return null}sync(e){if(2&this.dirty){let t,i=this.dom,n=null;for(let r of this.children){if(r.dirty){if(!r.dom&&(t=n?n.nextSibling:i.firstChild)){let e=Nt.get(t);e&&(e.parent||e.constructor!=r.constructor)||r.reuseDOM(t)}r.sync(e),r.dirty=0}if(t=n?n.nextSibling:i.firstChild,e&&!e.written&&e.node==i&&t!=r.dom&&(e.written=!0),r.dom.parentNode==i)while(t&&t!=r.dom)t=Pt(t);else i.insertBefore(r.dom,t);n=r.dom}t=n?n.nextSibling:i.firstChild,t&&e&&e.node==i&&(e.written=!0);while(t)t=Pt(t)}else if(1&this.dirty)for(let t of this.children)t.dirty&&(t.sync(e),t.dirty=0)}reuseDOM(e){}localPosFromDOM(e,t){let i;if(e==this.dom)i=this.dom.childNodes[t];else{let n=0==yt(e)?0:0==t?-1:1;for(;;){let t=e.parentNode;if(t==this.dom)break;0==n&&t.firstChild!=t.lastChild&&(n=e==t.firstChild?-1:1),e=t}i=n<0?e:e.nextSibling}if(i==this.dom.firstChild)return 0;while(i&&!Nt.get(i))i=i.nextSibling;if(!i)return this.length;for(let n=0,r=0;;n++){let e=this.children[n];if(e.dom==i)return r;r+=e.length+e.breakAfter}}domBoundsAround(e,t,i=0){let n=-1,r=-1,s=-1,o=-1;for(let l=0,a=i,h=i;l<this.children.length;l++){let i=this.children[l],c=a+i.length;if(a<e&&c>t)return i.domBoundsAround(e,t,a);if(c>=e&&-1==n&&(n=l,r=a),a>t&&i.dom.parentNode==this.dom){s=l,o=h;break}h=c,a=c+i.breakAfter}return{from:r,to:o<0?i+this.length:o,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:s<this.children.length&&s>=0?this.children[s].dom:null}}markDirty(e=!1){this.dirty|=2,this.markParentsDirty(e)}markParentsDirty(e){for(let t=this.parent;t;t=t.parent){if(e&&(t.dirty|=2),1&t.dirty)return;t.dirty|=1,e=!1}}setParent(e){this.parent!=e&&(this.parent=e,this.dirty&&this.markParentsDirty(!0))}setDOM(e){this.dom&&(this.dom.cmView=null),this.dom=e,e.cmView=this}get rootView(){for(let e=this;;){let t=e.parent;if(!t)return e;e=t}}replaceChildren(e,t,i=It){this.markDirty();for(let n=e;n<t;n++){let e=this.children[n];e.parent==this&&e.destroy()}this.children.splice(e,t-e,...i);for(let n=0;n<i.length;n++)i[n].setParent(this)}ignoreMutation(e){return!1}ignoreEvent(e){return!1}childCursor(e=this.length){return new zt(this.children,e,this.children.length)}childPos(e,t=1){return this.childCursor().findPos(e,t)}toString(){let e=this.constructor.name.replace("View","");return e+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==e?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(e){return e.cmView}get isEditable(){return!0}merge(e,t,i,n,r,s){return!1}become(e){return!1}getSide(){return 0}destroy(){this.parent=null}}function Pt(e){let t=e.nextSibling;return e.parentNode.removeChild(e),t}Nt.prototype.breakAfter=0;class zt{constructor(e,t,i){this.children=e,this.pos=t,this.i=i,this.off=0}findPos(e,t=1){for(;;){if(e>this.pos||e==this.pos&&(t>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=e-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function _t(e,t,i,n,r,s,o,l,a){let{children:h}=e,c=h.length?h[t]:null,u=s.length?s[s.length-1]:null,d=u?u.breakAfter:o;if(!(t==n&&c&&!o&&!d&&s.length<2&&c.merge(i,r,s.length?u:null,0==i,l,a))){if(n<h.length){let e=h[n];e&&r<e.length?(t==n&&(e=e.split(r),r=0),!d&&u&&e.merge(0,r,u,!0,0,a)?s[s.length-1]=e:(r&&e.merge(0,r,null,!1,0,a),s.push(e))):(null===e||void 0===e?void 0:e.breakAfter)&&(u?u.breakAfter=1:o=1),n++}c&&(c.breakAfter=o,i>0&&(!o&&s.length&&c.merge(i,c.length,s[0],!1,l,0)?c.breakAfter=s.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,l,0),t++));while(t<n&&s.length)if(h[n-1].become(s[s.length-1]))n--,s.pop(),a=s.length?0:l;else{if(!h[t].become(s[0]))break;t++,s.shift(),l=s.length?0:a}!s.length&&t&&n<h.length&&!h[t-1].breakAfter&&h[n].merge(0,0,h[t-1],!1,l,a)&&t--,(t<n||s.length)&&e.replaceChildren(t,n,s)}}function Ht(e,t,i,n,r,s){let o=e.childCursor(),{i:l,off:a}=o.findPos(i,1),{i:h,off:c}=o.findPos(t,-1),u=t-i;for(let d of n)u+=d.length;e.length+=u,_t(e,h,c,l,a,n,0,r,s)}let qt="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},Vt="undefined"!=typeof document?document:{documentElement:{style:{}}};const Wt=/Edge\/(\d+)/.exec(qt.userAgent),jt=/MSIE \d/.test(qt.userAgent),$t=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(qt.userAgent),Ut=!!(jt||$t||Wt),Gt=!Ut&&/gecko\/(\d+)/i.test(qt.userAgent),Kt=!Ut&&/Chrome\/(\d+)/.exec(qt.userAgent),Jt="webkitFontSmoothing"in Vt.documentElement.style,Xt=!Ut&&/Apple Computer/.test(qt.vendor),Zt=Xt&&(/Mobile\/\w+/.test(qt.userAgent)||qt.maxTouchPoints>2);var Qt={mac:Zt||/Mac/.test(qt.platform),windows:/Win/.test(qt.platform),linux:/Linux|X11/.test(qt.platform),ie:Ut,ie_version:jt?Vt.documentMode||6:$t?+$t[1]:Wt?+Wt[1]:0,gecko:Gt,gecko_version:Gt?+(/Firefox\/(\d+)/.exec(qt.userAgent)||[0,0])[1]:0,chrome:!!Kt,chrome_version:Kt?+Kt[1]:0,ios:Zt,android:/Android\b/.test(qt.userAgent),webkit:Jt,safari:Xt,webkit_version:Jt?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:null!=Vt.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};const Yt=256;class ei extends Nt{constructor(e){super(),this.text=e}get length(){return this.text.length}createDOM(e){this.setDOM(e||document.createTextNode(this.text))}sync(e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(e){3==e.nodeType&&this.createDOM(e)}merge(e,t,i){return(!i||i instanceof ei&&!(this.length-(t-e)+i.length>Yt))&&(this.text=this.text.slice(0,e)+(i?i.text:"")+this.text.slice(t),this.markDirty(),!0)}split(e){let t=new ei(this.text.slice(e));return this.text=this.text.slice(0,e),this.markDirty(),t}localPosFromDOM(e,t){return e==this.dom?t:t?this.text.length:0}domAtPos(e){return new Rt(this.dom,e)}domBoundsAround(e,t,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(e,t){return ii(this.dom,e,t)}}class ti extends Nt{constructor(e,t=[],i=0){super(),this.mark=e,this.children=t,this.length=i;for(let n of t)n.setParent(this)}setAttrs(e){if(Lt(e),this.mark.class&&(e.className=this.mark.class),this.mark.attrs)for(let t in this.mark.attrs)e.setAttribute(t,this.mark.attrs[t]);return e}reuseDOM(e){e.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(e),this.dirty|=6)}sync(e){this.dom?4&this.dirty&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(e)}merge(e,t,i,n,r,s){return(!i||!(!(i instanceof ti&&i.mark.eq(this.mark))||e&&r<=0||t<this.length&&s<=0))&&(Ht(this,e,t,i?i.children:[],r-1,s-1),this.markDirty(),!0)}split(e){let t=[],i=0,n=-1,r=0;for(let o of this.children){let s=i+o.length;s>e&&t.push(i<e?o.split(e-i):o),n<0&&i>=e&&(n=r),i=s,r++}let s=this.length-e;return this.length=e,n>-1&&(this.children.length=n,this.markDirty()),new ti(this.mark,t,s)}domAtPos(e){return hi(this.dom,this.children,e)}coordsAt(e,t){return ui(this,e,t)}}function ii(e,t,i){let n=e.nodeValue.length;t>n&&(t=n);let r=t,s=t,o=0;0==t&&i<0||t==n&&i>=0?Qt.chrome||Qt.gecko||(t?(r--,o=1):s<n&&(s++,o=-1)):i<0?r--:s<n&&s++;let l=Ft(e,r,s).getClientRects();if(!l.length)return kt;let a=l[(o?o<0:i>=0)?0:l.length-1];return Qt.safari&&!o&&0==a.width&&(a=Array.prototype.find.call(l,e=>e.width)||a),o?Dt(a,o<0):a||null}class ni extends Nt{constructor(e,t,i){super(),this.widget=e,this.length=t,this.side=i,this.prevWidget=null}static create(e,t,i){return new(e.customView||ni)(e,t,i)}split(e){let t=ni.create(this.widget,this.length-e,this.side);return this.length-=e,t}sync(){this.dom&&this.widget.updateDOM(this.dom)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(this.editorView)),this.dom.contentEditable="false")}getSide(){return this.side}merge(e,t,i,n,r,s){return!(i&&(!(i instanceof ni&&this.widget.compare(i.widget))||e>0&&r<=0||t<this.length&&s<=0))&&(this.length=e+(i?i.length:0)+(this.length-t),!0)}become(e){return e.length==this.length&&e instanceof ni&&e.side==this.side&&this.widget.constructor==e.widget.constructor&&(this.widget.eq(e.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,!0)}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}get overrideDOMText(){if(0==this.length)return n.empty;let e=this;while(e.parent)e=e.parent;let t=e.editorView,i=t&&t.state.doc,r=this.posAtStart;return i?i.slice(r,r+this.length):n.empty}domAtPos(e){return 0==e?Rt.before(this.dom):Rt.after(this.dom,e==this.length)}domBoundsAround(){return null}coordsAt(e,t){let i=this.dom.getClientRects(),n=null;if(!i.length)return kt;for(let r=e>0?i.length-1:0;;r+=e>0?-1:1)if(n=i[r],e>0?0==r:r==i.length-1||n.top<n.bottom)break;return 0==e&&t>0||e==this.length&&t<=0?n:Dt(n,0==e)}get isEditable(){return!1}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class ri extends ni{domAtPos(e){let{topView:t,text:i}=this.widget;return t?si(e,0,t,i,(e,t)=>e.domAtPos(t),e=>new Rt(i,Math.min(e,i.nodeValue.length))):new Rt(i,Math.min(e,i.nodeValue.length))}sync(){this.setDOM(this.widget.toDOM())}localPosFromDOM(e,t){let{topView:i,text:n}=this.widget;return i?oi(e,t,i,n):Math.min(t,this.length)}ignoreMutation(){return!1}get overrideDOMText(){return null}coordsAt(e,t){let{topView:i,text:n}=this.widget;return i?si(e,t,i,n,(e,t,i)=>e.coordsAt(t,i),(e,t)=>ii(n,e,t)):ii(n,e,t)}destroy(){var e;super.destroy(),null===(e=this.widget.topView)||void 0===e||e.destroy()}get isEditable(){return!0}}function si(e,t,i,n,r,s){if(i instanceof ti){for(let o of i.children){let i=pt(o.dom,n),l=i?n.nodeValue.length:o.length;if(e<l||e==l&&o.getSide()<=0)return i?si(e,t,o,n,r,s):r(o,e,t);e-=l}return r(i,i.length,-1)}return i.dom==n?s(e,t):r(i,e,t)}function oi(e,t,i,n){if(i instanceof ti)for(let r of i.children){let i=0,s=pt(r.dom,n);if(pt(r.dom,e))return i+(s?oi(e,t,r,n):r.localPosFromDOM(e,t));i+=s?n.nodeValue.length:r.length}else if(i.dom==n)return Math.min(t,n.nodeValue.length);return i.localPosFromDOM(e,t)}class li extends Nt{constructor(e){super(),this.side=e}get length(){return 0}merge(){return!1}become(e){return e instanceof li&&e.side==this.side}split(){return new li(this.side)}sync(){if(!this.dom){let e=document.createElement("img");e.className="cm-widgetBuffer",e.setAttribute("aria-hidden","true"),this.setDOM(e)}}getSide(){return this.side}domAtPos(e){return Rt.before(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(e){let t=this.dom.getBoundingClientRect(),i=ai(this,this.side>0?-1:1);return i&&i.top<t.bottom&&i.bottom>t.top?{left:t.left,right:t.right,top:i.top,bottom:i.bottom}:t}get overrideDOMText(){return n.empty}}function ai(e,t){let i=e.parent,n=i?i.children.indexOf(e):-1;while(i&&n>=0)if(t<0?n>0:n<i.children.length){let e=i.children[n+t];if(e instanceof ei){let i=e.coordsAt(t<0?e.length:0,t);if(i)return i}n+=t}else{if(!(i instanceof ti&&i.parent)){let e=i.dom.lastChild;if(e&&"BR"==e.nodeName)return e.getClientRects()[0];break}n=i.parent.children.indexOf(i)+(t<0?0:1),i=i.parent}}function hi(e,t,i){let n=0;for(let r=0;n<t.length;n++){let s=t[n],o=r+s.length;if(!(o==r&&s.getSide()<=0)){if(i>r&&i<o&&s.dom.parentNode==e)return s.domAtPos(i-r);if(i<=r)break;r=o}}for(;n>0;n--){let i=t[n-1].dom;if(i.parentNode==e)return Rt.after(i)}return new Rt(e,0)}function ci(e,t,i){let n,{children:r}=e;i>0&&t instanceof ti&&r.length&&(n=r[r.length-1])instanceof ti&&n.mark.eq(t.mark)?ci(n,t.children[0],i-1):(r.push(t),t.setParent(e)),e.length+=t.length}function ui(e,t,i){for(let s=0,o=0;o<e.children.length;o++){let n,r=e.children[o],l=s+r.length;if((i<=0||l==e.length||r.getSide()>0?l>=t:l>t)&&(t<l||o+1==e.children.length||(n=e.children[o+1]).length||n.getSide()>0)){let e=0;if(l==s){if(r.getSide()<=0)continue;e=i=-r.getSide()}let n=r.coordsAt(Math.max(0,t-s),i);return e&&n?Dt(n,i<0):n}s=l}let n=e.dom.lastChild;if(!n)return e.dom.getBoundingClientRect();let r=vt(n);return r[r.length-1]||null}function di(e,t){for(let i in e)"class"==i&&t.class?t.class+=" "+e.class:"style"==i&&t.style?t.style+=";"+e.style:t[i]=e[i];return t}function fi(e,t){if(e==t)return!0;if(!e||!t)return!1;let i=Object.keys(e),n=Object.keys(t);if(i.length!=n.length)return!1;for(let r of i)if(-1==n.indexOf(r)||e[r]!==t[r])return!1;return!0}function pi(e,t,i){let n=null;if(t)for(let r in t)i&&r in i||e.removeAttribute(n=r);if(i)for(let r in i)t&&t[r]==i[r]||e.setAttribute(n=r,i[r]);return!!n}ei.prototype.children=ni.prototype.children=li.prototype.children=It;class mi{eq(e){return!1}updateDOM(e){return!1}compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}get estimatedHeight(){return-1}ignoreEvent(e){return!0}get customView(){return null}destroy(e){}}var gi=function(e){return e[e["Text"]=0]="Text",e[e["WidgetBefore"]=1]="WidgetBefore",e[e["WidgetAfter"]=2]="WidgetAfter",e[e["WidgetRange"]=3]="WidgetRange",e}(gi||(gi={}));class vi extends Fe{constructor(e,t,i,n){super(),this.startSide=e,this.endSide=t,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(e){return new xi(e)}static widget(e){let t=e.side||0,i=!!e.block;return t+=i?t>0?3e8:-4e8:t>0?1e8:-1e8,new bi(e,t,t,i,e.widget||null,!1)}static replace(e){let t,i,n=!!e.block;if(e.isBlockGap)t=-5e8,i=4e8;else{let{start:r,end:s}=yi(e,n);t=(r?n?-3e8:-1:5e8)-1,i=1+(s?n?2e8:1:-6e8)}return new bi(e,t,i,n,e.widget||null,!0)}static line(e){return new wi(e)}static set(e,t=!1){return Re.of(e,t)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}vi.none=Re.empty;class xi extends vi{constructor(e){let{start:t,end:i}=yi(e);super(t?-1:5e8,i?1:-6e8,null,e),this.tagName=e.tagName||"span",this.class=e.class||"",this.attrs=e.attributes||null}eq(e){return this==e||e instanceof xi&&this.tagName==e.tagName&&this.class==e.class&&fi(this.attrs,e.attrs)}range(e,t=e){if(e>=t)throw new RangeError("Mark decorations may not be empty");return super.range(e,t)}}xi.prototype.point=!1;class wi extends vi{constructor(e){super(-2e8,-2e8,null,e)}eq(e){return e instanceof wi&&fi(this.spec.attributes,e.spec.attributes)}range(e,t=e){if(t!=e)throw new RangeError("Line decoration ranges must be zero-length");return super.range(e,t)}}wi.prototype.mapMode=A.TrackBefore,wi.prototype.point=!0;class bi extends vi{constructor(e,t,i,n,r,s){super(t,i,r,e),this.block=n,this.isReplace=s,this.mapMode=n?t<=0?A.TrackBefore:A.TrackAfter:A.TrackDel}get type(){return this.startSide<this.endSide?gi.WidgetRange:this.startSide<=0?gi.WidgetBefore:gi.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&this.widget.estimatedHeight>=5}eq(e){return e instanceof bi&&ki(this.widget,e.widget)&&this.block==e.block&&this.startSide==e.startSide&&this.endSide==e.endSide}range(e,t=e){if(this.isReplace&&(e>t||e==t&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&t!=e)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(e,t)}}function yi(e,t=!1){let{inclusiveStart:i,inclusiveEnd:n}=e;return null==i&&(i=e.inclusive),null==n&&(n=e.inclusive),{start:null!==i&&void 0!==i?i:t,end:null!==n&&void 0!==n?n:t}}function ki(e,t){return e==t||!!(e&&t&&e.compare(t))}function Di(e,t,i,n=0){let r=i.length-1;r>=0&&i[r]+n>=e?i[r]=Math.max(i[r],t):i.push(e,t)}bi.prototype.point=!0;class Ci extends Nt{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(e,t,i,n,r,s){if(i){if(!(i instanceof Ci))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),Ht(this,e,t,i?i.children:[],r,s),!0}split(e){let t=new Ci;if(t.breakAfter=this.breakAfter,0==this.length)return t;let{i:i,off:n}=this.childPos(e);n&&(t.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)t.append(this.children[r],0);while(i>0&&0==this.children[i-1].length)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=e,t}transferDOM(e){this.dom&&(this.markDirty(),e.setDOM(this.dom),e.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(e){fi(this.attrs,e)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=e)}append(e,t){ci(this,e,t)}addLineDeco(e){let t=e.spec.attributes,i=e.spec.class;t&&(this.attrs=di(t,this.attrs||{})),i&&(this.attrs=di({class:i},this.attrs||{}))}domAtPos(e){return hi(this.dom,this.children,e)}reuseDOM(e){"DIV"==e.nodeName&&(this.setDOM(e),this.dirty|=6)}sync(e){var t;this.dom?4&this.dirty&&(Lt(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(pi(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(e);let i=this.dom.lastChild;while(i&&Nt.get(i)instanceof ti)i=i.lastChild;if(!i||!this.length||"BR"!=i.nodeName&&0==(null===(t=Nt.get(i))||void 0===t?void 0:t.isEditable)&&(!Qt.ios||!this.children.some(e=>e instanceof ei))){let e=document.createElement("BR");e.cmIgnore=!0,this.dom.appendChild(e)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let e=0;for(let t of this.children){if(!(t instanceof ei))return null;let i=vt(t.dom);if(1!=i.length)return null;e+=i[0].width}return{lineHeight:this.dom.getBoundingClientRect().height,charWidth:e/this.length}}coordsAt(e,t){return ui(this,e,t)}become(e){return!1}get type(){return gi.Text}static find(e,t){for(let i=0,n=0;i<e.children.length;i++){let r=e.children[i],s=n+r.length;if(s>=t){if(r instanceof Ci)return r;if(s>t)break}n=s+r.breakAfter}return null}}class Si extends Nt{constructor(e,t,i){super(),this.widget=e,this.length=t,this.type=i,this.breakAfter=0,this.prevWidget=null}merge(e,t,i,n,r,s){return!(i&&(!(i instanceof Si&&this.widget.compare(i.widget))||e>0&&r<=0||t<this.length&&s<=0))&&(this.length=e+(i?i.length:0)+(this.length-t),!0)}domAtPos(e){return 0==e?Rt.before(this.dom):Rt.after(this.dom,e==this.length)}split(e){let t=this.length-e;this.length=e;let i=new Si(this.widget,t,this.type);return i.breakAfter=this.breakAfter,i}get children(){return It}sync(){this.dom&&this.widget.updateDOM(this.dom)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(this.editorView)),this.dom.contentEditable="false")}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):n.empty}domBoundsAround(){return null}become(e){return e instanceof Si&&e.type==this.type&&e.widget.constructor==this.widget.constructor&&(e.widget.eq(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=e.widget,this.length=e.length,this.breakAfter=e.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(e){return this.widget.ignoreEvent(e)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class Ai{constructor(e,t,i,n){this.doc=e,this.pos=t,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=e.iter(),this.skip=t}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let e=this.content[this.content.length-1];return!e.breakAfter&&!(e instanceof Si&&e.type==gi.WidgetBefore)}getLine(){return this.curLine||(this.content.push(this.curLine=new Ci),this.atCursorPos=!0),this.curLine}flushBuffer(e){this.pendingBuffer&&(this.curLine.append(Ei(new li(-1),e),e.length),this.pendingBuffer=0)}addBlockWidget(e){this.flushBuffer([]),this.curLine=null,this.content.push(e)}finish(e){e?this.pendingBuffer=0:this.flushBuffer([]),this.posCovered()||this.getLine()}buildText(e,t,i){while(e>0){if(this.textOff==this.text.length){let{value:t,lineBreak:i,done:n}=this.cursor.next(this.skip);if(this.skip=0,n)throw new Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer([]),this.curLine=null,e--;continue}this.text=t,this.textOff=0}let n=Math.min(this.text.length-this.textOff,e,512);this.flushBuffer(t.slice(0,i)),this.getLine().append(Ei(new ei(this.text.slice(this.textOff,this.textOff+n)),t),i),this.atCursorPos=!0,this.textOff+=n,e-=n,i=0}}span(e,t,i,n){this.buildText(t-e,i,n),this.pos=t,this.openStart<0&&(this.openStart=n)}point(e,t,i,n,r,s){if(this.disallowBlockEffectsFor[s]&&i instanceof bi){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(t>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let o=t-e;if(i instanceof bi)if(i.block){let{type:e}=i;e!=gi.WidgetAfter||this.posCovered()||this.getLine(),this.addBlockWidget(new Si(i.widget||new Mi("div"),o,e))}else{let s=ni.create(i.widget||new Mi("span"),o,i.startSide),l=this.atCursorPos&&!s.isEditable&&r<=n.length&&(e<t||i.startSide>0),a=!s.isEditable&&(e<t||i.startSide<=0),h=this.getLine();2!=this.pendingBuffer||l||(this.pendingBuffer=0),this.flushBuffer(n),l&&(h.append(Ei(new li(1),n),r),r=n.length+Math.max(0,r-n.length)),h.append(Ei(s,n),r),this.atCursorPos=a,this.pendingBuffer=a?e<t?1:2:0}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);o&&(this.textOff+o<=this.text.length?this.textOff+=o:(this.skip+=o-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=t),this.openStart<0&&(this.openStart=r)}static build(e,t,i,n,r){let s=new Ai(e,t,i,r);return s.openEnd=Re.spans(n,t,i,s),s.openStart<0&&(s.openStart=s.openEnd),s.finish(s.openEnd),s}}function Ei(e,t){for(let i of t)e=new ti(i,[e],e.length);return e}class Mi extends mi{constructor(e){super(),this.tag=e}eq(e){return e.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(e){return e.nodeName.toLowerCase()==this.tag}}const Bi=_.define(),Fi=_.define(),Ti=_.define(),Oi=_.define(),Li=_.define(),Ri=_.define(),Ii=_.define({combine:e=>e.some(e=>e)});class Ni{constructor(e,t="nearest",i="nearest",n=5,r=5){this.range=e,this.y=t,this.x=i,this.yMargin=n,this.xMargin=r}map(e){return e.empty?this:new Ni(this.range.map(e),this.y,this.x,this.yMargin,this.xMargin)}}const Pi=fe.define({map:(e,t)=>e.map(t)});function zi(e,t,i){let n=e.facet(Oi);n.length?n[0](t):window.onerror?window.onerror(String(t),i,void 0,void 0,t):i?console.error(i+":",t):console.error(t)}const _i=_.define({combine:e=>!e.length||e[0]});let Hi=0;const qi=_.define();class Vi{constructor(e,t,i,n){this.id=e,this.create=t,this.domEventHandlers=i,this.extension=n(this)}static define(e,t){const{eventHandlers:i,provide:n,decorations:r}=t||{};return new Vi(Hi++,e,i,e=>{let t=[qi.of(e)];return r&&t.push(Ui.of(t=>{let i=t.plugin(e);return i?r(i):vi.none})),n&&t.push(n(e)),t})}static fromClass(e,t){return Vi.define(t=>new e(t),t)}}class Wi{constructor(e){this.spec=e,this.mustUpdate=null,this.value=null}update(e){if(this.value){if(this.mustUpdate){let e=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(e)}catch(t){if(zi(e.state,t,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(vm){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(e)}catch(t){zi(e.state,t,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(e){var t;if(null===(t=this.value)||void 0===t?void 0:t.destroy)try{this.value.destroy()}catch(i){zi(e.state,i,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const ji=_.define(),$i=_.define(),Ui=_.define(),Gi=_.define(),Ki=_.define(),Ji=_.define();class Xi{constructor(e,t,i,n){this.fromA=e,this.toA=t,this.fromB=i,this.toB=n}join(e){return new Xi(Math.min(this.fromA,e.fromA),Math.max(this.toA,e.toA),Math.min(this.fromB,e.fromB),Math.max(this.toB,e.toB))}addToSet(e){let t=e.length,i=this;for(;t>0;t--){let n=e[t-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),e.splice(t-1,1)}}return e.splice(t,0,i),e}static extendWithRanges(e,t){if(0==t.length)return e;let i=[];for(let n=0,r=0,s=0,o=0;;n++){let l=n==e.length?null:e[n],a=s-o,h=l?l.fromB:1e9;while(r<t.length&&t[r]<h){let e=t[r],n=t[r+1],s=Math.max(o,e),l=Math.min(h,n);if(s<=l&&new Xi(s+a,l+a,s,l).addToSet(i),n>h)break;r+=2}if(!l)return i;new Xi(l.fromA,l.toA,l.fromB,l.toB).addToSet(i),s=l.toA,o=l.toB}}}class Zi{constructor(e,t,i){this.view=e,this.state=t,this.transactions=i,this.flags=0,this.startState=e.state,this.changes=M.empty(this.startState.doc.length);for(let s of i)this.changes=this.changes.compose(s.changes);let n=[];this.changes.iterChangedRanges((e,t,i,r)=>n.push(new Xi(e,t,i,r))),this.changedRanges=n;let r=e.hasFocus;r!=e.inputState.notifiedFocused&&(e.inputState.notifiedFocused=r,this.flags|=1)}static create(e,t,i){return new Zi(e,t,i)}get viewportChanged(){return(4&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(10&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(e=>e.selection)}get empty(){return 0==this.flags&&0==this.transactions.length}}var Qi=function(e){return e[e["LTR"]=0]="LTR",e[e["RTL"]=1]="RTL",e}(Qi||(Qi={}));const Yi=Qi.LTR,en=Qi.RTL;function tn(e){let t=[];for(let i=0;i<e.length;i++)t.push(1<<+e[i]);return t}const nn=tn("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),rn=tn("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),sn=Object.create(null),on=[];for(let xm of["()","[]","{}"]){let e=xm.charCodeAt(0),t=xm.charCodeAt(1);sn[e]=t,sn[t]=-e}function ln(e){return e<=247?nn[e]:1424<=e&&e<=1524?2:1536<=e&&e<=1785?rn[e-1536]:1774<=e&&e<=2220?4:8192<=e&&e<=8203||8204==e?256:1}const an=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;class hn{constructor(e,t,i){this.from=e,this.to=t,this.level=i}get dir(){return this.level%2?en:Yi}side(e,t){return this.dir==t==e?this.to:this.from}static find(e,t,i,n){let r=-1;for(let s=0;s<e.length;s++){let o=e[s];if(o.from<=t&&o.to>=t){if(o.level==i)return s;(r<0||(0!=n?n<0?o.from<t:o.to>t:e[r].level>o.level))&&(r=s)}}if(r<0)throw new RangeError("Index out of range");return r}}const cn=[];function un(e,t){let i=e.length,n=t==Yi?1:2,r=t==Yi?2:1;if(!e||1==n&&!an.test(e))return dn(i);for(let o=0,l=n,a=n;o<i;o++){let t=ln(e.charCodeAt(o));512==t?t=l:8==t&&4==a&&(t=16),cn[o]=4==t?2:t,7&t&&(a=t),l=t}for(let o=0,l=n,a=n;o<i;o++){let e=cn[o];if(128==e)o<i-1&&l==cn[o+1]&&24&l?e=cn[o]=l:cn[o]=256;else if(64==e){let e=o+1;while(e<i&&64==cn[e])e++;let t=o&&8==l||e<i&&8==cn[e]?1==a?1:8:256;for(let i=o;i<e;i++)cn[i]=t;o=e-1}else 8==e&&1==a&&(cn[o]=1);l=e,7&e&&(a=e)}for(let o,l,a,h=0,c=0,u=0;h<i;h++)if(l=sn[o=e.charCodeAt(h)])if(l<0){for(let e=c-3;e>=0;e-=3)if(on[e+1]==-l){let t=on[e+2],i=2&t?n:4&t?1&t?r:n:0;i&&(cn[h]=cn[on[e]]=i),c=e;break}}else{if(189==on.length)break;on[c++]=h,on[c++]=o,on[c++]=u}else if(2==(a=cn[h])||1==a){let e=a==n;u=e?0:1;for(let t=c-3;t>=0;t-=3){let i=on[t+2];if(2&i)break;if(e)on[t+2]|=2;else{if(4&i)break;on[t+2]|=4}}}for(let o=0;o<i;o++)if(256==cn[o]){let e=o+1;while(e<i&&256==cn[e])e++;let t=1==(o?cn[o-1]:n),r=1==(e<i?cn[e]:n),s=t==r?t?1:2:n;for(let i=o;i<e;i++)cn[i]=s;o=e-1}let s=[];if(1==n)for(let o=0;o<i;){let e=o,t=1!=cn[o++];while(o<i&&t==(1!=cn[o]))o++;if(t)for(let i=o;i>e;){let t=i,n=2!=cn[--i];while(i>e&&n==(2!=cn[i-1]))i--;s.push(new hn(i,t,n?2:1))}else s.push(new hn(e,o,0))}else for(let o=0;o<i;){let e=o,t=2==cn[o++];while(o<i&&t==(2==cn[o]))o++;s.push(new hn(e,o,t?1:2))}return s}function dn(e){return[new hn(0,e,0)]}let fn="";function pn(e,t,i,n,r){var s;let o=n.head-e.from,l=-1;if(0==o){if(!r||!e.length)return null;t[0].level!=i&&(o=t[0].side(!1,i),l=0)}else if(o==e.length){if(r)return null;let e=t[t.length-1];e.level!=i&&(o=e.side(!0,i),l=t.length-1)}l<0&&(l=hn.find(t,o,null!==(s=n.bidiLevel)&&void 0!==s?s:-1,n.assoc));let a=t[l];o==a.side(r,i)&&(a=t[l+=r?1:-1],o=a.side(!r,i));let h=r==(a.dir==i),c=v(e.text,o,h);if(fn=e.text.slice(Math.min(o,c),Math.max(o,c)),c!=a.side(r,i))return N.cursor(c+e.from,h?-1:1,a.level);let u=l==(r?t.length-1:0)?null:t[l+(r?1:-1)];return u||a.level==i?u&&u.level<a.level?N.cursor(u.side(!r,i)+e.from,r?1:-1,u.level):N.cursor(c+e.from,r?-1:1,a.level):N.cursor(r?e.to:e.from,r?-1:1,i)}const mn="￿";class gn{constructor(e,t){this.points=e,this.text="",this.lineSeparator=t.facet(Me.lineSeparator)}append(e){this.text+=e}lineBreak(){this.text+=mn}readRange(e,t){if(!e)return this;let i=e.parentNode;for(let n=e;;){this.findPointBefore(i,n),this.readNode(n);let e=n.nextSibling;if(e==t)break;let r=Nt.get(n),s=Nt.get(e);(r&&s?r.breakAfter:(r?r.breakAfter:vn(n))||vn(e)&&("BR"!=n.nodeName||n.cmIgnore))&&this.lineBreak(),n=e}return this.findPointBefore(i,t),this}readTextNode(e){let t=e.nodeValue;for(let i of this.points)i.node==e&&(i.pos=this.text.length+Math.min(i.offset,t.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let r,s=-1,o=1;if(this.lineSeparator?(s=t.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(r=n.exec(t))&&(s=r.index,o=r[0].length),this.append(t.slice(i,s<0?t.length:s)),s<0)break;if(this.lineBreak(),o>1)for(let t of this.points)t.node==e&&t.pos>this.text.length&&(t.pos-=o-1);i=s+o}}readNode(e){if(e.cmIgnore)return;let t=Nt.get(e),i=t&&t.overrideDOMText;if(null!=i){this.findPointInside(e,i.length);for(let e=i.iter();!e.next().done;)e.lineBreak?this.lineBreak():this.append(e.value)}else 3==e.nodeType?this.readTextNode(e):"BR"==e.nodeName?e.nextSibling&&this.lineBreak():1==e.nodeType&&this.readRange(e.firstChild,null)}findPointBefore(e,t){for(let i of this.points)i.node==e&&e.childNodes[i.offset]==t&&(i.pos=this.text.length)}findPointInside(e,t){for(let i of this.points)(3==e.nodeType?i.node==e:e.contains(i.node))&&(i.pos=this.text.length+Math.min(t,i.offset))}}function vn(e){return 1==e.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(e.nodeName)}class xn{constructor(e,t){this.node=e,this.offset=t,this.pos=-1}}class wn extends Nt{constructor(e){super(),this.view=e,this.compositionDeco=vi.none,this.decorations=[],this.dynamicDecorationMap=[],this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(e.contentDOM),this.children=[new Ci],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Xi(0,0,0,e.state.doc.length)],0)}get root(){return this.view.root}get editorView(){return this.view}get length(){return this.view.state.doc.length}update(e){let t=e.changedRanges;this.minWidth>0&&t.length&&(t.every(({fromA:e,toA:t})=>t<this.minWidthFrom||e>this.minWidthTo)?(this.minWidthFrom=e.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=e.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.view.inputState.composing<0?this.compositionDeco=vi.none:(e.transactions.length||this.dirty)&&(this.compositionDeco=Dn(this.view,e.changes)),(Qt.ie||Qt.chrome)&&!this.compositionDeco.size&&e&&e.state.doc.lines!=e.startState.doc.lines&&(this.forceSelection=!0);let i=this.decorations,n=this.updateDeco(),r=Mn(i,n,e.changes);return t=Xi.extendWithRanges(t,r),(0!=this.dirty||0!=t.length)&&(this.updateInner(t,e.startState.doc.length),e.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(e,t){this.view.viewState.mustMeasureContent=!0,this.updateChildren(e,t);let{observer:i}=this.view;i.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let e=Qt.chrome||Qt.ios?{node:i.selectionRange.focusNode,written:!1}:void 0;this.sync(e),this.dirty=0,e&&(e.written||i.selectionRange.focusNode!=e.node)&&(this.forceSelection=!0),this.dom.style.height=""});let n=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let r of this.children)r instanceof Si&&r.widget instanceof yn&&n.push(r.dom);i.updateGaps(n)}updateChildren(e,t){let i=this.childCursor(t);for(let n=e.length-1;;n--){let t=n>=0?e[n]:null;if(!t)break;let{fromA:r,toA:s,fromB:o,toB:l}=t,{content:a,breakAtStart:h,openStart:c,openEnd:u}=Ai.build(this.view.state.doc,o,l,this.decorations,this.dynamicDecorationMap),{i:d,off:f}=i.findPos(s,1),{i:p,off:m}=i.findPos(r,-1);_t(this,p,m,d,f,a,h,c,u)}}updateSelection(e=!1,t=!1){if(!e&&this.view.observer.selectionRange.focusNode||this.view.observer.readSelectionRange(),!t&&!this.mayControlSelection()||Qt.ios&&this.view.inputState.rapidCompositionStart)return;let i=this.forceSelection;this.forceSelection=!1;let n=this.view.state.selection.main,r=this.domAtPos(n.anchor),s=n.empty?r:this.domAtPos(n.head);if(Qt.gecko&&n.empty&&bn(r)){let e=document.createTextNode("");this.view.observer.ignore(()=>r.node.insertBefore(e,r.node.childNodes[r.offset]||null)),r=s=new Rt(e,0),i=!0}let o=this.view.observer.selectionRange;!i&&o.focusNode&&xt(r.node,r.offset,o.anchorNode,o.anchorOffset)&&xt(s.node,s.offset,o.focusNode,o.focusOffset)||(this.view.observer.ignore(()=>{Qt.android&&Qt.chrome&&this.dom.contains(o.focusNode)&&Bn(o.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let e=ft(this.root);if(e)if(n.empty){if(Qt.gecko){let e=An(r.node,r.offset);if(e&&3!=e){let t=Sn(r.node,r.offset,1==e?1:-1);t&&(r=new Rt(t,1==e?0:t.nodeValue.length))}}e.collapse(r.node,r.offset),null!=n.bidiLevel&&null!=o.cursorBidiLevel&&(o.cursorBidiLevel=n.bidiLevel)}else if(e.extend)e.collapse(r.node,r.offset),e.extend(s.node,s.offset);else{let t=document.createRange();n.anchor>n.head&&([r,s]=[s,r]),t.setEnd(s.node,s.offset),t.setStart(r.node,r.offset),e.removeAllRanges(),e.addRange(t)}else;}),this.view.observer.setSelectionRange(r,s)),this.impreciseAnchor=r.precise?null:new Rt(o.anchorNode,o.anchorOffset),this.impreciseHead=s.precise?null:new Rt(o.focusNode,o.focusOffset)}enforceCursorAssoc(){if(this.compositionDeco.size)return;let e=this.view.state.selection.main,t=ft(this.root);if(!t||!e.empty||!e.assoc||!t.modify)return;let i=Ci.find(this,e.head);if(!i)return;let n=i.posAtStart;if(e.head==n||e.head==n+i.length)return;let r=this.coordsAt(e.head,-1),s=this.coordsAt(e.head,1);if(!r||!s||r.bottom>s.top)return;let o=this.domAtPos(e.head+e.assoc);t.collapse(o.node,o.offset),t.modify("move",e.assoc<0?"forward":"backward","lineboundary")}mayControlSelection(){let e=this.root.activeElement;return e==this.dom||gt(this.dom,this.view.observer.selectionRange)&&!(e&&this.dom.contains(e))}nearest(e){for(let t=e;t;){let e=Nt.get(t);if(e&&e.rootView==this)return e;t=t.parentNode}return null}posFromDOM(e,t){let i=this.nearest(e);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(e,t)+i.posAtStart}domAtPos(e){let{i:t,off:i}=this.childCursor().findPos(e,-1);for(;t<this.children.length-1;){let e=this.children[t];if(i<e.length||e instanceof Ci)break;t++,i=0}return this.children[t].domAtPos(i)}coordsAt(e,t){for(let i=this.length,n=this.children.length-1;;n--){let r=this.children[n],s=i-r.breakAfter-r.length;if(e>s||e==s&&r.type!=gi.WidgetBefore&&r.type!=gi.WidgetAfter&&(!n||2==t||this.children[n-1].breakAfter||this.children[n-1].type==gi.WidgetBefore&&t>-2))return r.coordsAt(e-s,t);i=s}}measureVisibleLineHeights(e){let t=[],{from:i,to:n}=e,r=this.view.contentDOM.clientWidth,s=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,o=-1,l=this.view.textDirection==Qi.LTR;for(let a=0,h=0;h<this.children.length;h++){let e=this.children[h],c=a+e.length;if(c>n)break;if(a>=i){let i=e.dom.getBoundingClientRect();if(t.push(i.height),s){let t=e.dom.lastChild,n=t?vt(t):[];if(n.length){let e=n[n.length-1],t=l?e.right-i.left:i.right-e.left;t>o&&(o=t,this.minWidth=r,this.minWidthFrom=a,this.minWidthTo=c)}}}a=c+e.breakAfter}return t}textDirectionAt(e){let{i:t}=this.childPos(e,1);return"rtl"==getComputedStyle(this.children[t].dom).direction?Qi.RTL:Qi.LTR}measureTextSize(){for(let n of this.children)if(n instanceof Ci){let e=n.measureTextSize();if(e)return e}let e,t,i=document.createElement("div");return i.className="cm-line",i.style.width="99999px",i.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(i);let n=vt(i.firstChild)[0];e=i.getBoundingClientRect().height,t=n?n.width/27:7,i.remove()}),{lineHeight:e,charWidth:t}}childCursor(e=this.length){let t=this.children.length;return t&&(e-=this.children[--t].length),new zt(this.children,e,t)}computeBlockGapDeco(){let e=[],t=this.view.viewState;for(let i=0,n=0;;n++){let r=n==t.viewports.length?null:t.viewports[n],s=r?r.from-1:this.length;if(s>i){let n=t.lineBlockAt(s).bottom-t.lineBlockAt(i).top;e.push(vi.replace({widget:new yn(n),block:!0,inclusive:!0,isBlockGap:!0}).range(i,s))}if(!r)break;i=r.to+1}return vi.set(e)}updateDeco(){let e=this.view.state.facet(Ui).map((e,t)=>{let i=this.dynamicDecorationMap[t]="function"==typeof e;return i?e(this.view):e});for(let t=e.length;t<e.length+3;t++)this.dynamicDecorationMap[t]=!1;return this.decorations=[...e,this.compositionDeco,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco]}scrollIntoView(e){let t,{range:i}=e,n=this.coordsAt(i.head,i.empty?i.assoc:i.head>i.anchor?-1:1);if(!n)return;!i.empty&&(t=this.coordsAt(i.anchor,i.anchor>i.head?-1:1))&&(n={left:Math.min(n.left,t.left),top:Math.min(n.top,t.top),right:Math.max(n.right,t.right),bottom:Math.max(n.bottom,t.bottom)});let r=0,s=0,o=0,l=0;for(let h of this.view.state.facet(Ki).map(e=>e(this.view)))if(h){let{left:e,right:t,top:i,bottom:n}=h;null!=e&&(r=Math.max(r,e)),null!=t&&(s=Math.max(s,t)),null!=i&&(o=Math.max(o,i)),null!=n&&(l=Math.max(l,n))}let a={left:n.left-r,top:n.top-o,right:n.right+s,bottom:n.bottom+l};St(this.view.scrollDOM,a,i.head<i.anchor?-1:1,e.x,e.y,e.xMargin,e.yMargin,this.view.textDirection==Qi.LTR)}}function bn(e){return 1==e.node.nodeType&&e.node.firstChild&&(0==e.offset||"false"==e.node.childNodes[e.offset-1].contentEditable)&&(e.offset==e.node.childNodes.length||"false"==e.node.childNodes[e.offset].contentEditable)}class yn extends mi{constructor(e){super(),this.height=e}toDOM(){let e=document.createElement("div");return this.updateDOM(e),e}eq(e){return e.height==this.height}updateDOM(e){return e.style.height=this.height+"px",!0}get estimatedHeight(){return this.height}}function kn(e){let t=e.observer.selectionRange,i=t.focusNode&&Sn(t.focusNode,t.focusOffset,0);if(!i)return null;let n=e.docView.nearest(i);if(!n)return null;if(n instanceof Ci){let e=i;while(e.parentNode!=n.dom)e=e.parentNode;let t=e.previousSibling;while(t&&!Nt.get(t))t=t.previousSibling;let r=t?Nt.get(t).posAtEnd:n.posAtStart;return{from:r,to:r,node:e,text:i}}{for(;;){let{parent:e}=n;if(!e)return null;if(e instanceof Ci)break;n=e}let e=n.posAtStart;return{from:e,to:e+n.length,node:n.dom,text:i}}}function Dn(e,t){let i=kn(e);if(!i)return vi.none;let{from:n,to:r,node:s,text:o}=i,l=t.mapPos(n,1),a=Math.max(l,t.mapPos(r,-1)),{state:h}=e,c=3==s.nodeType?s.nodeValue:new gn([],h).readRange(s.firstChild,null).text;if(a-l<c.length)if(h.doc.sliceString(l,Math.min(h.doc.length,l+c.length),mn)==c)a=l+c.length;else{if(h.doc.sliceString(Math.max(0,a-c.length),a,mn)!=c)return vi.none;l=a-c.length}else if(h.doc.sliceString(l,a,mn)!=c)return vi.none;let u=Nt.get(s);return u instanceof ri?u=u.widget.topView:u&&(u.parent=null),vi.set(vi.replace({widget:new Cn(s,o,u),inclusive:!0}).range(l,a))}class Cn extends mi{constructor(e,t,i){super(),this.top=e,this.text=t,this.topView=i}eq(e){return this.top==e.top&&this.text==e.text}toDOM(){return this.top}ignoreEvent(){return!1}get customView(){return ri}}function Sn(e,t,i){for(;;){if(3==e.nodeType)return e;if(1==e.nodeType&&t>0&&i<=0)e=e.childNodes[t-1],t=yt(e);else{if(!(1==e.nodeType&&t<e.childNodes.length&&i>=0))return null;e=e.childNodes[t],t=0}}}function An(e,t){return 1!=e.nodeType?0:(t&&"false"==e.childNodes[t-1].contentEditable?1:0)|(t<e.childNodes.length&&"false"==e.childNodes[t].contentEditable?2:0)}class En{constructor(){this.changes=[]}compareRange(e,t){Di(e,t,this.changes)}comparePoint(e,t){Di(e,t,this.changes)}}function Mn(e,t,i){let n=new En;return Re.compare(e,t,i,n),n.changes}function Bn(e,t){for(let i=e;i&&i!=t;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}function Fn(e,t,i=1){let n=e.charCategorizer(t),r=e.doc.lineAt(t),s=t-r.from;if(0==r.length)return N.cursor(t);0==s?i=1:s==r.length&&(i=-1);let o=s,l=s;i<0?o=v(r.text,s,!1):l=v(r.text,s);let a=n(r.text.slice(o,l));while(o>0){let e=v(r.text,o,!1);if(n(r.text.slice(e,o))!=a)break;o=e}while(l<r.length){let e=v(r.text,l);if(n(r.text.slice(l,e))!=a)break;l=e}return N.range(o+r.from,l+r.from)}function Tn(e,t){return t.left>e?t.left-e:Math.max(0,e-t.right)}function On(e,t){return t.top>e?t.top-e:Math.max(0,e-t.bottom)}function Ln(e,t){return e.top<t.bottom-1&&e.bottom>t.top+1}function Rn(e,t){return t<e.top?{top:t,left:e.left,right:e.right,bottom:e.bottom}:e}function In(e,t){return t>e.bottom?{top:e.top,left:e.left,right:e.right,bottom:t}:e}function Nn(e,t,i){let n,r,s,o,l,a,h,c;for(let f=e.firstChild;f;f=f.nextSibling){let e=vt(f);for(let u=0;u<e.length;u++){let d=e[u];r&&Ln(r,d)&&(d=Rn(In(d,r.bottom),r.top));let p=Tn(t,d),m=On(i,d);if(0==p&&0==m)return 3==f.nodeType?Pn(f,t,i):Nn(f,t,i);(!n||o>m||o==m&&s>p)&&(n=f,r=d,s=p,o=m),0==p?i>d.bottom&&(!h||h.bottom<d.bottom)?(l=f,h=d):i<d.top&&(!c||c.top>d.top)&&(a=f,c=d):h&&Ln(h,d)?h=In(h,d.bottom):c&&Ln(c,d)&&(c=Rn(c,d.top))}}if(h&&h.bottom>=i?(n=l,r=h):c&&c.top<=i&&(n=a,r=c),!n)return{node:e,offset:0};let u=Math.max(r.left,Math.min(r.right,t));if(3==n.nodeType)return Pn(n,u,i);if(!s&&"true"==n.contentEditable)return Nn(n,u,i);let d=Array.prototype.indexOf.call(e.childNodes,n)+(t>=(r.left+r.right)/2?1:0);return{node:e,offset:d}}function Pn(e,t,i){let n=e.nodeValue.length,r=-1,s=1e9,o=0;for(let l=0;l<n;l++){let n=Ft(e,l,l+1).getClientRects();for(let a=0;a<n.length;a++){let h=n[a];if(h.top==h.bottom)continue;o||(o=t-h.left);let c=(h.top>i?h.top-i:i-h.bottom)-1;if(h.left-1<=t&&h.right+1>=t&&c<s){let i=t>=(h.left+h.right)/2,n=i;if(Qt.chrome||Qt.gecko){let t=Ft(e,l).getBoundingClientRect();t.left==h.right&&(n=!i)}if(c<=0)return{node:e,offset:l+(n?1:0)};r=l+(n?1:0),s=c}}}return{node:e,offset:r>-1?r:o>0?e.nodeValue.length:0}}function zn(e,{x:t,y:i},n,r=-1){var s;let o,l=e.contentDOM.getBoundingClientRect(),a=l.top+e.viewState.paddingTop,{docHeight:h}=e.viewState,c=i-a;if(c<0)return 0;if(c>h)return e.state.doc.length;for(let v=e.defaultLineHeight/2,x=!1;;){if(o=e.elementAtHeight(c),o.type==gi.Text)break;for(;;){if(c=r>0?o.bottom+v:o.top-v,c>=0&&c<=h)break;if(x)return n?null:0;x=!0,r=-r}}i=a+c;let u=o.from;if(u<e.viewport.from)return 0==e.viewport.from?0:n?null:_n(e,l,o,t,i);if(u>e.viewport.to)return e.viewport.to==e.state.doc.length?e.state.doc.length:n?null:_n(e,l,o,t,i);let d=e.dom.ownerDocument,f=e.root.elementFromPoint?e.root:d,p=f.elementFromPoint(t,i);p&&!e.contentDOM.contains(p)&&(p=null),p||(t=Math.max(l.left+1,Math.min(l.right-1,t)),p=f.elementFromPoint(t,i),p&&!e.contentDOM.contains(p)&&(p=null));let m,g=-1;if(p&&0!=(null===(s=e.docView.nearest(p))||void 0===s?void 0:s.isEditable))if(d.caretPositionFromPoint){let e=d.caretPositionFromPoint(t,i);e&&({offsetNode:m,offset:g}=e)}else if(d.caretRangeFromPoint){let e=d.caretRangeFromPoint(t,i);e&&(({startContainer:m,startOffset:g}=e),Qt.safari&&Hn(m,g,t)&&(m=void 0))}if(!m||!e.docView.dom.contains(m)){let n=Ci.find(e.docView,u);if(!n)return c>o.top+o.height/2?o.to:o.from;({node:m,offset:g}=Nn(n.dom,t,i))}return e.docView.posFromDOM(m,g)}function _n(e,t,i,n,r){let s=Math.round((n-t.left)*e.defaultCharacterWidth);if(e.lineWrapping&&i.height>1.5*e.defaultLineHeight){let t=Math.floor((r-i.top)/e.defaultLineHeight);s+=t*e.viewState.heightOracle.lineLength}let o=e.state.sliceDoc(i.from,i.to);return i.from+Ke(o,s,e.state.tabSize)}function Hn(e,t,i){let n;if(3!=e.nodeType||t!=(n=e.nodeValue.length))return!1;for(let r=e.nextSibling;r;r=r.nextSibling)if(1!=r.nodeType||"BR"!=r.nodeName)return!1;return Ft(e,n-1,n).getBoundingClientRect().left>i}function qn(e,t,i,n){let r=e.state.doc.lineAt(t.head),s=n&&e.lineWrapping?e.coordsAtPos(t.assoc<0&&t.head>r.from?t.head-1:t.head):null;if(s){let t=e.dom.getBoundingClientRect(),n=e.textDirectionAt(r.from),o=e.posAtCoords({x:i==(n==Qi.LTR)?t.right-1:t.left+1,y:(s.top+s.bottom)/2});if(null!=o)return N.cursor(o,i?-1:1)}let o=Ci.find(e.docView,t.head),l=o?i?o.posAtEnd:o.posAtStart:i?r.to:r.from;return N.cursor(l,i?-1:1)}function Vn(e,t,i,n){let r=e.state.doc.lineAt(t.head),s=e.bidiSpans(r),o=e.textDirectionAt(r.from);for(let l=t,a=null;;){let t=pn(r,s,o,l,i),h=fn;if(!t){if(r.number==(i?e.state.doc.lines:1))return l;h="\n",r=e.state.doc.line(r.number+(i?1:-1)),s=e.bidiSpans(r),t=N.cursor(i?r.from:r.to)}if(a){if(!a(h))return l}else{if(!n)return t;a=n(h)}l=t}}function Wn(e,t,i){let n=e.state.charCategorizer(t),r=n(i);return e=>{let t=n(e);return r==De.Space&&(r=t),r==t}}function jn(e,t,i,n){let r=t.head,s=i?1:-1;if(r==(i?e.state.doc.length:0))return N.cursor(r,t.assoc);let o,l=t.goalColumn,a=e.contentDOM.getBoundingClientRect(),h=e.coordsAtPos(r),c=e.documentTop;if(h)null==l&&(l=h.left-a.left),o=s<0?h.top:h.bottom;else{let t=e.viewState.lineBlockAt(r);null==l&&(l=Math.min(a.right-a.left,e.defaultCharacterWidth*(r-t.from))),o=(s<0?t.top:t.bottom)+c}let u=a.left+l,d=null!==n&&void 0!==n?n:e.defaultLineHeight>>1;for(let f=0;;f+=10){let i=o+(d+f)*s,n=zn(e,{x:u,y:i},!1,s);if(i<a.top||i>a.bottom||(s<0?n<r:n>r))return N.cursor(n,t.assoc,void 0,l)}}function $n(e,t,i){let n=e.state.facet(Gi).map(t=>t(e));for(;;){let e=!1;for(let r of n)r.between(i.from-1,i.from+1,(n,r,s)=>{i.from>n&&i.from<r&&(i=t.from>i.from?N.cursor(n,1):N.cursor(r,-1),e=!0)});if(!e)return i}}class Un{constructor(e){this.lastKeyCode=0,this.lastKeyTime=0,this.chromeScrollHack=-1,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.registeredEvents=[],this.customHandlers=[],this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.rapidCompositionStart=!1,this.mouseSelection=null;for(let t in er){let i=er[t];e.contentDOM.addEventListener(t,n=>{Yn(e,n)&&!this.ignoreDuringComposition(n)&&("keydown"==t&&this.keydown(e,n)||(this.mustFlushObserver(n)&&e.observer.forceFlush(),this.runCustomHandlers(t,e,n)?n.preventDefault():i(e,n)))}),this.registeredEvents.push(t)}Qt.chrome&&Qt.chrome_version>=102&&e.scrollDOM.addEventListener("wheel",()=>{this.chromeScrollHack<0?e.contentDOM.style.pointerEvents="none":window.clearTimeout(this.chromeScrollHack),this.chromeScrollHack=setTimeout(()=>{this.chromeScrollHack=-1,e.contentDOM.style.pointerEvents=""},100)},{passive:!0}),this.notifiedFocused=e.hasFocus,Qt.safari&&e.contentDOM.addEventListener("input",()=>null)}setSelectionOrigin(e){this.lastSelectionOrigin=e,this.lastSelectionTime=Date.now()}ensureHandlers(e,t){var i;let n;this.customHandlers=[];for(let r of t)if(n=null===(i=r.update(e).spec)||void 0===i?void 0:i.domEventHandlers){this.customHandlers.push({plugin:r.value,handlers:n});for(let t in n)this.registeredEvents.indexOf(t)<0&&"scroll"!=t&&(this.registeredEvents.push(t),e.contentDOM.addEventListener(t,i=>{Yn(e,i)&&this.runCustomHandlers(t,e,i)&&i.preventDefault()}))}}runCustomHandlers(e,t,i){for(let r of this.customHandlers){let s=r.handlers[e];if(s)try{if(s.call(r.plugin,i,t)||i.defaultPrevented)return!0}catch(n){zi(t.state,n)}}return!1}runScrollHandlers(e,t){for(let n of this.customHandlers){let r=n.handlers.scroll;if(r)try{r.call(n.plugin,t,e)}catch(i){zi(e.state,i)}}}keydown(e,t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),9==t.keyCode&&Date.now()<this.lastEscPress+2e3)return!0;if(Qt.android&&Qt.chrome&&!t.synthetic&&(13==t.keyCode||8==t.keyCode))return e.observer.delayAndroidKey(t.key,t.keyCode),!0;let i;return!(!Qt.ios||!(i=Gn.find(e=>e.keyCode==t.keyCode))||t.ctrlKey||t.altKey||t.metaKey||t.synthetic)&&(this.pendingIOSKey=i,setTimeout(()=>this.flushIOSKey(e),250),!0)}flushIOSKey(e){let t=this.pendingIOSKey;return!!t&&(this.pendingIOSKey=void 0,Tt(e.contentDOM,t.key,t.keyCode))}ignoreDuringComposition(e){return!!/^key/.test(e.type)&&(this.composing>0||!!(Qt.safari&&Date.now()-this.compositionEndedAt<100)&&(this.compositionEndedAt=0,!0))}mustFlushObserver(e){return"keydown"==e.type&&229!=e.keyCode||"compositionend"==e.type&&!Qt.ios}startMouseSelection(e){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=e}update(e){this.mouseSelection&&this.mouseSelection.update(e),e.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}const Gn=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Kn=[16,17,18,20,91,92,224,225];class Jn{constructor(e,t,i,n){this.view=e,this.style=i,this.mustSelect=n,this.lastEvent=t;let r=e.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=t.shiftKey,this.multiple=e.state.facet(Me.allowMultipleSelections)&&Xn(e,t),this.dragMove=Zn(e,t),this.dragging=!(!Qn(e,t)||1!=pr(t))&&null,!1===this.dragging&&(t.preventDefault(),this.select(t))}move(e){if(0==e.buttons)return this.destroy();!1===this.dragging&&this.select(this.lastEvent=e)}up(e){null==this.dragging&&this.select(this.lastEvent),this.dragging||e.preventDefault(),this.destroy()}destroy(){let e=this.view.contentDOM.ownerDocument;e.removeEventListener("mousemove",this.move),e.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=null}select(e){let t=this.style.get(e,this.extend,this.multiple);!this.mustSelect&&t.eq(this.view.state.selection)&&t.main.assoc==this.view.state.selection.main.assoc||this.view.dispatch({selection:t,userEvent:"select.pointer",scrollIntoView:!0}),this.mustSelect=!1}update(e){e.docChanged&&this.dragging&&(this.dragging=this.dragging.map(e.changes)),this.style.update(e)&&setTimeout(()=>this.select(this.lastEvent),20)}}function Xn(e,t){let i=e.state.facet(Bi);return i.length?i[0](t):Qt.mac?t.metaKey:t.ctrlKey}function Zn(e,t){let i=e.state.facet(Fi);return i.length?i[0](t):Qt.mac?!t.altKey:!t.ctrlKey}function Qn(e,t){let{main:i}=e.state.selection;if(i.empty)return!1;let n=ft(e.root);if(!n||0==n.rangeCount)return!0;let r=n.getRangeAt(0).getClientRects();for(let s=0;s<r.length;s++){let e=r[s];if(e.left<=t.clientX&&e.right>=t.clientX&&e.top<=t.clientY&&e.bottom>=t.clientY)return!0}return!1}function Yn(e,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let i,n=t.target;n!=e.contentDOM;n=n.parentNode)if(!n||11==n.nodeType||(i=Nt.get(n))&&i.ignoreEvent(t))return!1;return!0}const er=Object.create(null),tr=Qt.ie&&Qt.ie_version<15||Qt.ios&&Qt.webkit_version<604;function ir(e){let t=e.dom.parentNode;if(!t)return;let i=t.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout(()=>{e.focus(),i.remove(),nr(e,i.value)},50)}function nr(e,t){let i,{state:n}=e,r=1,s=n.toText(t),o=s.lines==n.selection.ranges.length,l=null!=wr&&n.selection.ranges.every(e=>e.empty)&&wr==s.toString();if(l){let e=-1;i=n.changeByRange(i=>{let l=n.doc.lineAt(i.from);if(l.from==e)return{range:i};e=l.from;let a=n.toText((o?s.line(r++).text:t)+n.lineBreak);return{changes:{from:l.from,insert:a},range:N.cursor(i.from+a.length)}})}else i=o?n.changeByRange(e=>{let t=s.line(r++);return{changes:{from:e.from,to:e.to,insert:t.text},range:N.cursor(e.from+t.length)}}):n.replaceSelection(s);e.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}er.keydown=(e,t)=>{e.inputState.setSelectionOrigin("select"),27==t.keyCode?e.inputState.lastEscPress=Date.now():Kn.indexOf(t.keyCode)<0&&(e.inputState.lastEscPress=0)};let rr=0;function sr(e,t,i,n){if(1==n)return N.cursor(t,i);if(2==n)return Fn(e.state,t,i);{let i=Ci.find(e.docView,t),n=e.state.doc.lineAt(i?i.posAtEnd:t),r=i?i.posAtStart:n.from,s=i?i.posAtEnd:n.to;return s<e.state.doc.length&&s==n.to&&s++,N.range(r,s)}}er.touchstart=(e,t)=>{rr=Date.now(),e.inputState.setSelectionOrigin("select.pointer")},er.touchmove=e=>{e.inputState.setSelectionOrigin("select.pointer")},er.mousedown=(e,t)=>{if(e.observer.flush(),rr>Date.now()-2e3&&1==pr(t))return;let i=null;for(let n of e.state.facet(Ti))if(i=n(e,t),i)break;if(i||0!=t.button||(i=mr(e,t)),i){let n=e.root.activeElement!=e.contentDOM;n&&e.observer.ignore(()=>Bt(e.contentDOM)),e.inputState.startMouseSelection(new Jn(e,t,i,n))}};let or=(e,t)=>e>=t.top&&e<=t.bottom,lr=(e,t,i)=>or(t,i)&&e>=i.left&&e<=i.right;function ar(e,t,i,n){let r=Ci.find(e.docView,t);if(!r)return 1;let s=t-r.posAtStart;if(0==s)return 1;if(s==r.length)return-1;let o=r.coordsAt(s,-1);if(o&&lr(i,n,o))return-1;let l=r.coordsAt(s,1);return l&&lr(i,n,l)?1:o&&or(n,o)?-1:1}function hr(e,t){let i=e.posAtCoords({x:t.clientX,y:t.clientY},!1);return{pos:i,bias:ar(e,i,t.clientX,t.clientY)}}const cr=Qt.ie&&Qt.ie_version<=11;let ur=null,dr=0,fr=0;function pr(e){if(!cr)return e.detail;let t=ur,i=fr;return ur=e,fr=Date.now(),dr=!t||i>Date.now()-400&&Math.abs(t.clientX-e.clientX)<2&&Math.abs(t.clientY-e.clientY)<2?(dr+1)%3:1}function mr(e,t){let i=hr(e,t),n=pr(t),r=e.state.selection,s=i,o=t;return{update(e){e.docChanged&&(i&&(i.pos=e.changes.mapPos(i.pos)),r=r.map(e.changes),o=null)},get(t,l,a){let h;if(o&&t.clientX==o.clientX&&t.clientY==o.clientY?h=s:(h=s=hr(e,t),o=t),!h||!i)return r;let c=sr(e,h.pos,h.bias,n);if(i.pos!=h.pos&&!l){let t=sr(e,i.pos,i.bias,n),r=Math.min(t.from,c.from),s=Math.max(t.to,c.to);c=r<c.from?N.range(r,s):N.range(s,r)}return l?r.replaceRange(r.main.extend(c.from,c.to)):a?r.addRange(c):N.create([c])}}}function gr(e,t,i,n){if(!i)return;let r=e.posAtCoords({x:t.clientX,y:t.clientY},!1);t.preventDefault();let{mouseSelection:s}=e.inputState,o=n&&s&&s.dragging&&s.dragMove?{from:s.dragging.from,to:s.dragging.to}:null,l={from:r,insert:i},a=e.state.changes(o?[o,l]:l);e.focus(),e.dispatch({changes:a,selection:{anchor:a.mapPos(r,-1),head:a.mapPos(r,1)},userEvent:o?"move.drop":"input.drop"})}function vr(e,t){let i=e.dom.parentNode;if(!i)return;let n=i.appendChild(document.createElement("textarea"));n.style.cssText="position: fixed; left: -10000px; top: 10px",n.value=t,n.focus(),n.selectionEnd=t.length,n.selectionStart=0,setTimeout(()=>{n.remove(),e.focus()},50)}function xr(e){let t=[],i=[],n=!1;for(let r of e.selection.ranges)r.empty||(t.push(e.sliceDoc(r.from,r.to)),i.push(r));if(!t.length){let r=-1;for(let{from:n}of e.selection.ranges){let s=e.doc.lineAt(n);s.number>r&&(t.push(s.text),i.push({from:s.from,to:Math.min(e.doc.length,s.to+1)})),r=s.number}n=!0}return{text:t.join(e.lineBreak),ranges:i,linewise:n}}er.dragstart=(e,t)=>{let{selection:{main:i}}=e.state,{mouseSelection:n}=e.inputState;n&&(n.dragging=i),t.dataTransfer&&(t.dataTransfer.setData("Text",e.state.sliceDoc(i.from,i.to)),t.dataTransfer.effectAllowed="copyMove")},er.drop=(e,t)=>{if(!t.dataTransfer)return;if(e.state.readOnly)return t.preventDefault();let i=t.dataTransfer.files;if(i&&i.length){t.preventDefault();let n=Array(i.length),r=0,s=()=>{++r==i.length&&gr(e,t,n.filter(e=>null!=e).join(e.state.lineBreak),!1)};for(let e=0;e<i.length;e++){let t=new FileReader;t.onerror=s,t.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(t.result)||(n[e]=t.result),s()},t.readAsText(i[e])}}else gr(e,t,t.dataTransfer.getData("Text"),!0)},er.paste=(e,t)=>{if(e.state.readOnly)return t.preventDefault();e.observer.flush();let i=tr?null:t.clipboardData;i?(nr(e,i.getData("text/plain")),t.preventDefault()):ir(e)};let wr=null;function br(e){setTimeout(()=>{e.hasFocus!=e.inputState.notifiedFocused&&e.update([])},10)}function yr(e,t){if(e.docView.compositionDeco.size){e.inputState.rapidCompositionStart=t;try{e.update([])}finally{e.inputState.rapidCompositionStart=!1}}}er.copy=er.cut=(e,t)=>{let{text:i,ranges:n,linewise:r}=xr(e.state);if(!i&&!r)return;wr=r?i:null;let s=tr?null:t.clipboardData;s?(t.preventDefault(),s.clearData(),s.setData("text/plain",i)):vr(e,i),"cut"!=t.type||e.state.readOnly||e.dispatch({changes:n,scrollIntoView:!0,userEvent:"delete.cut"})},er.focus=br,er.blur=e=>{e.observer.clearSelectionRange(),br(e)},er.compositionstart=er.compositionupdate=e=>{null==e.inputState.compositionFirstChange&&(e.inputState.compositionFirstChange=!0),e.inputState.composing<0&&(e.inputState.composing=0,e.docView.compositionDeco.size&&(e.observer.flush(),yr(e,!0)))},er.compositionend=e=>{e.inputState.composing=-1,e.inputState.compositionEndedAt=Date.now(),e.inputState.compositionFirstChange=null,setTimeout(()=>{e.inputState.composing<0&&yr(e,!1)},50)},er.contextmenu=e=>{e.inputState.lastContextMenu=Date.now()},er.beforeinput=(e,t)=>{var i;let n;if(Qt.chrome&&Qt.android&&(n=Gn.find(e=>e.inputType==t.inputType))&&(e.observer.delayAndroidKey(n.key,n.keyCode),"Backspace"==n.key||"Delete"==n.key)){let t=(null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0;setTimeout(()=>{var i;((null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0)>t+10&&e.hasFocus&&(e.contentDOM.blur(),e.focus())},100)}};const kr=["pre-wrap","normal","pre-line","break-spaces"];class Dr{constructor(){this.doc=n.empty,this.lineWrapping=!1,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.lineLength=30,this.heightChanged=!1}heightForGap(e,t){let i=this.doc.lineAt(t).number-this.doc.lineAt(e).number+1;return this.lineWrapping&&(i+=Math.ceil((t-e-i*this.lineLength*.5)/this.lineLength)),this.lineHeight*i}heightForLine(e){if(!this.lineWrapping)return this.lineHeight;let t=1+Math.max(0,Math.ceil((e-this.lineLength)/(this.lineLength-5)));return t*this.lineHeight}setDoc(e){return this.doc=e,this}mustRefreshForWrapping(e){return kr.indexOf(e)>-1!=this.lineWrapping}mustRefreshForHeights(e){let t=!1;for(let i=0;i<e.length;i++){let n=e[i];n<0?i++:this.heightSamples[Math.floor(10*n)]||(t=!0,this.heightSamples[Math.floor(10*n)]=!0)}return t}refresh(e,t,i,n,r){let s=kr.indexOf(e)>-1,o=Math.round(t)!=Math.round(this.lineHeight)||this.lineWrapping!=s;if(this.lineWrapping=s,this.lineHeight=t,this.charWidth=i,this.lineLength=n,o){this.heightSamples={};for(let e=0;e<r.length;e++){let t=r[e];t<0?e++:this.heightSamples[Math.floor(10*t)]=!0}}return o}}class Cr{constructor(e,t){this.from=e,this.heights=t,this.index=0}get more(){return this.index<this.heights.length}}class Sr{constructor(e,t,i,n,r){this.from=e,this.length=t,this.top=i,this.height=n,this.type=r}get to(){return this.from+this.length}get bottom(){return this.top+this.height}join(e){let t=(Array.isArray(this.type)?this.type:[this]).concat(Array.isArray(e.type)?e.type:[e]);return new Sr(this.from,this.length+e.length,this.top,this.height+e.height,t)}}var Ar=function(e){return e[e["ByPos"]=0]="ByPos",e[e["ByHeight"]=1]="ByHeight",e[e["ByPosNoHeight"]=2]="ByPosNoHeight",e}(Ar||(Ar={}));const Er=.001;class Mr{constructor(e,t,i=2){this.length=e,this.height=t,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(e){this.flags=(e?2:0)|-3&this.flags}setHeight(e,t){this.height!=t&&(Math.abs(this.height-t)>Er&&(e.heightChanged=!0),this.height=t)}replace(e,t,i){return Mr.of(i)}decomposeLeft(e,t){t.push(this)}decomposeRight(e,t){t.push(this)}applyChanges(e,t,i,n){let r=this;for(let s=n.length-1;s>=0;s--){let{fromA:o,toA:l,fromB:a,toB:h}=n[s],c=r.lineAt(o,Ar.ByPosNoHeight,t,0,0),u=c.to>=l?c:r.lineAt(l,Ar.ByPosNoHeight,t,0,0);h+=u.to-l,l=u.to;while(s>0&&c.from<=n[s-1].toA)o=n[s-1].fromA,a=n[s-1].fromB,s--,o<c.from&&(c=r.lineAt(o,Ar.ByPosNoHeight,t,0,0));a+=c.from-o,o=c.from;let d=Ir.build(i,e,a,h);r=r.replace(o,l,d)}return r.updateHeight(i,0)}static empty(){return new Fr(0,0)}static of(e){if(1==e.length)return e[0];let t=0,i=e.length,n=0,r=0;for(;;)if(t==i)if(n>2*r){let r=e[t-1];r.break?e.splice(--t,1,r.left,null,r.right):e.splice(--t,1,r.left,r.right),i+=1+r.break,n-=r.size}else{if(!(r>2*n))break;{let t=e[i];t.break?e.splice(i,1,t.left,null,t.right):e.splice(i,1,t.left,t.right),i+=2+t.break,r-=t.size}}else if(n<r){let i=e[t++];i&&(n+=i.size)}else{let t=e[--i];t&&(r+=t.size)}let s=0;return null==e[t-1]?(s=1,t--):null==e[t]&&(s=1,i++),new Or(Mr.of(e.slice(0,t)),s,Mr.of(e.slice(i)))}}Mr.prototype.size=1;class Br extends Mr{constructor(e,t,i){super(e,t),this.type=i}blockAt(e,t,i,n){return new Sr(n,this.length,i,this.height,this.type)}lineAt(e,t,i,n,r){return this.blockAt(0,i,n,r)}forEachLine(e,t,i,n,r,s){e<=r+this.length&&t>=r&&s(this.blockAt(0,i,n,r))}updateHeight(e,t=0,i=!1,n){return n&&n.from<=t&&n.more&&this.setHeight(e,n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class Fr extends Br{constructor(e,t){super(e,t,gi.Text),this.collapsed=0,this.widgetHeight=0}replace(e,t,i){let n=i[0];return 1==i.length&&(n instanceof Fr||n instanceof Tr&&4&n.flags)&&Math.abs(this.length-n.length)<10?(n instanceof Tr?n=new Fr(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):Mr.of(i)}updateHeight(e,t=0,i=!1,n){return n&&n.from<=t&&n.more?this.setHeight(e,n.heights[n.index++]):(i||this.outdated)&&this.setHeight(e,Math.max(this.widgetHeight,e.heightForLine(this.length-this.collapsed))),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class Tr extends Mr{constructor(e){super(e,0)}lines(e,t){let i=e.lineAt(t).number,n=e.lineAt(t+this.length).number;return{firstLine:i,lastLine:n,lineHeight:this.height/(n-i+1)}}blockAt(e,t,i,n){let{firstLine:r,lastLine:s,lineHeight:o}=this.lines(t,n),l=Math.max(0,Math.min(s-r,Math.floor((e-i)/o))),{from:a,length:h}=t.line(r+l);return new Sr(a,h,i+o*l,o,gi.Text)}lineAt(e,t,i,n,r){if(t==Ar.ByHeight)return this.blockAt(e,i,n,r);if(t==Ar.ByPosNoHeight){let{from:t,to:n}=i.lineAt(e);return new Sr(t,n-t,0,0,gi.Text)}let{firstLine:s,lineHeight:o}=this.lines(i,r),{from:l,length:a,number:h}=i.lineAt(e);return new Sr(l,a,n+o*(h-s),o,gi.Text)}forEachLine(e,t,i,n,r,s){let{firstLine:o,lineHeight:l}=this.lines(i,r);for(let a=Math.max(e,r),h=Math.min(r+this.length,t);a<=h;){let t=i.lineAt(a);a==e&&(n+=l*(t.number-o)),s(new Sr(t.from,t.length,n,l,gi.Text)),n+=l,a=t.to+1}}replace(e,t,i){let n=this.length-t;if(n>0){let e=i[i.length-1];e instanceof Tr?i[i.length-1]=new Tr(e.length+n):i.push(null,new Tr(n-1))}if(e>0){let t=i[0];t instanceof Tr?i[0]=new Tr(e+t.length):i.unshift(new Tr(e-1),null)}return Mr.of(i)}decomposeLeft(e,t){t.push(new Tr(e-1),null)}decomposeRight(e,t){t.push(null,new Tr(this.length-e-1))}updateHeight(e,t=0,i=!1,n){let r=t+this.length;if(n&&n.from<=t+this.length&&n.more){let i=[],s=Math.max(t,n.from),o=-1,l=e.heightChanged;n.from>t&&i.push(new Tr(n.from-t-1).updateHeight(e,t));while(s<=r&&n.more){let t=e.doc.lineAt(s).length;i.length&&i.push(null);let r=n.heights[n.index++];-1==o?o=r:Math.abs(r-o)>=Er&&(o=-2);let l=new Fr(t,r);l.outdated=!1,i.push(l),s+=t+1}s<=r&&i.push(null,new Tr(r-s).updateHeight(e,s));let a=Mr.of(i);return e.heightChanged=l||o<0||Math.abs(a.height-this.height)>=Er||Math.abs(o-this.lines(e.doc,t).lineHeight)>=Er,a}return(i||this.outdated)&&(this.setHeight(e,e.heightForGap(t,t+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class Or extends Mr{constructor(e,t,i){super(e.length+t+i.length,e.height+i.height,t|(e.outdated||i.outdated?2:0)),this.left=e,this.right=i,this.size=e.size+i.size}get break(){return 1&this.flags}blockAt(e,t,i,n){let r=i+this.left.height;return e<r?this.left.blockAt(e,t,i,n):this.right.blockAt(e,t,r,n+this.left.length+this.break)}lineAt(e,t,i,n,r){let s=n+this.left.height,o=r+this.left.length+this.break,l=t==Ar.ByHeight?e<s:e<o,a=l?this.left.lineAt(e,t,i,n,r):this.right.lineAt(e,t,i,s,o);if(this.break||(l?a.to<o:a.from>o))return a;let h=t==Ar.ByPosNoHeight?Ar.ByPosNoHeight:Ar.ByPos;return l?a.join(this.right.lineAt(o,h,i,s,o)):this.left.lineAt(o,h,i,n,r).join(a)}forEachLine(e,t,i,n,r,s){let o=n+this.left.height,l=r+this.left.length+this.break;if(this.break)e<l&&this.left.forEachLine(e,t,i,n,r,s),t>=l&&this.right.forEachLine(e,t,i,o,l,s);else{let a=this.lineAt(l,Ar.ByPos,i,n,r);e<a.from&&this.left.forEachLine(e,a.from-1,i,n,r,s),a.to>=e&&a.from<=t&&s(a),t>a.to&&this.right.forEachLine(a.to+1,t,i,o,l,s)}}replace(e,t,i){let n=this.left.length+this.break;if(t<n)return this.balanced(this.left.replace(e,t,i),this.right);if(e>this.left.length)return this.balanced(this.left,this.right.replace(e-n,t-n,i));let r=[];e>0&&this.decomposeLeft(e,r);let s=r.length;for(let o of i)r.push(o);if(e>0&&Lr(r,s-1),t<this.length){let e=r.length;this.decomposeRight(t,r),Lr(r,e)}return Mr.of(r)}decomposeLeft(e,t){let i=this.left.length;if(e<=i)return this.left.decomposeLeft(e,t);t.push(this.left),this.break&&(i++,e>=i&&t.push(null)),e>i&&this.right.decomposeLeft(e-i,t)}decomposeRight(e,t){let i=this.left.length,n=i+this.break;if(e>=n)return this.right.decomposeRight(e-n,t);e<i&&this.left.decomposeRight(e,t),this.break&&e<n&&t.push(null),t.push(this.right)}balanced(e,t){return e.size>2*t.size||t.size>2*e.size?Mr.of(this.break?[e,null,t]:[e,t]):(this.left=e,this.right=t,this.height=e.height+t.height,this.outdated=e.outdated||t.outdated,this.size=e.size+t.size,this.length=e.length+this.break+t.length,this)}updateHeight(e,t=0,i=!1,n){let{left:r,right:s}=this,o=t+r.length+this.break,l=null;return n&&n.from<=t+r.length&&n.more?l=r=r.updateHeight(e,t,i,n):r.updateHeight(e,t,i),n&&n.from<=o+s.length&&n.more?l=s=s.updateHeight(e,o,i,n):s.updateHeight(e,o,i),l?this.balanced(r,s):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function Lr(e,t){let i,n;null==e[t]&&(i=e[t-1])instanceof Tr&&(n=e[t+1])instanceof Tr&&e.splice(t-1,3,new Tr(i.length+1+n.length))}const Rr=5;class Ir{constructor(e,t){this.pos=e,this.oracle=t,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=e}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(e,t){if(this.lineStart>-1){let e=Math.min(t,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof Fr?i.length+=e-this.pos:(e>this.pos||!this.isCovered)&&this.nodes.push(new Fr(e-this.pos,-1)),this.writtenTo=e,t>e&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=t}point(e,t,i){if(e<t||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0;n<0&&(n=this.oracle.lineHeight);let r=t-e;i.block?this.addBlock(new Br(r,n,i.type)):(r||n>=Rr)&&this.addLineDeco(n,r)}else t>e&&this.span(e,t);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:e,to:t}=this.oracle.doc.lineAt(this.pos);this.lineStart=e,this.lineEnd=t,this.writtenTo<e&&((this.writtenTo<e-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,e-1)),this.nodes.push(null)),this.pos>e&&this.nodes.push(new Fr(this.pos-e,-1)),this.writtenTo=this.pos}blankContent(e,t){let i=new Tr(t-e);return this.oracle.doc.lineAt(e).to==t&&(i.flags|=4),i}ensureLine(){this.enterLine();let e=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(e instanceof Fr)return e;let t=new Fr(0,-1);return this.nodes.push(t),t}addBlock(e){this.enterLine(),e.type!=gi.WidgetAfter||this.isCovered||this.ensureLine(),this.nodes.push(e),this.writtenTo=this.pos=this.pos+e.length,e.type!=gi.WidgetBefore&&(this.covering=e)}addLineDeco(e,t){let i=this.ensureLine();i.length+=t,i.collapsed+=t,i.widgetHeight=Math.max(i.widgetHeight,e),this.writtenTo=this.pos=this.pos+t}finish(e){let t=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||t instanceof Fr||this.isCovered?(this.writtenTo<this.pos||null==t)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new Fr(0,-1));let i=e;for(let n of this.nodes)n instanceof Fr&&n.updateHeight(this.oracle,i),i+=n?n.length:1;return this.nodes}static build(e,t,i,n){let r=new Ir(i,e);return Re.spans(t,i,n,r,0),r.finish(i)}}function Nr(e,t,i){let n=new Pr;return Re.compare(e,t,i,n,0),n.changes}class Pr{constructor(){this.changes=[]}compareRange(){}comparePoint(e,t,i,n){(e<t||i&&i.heightRelevant||n&&n.heightRelevant)&&Di(e,t,this.changes,5)}}function zr(e,t){let i=e.getBoundingClientRect(),n=Math.max(0,i.left),r=Math.min(innerWidth,i.right),s=Math.max(0,i.top),o=Math.min(innerHeight,i.bottom),l=e.ownerDocument.body;for(let a=e.parentNode;a&&a!=l;)if(1==a.nodeType){let e=a,t=window.getComputedStyle(e);if((e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth)&&"visible"!=t.overflow){let t=e.getBoundingClientRect();n=Math.max(n,t.left),r=Math.min(r,t.right),s=Math.max(s,t.top),o=Math.min(o,t.bottom)}a="absolute"==t.position||"fixed"==t.position?e.offsetParent:e.parentNode}else{if(11!=a.nodeType)break;a=a.host}return{left:n-i.left,right:Math.max(n,r)-i.left,top:s-(i.top+t),bottom:Math.max(s,o)-(i.top+t)}}function _r(e,t){let i=e.getBoundingClientRect();return{left:0,right:i.right-i.left,top:t,bottom:i.bottom-(i.top+t)}}class Hr{constructor(e,t,i){this.from=e,this.to=t,this.size=i}static same(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++){let n=e[i],r=t[i];if(n.from!=r.from||n.to!=r.to||n.size!=r.size)return!1}return!0}draw(e){return vi.replace({widget:new qr(this.size,e)}).range(this.from,this.to)}}class qr extends mi{constructor(e,t){super(),this.size=e,this.vertical=t}eq(e){return e.size==this.size&&e.vertical==this.vertical}toDOM(){let e=document.createElement("div");return this.vertical?e.style.height=this.size+"px":(e.style.width=this.size+"px",e.style.height="2px",e.style.display="inline-block"),e}get estimatedHeight(){return this.vertical?this.size:-1}}class Vr{constructor(e){this.state=e,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.heightOracle=new Dr,this.scaler=Jr,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=Qi.RTL,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1,this.stateDeco=e.facet(Ui).filter(e=>"function"!=typeof e),this.heightMap=Mr.empty().applyChanges(this.stateDeco,n.empty,this.heightOracle.setDoc(e.doc),[new Xi(0,0,0,e.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=vi.set(this.lineGaps.map(e=>e.draw(!1))),this.computeVisibleRanges()}updateForViewport(){let e=[this.viewport],{main:t}=this.state.selection;for(let i=0;i<=1;i++){let n=i?t.head:t.anchor;if(!e.some(({from:e,to:t})=>n>=e&&n<=t)){let{from:t,to:i}=this.lineBlockAt(n);e.push(new Wr(t,i))}}this.viewports=e.sort((e,t)=>e.from-t.from),this.scaler=this.heightMap.height<=7e6?Jr:new Xr(this.heightOracle.doc,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.state.doc,0,0,e=>{this.viewportLines.push(1==this.scaler.scale?e:Zr(e,this.scaler))})}update(e,t=null){this.state=e.state;let i=this.stateDeco;this.stateDeco=this.state.facet(Ui).filter(e=>"function"!=typeof e);let n=e.changedRanges,r=Xi.extendWithRanges(n,Nr(i,this.stateDeco,e?e.changes:M.empty(this.state.doc.length))),s=this.heightMap.height;this.heightMap=this.heightMap.applyChanges(this.stateDeco,e.startState.doc,this.heightOracle.setDoc(this.state.doc),r),this.heightMap.height!=s&&(e.flags|=2);let o=r.length?this.mapViewport(this.viewport,e.changes):this.viewport;(t&&(t.range.head<o.from||t.range.head>o.to)||!this.viewportIsAppropriate(o))&&(o=this.getViewport(0,t));let l=!e.changes.empty||2&e.flags||o.from!=this.viewport.from||o.to!=this.viewport.to;this.viewport=o,this.updateForViewport(),l&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,e.changes))),e.flags|=this.computeVisibleRanges(),t&&(this.scrollTarget=t),!this.mustEnforceCursorAssoc&&e.selectionSet&&e.view.lineWrapping&&e.state.selection.main.empty&&e.state.selection.main.assoc&&(this.mustEnforceCursorAssoc=!0)}measure(e){let t=e.contentDOM,i=window.getComputedStyle(t),n=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection="rtl"==i.direction?Qi.RTL:Qi.LTR;let s=this.heightOracle.mustRefreshForWrapping(r),o=s||this.mustMeasureContent||this.contentDOMHeight!=t.clientHeight;this.contentDOMHeight=t.clientHeight,this.mustMeasureContent=!1;let l=0,a=0,h=parseInt(i.paddingTop)||0,c=parseInt(i.paddingBottom)||0;this.paddingTop==h&&this.paddingBottom==c||(this.paddingTop=h,this.paddingBottom=c,l|=10),this.editorWidth!=e.scrollDOM.clientWidth&&(n.lineWrapping&&(o=!0),this.editorWidth=e.scrollDOM.clientWidth,l|=8);let u=(this.printing?_r:zr)(t,this.paddingTop),d=u.top-this.pixelViewport.top,f=u.bottom-this.pixelViewport.bottom;this.pixelViewport=u;let p=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(p!=this.inView&&(this.inView=p,p&&(o=!0)),!this.inView)return 0;let m=t.clientWidth;if(this.contentDOMWidth==m&&this.editorHeight==e.scrollDOM.clientHeight||(this.contentDOMWidth=m,this.editorHeight=e.scrollDOM.clientHeight,l|=8),o){let t=e.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(t)&&(s=!0),s||n.lineWrapping&&Math.abs(m-this.contentDOMWidth)>n.charWidth){let{lineHeight:i,charWidth:o}=e.docView.measureTextSize();s=n.refresh(r,i,o,m/o,t),s&&(e.docView.minWidth=0,l|=8)}d>0&&f>0?a=Math.max(d,f):d<0&&f<0&&(a=Math.min(d,f)),n.heightChanged=!1;for(let i of this.viewports){let r=i.from==this.viewport.from?t:e.docView.measureVisibleLineHeights(i);this.heightMap=this.heightMap.updateHeight(n,0,s,new Cr(i.from,r))}n.heightChanged&&(l|=2)}let g=!this.viewportIsAppropriate(this.viewport,a)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return g&&(this.viewport=this.getViewport(a,this.scrollTarget)),this.updateForViewport(),(2&l||g)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(s?[]:this.lineGaps)),l|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,e.docView.enforceCursorAssoc()),l}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(e,t){let i=.5-Math.max(-.5,Math.min(.5,e/1e3/2)),n=this.heightMap,r=this.state.doc,{visibleTop:s,visibleBottom:o}=this,l=new Wr(n.lineAt(s-1e3*i,Ar.ByHeight,r,0,0).from,n.lineAt(o+1e3*(1-i),Ar.ByHeight,r,0,0).to);if(t){let{head:e}=t.range;if(e<l.from||e>l.to){let i,s=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),o=n.lineAt(e,Ar.ByPos,r,0,0);i="center"==t.y?(o.top+o.bottom)/2-s/2:"start"==t.y||"nearest"==t.y&&e<l.from?o.top:o.bottom-s,l=new Wr(n.lineAt(i-500,Ar.ByHeight,r,0,0).from,n.lineAt(i+s+500,Ar.ByHeight,r,0,0).to)}}return l}mapViewport(e,t){let i=t.mapPos(e.from,-1),n=t.mapPos(e.to,1);return new Wr(this.heightMap.lineAt(i,Ar.ByPos,this.state.doc,0,0).from,this.heightMap.lineAt(n,Ar.ByPos,this.state.doc,0,0).to)}viewportIsAppropriate({from:e,to:t},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(e,Ar.ByPos,this.state.doc,0,0),{bottom:r}=this.heightMap.lineAt(t,Ar.ByPos,this.state.doc,0,0),{visibleTop:s,visibleBottom:o}=this;return(0==e||n<=s-Math.max(10,Math.min(-i,250)))&&(t==this.state.doc.length||r>=o+Math.max(10,Math.min(i,250)))&&n>s-2e3&&r<o+2e3}mapLineGaps(e,t){if(!e.length||t.empty)return e;let i=[];for(let n of e)t.touchesRange(n.from,n.to)||i.push(new Hr(t.mapPos(n.from),t.mapPos(n.to),n.size));return i}ensureLineGaps(e){let t=[];if(this.defaultTextDirection!=Qi.LTR)return t;for(let i of this.viewportLines){if(i.length<4e3)continue;let n,r,s=jr(i.from,i.to,this.stateDeco);if(s.total<4e3)continue;if(this.heightOracle.lineWrapping){let e=2e3/this.heightOracle.lineLength*this.heightOracle.lineHeight;n=$r(s,(this.visibleTop-i.top-e)/i.height),r=$r(s,(this.visibleBottom-i.top+e)/i.height)}else{let e=s.total*this.heightOracle.charWidth,t=2e3*this.heightOracle.charWidth;n=$r(s,(this.pixelViewport.left-t)/e),r=$r(s,(this.pixelViewport.right+t)/e)}let o=[];n>i.from&&o.push({from:i.from,to:n}),r<i.to&&o.push({from:r,to:i.to});let l=this.state.selection.main;l.from>=i.from&&l.from<=i.to&&Gr(o,l.from-10,l.from+10),!l.empty&&l.to>=i.from&&l.to<=i.to&&Gr(o,l.to-10,l.to+10);for(let{from:a,to:h}of o)h-a>1e3&&t.push(Kr(e,e=>e.from>=i.from&&e.to<=i.to&&Math.abs(e.from-a)<1e3&&Math.abs(e.to-h)<1e3)||new Hr(a,h,this.gapSize(i,a,h,s)))}return t}gapSize(e,t,i,n){let r=Ur(n,i)-Ur(n,t);return this.heightOracle.lineWrapping?e.height*r:n.total*this.heightOracle.charWidth*r}updateLineGaps(e){Hr.same(e,this.lineGaps)||(this.lineGaps=e,this.lineGapDeco=vi.set(e.map(e=>e.draw(this.heightOracle.lineWrapping))))}computeVisibleRanges(){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let t=[];Re.spans(e,this.viewport.from,this.viewport.to,{span(e,i){t.push({from:e,to:i})},point(){}},20);let i=t.length!=this.visibleRanges.length||this.visibleRanges.some((e,i)=>e.from!=t[i].from||e.to!=t[i].to);return this.visibleRanges=t,i?4:0}lineBlockAt(e){return e>=this.viewport.from&&e<=this.viewport.to&&this.viewportLines.find(t=>t.from<=e&&t.to>=e)||Zr(this.heightMap.lineAt(e,Ar.ByPos,this.state.doc,0,0),this.scaler)}lineBlockAtHeight(e){return Zr(this.heightMap.lineAt(this.scaler.fromDOM(e),Ar.ByHeight,this.state.doc,0,0),this.scaler)}elementAtHeight(e){return Zr(this.heightMap.blockAt(this.scaler.fromDOM(e),this.state.doc,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class Wr{constructor(e,t){this.from=e,this.to=t}}function jr(e,t,i){let n=[],r=e,s=0;return Re.spans(i,e,t,{span(){},point(e,t){e>r&&(n.push({from:r,to:e}),s+=e-r),r=t}},20),r<t&&(n.push({from:r,to:t}),s+=t-r),{total:s,ranges:n}}function $r({total:e,ranges:t},i){if(i<=0)return t[0].from;if(i>=1)return t[t.length-1].to;let n=Math.floor(e*i);for(let r=0;;r++){let{from:e,to:i}=t[r],s=i-e;if(n<=s)return e+n;n-=s}}function Ur(e,t){let i=0;for(let{from:n,to:r}of e.ranges){if(t<=r){i+=t-n;break}i+=r-n}return i/e.total}function Gr(e,t,i){for(let n=0;n<e.length;n++){let r=e[n];if(r.from<i&&r.to>t){let s=[];r.from<t&&s.push({from:r.from,to:t}),r.to>i&&s.push({from:i,to:r.to}),e.splice(n,1,...s),n+=s.length-1}}}function Kr(e,t){for(let i of e)if(t(i))return i}const Jr={toDOM(e){return e},fromDOM(e){return e},scale:1};class Xr{constructor(e,t,i){let n=0,r=0,s=0;this.viewports=i.map(({from:i,to:r})=>{let s=t.lineAt(i,Ar.ByPos,e,0,0).top,o=t.lineAt(r,Ar.ByPos,e,0,0).bottom;return n+=o-s,{from:i,to:r,top:s,bottom:o,domTop:0,domBottom:0}}),this.scale=(7e6-n)/(t.height-n);for(let o of this.viewports)o.domTop=s+(o.top-r)*this.scale,s=o.domBottom=o.domTop+(o.bottom-o.top),r=o.bottom}toDOM(e){for(let t=0,i=0,n=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.top)return n+(e-i)*this.scale;if(e<=r.bottom)return r.domTop+(e-r.top);i=r.bottom,n=r.domBottom}}fromDOM(e){for(let t=0,i=0,n=0;;t++){let r=t<this.viewports.length?this.viewports[t]:null;if(!r||e<r.domTop)return i+(e-n)/this.scale;if(e<=r.domBottom)return r.top+(e-r.domTop);i=r.bottom,n=r.domBottom}}}function Zr(e,t){if(1==t.scale)return e;let i=t.toDOM(e.top),n=t.toDOM(e.bottom);return new Sr(e.from,e.length,i,n-i,Array.isArray(e.type)?e.type.map(e=>Zr(e,t)):e.type)}const Qr=_.define({combine:e=>e.join(" ")}),Yr=_.define({combine:e=>e.indexOf(!0)>-1}),es=Ye.newName(),ts=Ye.newName(),is=Ye.newName(),ns={"&light":"."+ts,"&dark":"."+is};function rs(e,t,i){return new Ye(t,{finish(t){return/&/.test(t)?t.replace(/&\w*/,t=>{if("&"==t)return e;if(!i||!i[t])throw new RangeError("Unsupported selector: "+t);return i[t]}):e+" "+t}})}const ss=rs("."+es,{"&.cm-editor":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,flexShrink:0,minHeight:"100%",display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 4px"},".cm-selectionLayer":{zIndex:-1,contain:"size style"},".cm-selectionBackground":{position:"absolute"},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{zIndex:100,contain:"size style",pointerEvents:"none"},"&.cm-focused .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{visibility:"hidden"},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{visibility:"hidden"},"100%":{}},".cm-cursor, .cm-dropCursor":{position:"absolute",borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},"&.cm-focused .cm-cursor":{display:"block"},"&light .cm-activeLine":{backgroundColor:"#f3f9ff"},"&dark .cm-activeLine":{backgroundColor:"#223039"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{display:"flex",height:"100%",boxSizing:"border-box",left:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},ns),os={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},ls=Qt.ie&&Qt.ie_version<=11;class as{constructor(e,t,i){this.view=e,this.onChange=t,this.onScrollChanged=i,this.active=!1,this.selectionRange=new At,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.scrollTargets=[],this.intersection=null,this.resize=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.parentCheck=-1,this.dom=e.contentDOM,this.observer=new MutationObserver(t=>{for(let e of t)this.queue.push(e);(Qt.ie&&Qt.ie_version<=11||Qt.ios&&e.composing)&&t.some(e=>"childList"==e.type&&e.removedNodes.length||"characterData"==e.type&&e.oldValue.length>e.target.nodeValue.length)?this.flushSoon():this.flush()}),ls&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),window.addEventListener("resize",this.onResize=this.onResize.bind(this)),"function"==typeof ResizeObserver&&(this.resize=new ResizeObserver(()=>{this.view.docView.lastUpdate<Date.now()-75&&this.onResize()}),this.resize.observe(e.scrollDOM)),window.addEventListener("beforeprint",this.onPrint=this.onPrint.bind(this)),this.start(),window.addEventListener("scroll",this.onScroll=this.onScroll.bind(this)),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver(e=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),e.length>0&&e[e.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(e=>{e.length>0&&e[e.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange(),this.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}onScroll(e){this.intersecting&&this.flush(!1),this.onScrollChanged(e)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(){this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500)}updateGaps(e){if(this.gapIntersection&&(e.length!=this.gaps.length||this.gaps.some((t,i)=>t!=e[i]))){this.gapIntersection.disconnect();for(let t of e)this.gapIntersection.observe(t);this.gaps=e}}onSelectionChange(e){if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:t}=this,i=this.selectionRange;if(t.state.facet(_i)?t.root.activeElement!=this.dom:!gt(t.dom,i))return;let n=i.anchorNode&&t.docView.nearest(i.anchorNode);n&&n.ignoreEvent(e)||((Qt.ie&&Qt.ie_version<=11||Qt.android&&Qt.chrome)&&!t.state.selection.main.empty&&i.focusNode&&xt(i.focusNode,i.focusOffset,i.anchorNode,i.anchorOffset)?this.flushSoon():this.flush(!1))}readSelectionRange(){let{root:e}=this.view,t=Qt.safari&&11==e.nodeType&&mt()==this.view.contentDOM&&cs(this.view)||ft(e);return!(!t||this.selectionRange.eq(t))&&(this.selectionRange.setRange(t),this.selectionChanged=!0)}setSelectionRange(e,t){this.selectionRange.set(e.node,e.offset,t.node,t.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let e=0,t=null;for(let i=this.dom;i;)if(1==i.nodeType)!t&&e<this.scrollTargets.length&&this.scrollTargets[e]==i?e++:t||(t=this.scrollTargets.slice(0,e)),t&&t.push(i),i=i.assignedSlot||i.parentNode;else{if(11!=i.nodeType)break;i=i.host}if(e<this.scrollTargets.length&&!t&&(t=this.scrollTargets.slice(0,e)),t){for(let e of this.scrollTargets)e.removeEventListener("scroll",this.onScroll);for(let e of this.scrollTargets=t)e.addEventListener("scroll",this.onScroll)}}ignore(e){if(!this.active)return e();try{return this.stop(),e()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,os),ls&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),ls&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(e,t){this.delayedAndroidKey||requestAnimationFrame(()=>{let e=this.delayedAndroidKey;this.delayedAndroidKey=null,this.delayedFlush=-1,this.flush()||Tt(this.view.contentDOM,e.key,e.keyCode)}),this.delayedAndroidKey&&"Enter"!=e||(this.delayedAndroidKey={key:e,keyCode:t})}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=window.setTimeout(()=>{this.delayedFlush=-1,this.flush()},20))}forceFlush(){this.delayedFlush>=0&&(window.clearTimeout(this.delayedFlush),this.delayedFlush=-1,this.flush())}processRecords(){let e=this.queue;for(let r of this.observer.takeRecords())e.push(r);e.length&&(this.queue=[]);let t=-1,i=-1,n=!1;for(let r of e){let e=this.readMutation(r);e&&(e.typeOver&&(n=!0),-1==t?({from:t,to:i}=e):(t=Math.min(e.from,t),i=Math.max(e.to,i)))}return{from:t,to:i,typeOver:n}}flush(e=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return;e&&this.readSelectionRange();let{from:t,to:i,typeOver:n}=this.processRecords(),r=this.selectionChanged&&gt(this.dom,this.selectionRange);if(t<0&&!r)return;this.selectionChanged=!1;let s=this.view.state,o=this.onChange(t,i,n);return this.view.state==s&&this.view.update([]),o}readMutation(e){let t=this.view.docView.nearest(e.target);if(!t||t.ignoreMutation(e))return null;if(t.markDirty("attributes"==e.type),"attributes"==e.type&&(t.dirty|=4),"childList"==e.type){let i=hs(t,e.previousSibling||e.target.previousSibling,-1),n=hs(t,e.nextSibling||e.target.nextSibling,1);return{from:i?t.posAfter(i):t.posAtStart,to:n?t.posBefore(n):t.posAtEnd,typeOver:!1}}return"characterData"==e.type?{from:t.posAtStart,to:t.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}:null}destroy(){var e,t,i;this.stop(),null===(e=this.intersection)||void 0===e||e.disconnect(),null===(t=this.gapIntersection)||void 0===t||t.disconnect(),null===(i=this.resize)||void 0===i||i.disconnect();for(let n of this.scrollTargets)n.removeEventListener("scroll",this.onScroll);window.removeEventListener("scroll",this.onScroll),window.removeEventListener("resize",this.onResize),window.removeEventListener("beforeprint",this.onPrint),this.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout)}}function hs(e,t,i){while(t){let n=Nt.get(t);if(n&&n.parent==e)return n;let r=t.parentNode;t=r!=e.dom?r:i>0?t.nextSibling:t.previousSibling}return null}function cs(e){let t=null;function i(e){e.preventDefault(),e.stopImmediatePropagation(),t=e.getTargetRanges()[0]}if(e.contentDOM.addEventListener("beforeinput",i,!0),document.execCommand("indent"),e.contentDOM.removeEventListener("beforeinput",i,!0),!t)return null;let n=t.startContainer,r=t.startOffset,s=t.endContainer,o=t.endOffset,l=e.docView.domAtPos(e.state.selection.main.anchor);return xt(l.node,l.offset,s,o)&&([n,r,s,o]=[s,o,n,r]),{anchorNode:n,anchorOffset:r,focusNode:s,focusOffset:o}}function us(e,t,i,r){let s,o,l=e.state.selection.main;if(t>-1){let r=e.docView.domBoundsAround(t,i,0);if(!r||e.state.readOnly)return!1;let{from:a,to:h}=r,c=e.docView.impreciseHead||e.docView.impreciseAnchor?[]:fs(e),u=new gn(c,e.state);u.readRange(r.startDOM,r.endDOM);let d=l.from,f=null;(8===e.inputState.lastKeyCode&&e.inputState.lastKeyTime>Date.now()-100||Qt.android&&u.text.length<h-a)&&(d=l.to,f="end");let p=ds(e.state.doc.sliceString(a,h,mn),u.text,d-a,f);p&&(Qt.chrome&&13==e.inputState.lastKeyCode&&p.toB==p.from+2&&u.text.slice(p.from,p.toB)==mn+mn&&p.toB--,s={from:a+p.from,to:a+p.toA,insert:n.of(u.text.slice(p.from,p.toB).split(mn))}),o=ps(c,a)}else if(e.hasFocus||!e.state.facet(_i)){let t=e.observer.selectionRange,{impreciseHead:i,impreciseAnchor:n}=e.docView,r=i&&i.node==t.focusNode&&i.offset==t.focusOffset||!pt(e.contentDOM,t.focusNode)?e.state.selection.main.head:e.docView.posFromDOM(t.focusNode,t.focusOffset),s=n&&n.node==t.anchorNode&&n.offset==t.anchorOffset||!pt(e.contentDOM,t.anchorNode)?e.state.selection.main.anchor:e.docView.posFromDOM(t.anchorNode,t.anchorOffset);r==l.head&&s==l.anchor||(o=N.single(s,r))}if(!s&&!o)return!1;if(!s&&r&&!l.empty&&o&&o.main.empty?s={from:l.from,to:l.to,insert:e.state.doc.slice(l.from,l.to)}:s&&s.from>=l.from&&s.to<=l.to&&(s.from!=l.from||s.to!=l.to)&&l.to-l.from-(s.to-s.from)<=4?s={from:l.from,to:l.to,insert:e.state.doc.slice(l.from,s.from).append(s.insert).append(e.state.doc.slice(s.to,l.to))}:(Qt.mac||Qt.android)&&s&&s.from==s.to&&s.from==l.head-1&&"."==s.insert.toString()&&(s={from:l.from,to:l.to,insert:n.of([" "])}),s){let t=e.state;if(Qt.ios&&e.inputState.flushIOSKey(e))return!0;if(Qt.android&&(s.from==l.from&&s.to==l.to&&1==s.insert.length&&2==s.insert.lines&&Tt(e.contentDOM,"Enter",13)||s.from==l.from-1&&s.to==l.to&&0==s.insert.length&&Tt(e.contentDOM,"Backspace",8)||s.from==l.from&&s.to==l.to+1&&0==s.insert.length&&Tt(e.contentDOM,"Delete",46)))return!0;let i,n=s.insert.toString();if(e.state.facet(Ri).some(t=>t(e,s.from,s.to,n)))return!0;if(e.inputState.composing>=0&&e.inputState.composing++,s.from>=l.from&&s.to<=l.to&&s.to-s.from>=(l.to-l.from)/3&&(!o||o.main.empty&&o.main.from==s.from+s.insert.length)&&e.inputState.composing<0){let n=l.from<s.from?t.sliceDoc(l.from,s.from):"",r=l.to>s.to?t.sliceDoc(s.to,l.to):"";i=t.replaceSelection(e.state.toText(n+s.insert.sliceString(0,void 0,e.state.lineBreak)+r))}else{let n=t.changes(s),r=o&&!t.selection.main.eq(o.main)&&o.main.to<=n.newLength?o.main:void 0;if(t.selection.ranges.length>1&&e.inputState.composing>=0&&s.to<=l.to&&s.to>=l.to-10){let o=e.state.sliceDoc(s.from,s.to),a=kn(e)||e.state.doc.lineAt(l.head),h=l.to-s.to,c=l.to-l.from;i=t.changeByRange(i=>{if(i.from==l.from&&i.to==l.to)return{changes:n,range:r||i.map(n)};let u=i.to-h,d=u-o.length;if(i.to-i.from!=c||e.state.sliceDoc(d,u)!=o||a&&i.to>=a.from&&i.from<=a.to)return{range:i};let f=t.changes({from:d,to:u,insert:s.insert}),p=i.to-l.to;return{changes:f,range:r?N.range(Math.max(0,r.anchor+p),Math.max(0,r.head+p)):i.map(f)}})}else i={changes:n,selection:r&&t.selection.replaceRange(r)}}let r="input.type";return e.composing&&(r+=".compose",e.inputState.compositionFirstChange&&(r+=".start",e.inputState.compositionFirstChange=!1)),e.dispatch(i,{scrollIntoView:!0,userEvent:r}),!0}if(o&&!o.main.eq(l)){let t=!1,i="select";return e.inputState.lastSelectionTime>Date.now()-50&&("select"==e.inputState.lastSelectionOrigin&&(t=!0),i=e.inputState.lastSelectionOrigin),e.dispatch({selection:o,scrollIntoView:t,userEvent:i}),!0}return!1}function ds(e,t,i,n){let r=Math.min(e.length,t.length),s=0;while(s<r&&e.charCodeAt(s)==t.charCodeAt(s))s++;if(s==r&&e.length==t.length)return null;let o=e.length,l=t.length;while(o>0&&l>0&&e.charCodeAt(o-1)==t.charCodeAt(l-1))o--,l--;if("end"==n){let e=Math.max(0,s-Math.min(o,l));i-=o+e-s}if(o<s&&e.length<t.length){let e=i<=s&&i>=o?s-i:0;s-=e,l=s+(l-o),o=s}else if(l<s){let e=i<=s&&i>=l?s-i:0;s-=e,o=s+(o-l),l=s}return{from:s,toA:o,toB:l}}function fs(e){let t=[];if(e.root.activeElement!=e.contentDOM)return t;let{anchorNode:i,anchorOffset:n,focusNode:r,focusOffset:s}=e.observer.selectionRange;return i&&(t.push(new xn(i,n)),r==i&&s==n||t.push(new xn(r,s))),t}function ps(e,t){if(0==e.length)return null;let i=e[0].pos,n=2==e.length?e[1].pos:i;return i>-1&&n>-1?N.single(i+t,n+t):null}class ms{constructor(e={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.style.cssText="position: absolute; top: -10000px",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),this._dispatch=e.dispatch||(e=>this.update([e])),this.dispatch=this.dispatch.bind(this),this.root=e.root||Ot(e.parent)||document,this.viewState=new Vr(e.state||Me.create(e)),this.plugins=this.state.facet(qi).map(e=>new Wi(e));for(let t of this.plugins)t.update(this);this.observer=new as(this,(e,t,i)=>us(this,e,t,i),e=>{this.inputState.runScrollHandlers(this,e),this.observer.intersecting&&this.measure()}),this.inputState=new Un(this),this.inputState.ensureHandlers(this,this.plugins),this.docView=new wn(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),e.parent&&e.parent.appendChild(this.dom)}get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}dispatch(...e){this._dispatch(1==e.length&&e[0]instanceof pe?e[0]:this.state.update(...e))}update(e){if(0!=this.updateState)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let t,i=!1,n=!1,r=this.state;for(let o of e){if(o.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=o.state}if(this.destroyed)return void(this.viewState.state=r);if(this.observer.clear(),r.facet(Me.phrases)!=this.state.facet(Me.phrases))return this.setState(r);t=Zi.create(this,r,e);let s=this.viewState.scrollTarget;try{this.updateState=2;for(let t of e){if(s&&(s=s.map(t.changes)),t.scrollIntoView){let{main:e}=t.state.selection;s=new Ni(e.empty?e:N.cursor(e.head,e.head>e.anchor?-1:1))}for(let e of t.effects)e.is(Pi)&&(s=e.value)}this.viewState.update(t,s),this.bidiCache=xs.update(this.bidiCache,t.changes),t.empty||(this.updatePlugins(t),this.inputState.update(t)),i=this.docView.update(t),this.state.facet(Ji)!=this.styleModules&&this.mountStyles(),n=this.updateAttrs(),this.showAnnouncements(e),this.docView.updateSelection(i,e.some(e=>e.isUserEvent("select.pointer")))}finally{this.updateState=0}if(t.startState.facet(Qr)!=t.state.facet(Qr)&&(this.viewState.mustMeasureContent=!0),(i||n||s||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),!t.empty)for(let o of this.state.facet(Li))o(t)}setState(e){if(0!=this.updateState)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed)return void(this.viewState.state=e);this.updateState=2;let t=this.hasFocus;try{for(let e of this.plugins)e.destroy(this);this.viewState=new Vr(e),this.plugins=e.facet(qi).map(e=>new Wi(e)),this.pluginMap.clear();for(let e of this.plugins)e.update(this);this.docView=new wn(this),this.inputState.ensureHandlers(this,this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}t&&this.focus(),this.requestMeasure()}updatePlugins(e){let t=e.startState.facet(qi),i=e.state.facet(qi);if(t!=i){let n=[];for(let r of i){let i=t.indexOf(r);if(i<0)n.push(new Wi(r));else{let t=this.plugins[i];t.mustUpdate=e,n.push(t)}}for(let t of this.plugins)t.mustUpdate!=e&&t.destroy(this);this.plugins=n,this.pluginMap.clear(),this.inputState.ensureHandlers(this,this.plugins)}else for(let n of this.plugins)n.mustUpdate=e;for(let n=0;n<this.plugins.length;n++)this.plugins[n].update(this)}measure(e=!0){if(this.destroyed)return;this.measureScheduled>-1&&cancelAnimationFrame(this.measureScheduled),this.measureScheduled=0,e&&this.observer.flush();let t=null;try{for(let e=0;;e++){this.updateState=1;let n=this.viewport,r=this.viewState.measure(this);if(!r&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(e>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let s=[];4&r||([this.measureRequests,s]=[s,this.measureRequests]);let o=s.map(e=>{try{return e.read(this)}catch(t){return zi(this.state,t),vs}}),l=Zi.create(this,this.state,[]),a=!1,h=!1;l.flags|=r,t?t.flags|=r:t=l,this.updateState=2,l.empty||(this.updatePlugins(l),this.inputState.update(l),this.updateAttrs(),a=this.docView.update(l));for(let e=0;e<s.length;e++)if(o[e]!=vs)try{let t=s[e];t.write&&t.write(o[e],this)}catch(i){zi(this.state,i)}if(this.viewState.scrollTarget&&(this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,h=!0),a&&this.docView.updateSelection(!0),this.viewport.from==n.from&&this.viewport.to==n.to&&!h&&0==this.measureRequests.length)break}}finally{this.updateState=0,this.measureScheduled=-1}if(t&&!t.empty)for(let n of this.state.facet(Li))n(t)}get themeClasses(){return es+" "+(this.state.facet(Yr)?is:ts)+" "+this.state.facet(Qr)}updateAttrs(){let e=ws(this,ji,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),t={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(_i)?"true":"false",class:"cm-content",style:`${Qt.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(t["aria-readonly"]="true"),ws(this,$i,t);let i=this.observer.ignore(()=>{let i=pi(this.contentDOM,this.contentAttrs,t),n=pi(this.dom,this.editorAttrs,e);return i||n});return this.editorAttrs=e,this.contentAttrs=t,i}showAnnouncements(e){let t=!0;for(let i of e)for(let e of i.effects)if(e.is(ms.announce)){t&&(this.announceDOM.textContent=""),t=!1;let i=this.announceDOM.appendChild(document.createElement("div"));i.textContent=e.value}}mountStyles(){this.styleModules=this.state.facet(Ji),Ye.mount(this.root,this.styleModules.concat(ss).reverse())}readMeasured(){if(2==this.updateState)throw new Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(e){if(this.measureScheduled<0&&(this.measureScheduled=requestAnimationFrame(()=>this.measure())),e){if(null!=e.key)for(let t=0;t<this.measureRequests.length;t++)if(this.measureRequests[t].key===e.key)return void(this.measureRequests[t]=e);this.measureRequests.push(e)}}plugin(e){let t=this.pluginMap.get(e);return(void 0===t||t&&t.spec!=e)&&this.pluginMap.set(e,t=this.plugins.find(t=>t.spec==e)||null),t&&t.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}elementAtHeight(e){return this.readMeasured(),this.viewState.elementAtHeight(e)}lineBlockAtHeight(e){return this.readMeasured(),this.viewState.lineBlockAtHeight(e)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(e){return this.viewState.lineBlockAt(e)}get contentHeight(){return this.viewState.contentHeight}moveByChar(e,t,i){return $n(this,e,Vn(this,e,t,i))}moveByGroup(e,t){return $n(this,e,Vn(this,e,t,t=>Wn(this,e.head,t)))}moveToLineBoundary(e,t,i=!0){return qn(this,e,t,i)}moveVertically(e,t,i){return $n(this,e,jn(this,e,t,i))}domAtPos(e){return this.docView.domAtPos(e)}posAtDOM(e,t=0){return this.docView.posFromDOM(e,t)}posAtCoords(e,t=!0){return this.readMeasured(),zn(this,e,t)}coordsAtPos(e,t=1){this.readMeasured();let i=this.docView.coordsAt(e,t);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(e),r=this.bidiSpans(n),s=r[hn.find(r,e-n.from,-1,t)];return Dt(i,s.dir==Qi.LTR==t>0)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(e){let t=this.state.facet(Ii);return!t||e<this.viewport.from||e>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(e))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(e){if(e.length>gs)return dn(e.length);let t=this.textDirectionAt(e.from);for(let n of this.bidiCache)if(n.from==e.from&&n.dir==t)return n.order;let i=un(e.text,t);return this.bidiCache.push(new xs(e.from,e.to,t,i)),i}get hasFocus(){var e;return(document.hasFocus()||Qt.safari&&(null===(e=this.inputState)||void 0===e?void 0:e.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{Bt(this.contentDOM),this.docView.updateSelection()})}destroy(){for(let e of this.plugins)e.destroy(this);this.plugins=[],this.inputState.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(e,t={}){return Pi.of(new Ni("number"==typeof e?N.cursor(e):e,t.y,t.x,t.yMargin,t.xMargin))}static domEventHandlers(e){return Vi.define(()=>({}),{eventHandlers:e})}static theme(e,t){let i=Ye.newName(),n=[Qr.of(i),Ji.of(rs("."+i,e))];return t&&t.dark&&n.push(Yr.of(!0)),n}static baseTheme(e){return J.lowest(Ji.of(rs("."+es,e,ns)))}static findFromDOM(e){var t;let i=e.querySelector(".cm-content"),n=i&&Nt.get(i)||Nt.get(e);return(null===(t=null===n||void 0===n?void 0:n.rootView)||void 0===t?void 0:t.view)||null}}ms.styleModule=Ji,ms.inputHandler=Ri,ms.perLineTextDirection=Ii,ms.exceptionSink=Oi,ms.updateListener=Li,ms.editable=_i,ms.mouseSelectionStyle=Ti,ms.dragMovesSelection=Fi,ms.clickAddsSelectionRange=Bi,ms.decorations=Ui,ms.atomicRanges=Gi,ms.scrollMargins=Ki,ms.darkTheme=Yr,ms.contentAttributes=$i,ms.editorAttributes=ji,ms.lineWrapping=ms.contentAttributes.of({class:"cm-lineWrapping"}),ms.announce=fe.define();const gs=4096,vs={};class xs{constructor(e,t,i,n){this.from=e,this.to=t,this.dir=i,this.order=n}static update(e,t){if(t.empty)return e;let i=[],n=e.length?e[e.length-1].dir:Qi.LTR;for(let r=Math.max(0,e.length-10);r<e.length;r++){let s=e[r];s.dir!=n||t.touchesRange(s.from,s.to)||i.push(new xs(t.mapPos(s.from,1),t.mapPos(s.to,-1),s.dir,s.order))}return i}}function ws(e,t,i){for(let n=e.state.facet(t),r=n.length-1;r>=0;r--){let t=n[r],s="function"==typeof t?t(e):t;s&&di(s,i)}return i}const bs=Qt.mac?"mac":Qt.windows?"win":Qt.linux?"linux":"key";function ys(e,t){const i=e.split(/-(?!$)/);let n,r,s,o,l=i[i.length-1];"Space"==l&&(l=" ");for(let a=0;a<i.length-1;++a){const e=i[a];if(/^(cmd|meta|m)$/i.test(e))o=!0;else if(/^a(lt)?$/i.test(e))n=!0;else if(/^(c|ctrl|control)$/i.test(e))r=!0;else if(/^s(hift)?$/i.test(e))s=!0;else{if(!/^mod$/i.test(e))throw new Error("Unrecognized modifier name: "+e);"mac"==t?o=!0:r=!0}}return n&&(l="Alt-"+l),r&&(l="Ctrl-"+l),o&&(l="Meta-"+l),s&&(l="Shift-"+l),l}function ks(e,t,i){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),!1!==i&&t.shiftKey&&(e="Shift-"+e),e}const Ds=ms.domEventHandlers({keydown(e,t){return Ts(As(t.state),e,t,"editor")}}),Cs=_.define({enables:Ds}),Ss=new WeakMap;function As(e){let t=e.facet(Cs),i=Ss.get(t);return i||Ss.set(t,i=Fs(t.reduce((e,t)=>e.concat(t),[]))),i}function Es(e,t,i){return Ts(As(e.state),t,e,i)}let Ms=null;const Bs=4e3;function Fs(e,t=bs){let i=Object.create(null),n=Object.create(null),r=(e,t)=>{let i=n[e];if(null==i)n[e]=t;else if(i!=t)throw new Error("Key binding "+e+" is used both as a regular binding and as a multi-stroke prefix")},s=(e,n,s,o)=>{let l=i[e]||(i[e]=Object.create(null)),a=n.split(/ (?!$)/).map(e=>ys(e,t));for(let t=1;t<a.length;t++){let i=a.slice(0,t).join(" ");r(i,!0),l[i]||(l[i]={preventDefault:!0,commands:[t=>{let n=Ms={view:t,prefix:i,scope:e};return setTimeout(()=>{Ms==n&&(Ms=null)},Bs),!0}]})}let h=a.join(" ");r(h,!1);let c=l[h]||(l[h]={preventDefault:!1,commands:[]});c.commands.push(s),o&&(c.preventDefault=!0)};for(let o of e){let e=o[t]||o.key;if(e)for(let t of o.scope?o.scope.split(" "):["editor"])s(t,e,o.run,o.preventDefault),o.shift&&s(t,"Shift-"+e,o.shift,o.preventDefault)}return i}function Ts(e,t,i,n){let r=dt(t),s=k(r,0),o=C(s)==r.length&&" "!=r,l="",a=!1;Ms&&Ms.view==i&&Ms.scope==n&&(l=Ms.prefix+" ",(a=Kn.indexOf(t.keyCode)<0)&&(Ms=null));let h,c=e=>{if(e){for(let t of e.commands)if(t(i))return!0;e.preventDefault&&(a=!0)}return!1},u=e[n];if(u){if(c(u[l+ks(r,t,!o)]))return!0;if(o&&(t.shiftKey||t.altKey||t.metaKey||s>127)&&(h=it[t.keyCode])&&h!=r){if(c(u[l+ks(h,t,!0)]))return!0;if(t.shiftKey&&nt[t.keyCode]!=h&&c(u[l+ks(nt[t.keyCode],t,!1)]))return!0}else if(o&&t.shiftKey&&c(u[l+ks(r,t,!0)]))return!0}return a}const Os=!Qt.ios,Ls=_.define({combine(e){return Be(e,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(e,t)=>Math.min(e,t),drawRangeCursor:(e,t)=>e||t})}});function Rs(e={}){return[Ls.of(e),Ns,zs]}class Is{constructor(e,t,i,n,r){this.left=e,this.top=t,this.width=i,this.height=n,this.className=r}draw(){let e=document.createElement("div");return e.className=this.className,this.adjust(e),e}adjust(e){e.style.left=this.left+"px",e.style.top=this.top+"px",this.width>=0&&(e.style.width=this.width+"px"),e.style.height=this.height+"px"}eq(e){return this.left==e.left&&this.top==e.top&&this.width==e.width&&this.height==e.height&&this.className==e.className}}const Ns=Vi.fromClass(class{constructor(e){this.view=e,this.rangePieces=[],this.cursors=[],this.measureReq={read:this.readPos.bind(this),write:this.drawSel.bind(this)},this.selectionLayer=e.scrollDOM.appendChild(document.createElement("div")),this.selectionLayer.className="cm-selectionLayer",this.selectionLayer.setAttribute("aria-hidden","true"),this.cursorLayer=e.scrollDOM.appendChild(document.createElement("div")),this.cursorLayer.className="cm-cursorLayer",this.cursorLayer.setAttribute("aria-hidden","true"),e.requestMeasure(this.measureReq),this.setBlinkRate()}setBlinkRate(){this.cursorLayer.style.animationDuration=this.view.state.facet(Ls).cursorBlinkRate+"ms"}update(e){let t=e.startState.facet(Ls)!=e.state.facet(Ls);(t||e.selectionSet||e.geometryChanged||e.viewportChanged)&&this.view.requestMeasure(this.measureReq),e.transactions.some(e=>e.scrollIntoView)&&(this.cursorLayer.style.animationName="cm-blink"==this.cursorLayer.style.animationName?"cm-blink2":"cm-blink"),t&&this.setBlinkRate()}readPos(){let{state:e}=this.view,t=e.facet(Ls),i=e.selection.ranges.map(e=>e.empty?[]:Vs(this.view,e)).reduce((e,t)=>e.concat(t)),n=[];for(let r of e.selection.ranges){let i=r==e.selection.main;if(r.empty?!i||Os:t.drawRangeCursor){let e=Ws(this.view,r,i);e&&n.push(e)}}return{rangePieces:i,cursors:n}}drawSel({rangePieces:e,cursors:t}){if(e.length!=this.rangePieces.length||e.some((e,t)=>!e.eq(this.rangePieces[t]))){this.selectionLayer.textContent="";for(let t of e)this.selectionLayer.appendChild(t.draw());this.rangePieces=e}if(t.length!=this.cursors.length||t.some((e,t)=>!e.eq(this.cursors[t]))){let e=this.cursorLayer.children;if(e.length!==t.length){this.cursorLayer.textContent="";for(const e of t)this.cursorLayer.appendChild(e.draw())}else t.forEach((t,i)=>t.adjust(e[i]));this.cursors=t}}destroy(){this.selectionLayer.remove(),this.cursorLayer.remove()}}),Ps={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};Os&&(Ps[".cm-line"].caretColor="transparent !important");const zs=J.highest(ms.theme(Ps));function _s(e){let t=e.scrollDOM.getBoundingClientRect(),i=e.textDirection==Qi.LTR?t.left:t.right-e.scrollDOM.clientWidth;return{left:i-e.scrollDOM.scrollLeft,top:t.top-e.scrollDOM.scrollTop}}function Hs(e,t,i){let n=N.cursor(t);return{from:Math.max(i.from,e.moveToLineBoundary(n,!1,!0).from),to:Math.min(i.to,e.moveToLineBoundary(n,!0,!0).from),type:gi.Text}}function qs(e,t){let i=e.lineBlockAt(t);if(Array.isArray(i.type))for(let n of i.type)if(n.to>t||n.to==t&&(n.to==i.to||n.type==gi.Text))return n;return i}function Vs(e,t){if(t.to<=e.viewport.from||t.from>=e.viewport.to)return[];let i=Math.max(t.from,e.viewport.from),n=Math.min(t.to,e.viewport.to),r=e.textDirection==Qi.LTR,s=e.contentDOM,o=s.getBoundingClientRect(),l=_s(e),a=window.getComputedStyle(s.firstChild),h=o.left+parseInt(a.paddingLeft)+Math.min(0,parseInt(a.textIndent)),c=o.right-parseInt(a.paddingRight),u=qs(e,i),d=qs(e,n),f=u.type==gi.Text?u:null,p=d.type==gi.Text?d:null;if(e.lineWrapping&&(f&&(f=Hs(e,i,f)),p&&(p=Hs(e,n,p))),f&&p&&f.from==p.from)return g(v(t.from,t.to,f));{let i=f?v(t.from,null,f):x(u,!1),n=p?v(null,t.to,p):x(d,!0),r=[];return(f||u).to<(p||d).from-1?r.push(m(h,i.bottom,c,n.top)):i.bottom<n.top&&e.elementAtHeight((i.bottom+n.top)/2).type==gi.Text&&(i.bottom=n.top=(i.bottom+n.top)/2),g(i).concat(r).concat(g(n))}function m(e,t,i,n){return new Is(e-l.left,t-l.top-.01,i-e,n-t+.01,"cm-selectionBackground")}function g({top:e,bottom:t,horizontal:i}){let n=[];for(let r=0;r<i.length;r+=2)n.push(m(i[r],e,i[r+1],t));return n}function v(t,i,n){let s=1e9,o=-1e9,l=[];function a(t,i,a,u,d){let f=e.coordsAtPos(t,t==n.to?-2:2),p=e.coordsAtPos(a,a==n.from?2:-2);s=Math.min(f.top,p.top,s),o=Math.max(f.bottom,p.bottom,o),d==Qi.LTR?l.push(r&&i?h:f.left,r&&u?c:p.right):l.push(!r&&u?h:p.left,!r&&i?c:f.right)}let u=null!==t&&void 0!==t?t:n.from,d=null!==i&&void 0!==i?i:n.to;for(let r of e.visibleRanges)if(r.to>u&&r.from<d)for(let n=Math.max(r.from,u),s=Math.min(r.to,d);;){let r=e.state.doc.lineAt(n);for(let o of e.bidiSpans(r)){let e=o.from+r.from,l=o.to+r.from;if(e>=s)break;l>n&&a(Math.max(e,n),null==t&&e<=u,Math.min(l,s),null==i&&l>=d,o.dir)}if(n=r.to+1,n>=s)break}return 0==l.length&&a(u,null==t,d,null==i,e.textDirection),{top:s,bottom:o,horizontal:l}}function x(e,t){let i=o.top+(t?e.top:e.bottom);return{top:i,bottom:i,horizontal:[]}}}function Ws(e,t,i){let n=e.coordsAtPos(t.head,t.assoc||1);if(!n)return null;let r=_s(e);return new Is(n.left-r.left,n.top-r.top,-1,n.bottom-n.top,i?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary")}const js=fe.define({map(e,t){return null==e?null:t.mapPos(e)}}),$s=U.define({create(){return null},update(e,t){return null!=e&&(e=t.changes.mapPos(e)),t.effects.reduce((e,t)=>t.is(js)?t.value:e,e)}}),Us=Vi.fromClass(class{constructor(e){this.view=e,this.cursor=null,this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(e){var t;let i=e.state.field($s);null==i?null!=this.cursor&&(null===(t=this.cursor)||void 0===t||t.remove(),this.cursor=null):(this.cursor||(this.cursor=this.view.scrollDOM.appendChild(document.createElement("div")),this.cursor.className="cm-dropCursor"),(e.startState.field($s)!=i||e.docChanged||e.geometryChanged)&&this.view.requestMeasure(this.measureReq))}readPos(){let e=this.view.state.field($s),t=null!=e&&this.view.coordsAtPos(e);if(!t)return null;let i=this.view.scrollDOM.getBoundingClientRect();return{left:t.left-i.left+this.view.scrollDOM.scrollLeft,top:t.top-i.top+this.view.scrollDOM.scrollTop,height:t.bottom-t.top}}drawCursor(e){this.cursor&&(e?(this.cursor.style.left=e.left+"px",this.cursor.style.top=e.top+"px",this.cursor.style.height=e.height+"px"):this.cursor.style.left="-100000px")}destroy(){this.cursor&&this.cursor.remove()}setDropPos(e){this.view.state.field($s)!=e&&this.view.dispatch({effects:js.of(e)})}},{eventHandlers:{dragover(e){this.setDropPos(this.view.posAtCoords({x:e.clientX,y:e.clientY}))},dragleave(e){e.target!=this.view.contentDOM&&this.view.contentDOM.contains(e.relatedTarget)||this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function Gs(){return[$s,Us]}function Ks(e,t,i,n,r){t.lastIndex=0;for(let s,o=e.iterRange(i,n),l=i;!o.next().done;l+=o.value.length)if(!o.lineBreak)while(s=t.exec(o.value))r(l+s.index,l+s.index+s[0].length,s)}function Js(e,t){let i=e.visibleRanges;if(1==i.length&&i[0].from==e.viewport.from&&i[0].to==e.viewport.to)return i;let n=[];for(let{from:r,to:s}of i)r=Math.max(e.state.doc.lineAt(r).from,r-t),s=Math.min(e.state.doc.lineAt(s).to,s+t),n.length&&n[n.length-1].to>=r?n[n.length-1].to=s:n.push({from:r,to:s});return n}class Xs{constructor(e){let{regexp:t,decoration:i,boundary:n,maxLength:r=1e3}=e;if(!t.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");this.regexp=t,this.getDeco="function"==typeof i?i:()=>i,this.boundary=n,this.maxLength=r}createDeco(e){let t=new Ne;for(let{from:i,to:n}of Js(e,this.maxLength))Ks(e.state.doc,this.regexp,i,n,(i,n,r)=>t.add(i,n,this.getDeco(r,e,i)));return t.finish()}updateDeco(e,t){let i=1e9,n=-1;return e.docChanged&&e.changes.iterChanges((t,r,s,o)=>{o>e.view.viewport.from&&s<e.view.viewport.to&&(i=Math.min(s,i),n=Math.max(o,n))}),e.viewportChanged||n-i>1e3?this.createDeco(e.view):n>-1?this.updateRange(e.view,t.map(e.changes),i,n):t}updateRange(e,t,i,n){for(let r of e.visibleRanges){let s=Math.max(r.from,i),o=Math.min(r.to,n);if(o>s){let i=e.state.doc.lineAt(s),n=i.to<o?e.state.doc.lineAt(o):i,l=Math.max(r.from,i.from),a=Math.min(r.to,n.to);if(this.boundary){for(;s>i.from;s--)if(this.boundary.test(i.text[s-1-i.from])){l=s;break}for(;o<n.to;o++)if(this.boundary.test(n.text[o-n.from])){a=o;break}}let h,c=[];if(i==n){this.regexp.lastIndex=l-i.from;while((h=this.regexp.exec(i.text))&&h.index<a-i.from){let t=h.index+i.from;c.push(this.getDeco(h,e,t).range(t,t+h[0].length))}}else Ks(e.state.doc,this.regexp,l,a,(t,i,n)=>c.push(this.getDeco(n,e,t).range(t,i)));t=t.update({filterFrom:l,filterTo:a,filter:(e,t)=>e<l||t>a,add:c})}}return t}}const Zs=null!=/x/.unicode?"gu":"g",Qs=new RegExp("[\0-\b\n--­؜​‎‏\u2028\u2029‭‮\ufeff￹-￼]",Zs),Ys={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let eo=null;function to(){var e;if(null==eo&&"undefined"!=typeof document&&document.body){let t=document.body.style;eo=null!=(null!==(e=t.tabSize)&&void 0!==e?e:t.MozTabSize)}return eo||!1}const io=_.define({combine(e){let t=Be(e,{render:null,specialChars:Qs,addSpecialChars:null});return(t.replaceTabs=!to())&&(t.specialChars=new RegExp("\t|"+t.specialChars.source,Zs)),t.addSpecialChars&&(t.specialChars=new RegExp(t.specialChars.source+"|"+t.addSpecialChars.source,Zs)),t}});function no(e={}){return[io.of(e),so()]}let ro=null;function so(){return ro||(ro=Vi.fromClass(class{constructor(e){this.view=e,this.decorations=vi.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(e.state.facet(io)),this.decorations=this.decorator.createDeco(e)}makeDecorator(e){return new Xs({regexp:e.specialChars,decoration:(t,i,n)=>{let{doc:r}=i.state,s=k(t[0],0);if(9==s){let e=r.lineAt(n),t=i.state.tabSize,s=Ge(e.text,t,n-e.from);return vi.replace({widget:new ho((t-s%t)*this.view.defaultCharacterWidth)})}return this.decorationCache[s]||(this.decorationCache[s]=vi.replace({widget:new ao(e,s)}))},boundary:e.replaceTabs?void 0:/[^]/})}update(e){let t=e.state.facet(io);e.startState.facet(io)!=t?(this.decorator=this.makeDecorator(t),this.decorations=this.decorator.createDeco(e.view)):this.decorations=this.decorator.updateDeco(e,this.decorations)}},{decorations:e=>e.decorations}))}const oo="•";function lo(e){return e>=32?oo:10==e?"␤":String.fromCharCode(9216+e)}class ao extends mi{constructor(e,t){super(),this.options=e,this.code=t}eq(e){return e.code==this.code}toDOM(e){let t=lo(this.code),i=e.state.phrase("Control character")+" "+(Ys[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,t);if(n)return n;let r=document.createElement("span");return r.textContent=t,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return!1}}class ho extends mi{constructor(e){super(),this.width=e}eq(e){return e.width==this.width}toDOM(){let e=document.createElement("span");return e.textContent="\t",e.className="cm-tab",e.style.width=this.width+"px",e}ignoreEvent(){return!1}}function co(){return fo}const uo=vi.line({class:"cm-activeLine"}),fo=Vi.fromClass(class{constructor(e){this.decorations=this.getDeco(e)}update(e){(e.docChanged||e.selectionSet)&&(this.decorations=this.getDeco(e.view))}getDeco(e){let t=-1,i=[];for(let n of e.state.selection.ranges){if(!n.empty)return vi.none;let r=e.lineBlockAt(n.head);r.from>t&&(i.push(uo.range(r.from)),t=r.from)}return vi.set(i)}},{decorations:e=>e.decorations});const po=2e3;function mo(e,t,i){let n=Math.min(t.line,i.line),r=Math.max(t.line,i.line),s=[];if(t.off>po||i.off>po||t.col<0||i.col<0){let o=Math.min(t.off,i.off),l=Math.max(t.off,i.off);for(let t=n;t<=r;t++){let i=e.doc.line(t);i.length<=l&&s.push(N.range(i.from+o,i.to+l))}}else{let o=Math.min(t.col,i.col),l=Math.max(t.col,i.col);for(let t=n;t<=r;t++){let i=e.doc.line(t),n=Ke(i.text,o,e.tabSize,!0);if(n>-1){let t=Ke(i.text,l,e.tabSize);s.push(N.range(i.from+n,i.from+t))}}}return s}function go(e,t){let i=e.coordsAtPos(e.viewport.from);return i?Math.round(Math.abs((i.left-t)/e.defaultCharacterWidth)):-1}function vo(e,t){let i=e.posAtCoords({x:t.clientX,y:t.clientY},!1),n=e.state.doc.lineAt(i),r=i-n.from,s=r>po?-1:r==n.length?go(e,t.clientX):Ge(n.text,e.state.tabSize,i-n.from);return{line:n.number,col:s,off:r}}function xo(e,t){let i=vo(e,t),n=e.state.selection;return i?{update(e){if(e.docChanged){let t=e.changes.mapPos(e.startState.doc.line(i.line).from),r=e.state.doc.lineAt(t);i={line:r.number,col:i.col,off:Math.min(i.off,r.length)},n=n.map(e.changes)}},get(t,r,s){let o=vo(e,t);if(!o)return n;let l=mo(e.state,i,o);return l.length?s?N.create(l.concat(n.ranges)):N.create(l):n}}:null}function wo(e){let t=(null===e||void 0===e?void 0:e.eventFilter)||(e=>e.altKey&&0==e.button);return ms.mouseSelectionStyle.of((e,i)=>t(i)?xo(e,i):null)}const bo={Alt:[18,e=>e.altKey],Control:[17,e=>e.ctrlKey],Shift:[16,e=>e.shiftKey],Meta:[91,e=>e.metaKey]},yo={style:"cursor: crosshair"};function ko(e={}){let[t,i]=bo[e.key||"Alt"],n=Vi.fromClass(class{constructor(e){this.view=e,this.isDown=!1}set(e){this.isDown!=e&&(this.isDown=e,this.view.update([]))}},{eventHandlers:{keydown(e){this.set(e.keyCode==t||i(e))},keyup(e){e.keyCode!=t&&i(e)||this.set(!1)}}});return[n,ms.contentAttributes.of(e=>{var t;return(null===(t=e.plugin(n))||void 0===t?void 0:t.isDown)?yo:null})]}const Do="-10000px";class Co{constructor(e,t,i){this.facet=t,this.createTooltipView=i,this.input=e.state.facet(t),this.tooltips=this.input.filter(e=>e),this.tooltipViews=this.tooltips.map(i)}update(e){let t=e.state.facet(this.facet),i=t.filter(e=>e);if(t===this.input){for(let t of this.tooltipViews)t.update&&t.update(e);return!1}let n=[];for(let r=0;r<i.length;r++){let t=i[r],s=-1;if(t){for(let e=0;e<this.tooltips.length;e++){let i=this.tooltips[e];i&&i.create==t.create&&(s=e)}if(s<0)n[r]=this.createTooltipView(t);else{let t=n[r]=this.tooltipViews[s];t.update&&t.update(e)}}}for(let r of this.tooltipViews)n.indexOf(r)<0&&r.dom.remove();return this.input=t,this.tooltips=i,this.tooltipViews=n,!0}}function So(){return{top:0,left:0,bottom:innerHeight,right:innerWidth}}const Ao=_.define({combine:e=>{var t,i,n;return{position:Qt.ios?"absolute":(null===(t=e.find(e=>e.position))||void 0===t?void 0:t.position)||"fixed",parent:(null===(i=e.find(e=>e.parent))||void 0===i?void 0:i.parent)||null,tooltipSpace:(null===(n=e.find(e=>e.tooltipSpace))||void 0===n?void 0:n.tooltipSpace)||So}}}),Eo=Vi.fromClass(class{constructor(e){var t;this.view=e,this.inView=!0,this.lastTransaction=0,this.measureTimeout=-1;let i=e.state.facet(Ao);this.position=i.position,this.parent=i.parent,this.classes=e.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.manager=new Co(e,Fo,e=>this.createTooltip(e)),this.intersectionObserver="function"==typeof IntersectionObserver?new IntersectionObserver(e=>{Date.now()>this.lastTransaction-50&&e.length>0&&e[e.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),null===(t=e.dom.ownerDocument.defaultView)||void 0===t||t.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let e of this.manager.tooltipViews)this.intersectionObserver.observe(e.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(e){e.transactions.length&&(this.lastTransaction=Date.now());let t=this.manager.update(e);t&&this.observeIntersection();let i=t||e.geometryChanged,n=e.state.facet(Ao);if(n.position!=this.position){this.position=n.position;for(let e of this.manager.tooltipViews)e.dom.style.position=this.position;i=!0}if(n.parent!=this.parent){this.parent&&this.container.remove(),this.parent=n.parent,this.createContainer();for(let e of this.manager.tooltipViews)this.container.appendChild(e.dom);i=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);i&&this.maybeMeasure()}createTooltip(e){let t=e.create(this.view);if(t.dom.classList.add("cm-tooltip"),e.arrow&&!t.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let e=document.createElement("div");e.className="cm-tooltip-arrow",t.dom.appendChild(e)}return t.dom.style.position=this.position,t.dom.style.top=Do,this.container.appendChild(t.dom),t.mount&&t.mount(this.view),t}destroy(){var e,t;null===(e=this.view.dom.ownerDocument.defaultView)||void 0===e||e.removeEventListener("resize",this.measureSoon);for(let{dom:i}of this.manager.tooltipViews)i.remove();null===(t=this.intersectionObserver)||void 0===t||t.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let e=this.view.dom.getBoundingClientRect();return{editor:e,parent:this.parent?this.container.getBoundingClientRect():e,pos:this.manager.tooltips.map((e,t)=>{let i=this.manager.tooltipViews[t];return i.getCoords?i.getCoords(e.pos):this.view.coordsAtPos(e.pos)}),size:this.manager.tooltipViews.map(({dom:e})=>e.getBoundingClientRect()),space:this.view.state.facet(Ao).tooltipSpace(this.view)}}writeMeasure(e){let{editor:t,space:i}=e,n=[];for(let r=0;r<this.manager.tooltips.length;r++){let s=this.manager.tooltips[r],o=this.manager.tooltipViews[r],{dom:l}=o,a=e.pos[r],h=e.size[r];if(!a||a.bottom<=Math.max(t.top,i.top)||a.top>=Math.min(t.bottom,i.bottom)||a.right<Math.max(t.left,i.left)-.1||a.left>Math.min(t.right,i.right)+.1){l.style.top=Do;continue}let c=s.arrow?o.dom.querySelector(".cm-tooltip-arrow"):null,u=c?7:0,d=h.right-h.left,f=h.bottom-h.top,p=o.offset||Bo,m=this.view.textDirection==Qi.LTR,g=h.width>i.right-i.left?m?i.left:i.right-h.width:m?Math.min(a.left-(c?14:0)+p.x,i.right-d):Math.max(i.left,a.left-d+(c?14:0)-p.x),v=!!s.above;!s.strictSide&&(v?a.top-(h.bottom-h.top)-p.y<i.top:a.bottom+(h.bottom-h.top)+p.y>i.bottom)&&v==i.bottom-a.bottom>a.top-i.top&&(v=!v);let x=v?a.top-f-u-p.y:a.bottom+u+p.y,w=g+d;if(!0!==o.overlap)for(let e of n)e.left<w&&e.right>g&&e.top<x+f&&e.bottom>x&&(x=v?e.top-f-2-u:e.bottom+u+2);"absolute"==this.position?(l.style.top=x-e.parent.top+"px",l.style.left=g-e.parent.left+"px"):(l.style.top=x+"px",l.style.left=g+"px"),c&&(c.style.left=a.left+(m?p.x:-p.x)-(g+14-7)+"px"),!0!==o.overlap&&n.push({left:g,top:x,right:w,bottom:x+f}),l.classList.toggle("cm-tooltip-above",v),l.classList.toggle("cm-tooltip-below",!v),o.positioned&&o.positioned()}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let e of this.manager.tooltipViews)e.dom.style.top=Do}},{eventHandlers:{scroll(){this.maybeMeasure()}}}),Mo=ms.baseTheme({".cm-tooltip":{zIndex:100},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:"14px",position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),Bo={x:0,y:0},Fo=_.define({enables:[Eo,Mo]}),To=_.define();class Oo{constructor(e){this.view=e,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new Co(e,To,e=>this.createHostedView(e))}static create(e){return new Oo(e)}createHostedView(e){let t=e.create(this.view);return t.dom.classList.add("cm-tooltip-section"),this.dom.appendChild(t.dom),this.mounted&&t.mount&&t.mount(this.view),t}mount(e){for(let t of this.manager.tooltipViews)t.mount&&t.mount(e);this.mounted=!0}positioned(){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned()}update(e){this.manager.update(e)}}const Lo=Fo.compute([To],e=>{let t=e.facet(To).filter(e=>e);return 0===t.length?null:{pos:Math.min(...t.map(e=>e.pos)),end:Math.max(...t.filter(e=>null!=e.end).map(e=>e.end)),create:Oo.create,above:t[0].above,arrow:t.some(e=>e.arrow)}});class Ro{constructor(e,t,i,n,r){this.view=e,this.source=t,this.field=i,this.setHover=n,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:e.dom,time:0},this.checkHover=this.checkHover.bind(this),e.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),e.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active)return;let e=Date.now()-this.lastMove.time;e<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-e):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{lastMove:e}=this,t=this.view.contentDOM.contains(e.target)?this.view.posAtCoords(e):null;if(null==t)return;let i=this.view.coordsAtPos(t);if(null==i||e.y<i.top||e.y>i.bottom||e.x<i.left-this.view.defaultCharacterWidth||e.x>i.right+this.view.defaultCharacterWidth)return;let n=this.view.bidiSpans(this.view.state.doc.lineAt(t)).find(e=>e.from<=t&&e.to>=t),r=n&&n.dir==Qi.RTL?-1:1,s=this.source(this.view,t,e.x<i.left?-r:r);if(null===s||void 0===s?void 0:s.then){let e=this.pending={pos:t};s.then(t=>{this.pending==e&&(this.pending=null,t&&this.view.dispatch({effects:this.setHover.of(t)}))},e=>zi(this.view.state,e,"hover tooltip"))}else s&&this.view.dispatch({effects:this.setHover.of(s)})}mousemove(e){var t;this.lastMove={x:e.clientX,y:e.clientY,target:e.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let i=this.active;if(i&&!Io(this.lastMove.target)||this.pending){let{pos:n}=i||this.pending,r=null!==(t=null===i||void 0===i?void 0:i.end)&&void 0!==t?t:n;(n==r?this.view.posAtCoords(this.lastMove)==n:No(this.view,n,r,e.clientX,e.clientY,6))||(this.view.dispatch({effects:this.setHover.of(null)}),this.pending=null)}}mouseleave(){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1,this.active&&this.view.dispatch({effects:this.setHover.of(null)})}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}function Io(e){for(let t=e;t;t=t.parentNode)if(1==t.nodeType&&t.classList.contains("cm-tooltip"))return!0;return!1}function No(e,t,i,n,r,s){let o=document.createRange(),l=e.domAtPos(t),a=e.domAtPos(i);o.setEnd(a.node,a.offset),o.setStart(l.node,l.offset);let h=o.getClientRects();o.detach();for(let c=0;c<h.length;c++){let e=h[c],t=Math.max(e.top-r,r-e.bottom,e.left-n,n-e.right);if(t<=s)return!0}return!1}function Po(e,t={}){let i=fe.define(),n=U.define({create(){return null},update(e,n){if(e&&(t.hideOnChange&&(n.docChanged||n.selection)||t.hideOn&&t.hideOn(n,e)))return null;if(e&&n.docChanged){let t=n.changes.mapPos(e.pos,-1,A.TrackDel);if(null==t)return null;let i=Object.assign(Object.create(null),e);i.pos=t,null!=e.end&&(i.end=n.changes.mapPos(e.end)),e=i}for(let t of n.effects)t.is(i)&&(e=t.value),t.is(_o)&&(e=null);return e},provide:e=>To.from(e)});return[n,Vi.define(r=>new Ro(r,e,n,i,t.hoverTime||300)),Lo]}function zo(e,t){let i=e.plugin(Eo);if(!i)return null;let n=i.manager.tooltips.indexOf(t);return n<0?null:i.manager.tooltipViews[n]}const _o=fe.define();const Ho=_.define({combine(e){let t,i;for(let n of e)t=t||n.topContainer,i=i||n.bottomContainer;return{topContainer:t,bottomContainer:i}}});function qo(e,t){let i=e.plugin(Vo),n=i?i.specs.indexOf(t):-1;return n>-1?i.panels[n]:null}const Vo=Vi.fromClass(class{constructor(e){this.input=e.state.facet($o),this.specs=this.input.filter(e=>e),this.panels=this.specs.map(t=>t(e));let t=e.state.facet(Ho);this.top=new Wo(e,!0,t.topContainer),this.bottom=new Wo(e,!1,t.bottomContainer),this.top.sync(this.panels.filter(e=>e.top)),this.bottom.sync(this.panels.filter(e=>!e.top));for(let i of this.panels)i.dom.classList.add("cm-panel"),i.mount&&i.mount()}update(e){let t=e.state.facet(Ho);this.top.container!=t.topContainer&&(this.top.sync([]),this.top=new Wo(e.view,!0,t.topContainer)),this.bottom.container!=t.bottomContainer&&(this.bottom.sync([]),this.bottom=new Wo(e.view,!1,t.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let i=e.state.facet($o);if(i!=this.input){let t=i.filter(e=>e),n=[],r=[],s=[],o=[];for(let i of t){let t,l=this.specs.indexOf(i);l<0?(t=i(e.view),o.push(t)):(t=this.panels[l],t.update&&t.update(e)),n.push(t),(t.top?r:s).push(t)}this.specs=t,this.panels=n,this.top.sync(r),this.bottom.sync(s);for(let e of o)e.dom.classList.add("cm-panel"),e.mount&&e.mount()}else for(let n of this.panels)n.update&&n.update(e)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:e=>ms.scrollMargins.of(t=>{let i=t.plugin(e);return i&&{top:i.top.scrollMargin(),bottom:i.bottom.scrollMargin()}})});class Wo{constructor(e,t,i){this.view=e,this.top=t,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(e){for(let t of this.panels)t.destroy&&e.indexOf(t)<0&&t.destroy();this.panels=e,this.syncDOM()}syncDOM(){if(0==this.panels.length)return void(this.dom&&(this.dom.remove(),this.dom=void 0));if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let e=this.container||this.view.dom;e.insertBefore(this.dom,this.top?e.firstChild:null)}let e=this.dom.firstChild;for(let t of this.panels)if(t.dom.parentNode==this.dom){while(e!=t.dom)e=jo(e);e=e.nextSibling}else this.dom.insertBefore(t.dom,e);while(e)e=jo(e)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(this.container&&this.classes!=this.view.themeClasses){for(let e of this.classes.split(" "))e&&this.container.classList.remove(e);for(let e of(this.classes=this.view.themeClasses).split(" "))e&&this.container.classList.add(e)}}}function jo(e){let t=e.nextSibling;return e.remove(),t}const $o=_.define({enables:Vo});class Uo extends Fe{compare(e){return this==e||this.constructor==e.constructor&&this.eq(e)}eq(e){return!1}destroy(e){}}Uo.prototype.elementClass="",Uo.prototype.toDOM=void 0,Uo.prototype.mapMode=A.TrackBefore,Uo.prototype.startSide=Uo.prototype.endSide=-1,Uo.prototype.point=!0;const Go=_.define(),Ko={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>Re.empty,lineMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Jo=_.define();function Xo(e){return[Qo(),Jo.of(Object.assign(Object.assign({},Ko),e))]}const Zo=_.define({combine:e=>e.some(e=>e)});function Qo(e){let t=[Yo];return e&&!1===e.fixed&&t.push(Zo.of(!0)),t}const Yo=Vi.fromClass(class{constructor(e){this.view=e,this.prevViewport=e.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight+"px",this.gutters=e.state.facet(Jo).map(t=>new nl(e,t));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!e.state.facet(Zo),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),e.scrollDOM.insertBefore(this.dom,e.contentDOM)}update(e){if(this.updateGutters(e)){let t=this.prevViewport,i=e.view.viewport,n=Math.min(t.to,i.to)-Math.max(t.from,i.from);this.syncGutters(n<.8*(i.to-i.from))}e.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight+"px"),this.view.state.facet(Zo)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=e.view.viewport}syncGutters(e){let t=this.dom.nextSibling;e&&this.dom.remove();let i=Re.iter(this.view.state.facet(Go),this.view.viewport.from),n=[],r=this.gutters.map(e=>new il(e,this.view.viewport,-this.view.documentPadding.top));for(let s of this.view.viewportLineBlocks){let e;if(Array.isArray(s.type)){for(let t of s.type)if(t.type==gi.Text){e=t;break}}else e=s.type==gi.Text?s:void 0;if(e){n.length&&(n=[]),tl(i,n,s.from);for(let t of r)t.line(this.view,e,n)}}for(let s of r)s.finish();e&&this.view.scrollDOM.insertBefore(this.dom,t)}updateGutters(e){let t=e.startState.facet(Jo),i=e.state.facet(Jo),n=e.docChanged||e.heightChanged||e.viewportChanged||!Re.eq(e.startState.facet(Go),e.state.facet(Go),e.view.viewport.from,e.view.viewport.to);if(t==i)for(let r of this.gutters)r.update(e)&&(n=!0);else{n=!0;let r=[];for(let n of i){let i=t.indexOf(n);i<0?r.push(new nl(this.view,n)):(this.gutters[i].update(e),r.push(this.gutters[i]))}for(let e of this.gutters)e.dom.remove(),r.indexOf(e)<0&&e.destroy();for(let e of r)this.dom.appendChild(e.dom);this.gutters=r}return n}destroy(){for(let e of this.gutters)e.destroy();this.dom.remove()}},{provide:e=>ms.scrollMargins.of(t=>{let i=t.plugin(e);return i&&0!=i.gutters.length&&i.fixed?t.textDirection==Qi.LTR?{left:i.dom.offsetWidth}:{right:i.dom.offsetWidth}:null})});function el(e){return Array.isArray(e)?e:[e]}function tl(e,t,i){while(e.value&&e.from<=i)e.from==i&&t.push(e.value),e.next()}class il{constructor(e,t,i){this.gutter=e,this.height=i,this.localMarkers=[],this.i=0,this.cursor=Re.iter(e.markers,t.from)}line(e,t,i){this.localMarkers.length&&(this.localMarkers=[]),tl(this.cursor,this.localMarkers,t.from);let n=i.length?this.localMarkers.concat(i):this.localMarkers,r=this.gutter.config.lineMarker(e,t,n);r&&n.unshift(r);let s=this.gutter;if(0==n.length&&!s.config.renderEmptyElements)return;let o=t.top-this.height;if(this.i==s.elements.length){let i=new rl(e,t.height,o,n);s.elements.push(i),s.dom.appendChild(i.dom)}else s.elements[this.i].update(e,t.height,o,n);this.height=t.bottom,this.i++}finish(){let e=this.gutter;while(e.elements.length>this.i){let t=e.elements.pop();e.dom.removeChild(t.dom),t.destroy()}}}class nl{constructor(e,t){this.view=e,this.config=t,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in t.domEventHandlers)this.dom.addEventListener(i,n=>{let r=e.lineBlockAtHeight(n.clientY-e.documentTop);t.domEventHandlers[i](e,r,n)&&n.preventDefault()});this.markers=el(t.markers(e)),t.initialSpacer&&(this.spacer=new rl(e,0,0,[t.initialSpacer(e)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(e){let t=this.markers;if(this.markers=el(this.config.markers(e.view)),this.spacer&&this.config.updateSpacer){let t=this.config.updateSpacer(this.spacer.markers[0],e);t!=this.spacer.markers[0]&&this.spacer.update(e.view,0,0,[t])}let i=e.view.viewport;return!Re.eq(this.markers,t,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(e)}destroy(){for(let e of this.elements)e.destroy()}}class rl{constructor(e,t,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(e,t,i,n)}update(e,t,i,n){this.height!=t&&(this.dom.style.height=(this.height=t)+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),sl(this.markers,n)||this.setMarkers(e,n)}setMarkers(e,t){let i="cm-gutterElement",n=this.dom.firstChild;for(let r=0,s=0;;){let o=s,l=r<t.length?t[r++]:null,a=!1;if(l){let e=l.elementClass;e&&(i+=" "+e);for(let t=s;t<this.markers.length;t++)if(this.markers[t].compare(l)){o=t,a=!0;break}}else o=this.markers.length;while(s<o){let e=this.markers[s++];if(e.toDOM){e.destroy(n);let t=n.nextSibling;n.remove(),n=t}}if(!l)break;l.toDOM&&(a?n=n.nextSibling:this.dom.insertBefore(l.toDOM(e),n)),a&&s++}this.dom.className=i,this.markers=t}destroy(){this.setMarkers(null,[])}}function sl(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++)if(!e[i].compare(t[i]))return!1;return!0}const ol=_.define(),ll=_.define({combine(e){return Be(e,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(e,t){let i=Object.assign({},e);for(let n in t){let e=i[n],r=t[n];i[n]=e?(t,i,n)=>e(t,i,n)||r(t,i,n):r}return i}})}});class al extends Uo{constructor(e){super(),this.number=e}eq(e){return this.number==e.number}toDOM(){return document.createTextNode(this.number)}}function hl(e,t){return e.state.facet(ll).formatNumber(t,e.state)}const cl=Jo.compute([ll],e=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(e){return e.state.facet(ol)},lineMarker(e,t,i){return i.some(e=>e.toDOM)?null:new al(hl(e,e.state.doc.lineAt(t.from).number))},lineMarkerChange:e=>e.startState.facet(ll)!=e.state.facet(ll),initialSpacer(e){return new al(hl(e,dl(e.state.doc.lines)))},updateSpacer(e,t){let i=hl(t.view,dl(t.view.state.doc.lines));return i==e.number?e:new al(i)},domEventHandlers:e.facet(ll).domEventHandlers}));function ul(e={}){return[ll.of(e),Qo(),cl]}function dl(e){let t=9;while(t<e)t=10*t+9;return t}const fl=new class extends Uo{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},pl=Go.compute(["selection"],e=>{let t=[],i=-1;for(let n of e.selection.ranges)if(n.empty){let r=e.doc.lineAt(n.head).from;r>i&&(i=r,t.push(fl.range(r)))}return Re.of(t)});function ml(){return pl}const gl=1024;let vl=0;class xl{constructor(e,t){this.from=e,this.to=t}}class wl{constructor(e={}){this.id=vl++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=yl.match(e)),t=>{let i=e(t);return void 0===i?null:[this,i]}}}wl.closedBy=new wl({deserialize:e=>e.split(" ")}),wl.openedBy=new wl({deserialize:e=>e.split(" ")}),wl.group=new wl({deserialize:e=>e.split(" ")}),wl.contextHash=new wl({perNode:!0}),wl.lookAhead=new wl({perNode:!0}),wl.mounted=new wl({perNode:!0});const bl=Object.create(null);class yl{constructor(e,t,i,n=0){this.name=e,this.props=t,this.id=i,this.flags=n}static define(e){let t=e.props&&e.props.length?Object.create(null):bl,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),n=new yl(e.name||"",t,e.id,i);if(e.props)for(let r of e.props)if(Array.isArray(r)||(r=r(n)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");t[r[0].id]=r[1]}return n}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(wl.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let n of i.split(" "))t[n]=e[i];return e=>{for(let i=e.prop(wl.group),n=-1;n<(i?i.length:0);n++){let r=t[n<0?e.name:i[n]];if(r)return r}}}}yl.none=new yl("",Object.create(null),0,8);const kl=new WeakMap,Dl=new WeakMap;var Cl;(function(e){e[e["ExcludeBuffers"]=1]="ExcludeBuffers",e[e["IncludeAnonymous"]=2]="IncludeAnonymous",e[e["IgnoreMounts"]=4]="IgnoreMounts",e[e["IgnoreOverlays"]=8]="IgnoreOverlays"})(Cl||(Cl={}));class Sl{constructor(e,t,i,n,r){if(this.type=e,this.children=t,this.positions=i,this.length=n,this.props=null,r&&r.length){this.props=Object.create(null);for(let[e,t]of r)this.props["number"==typeof e?e:e.id]=t}}toString(){let e=this.prop(wl.mounted);if(e&&!e.overlay)return e.tree.toString();let t="";for(let i of this.children){let e=i.toString();e&&(t&&(t+=","),t+=e)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new Nl(this.topNode,e)}cursorAt(e,t=0,i=0){let n=kl.get(this)||this.topNode,r=new Nl(n);return r.moveTo(e,t),kl.set(this,r._tree),r}get topNode(){return new Tl(this,0,0,null)}resolve(e,t=0){let i=Fl(kl.get(this)||this.topNode,e,t,!1);return kl.set(this,i),i}resolveInner(e,t=0){let i=Fl(Dl.get(this)||this.topNode,e,t,!0);return Dl.set(this,i),i}iterate(e){let{enter:t,leave:i,from:n=0,to:r=this.length}=e;for(let s=this.cursor((e.mode||0)|Cl.IncludeAnonymous);;){let e=!1;if(s.from<=r&&s.to>=n&&(s.type.isAnonymous||!1!==t(s))){if(s.firstChild())continue;e=!0}for(;;){if(e&&i&&!s.type.isAnonymous&&i(s),s.nextSibling())break;if(!s.parent())return;e=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:ql(yl.none,this.children,this.positions,0,this.children.length,0,this.length,(e,t,i)=>new Sl(this.type,e,t,i,this.propValues),e.makeTree||((e,t,i)=>new Sl(yl.none,e,t,i)))}static build(e){return zl(e)}}Sl.empty=new Sl(yl.none,[],[],0);class Al{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new Al(this.buffer,this.index)}}class El{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return yl.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],n=this.set.types[t],r=n.name;if(/\W/.test(r)&&!n.isError&&(r=JSON.stringify(r)),e+=4,i==e)return r;let s=[];while(e<i)s.push(this.childString(e)),e=this.buffer[e+3];return r+"("+s.join(",")+")"}findChild(e,t,i,n,r){let{buffer:s}=this,o=-1;for(let l=e;l!=t;l=s[l+3])if(Ml(r,n,s[l+1],s[l+2])&&(o=l,i>0))break;return o}slice(e,t,i,n){let r=this.buffer,s=new Uint16Array(t-e);for(let o=e,l=0;o<t;)s[l++]=r[o++],s[l++]=r[o++]-i,s[l++]=r[o++]-i,s[l++]=r[o++]-e;return new El(s,n-i,this.set)}}function Ml(e,t,i,n){switch(e){case-2:return i<t;case-1:return n>=t&&i<t;case 0:return i<t&&n>t;case 1:return i<=t&&n>t;case 2:return n>t;case 4:return!0}}function Bl(e,t){let i=e.childBefore(t);while(i){let t=i.lastChild;if(!t||t.to!=i.to)break;t.type.isError&&t.from==t.to?(e=i,i=t.prevSibling):i=t}return e}function Fl(e,t,i,n){var r;while(e.from==e.to||(i<1?e.from>=t:e.from>t)||(i>-1?e.to<=t:e.to<t)){let t=!n&&e instanceof Tl&&e.index<0?null:e.parent;if(!t)return e;e=t}let s=n?0:Cl.IgnoreOverlays;if(n)for(let o=e,l=o.parent;l;o=l,l=o.parent)o instanceof Tl&&o.index<0&&(null===(r=l.enter(t,i,s))||void 0===r?void 0:r.from)!=o.from&&(e=l);for(;;){let n=e.enter(t,i,s);if(!n)return e;e=n}}class Tl{constructor(e,t,i,n){this._tree=e,this.from=t,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,n,r=0){for(let s=this;;){for(let{children:o,positions:l}=s._tree,a=t>0?o.length:-1;e!=a;e+=t){let a=o[e],h=l[e]+s.from;if(Ml(n,i,h,h+a.length))if(a instanceof El){if(r&Cl.ExcludeBuffers)continue;let o=a.findChild(0,a.buffer.length,t,i-h,n);if(o>-1)return new Il(new Rl(s,a,e,h),null,o)}else if(r&Cl.IncludeAnonymous||!a.type.isAnonymous||Pl(a)){let o;if(!(r&Cl.IgnoreMounts)&&a.props&&(o=a.prop(wl.mounted))&&!o.overlay)return new Tl(o.tree,h,e,s);let l=new Tl(a,h,e,s);return r&Cl.IncludeAnonymous||!l.type.isAnonymous?l:l.nextChild(t<0?a.children.length-1:0,t,i,n)}}if(r&Cl.IncludeAnonymous||!s.type.isAnonymous)return null;if(e=s.index>=0?s.index+t:t<0?-1:s._parent._tree.children.length,s=s._parent,!s)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let n;if(!(i&Cl.IgnoreOverlays)&&(n=this._tree.prop(wl.mounted))&&n.overlay){let i=e-this.from;for(let{from:e,to:r}of n.overlay)if((t>0?e<=i:e<i)&&(t<0?r>=i:r>i))return new Tl(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;while(e.type.isAnonymous&&e._parent)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}cursor(e=0){return new Nl(this,e)}get tree(){return this._tree}toTree(){return this._tree}resolve(e,t=0){return Fl(this,e,t,!1)}resolveInner(e,t=0){return Fl(this,e,t,!0)}enterUnfinishedNodesBefore(e){return Bl(this,e)}getChild(e,t=null,i=null){let n=Ol(this,e,t,i);return n.length?n[0]:null}getChildren(e,t=null,i=null){return Ol(this,e,t,i)}toString(){return this._tree.toString()}get node(){return this}matchContext(e){return Ll(this,e)}}function Ol(e,t,i,n){let r=e.cursor(),s=[];if(!r.firstChild())return s;if(null!=i)while(!r.type.is(i))if(!r.nextSibling())return s;for(;;){if(null!=n&&r.type.is(n))return s;if(r.type.is(t)&&s.push(r.node),!r.nextSibling())return null==n?s:[]}}function Ll(e,t,i=t.length-1){for(let n=e.parent;i>=0;n=n.parent){if(!n)return!1;if(!n.type.isAnonymous){if(t[i]&&t[i]!=n.name)return!1;i--}}return!0}class Rl{constructor(e,t,i,n){this.parent=e,this.buffer=t,this.index=i,this.start=n}}class Il{constructor(e,t,i){this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}child(e,t,i){let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],e,t-this.context.start,i);return r<0?null:new Il(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&Cl.ExcludeBuffers)return null;let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return r<0?null:new Il(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new Il(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new Il(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}cursor(e=0){return new Nl(this,e)}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,n=this.index+4,r=i.buffer[this.index+3];if(r>n){let s=i.buffer[this.index+1],o=i.buffer[this.index+2];e.push(i.slice(n,r,s,o)),t.push(0)}return new Sl(this.type,e,t,this.to-this.from)}resolve(e,t=0){return Fl(this,e,t,!1)}resolveInner(e,t=0){return Fl(this,e,t,!0)}enterUnfinishedNodesBefore(e){return Bl(this,e)}toString(){return this.context.buffer.childString(this.index)}getChild(e,t=null,i=null){let n=Ol(this,e,t,i);return n.length?n[0]:null}getChildren(e,t=null,i=null){return Ol(this,e,t,i)}get node(){return this}matchContext(e){return Ll(this,e)}}class Nl{constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof Tl)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}get name(){return this.type.name}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:i,buffer:n}=this.buffer;return this.type=t||n.set.types[n.buffer[e]],this.from=i+n.buffer[e+1],this.to=i+n.buffer[e+2],!0}yield(e){return!!e&&(e instanceof Tl?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:n}=this.buffer,r=n.findChild(this.index+4,n.buffer[this.index+3],e,t-this.buffer.start,i);return!(r<0)&&(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?!(i&Cl.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&Cl.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&Cl.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let e=i<0?0:this.stack[i]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(e)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:n}=this;if(n){if(e>0){if(this.index<n.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(n.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:i}=n)}else({index:t,_parent:i}=this._tree);for(;i;({index:t,_parent:i}=i))if(t>-1)for(let n=t+e,r=e<0?-1:i._tree.children.length;n!=r;n+=e){let e=i._tree.children[n];if(this.mode&Cl.IncludeAnonymous||e instanceof El||!e.type.isAnonymous||Pl(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){while(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))if(!this.parent())break;while(this.enterChild(1,e,t));return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let n=this.index,r=this.stack.length;r>=0;){for(let s=e;s;s=s._parent)if(s.index==n){if(n==this.index)return s;t=s,i=r+1;break e}n=this.stack[--r]}for(let n=i;n<this.stack.length;n++)t=new Il(this.buffer,t,this.stack[n]);return this.bufferNode=new Il(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let n=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;;){if(n&&t&&t(this),n=this.type.isAnonymous,this.nextSibling())break;if(!i)return;this.parent(),i--,n=!0}}}matchContext(e){if(!this.buffer)return Ll(this.node,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let n=e.length-1,r=this.stack.length-1;n>=0;r--){if(r<0)return Ll(this.node,e,n);let s=i[t.buffer[this.stack[r]]];if(!s.isAnonymous){if(e[n]&&e[n]!=s.name)return!1;n--}}return!0}}function Pl(e){return e.children.some(e=>e instanceof El||!e.type.isAnonymous||Pl(e))}function zl(e){var t;let{buffer:i,nodeSet:n,maxBufferLength:r=gl,reused:s=[],minRepeatType:o=n.types.length}=e,l=Array.isArray(i)?new Al(i,i.length):i,a=n.types,h=0,c=0;function u(e,t,i,v,x){let{id:w,start:b,end:y,size:k}=l,D=c;while(k<0){if(l.next(),-1==k){let t=s[w];return i.push(t),void v.push(b-e)}if(-3==k)return void(h=w);if(-4==k)return void(c=w);throw new RangeError("Unrecognized record size: "+k)}let C,S,A=a[w],E=b-e;if(y-b<=r&&(S=m(l.pos-t,x))){let t=new Uint16Array(S.size-S.skip),i=l.pos-S.size,r=t.length;while(l.pos>i)r=g(S.start,t,r);C=new El(t,y-S.start,n),E=S.start-e}else{let e=l.pos-k;l.next();let t=[],i=[],n=w>=o?w:-1,s=0,a=y;while(l.pos>e)n>=0&&l.id==n&&l.size>=0?(l.end<=a-r&&(f(t,i,b,s,l.end,a,n,D),s=t.length,a=l.end),l.next()):u(b,e,t,i,n);if(n>=0&&s>0&&s<t.length&&f(t,i,b,s,b,a,n,D),t.reverse(),i.reverse(),n>-1&&s>0){let e=d(A);C=ql(A,t,i,0,t.length,0,y-b,e,e)}else C=p(A,t,i,y-b,D-y)}i.push(C),v.push(E)}function d(e){return(t,i,n)=>{let r,s,o=0,l=t.length-1;if(l>=0&&(r=t[l])instanceof Sl){if(!l&&r.type==e&&r.length==n)return r;(s=r.prop(wl.lookAhead))&&(o=i[l]+r.length+s)}return p(e,t,i,n,o)}}function f(e,t,i,r,s,o,l,a){let h=[],c=[];while(e.length>r)h.push(e.pop()),c.push(t.pop()+i-s);e.push(p(n.types[l],h,c,o-s,a-o)),t.push(s-i)}function p(e,t,i,n,r=0,s){if(h){let e=[wl.contextHash,h];s=s?[e].concat(s):[e]}if(r>25){let e=[wl.lookAhead,r];s=s?[e].concat(s):[e]}return new Sl(e,t,i,n,s)}function m(e,t){let i=l.fork(),n=0,s=0,a=0,h=i.end-r,c={size:0,start:0,skip:0};e:for(let r=i.pos-e;i.pos>r;){let e=i.size;if(i.id==t&&e>=0){c.size=n,c.start=s,c.skip=a,a+=4,n+=4,i.next();continue}let l=i.pos-e;if(e<0||l<r||i.start<h)break;let u=i.id>=o?4:0,d=i.start;i.next();while(i.pos>l){if(i.size<0){if(-3!=i.size)break e;u+=4}else i.id>=o&&(u+=4);i.next()}s=d,n+=e,a+=u}return(t<0||n==e)&&(c.size=n,c.start=s,c.skip=a),c.size>4?c:void 0}function g(e,t,i){let{id:n,start:r,end:s,size:a}=l;if(l.next(),a>=0&&n<o){let o=i;if(a>4){let n=l.pos-(a-4);while(l.pos>n)i=g(e,t,i)}t[--i]=o,t[--i]=s-e,t[--i]=r-e,t[--i]=n}else-3==a?h=n:-4==a&&(c=n);return i}let v=[],x=[];while(l.pos>0)u(e.start||0,e.bufferStart||0,v,x,-1);let w=null!==(t=e.length)&&void 0!==t?t:v.length?x[0]+v[0].length:0;return new Sl(a[e.topID],v.reverse(),x.reverse(),w)}const _l=new WeakMap;function Hl(e,t){if(!e.isAnonymous||t instanceof El||t.type!=e)return 1;let i=_l.get(t);if(null==i){i=1;for(let n of t.children){if(n.type!=e||!(n instanceof Sl)){i=1;break}i+=Hl(e,n)}_l.set(t,i)}return i}function ql(e,t,i,n,r,s,o,l,a){let h=0;for(let p=n;p<r;p++)h+=Hl(e,t[p]);let c=Math.ceil(1.5*h/8),u=[],d=[];function f(t,i,n,r,o){for(let l=n;l<r;){let n=l,h=i[l],p=Hl(e,t[l]);for(l++;l<r;l++){let i=Hl(e,t[l]);if(p+i>=c)break;p+=i}if(l==n+1){if(p>c){let e=t[n];f(e.children,e.positions,0,e.children.length,i[n]+o);continue}u.push(t[n])}else{let r=i[l-1]+t[l-1].length-h;u.push(ql(e,t,i,n,l,h,r,null,a))}d.push(h+o-s)}}return f(t,i,n,r,0),(l||a)(u,d,o)}class Vl{constructor(e,t,i,n,r=!1,s=!1){this.from=e,this.to=t,this.tree=i,this.offset=n,this.open=(r?1:0)|(s?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(e,t=[],i=!1){let n=[new Vl(0,e.length,e,0,!1,i)];for(let r of t)r.to>e.length&&n.push(r);return n}static applyChanges(e,t,i=128){if(!t.length)return e;let n=[],r=1,s=e.length?e[0]:null;for(let o=0,l=0,a=0;;o++){let h=o<t.length?t[o]:null,c=h?h.fromA:1e9;if(c-l>=i)while(s&&s.from<c){let t=s;if(l>=t.from||c<=t.to||a){let e=Math.max(t.from,l)-a,i=Math.min(t.to,c)-a;t=e>=i?null:new Vl(e,i,t.tree,t.offset+a,o>0,!!h)}if(t&&n.push(t),s.to>c)break;s=r<e.length?e[r++]:null}if(!h)break;l=h.toA,a=h.toA-h.toB}return n}}class Wl{startParse(e,t,i){return"string"==typeof e&&(e=new jl(e)),i=i?i.length?i.map(e=>new xl(e.from,e.to)):[new xl(0,0)]:[new xl(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let n=this.startParse(e,t,i);for(;;){let e=n.advance();if(e)return e}}}class jl{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new wl({perNode:!0});let $l=0;class Ul{constructor(e,t,i){this.set=e,this.base=t,this.modified=i,this.id=$l++}static define(e){if(null===e||void 0===e?void 0:e.base)throw new Error("Can not derive from a modified tag");let t=new Ul([],null,[]);if(t.set.push(t),e)for(let i of e.set)t.set.push(i);return t}static defineModifier(){let e=new Kl;return t=>t.modified.indexOf(e)>-1?t:Kl.get(t.base||t,t.modified.concat(e).sort((e,t)=>e.id-t.id))}}let Gl=0;class Kl{constructor(){this.instances=[],this.id=Gl++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(i=>i.base==e&&Jl(t,i.modified));if(i)return i;let n=[],r=new Ul(n,e,t);for(let o of t)o.instances.push(r);let s=Xl(t);for(let o of e.set)for(let e of s)n.push(Kl.get(o,e));return r}}function Jl(e,t){return e.length==t.length&&e.every((e,i)=>e==t[i])}function Xl(e){let t=[e];for(let i=0;i<e.length;i++)for(let n of Xl(e.slice(0,i).concat(e.slice(i+1))))t.push(n);return t}function Zl(e){let t=Object.create(null);for(let i in e){let n=e[i];Array.isArray(n)||(n=[n]);for(let e of i.split(" "))if(e){let i=[],r=2,s=e;for(let t=0;;){if("..."==s&&t>0&&t+3==e.length){r=1;break}let n=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!n)throw new RangeError("Invalid path: "+e);if(i.push("*"==n[0]?"":'"'==n[0][0]?JSON.parse(n[0]):n[0]),t+=n[0].length,t==e.length)break;let o=e[t++];if(t==e.length&&"!"==o){r=0;break}if("/"!=o)throw new RangeError("Invalid path: "+e);s=e.slice(t)}let o=i.length-1,l=i[o];if(!l)throw new RangeError("Invalid path: "+e);let a=new Yl(n,r,o>0?i.slice(0,o):null);t[l]=a.sort(t[l])}}return Ql.add(t)}const Ql=new wl;class Yl{constructor(e,t,i,n){this.tags=e,this.mode=t,this.context=i,this.next=n}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}function ea(e,t){let i=Object.create(null);for(let s of e)if(Array.isArray(s.tag))for(let e of s.tag)i[e.id]=s.class;else i[s.tag.id]=s.class;let{scope:n,all:r=null}=t||{};return{style:e=>{let t=r;for(let n of e)for(let e of n.set){let n=i[e.id];if(n){t=t?t+" "+n:n;break}}return t},scope:n}}function ta(e,t){let i=null;for(let n of e){let e=n.style(t);e&&(i=i?i+" "+e:e)}return i}function ia(e,t,i,n=0,r=e.length){let s=new na(n,Array.isArray(t)?t:[t],i);s.highlightRange(e.cursor(),n,r,"",s.highlighters),s.flush(r)}class na{constructor(e,t,i){this.at=e,this.highlighters=t,this.span=i,this.class=""}startSpan(e,t){t!=this.class&&(this.flush(e),e>this.at&&(this.at=e),this.class=t)}flush(e){e>this.at&&this.class&&this.span(this.at,e,this.class)}highlightRange(e,t,i,n,r){let{type:s,from:o,to:l}=e;if(o>=i||l<=t)return;s.isTop&&(r=this.highlighters.filter(e=>!e.scope||e.scope(s)));let a=n,h=s.prop(Ql),c=!1;while(h){if(!h.context||e.matchContext(h.context)){let e=ta(r,h.tags);e&&(a&&(a+=" "),a+=e,1==h.mode?n+=(n?" ":"")+e:0==h.mode&&(c=!0));break}h=h.next}if(this.startSpan(e.from,a),c)return;let u=e.tree&&e.tree.prop(wl.mounted);if(u&&u.overlay){let s=e.node.enter(u.overlay[0].from+o,1),h=this.highlighters.filter(e=>!e.scope||e.scope(u.tree.type)),c=e.firstChild();for(let d=0,f=o;;d++){let p=d<u.overlay.length?u.overlay[d]:null,m=p?p.from+o:l,g=Math.max(t,f),v=Math.min(i,m);if(g<v&&c)while(e.from<v)if(this.highlightRange(e,g,v,n,r),this.startSpan(Math.min(i,e.to),a),e.to>=m||!e.nextSibling())break;if(!p||m>i)break;f=p.to+o,f>t&&(this.highlightRange(s.cursor(),Math.max(t,p.from+o),Math.min(i,f),n,h),this.startSpan(f,a))}c&&e.parent()}else if(e.firstChild()){do{if(!(e.to<=t)){if(e.from>=i)break;this.highlightRange(e,t,i,n,r),this.startSpan(Math.min(i,e.to),a)}}while(e.nextSibling());e.parent()}}}const ra=Ul.define,sa=ra(),oa=ra(),la=ra(oa),aa=ra(oa),ha=ra(),ca=ra(ha),ua=ra(ha),da=ra(),fa=ra(da),pa=ra(),ma=ra(),ga=ra(),va=ra(ga),xa=ra(),wa={comment:sa,lineComment:ra(sa),blockComment:ra(sa),docComment:ra(sa),name:oa,variableName:ra(oa),typeName:la,tagName:ra(la),propertyName:aa,attributeName:ra(aa),className:ra(oa),labelName:ra(oa),namespace:ra(oa),macroName:ra(oa),literal:ha,string:ca,docString:ra(ca),character:ra(ca),attributeValue:ra(ca),number:ua,integer:ra(ua),float:ra(ua),bool:ra(ha),regexp:ra(ha),escape:ra(ha),color:ra(ha),url:ra(ha),keyword:pa,self:ra(pa),null:ra(pa),atom:ra(pa),unit:ra(pa),modifier:ra(pa),operatorKeyword:ra(pa),controlKeyword:ra(pa),definitionKeyword:ra(pa),moduleKeyword:ra(pa),operator:ma,derefOperator:ra(ma),arithmeticOperator:ra(ma),logicOperator:ra(ma),bitwiseOperator:ra(ma),compareOperator:ra(ma),updateOperator:ra(ma),definitionOperator:ra(ma),typeOperator:ra(ma),controlOperator:ra(ma),punctuation:ga,separator:ra(ga),bracket:va,angleBracket:ra(va),squareBracket:ra(va),paren:ra(va),brace:ra(va),content:da,heading:fa,heading1:ra(fa),heading2:ra(fa),heading3:ra(fa),heading4:ra(fa),heading5:ra(fa),heading6:ra(fa),contentSeparator:ra(da),list:ra(da),quote:ra(da),emphasis:ra(da),strong:ra(da),link:ra(da),monospace:ra(da),strikethrough:ra(da),inserted:ra(),deleted:ra(),changed:ra(),invalid:ra(),meta:xa,documentMeta:ra(xa),annotation:ra(xa),processingInstruction:ra(xa),definition:Ul.defineModifier(),constant:Ul.defineModifier(),function:Ul.defineModifier(),standard:Ul.defineModifier(),local:Ul.defineModifier(),special:Ul.defineModifier()};ea([{tag:wa.link,class:"tok-link"},{tag:wa.heading,class:"tok-heading"},{tag:wa.emphasis,class:"tok-emphasis"},{tag:wa.strong,class:"tok-strong"},{tag:wa.keyword,class:"tok-keyword"},{tag:wa.atom,class:"tok-atom"},{tag:wa.bool,class:"tok-bool"},{tag:wa.url,class:"tok-url"},{tag:wa.labelName,class:"tok-labelName"},{tag:wa.inserted,class:"tok-inserted"},{tag:wa.deleted,class:"tok-deleted"},{tag:wa.literal,class:"tok-literal"},{tag:wa.string,class:"tok-string"},{tag:wa.number,class:"tok-number"},{tag:[wa.regexp,wa.escape,wa.special(wa.string)],class:"tok-string2"},{tag:wa.variableName,class:"tok-variableName"},{tag:wa.local(wa.variableName),class:"tok-variableName tok-local"},{tag:wa.definition(wa.variableName),class:"tok-variableName tok-definition"},{tag:wa.special(wa.variableName),class:"tok-variableName2"},{tag:wa.definition(wa.propertyName),class:"tok-propertyName tok-definition"},{tag:wa.typeName,class:"tok-typeName"},{tag:wa.namespace,class:"tok-namespace"},{tag:wa.className,class:"tok-className"},{tag:wa.macroName,class:"tok-macroName"},{tag:wa.propertyName,class:"tok-propertyName"},{tag:wa.operator,class:"tok-operator"},{tag:wa.comment,class:"tok-comment"},{tag:wa.meta,class:"tok-meta"},{tag:wa.invalid,class:"tok-invalid"},{tag:wa.punctuation,class:"tok-punctuation"}]);var ba;const ya=new wl;class ka{constructor(e,t,i=[]){this.data=e,Me.prototype.hasOwnProperty("tree")||Object.defineProperty(Me.prototype,"tree",{get(){return Ca(this)}}),this.parser=t,this.extension=[La.of(this),Me.languageData.of((e,t,i)=>e.facet(Da(e,t,i)))].concat(i)}isActiveAt(e,t,i=-1){return Da(e,t,i)==this.data}findRegions(e){let t=e.facet(La);if((null===t||void 0===t?void 0:t.data)==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],n=(e,t)=>{if(e.prop(ya)==this.data)return void i.push({from:t,to:t+e.length});let r=e.prop(wl.mounted);if(r){if(r.tree.prop(ya)==this.data){if(r.overlay)for(let e of r.overlay)i.push({from:e.from+t,to:e.to+t});else i.push({from:t,to:t+e.length});return}if(r.overlay){let e=i.length;if(n(r.tree,r.overlay[0].from+t),i.length>e)return}}for(let i=0;i<e.children.length;i++){let r=e.children[i];r instanceof Sl&&n(r,e.positions[i]+t)}};return n(Ca(e),0),i}get allowsNesting(){return!0}}function Da(e,t,i){let n=e.facet(La);if(!n)return null;let r=n.data;if(n.allowsNesting)for(let s=Ca(e).topNode;s;s=s.enter(t,i,Cl.ExcludeBuffers))r=s.type.prop(ya)||r;return r}ka.setState=fe.define();function Ca(e){let t=e.field(ka.state,!1);return t?t.tree:Sl.empty}class Sa{constructor(e,t=e.length){this.doc=e,this.length=t,this.cursorPos=0,this.string="",this.cursor=e.iter()}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let Aa=null;class Ea{constructor(e,t,i=[],n,r,s,o,l){this.parser=e,this.state=t,this.fragments=i,this.tree=n,this.treeLen=r,this.viewport=s,this.skipped=o,this.scheduleOn=l,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new Ea(e,t,[],Sl.empty,0,i,[],null)}startParse(){return this.parser.startParse(new Sa(this.state.doc),this.fragments)}work(e,t){return null!=t&&t>=this.state.doc.length&&(t=void 0),this.tree!=Sl.empty&&this.isDone(null!==t&&void 0!==t?t:this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if("number"==typeof e){let t=Date.now()+e;e=()=>Date.now()>t}for(this.parse||(this.parse=this.startParse()),null!=t&&(null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let n=this.parse.advance();if(n){if(this.fragments=this.withoutTempSkipped(Vl.addTree(n,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=n,this.parse=null,!(this.treeLen<(null!==t&&void 0!==t?t:this.state.doc.length)))return!0;this.parse=this.startParse()}if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{while(!(t=this.parse.advance()));}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(Vl.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=Aa;Aa=this;try{return e()}finally{Aa=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=Ma(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:n,treeLen:r,viewport:s,skipped:o}=this;if(this.takeTree(),!e.empty){let t=[];if(e.iterChangedRanges((e,i,n,r)=>t.push({fromA:e,toA:i,fromB:n,toB:r})),i=Vl.applyChanges(i,t),n=Sl.empty,r=0,s={from:e.mapPos(s.from,-1),to:e.mapPos(s.to,1)},this.skipped.length){o=[];for(let t of this.skipped){let i=e.mapPos(t.from,1),n=e.mapPos(t.to,-1);i<n&&o.push({from:i,to:n})}}}return new Ea(this.parser,t,i,n,r,s,o,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:t,to:n}=this.skipped[i];t<e.to&&n>e.from&&(this.fragments=Ma(this.fragments,t,n),this.skipped.splice(i--,1))}return!(this.skipped.length>=t)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends Wl{createParse(t,i,n){let r=n[0].from,s=n[n.length-1].to,o={parsedPos:r,advance(){let t=Aa;if(t){for(let e of n)t.tempSkipped.push(e);e&&(t.scheduleOn=t.scheduleOn?Promise.all([t.scheduleOn,e]):e)}return this.parsedPos=s,new Sl(yl.none,[],[],s-r)},stoppedAt:null,stopAt(){}};return o}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&0==t[0].from&&t[0].to>=e}static get(){return Aa}}function Ma(e,t,i){return Vl.applyChanges(e,[{fromA:t,toA:i,fromB:t,toB:i}])}class Ba{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new Ba(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=Ea.create(e.facet(La).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new Ba(i)}}ka.state=U.define({create:Ba.init,update(e,t){for(let i of t.effects)if(i.is(ka.setState))return i.value;return t.startState.facet(La)!=t.state.facet(La)?Ba.init(t.state):e.apply(t)}});let Fa=e=>{let t=setTimeout(()=>e(),500);return()=>clearTimeout(t)};"undefined"!=typeof requestIdleCallback&&(Fa=e=>{let t=-1,i=setTimeout(()=>{t=requestIdleCallback(e,{timeout:400})},100);return()=>t<0?clearTimeout(i):cancelIdleCallback(t)});const Ta="undefined"!=typeof navigator&&(null===(ba=navigator.scheduling)||void 0===ba?void 0:ba.isInputPending)?()=>navigator.scheduling.isInputPending():null,Oa=Vi.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(ka.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),e.docChanged&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(ka.state);t.tree==t.context.tree&&t.context.isDone(e.doc.length)||(this.working=Fa(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,r=i.field(ka.state);if(r.tree==r.context.tree&&r.context.isDone(n+1e5))return;let s=Date.now()+Math.min(this.chunkBudget,100,e&&!Ta?Math.max(25,e.timeRemaining()-5):1e9),o=r.context.treeLen<n&&i.doc.length>n+1e3,l=r.context.work(()=>Ta&&Ta()||Date.now()>s,n+(o?0:1e5));this.chunkBudget-=Date.now()-t,(l||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:ka.setState.of(new Ba(r.context))})),this.chunkBudget>0&&(!l||o)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(e=>zi(this.view.state,e)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),La=_.define({combine(e){return e.length?e[0]:null},enables:[ka.state,Oa]});const Ra=_.define(),Ia=_.define({combine:e=>{if(!e.length)return"  ";if(!/^(?: +|\t+)$/.test(e[0]))throw new Error("Invalid indent unit: "+JSON.stringify(e[0]));return e[0]}});function Na(e){let t=e.facet(Ia);return 9==t.charCodeAt(0)?e.tabSize*t.length:t.length}function Pa(e,t){let i="",n=e.tabSize;if(9==e.facet(Ia).charCodeAt(0))while(t>=n)i+="\t",t-=n;for(let r=0;r<t;r++)i+=" ";return i}function za(e,t){e instanceof Me&&(e=new _a(e));for(let n of e.state.facet(Ra)){let i=n(e,t);if(null!=i)return i}let i=Ca(e.state);return i?qa(e,i,t):null}class _a{constructor(e,t={}){this.state=e,this.options=t,this.unit=Na(e)}lineAt(e,t=1){let i=this.state.doc.lineAt(e),{simulateBreak:n,simulateDoubleBreak:r}=this.options;return null!=n&&n>=i.from&&n<=i.to?r&&n==e?{text:"",from:e}:(t<0?n<e:n<=e)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(e,t=1){if(this.options.simulateDoubleBreak&&e==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(e,t);return i.slice(e-n,Math.min(i.length,e+100-n))}column(e,t=1){let{text:i,from:n}=this.lineAt(e,t),r=this.countColumn(i,e-n),s=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return s>-1&&(r+=s-this.countColumn(i,i.search(/\S|$/))),r}countColumn(e,t=e.length){return Ge(e,this.state.tabSize,t)}lineIndent(e,t=1){let{text:i,from:n}=this.lineAt(e,t),r=this.options.overrideIndentation;if(r){let e=r(n);if(e>-1)return e}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const Ha=new wl;function qa(e,t,i){return ja(t.resolveInner(i).enterUnfinishedNodesBefore(i),i,e)}function Va(e){return e.pos==e.options.simulateBreak&&e.options.simulateDoubleBreak}function Wa(e){let t=e.type.prop(Ha);if(t)return t;let i,n=e.firstChild;if(n&&(i=n.type.prop(wl.closedBy))){let t=e.lastChild,n=t&&i.indexOf(t.name)>-1;return e=>Ja(e,!0,1,void 0,n&&!Va(e)?t.from:void 0)}return null==e.parent?$a:null}function ja(e,t,i){for(;e;e=e.parent){let n=Wa(e);if(n)return n(Ua.create(i,t,e))}return null}function $a(){return 0}class Ua extends _a{constructor(e,t,i){super(e.state,e.options),this.base=e,this.pos=t,this.node=i}static create(e,t,i){return new Ua(e,t,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){let e=this.state.doc.lineAt(this.node.from);for(;;){let t=this.node.resolve(e.from);while(t.parent&&t.parent.from==t.from)t=t.parent;if(Ga(t,this.node))break;e=this.state.doc.lineAt(t.from)}return this.lineIndent(e.from)}continue(){let e=this.node.parent;return e?ja(e,this.pos,this.base):0}}function Ga(e,t){for(let i=t;i;i=i.parent)if(e==i)return!0;return!1}function Ka(e){let t=e.node,i=t.childAfter(t.from),n=t.lastChild;if(!i)return null;let r=e.options.simulateBreak,s=e.state.doc.lineAt(i.from),o=null==r||r<=s.from?s.to:Math.min(s.to,r);for(let l=i.to;;){let e=t.childAfter(l);if(!e||e==n)return null;if(!e.type.isSkipped)return e.from<o?i:null;l=e.to}}function Ja(e,t,i,n,r){let s=e.textAfter,o=s.match(/^\s*/)[0].length,l=n&&s.slice(o,o+n.length)==n||r==e.pos+o,a=t?Ka(e):null;return a?l?e.column(a.from):e.column(a.to):e.baseIndent+(l?0:e.unit*i)}const Xa=200;function Za(){return Me.transactionFilter.of(e=>{if(!e.docChanged||!e.isUserEvent("input.type")&&!e.isUserEvent("input.complete"))return e;let t=e.startState.languageDataAt("indentOnInput",e.startState.selection.main.head);if(!t.length)return e;let i=e.newDoc,{head:n}=e.newSelection.main,r=i.lineAt(n);if(n>r.from+Xa)return e;let s=i.sliceString(r.from,n);if(!t.some(e=>e.test(s)))return e;let{state:o}=e,l=-1,a=[];for(let{head:h}of o.selection.ranges){let e=o.doc.lineAt(h);if(e.from==l)continue;l=e.from;let t=za(o,e.from);if(null==t)continue;let i=/^\s*/.exec(e.text)[0],n=Pa(o,t);i!=n&&a.push({from:e.from,to:e.from+i.length,insert:n})}return a.length?[e,{changes:a,sequential:!0}]:e})}const Qa=_.define(),Ya=new wl;function eh(e,t,i){let n=Ca(e);if(n.length<i)return null;let r=n.resolveInner(i),s=null;for(let o=r;o;o=o.parent){if(o.to<=i||o.from>i)continue;if(s&&o.from<t)break;let r=o.type.prop(Ya);if(r&&(o.to<n.length-50||n.length==e.doc.length||!th(o))){let n=r(o,e);n&&n.from<=i&&n.from>=t&&n.to>i&&(s=n)}}return s}function th(e){let t=e.lastChild;return t&&t.to==e.to&&t.type.isError}function ih(e,t,i){for(let n of e.facet(Qa)){let r=n(e,t,i);if(r)return r}return eh(e,t,i)}function nh(e,t){let i=t.mapPos(e.from,1),n=t.mapPos(e.to,-1);return i>=n?void 0:{from:i,to:n}}const rh=fe.define({map:nh}),sh=fe.define({map:nh});function oh(e){let t=[];for(let{head:i}of e.state.selection.ranges)t.some(e=>e.from<=i&&e.to>=i)||t.push(e.lineBlockAt(i));return t}const lh=U.define({create(){return vi.none},update(e,t){e=e.map(t.changes);for(let i of t.effects)i.is(rh)&&!hh(e,i.value.from,i.value.to)?e=e.update({add:[bh.range(i.value.from,i.value.to)]}):i.is(sh)&&(e=e.update({filter:(e,t)=>i.value.from!=e||i.value.to!=t,filterFrom:i.value.from,filterTo:i.value.to}));if(t.selection){let i=!1,{head:n}=t.selection.main;e.between(n,n,(e,t)=>{e<n&&t>n&&(i=!0)}),i&&(e=e.update({filterFrom:n,filterTo:n,filter:(e,t)=>t<=n||e>=n}))}return e},provide:e=>ms.decorations.from(e),toJSON(e,t){let i=[];return e.between(0,t.doc.length,(e,t)=>{i.push(e,t)}),i},fromJSON(e){if(!Array.isArray(e)||e.length%2)throw new RangeError("Invalid JSON for fold state");let t=[];for(let i=0;i<e.length;){let n=e[i++],r=e[i++];if("number"!=typeof n||"number"!=typeof r)throw new RangeError("Invalid JSON for fold state");t.push(bh.range(n,r))}return vi.set(t,!0)}});function ah(e,t,i){var n;let r=null;return null===(n=e.field(lh,!1))||void 0===n||n.between(t,i,(e,t)=>{(!r||r.from>e)&&(r={from:e,to:t})}),r}function hh(e,t,i){let n=!1;return e.between(t,t,(e,r)=>{e==t&&r==i&&(n=!0)}),n}function ch(e,t){return e.field(lh,!1)?t:t.concat(fe.appendConfig.of(wh()))}const uh=e=>{for(let t of oh(e)){let i=ih(e.state,t.from,t.to);if(i)return e.dispatch({effects:ch(e.state,[rh.of(i),fh(e,i)])}),!0}return!1},dh=e=>{if(!e.state.field(lh,!1))return!1;let t=[];for(let i of oh(e)){let n=ah(e.state,i.from,i.to);n&&t.push(sh.of(n),fh(e,n,!1))}return t.length&&e.dispatch({effects:t}),t.length>0};function fh(e,t,i=!0){let n=e.state.doc.lineAt(t.from).number,r=e.state.doc.lineAt(t.to).number;return ms.announce.of(`${e.state.phrase(i?"Folded lines":"Unfolded lines")} ${n} ${e.state.phrase("to")} ${r}.`)}const ph=e=>{let{state:t}=e,i=[];for(let n=0;n<t.doc.length;){let r=e.lineBlockAt(n),s=ih(t,r.from,r.to);s&&i.push(rh.of(s)),n=(s?e.lineBlockAt(s.to):r).to+1}return i.length&&e.dispatch({effects:ch(e.state,i)}),!!i.length},mh=e=>{let t=e.state.field(lh,!1);if(!t||!t.size)return!1;let i=[];return t.between(0,e.state.doc.length,(e,t)=>{i.push(sh.of({from:e,to:t}))}),e.dispatch({effects:i}),!0},gh=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:uh},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:dh},{key:"Ctrl-Alt-[",run:ph},{key:"Ctrl-Alt-]",run:mh}],vh={placeholderDOM:null,placeholderText:"…"},xh=_.define({combine(e){return Be(e,vh)}});function wh(e){let t=[lh,Ch];return e&&t.push(xh.of(e)),t}const bh=vi.replace({widget:new class extends mi{toDOM(e){let{state:t}=e,i=t.facet(xh),n=t=>{let i=e.lineBlockAt(e.posAtDOM(t.target)),n=ah(e.state,i.from,i.to);n&&e.dispatch({effects:sh.of(n)}),t.preventDefault()};if(i.placeholderDOM)return i.placeholderDOM(e,n);let r=document.createElement("span");return r.textContent=i.placeholderText,r.setAttribute("aria-label",t.phrase("folded code")),r.title=t.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=n,r}}}),yh={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class kh extends Uo{constructor(e,t){super(),this.config=e,this.open=t}eq(e){return this.config==e.config&&this.open==e.open}toDOM(e){if(this.config.markerDOM)return this.config.markerDOM(this.open);let t=document.createElement("span");return t.textContent=this.open?this.config.openText:this.config.closedText,t.title=e.state.phrase(this.open?"Fold line":"Unfold line"),t}}function Dh(e={}){let t=Object.assign(Object.assign({},yh),e),i=new kh(t,!0),n=new kh(t,!1),r=Vi.fromClass(class{constructor(e){this.from=e.viewport.from,this.markers=this.buildMarkers(e)}update(e){(e.docChanged||e.viewportChanged||e.startState.facet(La)!=e.state.facet(La)||e.startState.field(lh,!1)!=e.state.field(lh,!1)||Ca(e.startState)!=Ca(e.state)||t.foldingChanged(e))&&(this.markers=this.buildMarkers(e.view))}buildMarkers(e){let t=new Ne;for(let r of e.viewportLineBlocks){let s=ah(e.state,r.from,r.to)?n:ih(e.state,r.from,r.to)?i:null;s&&t.add(r.from,r.from,s)}return t.finish()}}),{domEventHandlers:s}=t;return[r,Xo({class:"cm-foldGutter",markers(e){var t;return(null===(t=e.plugin(r))||void 0===t?void 0:t.markers)||Re.empty},initialSpacer(){return new kh(t,!1)},domEventHandlers:Object.assign(Object.assign({},s),{click:(e,t,i)=>{if(s.click&&s.click(e,t,i))return!0;let n=ah(e.state,t.from,t.to);if(n)return e.dispatch({effects:sh.of(n)}),!0;let r=ih(e.state,t.from,t.to);return!!r&&(e.dispatch({effects:rh.of(r)}),!0)}})}),wh()]}const Ch=ms.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class Sh{constructor(e,t){let i;function n(e){let t=Ye.newName();return(i||(i=Object.create(null)))["."+t]=e,t}const r="string"==typeof t.all?t.all:t.all?n(t.all):void 0,s=t.scope;this.scope=s instanceof ka?e=>e.prop(ya)==s.data:s?e=>e==s:void 0,this.style=ea(e.map(e=>({tag:e.tag,class:e.class||n(Object.assign({},e,{tag:null}))})),{all:r}).style,this.module=i?new Ye(i):null,this.themeType=t.themeType}static define(e,t){return new Sh(e,t||{})}}const Ah=_.define(),Eh=_.define({combine(e){return e.length?[e[0]]:null}});function Mh(e){let t=e.facet(Ah);return t.length?t:e.facet(Eh)}function Bh(e,t){let i,n=[Th];return e instanceof Sh&&(e.module&&n.push(ms.styleModule.of(e.module)),i=e.themeType),(null===t||void 0===t?void 0:t.fallback)?n.push(Eh.of(e)):i?n.push(Ah.computeN([ms.darkTheme],t=>t.facet(ms.darkTheme)==("dark"==i)?[e]:[])):n.push(Ah.of(e)),n}class Fh{constructor(e){this.markCache=Object.create(null),this.tree=Ca(e.state),this.decorations=this.buildDeco(e,Mh(e.state))}update(e){let t=Ca(e.state),i=Mh(e.state),n=i!=Mh(e.startState);t.length<e.view.viewport.to&&!n&&t.type==this.tree.type?this.decorations=this.decorations.map(e.changes):(t!=this.tree||e.viewportChanged||n)&&(this.tree=t,this.decorations=this.buildDeco(e.view,i))}buildDeco(e,t){if(!t||!this.tree.length)return vi.none;let i=new Ne;for(let{from:n,to:r}of e.visibleRanges)ia(this.tree,t,(e,t,n)=>{i.add(e,t,this.markCache[n]||(this.markCache[n]=vi.mark({class:n})))},n,r);return i.finish()}}const Th=J.high(Vi.fromClass(Fh,{decorations:e=>e.decorations})),Oh=Sh.define([{tag:wa.meta,color:"#7a757a"},{tag:wa.link,textDecoration:"underline"},{tag:wa.heading,textDecoration:"underline",fontWeight:"bold"},{tag:wa.emphasis,fontStyle:"italic"},{tag:wa.strong,fontWeight:"bold"},{tag:wa.strikethrough,textDecoration:"line-through"},{tag:wa.keyword,color:"#708"},{tag:[wa.atom,wa.bool,wa.url,wa.contentSeparator,wa.labelName],color:"#219"},{tag:[wa.literal,wa.inserted],color:"#164"},{tag:[wa.string,wa.deleted],color:"#a11"},{tag:[wa.regexp,wa.escape,wa.special(wa.string)],color:"#e40"},{tag:wa.definition(wa.variableName),color:"#00f"},{tag:wa.local(wa.variableName),color:"#30a"},{tag:[wa.typeName,wa.namespace],color:"#085"},{tag:wa.className,color:"#167"},{tag:[wa.special(wa.variableName),wa.macroName],color:"#256"},{tag:wa.definition(wa.propertyName),color:"#00c"},{tag:wa.comment,color:"#940"},{tag:wa.invalid,color:"#f00"}]),Lh=ms.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),Rh=1e4,Ih="()[]{}",Nh=_.define({combine(e){return Be(e,{afterCursor:!0,brackets:Ih,maxScanDistance:Rh,renderMatch:_h})}}),Ph=vi.mark({class:"cm-matchingBracket"}),zh=vi.mark({class:"cm-nonmatchingBracket"});function _h(e){let t=[],i=e.matched?Ph:zh;return t.push(i.range(e.start.from,e.start.to)),e.end&&t.push(i.range(e.end.from,e.end.to)),t}const Hh=U.define({create(){return vi.none},update(e,t){if(!t.docChanged&&!t.selection)return e;let i=[],n=t.state.facet(Nh);for(let r of t.state.selection.ranges){if(!r.empty)continue;let e=jh(t.state,r.head,-1,n)||r.head>0&&jh(t.state,r.head-1,1,n)||n.afterCursor&&(jh(t.state,r.head,1,n)||r.head<t.state.doc.length&&jh(t.state,r.head+1,-1,n));e&&(i=i.concat(n.renderMatch(e,t.state)))}return vi.set(i,!0)},provide:e=>ms.decorations.from(e)}),qh=[Hh,Lh];function Vh(e={}){return[Nh.of(e),qh]}function Wh(e,t,i){let n=e.prop(t<0?wl.openedBy:wl.closedBy);if(n)return n;if(1==e.name.length){let n=i.indexOf(e.name);if(n>-1&&n%2==(t<0?1:0))return[i[n+t]]}return null}function jh(e,t,i,n={}){let r=n.maxScanDistance||Rh,s=n.brackets||Ih,o=Ca(e),l=o.resolveInner(t,i);for(let a=l;a;a=a.parent){let n=Wh(a.type,i,s);if(n&&a.from<a.to)return $h(e,t,i,a,n,s)}return Uh(e,t,i,o,l.type,r,s)}function $h(e,t,i,n,r,s){let o=n.parent,l={from:n.from,to:n.to},a=0,h=null===o||void 0===o?void 0:o.cursor();if(h&&(i<0?h.childBefore(n.from):h.childAfter(n.to)))do{if(i<0?h.to<=n.from:h.from>=n.to){if(0==a&&r.indexOf(h.type.name)>-1&&h.from<h.to)return{start:l,end:{from:h.from,to:h.to},matched:!0};if(Wh(h.type,i,s))a++;else if(Wh(h.type,-i,s)&&(a--,0==a))return{start:l,end:h.from==h.to?void 0:{from:h.from,to:h.to},matched:!1}}}while(i<0?h.prevSibling():h.nextSibling());return{start:l,matched:!1}}function Uh(e,t,i,n,r,s,o){let l=i<0?e.sliceDoc(t-1,t):e.sliceDoc(t,t+1),a=o.indexOf(l);if(a<0||a%2==0!=i>0)return null;let h={from:i<0?t-1:t,to:i>0?t+1:t},c=e.doc.iterRange(t,i>0?e.doc.length:0),u=0;for(let d=0;!c.next().done&&d<=s;){let e=c.value;i<0&&(d+=e.length);let s=t+d*i;for(let t=i>0?0:e.length-1,l=i>0?e.length:-1;t!=l;t+=i){let l=o.indexOf(e[t]);if(!(l<0||n.resolve(s+t,1).type!=r))if(l%2==0==i>0)u++;else{if(1==u)return{start:h,end:{from:s+t,to:s+t+1},matched:l>>1==a>>1};u--}}i>0&&(d+=e.length)}return c.done?{start:h,matched:!1}:null}const Gh=Object.create(null),Kh=[yl.none],Jh=[],Xh=Object.create(null);for(let[xm,wm]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])Xh[xm]=Qh(Gh,wm);function Zh(e,t){Jh.indexOf(e)>-1||(Jh.push(e),console.warn(t))}function Qh(e,t){let i=null;for(let s of t.split(".")){let t=e[s]||wa[s];t?"function"==typeof t?i?i=t(i):Zh(s,`Modifier ${s} used at start of tag`):i?Zh(s,`Tag ${s} used as modifier`):i=t:Zh(s,"Unknown highlighting tag "+s)}if(!i)return 0;let n=t.replace(/ /g,"_"),r=yl.define({id:Kh.length,name:n,props:[Zl({[n]:i})]});return Kh.push(r),r.id}const Yh=e=>{let t=rc(e.state);return t.line?tc(e):!!t.block&&nc(e)};function ec(e,t){return({state:i,dispatch:n})=>{if(i.readOnly)return!1;let r=e(t,i);return!!r&&(n(i.update(r)),!0)}}const tc=ec(hc,0),ic=ec(ac,0),nc=ec((e,t)=>ac(e,t,lc(t)),0);function rc(e,t=e.selection.main.head){let i=e.languageDataAt("commentTokens",t);return i.length?i[0]:{}}const sc=50;function oc(e,{open:t,close:i},n,r){let s,o,l=e.sliceDoc(n-sc,n),a=e.sliceDoc(r,r+sc),h=/\s*$/.exec(l)[0].length,c=/^\s*/.exec(a)[0].length,u=l.length-h;if(l.slice(u-t.length,u)==t&&a.slice(c,c+i.length)==i)return{open:{pos:n-h,margin:h&&1},close:{pos:r+c,margin:c&&1}};r-n<=2*sc?s=o=e.sliceDoc(n,r):(s=e.sliceDoc(n,n+sc),o=e.sliceDoc(r-sc,r));let d=/^\s*/.exec(s)[0].length,f=/\s*$/.exec(o)[0].length,p=o.length-f-i.length;return s.slice(d,d+t.length)==t&&o.slice(p,p+i.length)==i?{open:{pos:n+d+t.length,margin:/\s/.test(s.charAt(d+t.length))?1:0},close:{pos:r-f-i.length,margin:/\s/.test(o.charAt(p-1))?1:0}}:null}function lc(e){let t=[];for(let i of e.selection.ranges){let n=e.doc.lineAt(i.from),r=i.to<=n.to?n:e.doc.lineAt(i.to),s=t.length-1;s>=0&&t[s].to>n.from?t[s].to=r.to:t.push({from:n.from,to:r.to})}return t}function ac(e,t,i=t.selection.ranges){let n=i.map(e=>rc(t,e.from).block);if(!n.every(e=>e))return null;let r=i.map((e,i)=>oc(t,n[i],e.from,e.to));if(2!=e&&!r.every(e=>e))return{changes:t.changes(i.map((e,t)=>r[t]?[]:[{from:e.from,insert:n[t].open+" "},{from:e.to,insert:" "+n[t].close}]))};if(1!=e&&r.some(e=>e)){let e=[];for(let t,i=0;i<r.length;i++)if(t=r[i]){let r=n[i],{open:s,close:o}=t;e.push({from:s.pos-r.open.length,to:s.pos+s.margin},{from:o.pos-o.margin,to:o.pos+r.close.length})}return{changes:e}}return null}function hc(e,t,i=t.selection.ranges){let n=[],r=-1;for(let{from:s,to:o}of i){let e=n.length,i=1e9;for(let l=s;l<=o;){let e=t.doc.lineAt(l);if(e.from>r&&(s==o||o>e.from)){r=e.from;let s=rc(t,l).line;if(!s)continue;let o=/^\s*/.exec(e.text)[0].length,a=o==e.length,h=e.text.slice(o,o+s.length)==s?o:-1;o<e.text.length&&o<i&&(i=o),n.push({line:e,comment:h,token:s,indent:o,empty:a,single:!1})}l=e.to+1}if(i<1e9)for(let t=e;t<n.length;t++)n[t].indent<n[t].line.text.length&&(n[t].indent=i);n.length==e+1&&(n[e].single=!0)}if(2!=e&&n.some(e=>e.comment<0&&(!e.empty||e.single))){let e=[];for(let{line:t,token:r,indent:s,empty:o,single:l}of n)!l&&o||e.push({from:t.from+s,insert:r+" "});let i=t.changes(e);return{changes:i,selection:t.selection.map(i,1)}}if(1!=e&&n.some(e=>e.comment>=0)){let e=[];for(let{line:t,comment:i,token:r}of n)if(i>=0){let n=t.from+i,s=n+r.length;" "==t.text[s-t.from]&&s++,e.push({from:n,to:s})}return{changes:e}}return null}const cc=ce.define(),uc=ce.define(),dc=_.define(),fc=_.define({combine(e){return Be(e,{minDepth:100,newGroupDelay:500},{minDepth:Math.max,newGroupDelay:Math.min})}});function pc(e){let t=0;return e.iterChangedRanges((e,i)=>t=i),t}const mc=U.define({create(){return Rc.empty},update(e,t){let i=t.state.facet(fc),n=t.annotation(cc);if(n){let r=t.docChanged?N.single(pc(t.changes)):void 0,s=kc.fromTransaction(t,r),o=n.side,l=0==o?e.undone:e.done;return l=s?Dc(l,l.length,i.minDepth,s):Bc(l,t.startState.selection),new Rc(0==o?n.rest:l,0==o?l:n.rest)}let r=t.annotation(uc);if("full"!=r&&"before"!=r||(e=e.isolate()),!1===t.annotation(pe.addToHistory))return t.changes.empty?e:e.addMapping(t.changes.desc);let s=kc.fromTransaction(t),o=t.annotation(pe.time),l=t.annotation(pe.userEvent);return s?e=e.addChanges(s,o,l,i.newGroupDelay,i.minDepth):t.selection&&(e=e.addSelection(t.startState.selection,o,l,i.newGroupDelay)),"full"!=r&&"after"!=r||(e=e.isolate()),e},toJSON(e){return{done:e.done.map(e=>e.toJSON()),undone:e.undone.map(e=>e.toJSON())}},fromJSON(e){return new Rc(e.done.map(kc.fromJSON),e.undone.map(kc.fromJSON))}});function gc(e={}){return[mc,fc.of(e),ms.domEventHandlers({beforeinput(e,t){let i="historyUndo"==e.inputType?xc:"historyRedo"==e.inputType?wc:null;return!!i&&(e.preventDefault(),i(t))}})]}function vc(e,t){return function({state:i,dispatch:n}){if(!t&&i.readOnly)return!1;let r=i.field(mc,!1);if(!r)return!1;let s=r.pop(e,i,t);return!!s&&(n(s),!0)}}const xc=vc(0,!1),wc=vc(1,!1),bc=vc(0,!0),yc=vc(1,!0);class kc{constructor(e,t,i,n,r){this.changes=e,this.effects=t,this.mapped=i,this.startSelection=n,this.selectionsAfter=r}setSelAfter(e){return new kc(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,i;return{changes:null===(e=this.changes)||void 0===e?void 0:e.toJSON(),mapped:null===(t=this.mapped)||void 0===t?void 0:t.toJSON(),startSelection:null===(i=this.startSelection)||void 0===i?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(e=>e.toJSON())}}static fromJSON(e){return new kc(e.changes&&M.fromJSON(e.changes),[],e.mapped&&E.fromJSON(e.mapped),e.startSelection&&N.fromJSON(e.startSelection),e.selectionsAfter.map(N.fromJSON))}static fromTransaction(e,t){let i=Ec;for(let n of e.startState.facet(dc)){let t=n(e);t.length&&(i=i.concat(t))}return!i.length&&e.changes.empty?null:new kc(e.changes.invert(e.startState.doc),i,void 0,t||e.startState.selection,Ec)}static selection(e){return new kc(void 0,Ec,void 0,void 0,e)}}function Dc(e,t,i,n){let r=t+1>i+20?t-i-1:0,s=e.slice(r,t);return s.push(n),s}function Cc(e,t){let i=[],n=!1;return e.iterChangedRanges((e,t)=>i.push(e,t)),t.iterChangedRanges((e,t,r,s)=>{for(let o=0;o<i.length;){let e=i[o++],t=i[o++];s>=e&&r<=t&&(n=!0)}}),n}function Sc(e,t){return e.ranges.length==t.ranges.length&&0===e.ranges.filter((e,i)=>e.empty!=t.ranges[i].empty).length}function Ac(e,t){return e.length?t.length?e.concat(t):e:t}const Ec=[],Mc=200;function Bc(e,t){if(e.length){let i=e[e.length-1],n=i.selectionsAfter.slice(Math.max(0,i.selectionsAfter.length-Mc));return n.length&&n[n.length-1].eq(t)?e:(n.push(t),Dc(e,e.length-1,1e9,i.setSelAfter(n)))}return[kc.selection([t])]}function Fc(e){let t=e[e.length-1],i=e.slice();return i[e.length-1]=t.setSelAfter(t.selectionsAfter.slice(0,t.selectionsAfter.length-1)),i}function Tc(e,t){if(!e.length)return e;let i=e.length,n=Ec;while(i){let r=Oc(e[i-1],t,n);if(r.changes&&!r.changes.empty||r.effects.length){let t=e.slice(0,i);return t[i-1]=r,t}t=r.mapped,i--,n=r.selectionsAfter}return n.length?[kc.selection(n)]:Ec}function Oc(e,t,i){let n=Ac(e.selectionsAfter.length?e.selectionsAfter.map(e=>e.map(t)):Ec,i);if(!e.changes)return kc.selection(n);let r=e.changes.map(t),s=t.mapDesc(e.changes,!0),o=e.mapped?e.mapped.composeDesc(s):s;return new kc(r,fe.mapEffects(e.effects,t),o,e.startSelection.map(s),n)}const Lc=/^(input\.type|delete)($|\.)/;class Rc{constructor(e,t,i=0,n){this.done=e,this.undone=t,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new Rc(this.done,this.undone):this}addChanges(e,t,i,n,r){let s=this.done,o=s[s.length-1];return s=o&&o.changes&&!o.changes.empty&&e.changes&&(!i||Lc.test(i))&&(!o.selectionsAfter.length&&t-this.prevTime<n&&Cc(o.changes,e.changes)||"input.type.compose"==i)?Dc(s,s.length-1,r,new kc(e.changes.compose(o.changes),Ac(e.effects,o.effects),o.mapped,o.startSelection,Ec)):Dc(s,s.length,r,e),new Rc(s,Ec,t,i)}addSelection(e,t,i,n){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Ec;return r.length>0&&t-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&Sc(r[r.length-1],e)?this:new Rc(Bc(this.done,e),this.undone,t,i)}addMapping(e){return new Rc(Tc(this.done,e),Tc(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,i){let n=0==e?this.done:this.undone;if(0==n.length)return null;let r=n[n.length-1];if(i&&r.selectionsAfter.length)return t.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:cc.of({side:e,rest:Fc(n)}),userEvent:0==e?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let i=1==n.length?Ec:n.slice(0,n.length-1);return r.mapped&&(i=Tc(i,r.mapped)),t.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:cc.of({side:e,rest:i}),filter:!1,userEvent:0==e?"undo":"redo",scrollIntoView:!0})}return null}}Rc.empty=new Rc(Ec,Ec);const Ic=[{key:"Mod-z",run:xc,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:wc,preventDefault:!0},{key:"Mod-u",run:bc,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:yc,preventDefault:!0}];function Nc(e,t){return N.create(e.ranges.map(t),e.mainIndex)}function Pc(e,t){return e.update({selection:t,scrollIntoView:!0,userEvent:"select"})}function zc({state:e,dispatch:t},i){let n=Nc(e.selection,i);return!n.eq(e.selection)&&(t(Pc(e,n)),!0)}function _c(e,t){return N.cursor(t?e.to:e.from)}function Hc(e,t){return zc(e,i=>i.empty?e.moveByChar(i,t):_c(i,t))}function qc(e){return e.textDirectionAt(e.state.selection.main.head)==Qi.LTR}const Vc=e=>Hc(e,!qc(e)),Wc=e=>Hc(e,qc(e));function jc(e,t){return zc(e,i=>i.empty?e.moveByGroup(i,t):_c(i,t))}const $c=e=>jc(e,!qc(e)),Uc=e=>jc(e,qc(e));function Gc(e,t,i){if(t.type.prop(i))return!0;let n=t.to-t.from;return n&&(n>2||/[^\s,.;:]/.test(e.sliceDoc(t.from,t.to)))||t.firstChild}function Kc(e,t,i){let n=Ca(e).resolveInner(t.head),r=i?wl.closedBy:wl.openedBy;for(let a=t.head;;){let t=i?n.childAfter(a):n.childBefore(a);if(!t)break;Gc(e,t,r)?n=t:a=i?t.to:t.from}let s,o,l=n.type.prop(r);return o=l&&(s=i?jh(e,n.from,1):jh(e,n.to,-1))&&s.matched?i?s.end.to:s.end.from:i?n.to:n.from,N.cursor(o,i?-1:1)}const Jc=e=>zc(e,t=>Kc(e.state,t,!qc(e))),Xc=e=>zc(e,t=>Kc(e.state,t,qc(e)));function Zc(e,t){return zc(e,i=>{if(!i.empty)return _c(i,t);let n=e.moveVertically(i,t);return n.head!=i.head?n:e.moveToLineBoundary(i,t)})}const Qc=e=>Zc(e,!1),Yc=e=>Zc(e,!0);function eu(e){return Math.max(e.defaultLineHeight,Math.min(e.dom.clientHeight,innerHeight)-5)}function tu(e,t){let{state:i}=e,n=Nc(i.selection,i=>i.empty?e.moveVertically(i,t,eu(e)):_c(i,t));if(n.eq(i.selection))return!1;let r,s=e.coordsAtPos(i.selection.main.head),o=e.scrollDOM.getBoundingClientRect();return s&&s.top>o.top&&s.bottom<o.bottom&&s.top-o.top<=e.scrollDOM.scrollHeight-e.scrollDOM.scrollTop-e.scrollDOM.clientHeight&&(r=ms.scrollIntoView(n.main.head,{y:"start",yMargin:s.top-o.top})),e.dispatch(Pc(i,n),{effects:r}),!0}const iu=e=>tu(e,!1),nu=e=>tu(e,!0);function ru(e,t,i){let n=e.lineBlockAt(t.head),r=e.moveToLineBoundary(t,i);if(r.head==t.head&&r.head!=(i?n.to:n.from)&&(r=e.moveToLineBoundary(t,i,!1)),!i&&r.head==n.from&&n.length){let i=/^\s*/.exec(e.state.sliceDoc(n.from,Math.min(n.from+100,n.to)))[0].length;i&&t.head!=n.from+i&&(r=N.cursor(n.from+i))}return r}const su=e=>zc(e,t=>ru(e,t,!0)),ou=e=>zc(e,t=>ru(e,t,!1)),lu=e=>zc(e,t=>N.cursor(e.lineBlockAt(t.head).from,1)),au=e=>zc(e,t=>N.cursor(e.lineBlockAt(t.head).to,-1));function hu(e,t,i){let n=!1,r=Nc(e.selection,t=>{let r=jh(e,t.head,-1)||jh(e,t.head,1)||t.head>0&&jh(e,t.head-1,1)||t.head<e.doc.length&&jh(e,t.head+1,-1);if(!r||!r.end)return t;n=!0;let s=r.start.from==t.head?r.end.to:r.end.from;return i?N.range(t.anchor,s):N.cursor(s)});return!!n&&(t(Pc(e,r)),!0)}const cu=({state:e,dispatch:t})=>hu(e,t,!1);function uu(e,t){let i=Nc(e.state.selection,e=>{let i=t(e);return N.range(e.anchor,i.head,i.goalColumn)});return!i.eq(e.state.selection)&&(e.dispatch(Pc(e.state,i)),!0)}function du(e,t){return uu(e,i=>e.moveByChar(i,t))}const fu=e=>du(e,!qc(e)),pu=e=>du(e,qc(e));function mu(e,t){return uu(e,i=>e.moveByGroup(i,t))}const gu=e=>mu(e,!qc(e)),vu=e=>mu(e,qc(e));const xu=e=>uu(e,t=>Kc(e.state,t,!qc(e))),wu=e=>uu(e,t=>Kc(e.state,t,qc(e)));function bu(e,t){return uu(e,i=>e.moveVertically(i,t))}const yu=e=>bu(e,!1),ku=e=>bu(e,!0);function Du(e,t){return uu(e,i=>e.moveVertically(i,t,eu(e)))}const Cu=e=>Du(e,!1),Su=e=>Du(e,!0),Au=e=>uu(e,t=>ru(e,t,!0)),Eu=e=>uu(e,t=>ru(e,t,!1)),Mu=e=>uu(e,t=>N.cursor(e.lineBlockAt(t.head).from)),Bu=e=>uu(e,t=>N.cursor(e.lineBlockAt(t.head).to)),Fu=({state:e,dispatch:t})=>(t(Pc(e,{anchor:0})),!0),Tu=({state:e,dispatch:t})=>(t(Pc(e,{anchor:e.doc.length})),!0),Ou=({state:e,dispatch:t})=>(t(Pc(e,{anchor:e.selection.main.anchor,head:0})),!0),Lu=({state:e,dispatch:t})=>(t(Pc(e,{anchor:e.selection.main.anchor,head:e.doc.length})),!0),Ru=({state:e,dispatch:t})=>(t(e.update({selection:{anchor:0,head:e.doc.length},userEvent:"select"})),!0),Iu=({state:e,dispatch:t})=>{let i=Xu(e).map(({from:t,to:i})=>N.range(t,Math.min(i+1,e.doc.length)));return t(e.update({selection:N.create(i),userEvent:"select"})),!0},Nu=({state:e,dispatch:t})=>{let i=Nc(e.selection,t=>{var i;let n=Ca(e).resolveInner(t.head,1);while(!(n.from<t.from&&n.to>=t.to||n.to>t.to&&n.from<=t.from)&&(null===(i=n.parent)||void 0===i?void 0:i.parent))n=n.parent;return N.range(n.to,n.from)});return t(Pc(e,i)),!0},Pu=({state:e,dispatch:t})=>{let i=e.selection,n=null;return i.ranges.length>1?n=N.create([i.main]):i.main.empty||(n=N.create([N.cursor(i.main.head)])),!!n&&(t(Pc(e,n)),!0)};function zu({state:e,dispatch:t},i){if(e.readOnly)return!1;let n="delete.selection",r=e.changeByRange(e=>{let{from:t,to:r}=e;if(t==r){let e=i(t);e<t?n="delete.backward":e>t&&(n="delete.forward"),t=Math.min(t,e),r=Math.max(r,e)}return t==r?{range:e}:{changes:{from:t,to:r},range:N.cursor(t)}});return!r.changes.empty&&(t(e.update(r,{scrollIntoView:!0,userEvent:n})),!0)}function _u(e,t,i){if(e instanceof ms)for(let n of e.state.facet(ms.atomicRanges).map(t=>t(e)))n.between(t,t,(e,n)=>{e<t&&n>t&&(t=i?n:e)});return t}const Hu=(e,t)=>zu(e,i=>{let n,r,{state:s}=e,o=s.doc.lineAt(i);if(!t&&i>o.from&&i<o.from+200&&!/[^ \t]/.test(n=o.text.slice(0,i-o.from))){if("\t"==n[n.length-1])return i-1;let e=Ge(n,s.tabSize),t=e%Na(s)||Na(s);for(let r=0;r<t&&" "==n[n.length-1-r];r++)i--;r=i}else r=v(o.text,i-o.from,t,t)+o.from,r==i&&o.number!=(t?s.doc.lines:1)&&(r+=t?1:-1);return _u(e,r,t)}),qu=e=>Hu(e,!1),Vu=e=>Hu(e,!0),Wu=(e,t)=>zu(e,i=>{let n=i,{state:r}=e,s=r.doc.lineAt(n),o=r.charCategorizer(n);for(let e=null;;){if(n==(t?s.to:s.from)){n==i&&s.number!=(t?r.doc.lines:1)&&(n+=t?1:-1);break}let l=v(s.text,n-s.from,t)+s.from,a=s.text.slice(Math.min(n,l)-s.from,Math.max(n,l)-s.from),h=o(a);if(null!=e&&h!=e)break;" "==a&&n==i||(e=h),n=l}return _u(e,n,t)}),ju=e=>Wu(e,!1),$u=e=>Wu(e,!0),Uu=e=>zu(e,t=>{let i=e.lineBlockAt(t).to;return _u(e,t<i?i:Math.min(e.state.doc.length,t+1),!0)}),Gu=e=>zu(e,t=>{let i=e.lineBlockAt(t).from;return _u(e,t>i?i:Math.max(0,t-1),!1)}),Ku=({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=e.changeByRange(e=>({changes:{from:e.from,to:e.to,insert:n.of(["",""])},range:N.cursor(e.from)}));return t(e.update(i,{scrollIntoView:!0,userEvent:"input"})),!0},Ju=({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=e.changeByRange(t=>{if(!t.empty||0==t.from||t.from==e.doc.length)return{range:t};let i=t.from,n=e.doc.lineAt(i),r=i==n.from?i-1:v(n.text,i-n.from,!1)+n.from,s=i==n.to?i+1:v(n.text,i-n.from,!0)+n.from;return{changes:{from:r,to:s,insert:e.doc.slice(i,s).append(e.doc.slice(r,i))},range:N.cursor(s)}});return!i.changes.empty&&(t(e.update(i,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function Xu(e){let t=[],i=-1;for(let n of e.selection.ranges){let r=e.doc.lineAt(n.from),s=e.doc.lineAt(n.to);if(n.empty||n.to!=s.from||(s=e.doc.lineAt(n.to-1)),i>=r.number){let e=t[t.length-1];e.to=s.to,e.ranges.push(n)}else t.push({from:r.from,to:s.to,ranges:[n]});i=s.number+1}return t}function Zu(e,t,i){if(e.readOnly)return!1;let n=[],r=[];for(let s of Xu(e)){if(i?s.to==e.doc.length:0==s.from)continue;let t=e.doc.lineAt(i?s.to+1:s.from-1),o=t.length+1;if(i){n.push({from:s.to,to:t.to},{from:s.from,insert:t.text+e.lineBreak});for(let t of s.ranges)r.push(N.range(Math.min(e.doc.length,t.anchor+o),Math.min(e.doc.length,t.head+o)))}else{n.push({from:t.from,to:s.from},{from:s.to,insert:e.lineBreak+t.text});for(let e of s.ranges)r.push(N.range(e.anchor-o,e.head-o))}}return!!n.length&&(t(e.update({changes:n,scrollIntoView:!0,selection:N.create(r,e.selection.mainIndex),userEvent:"move.line"})),!0)}const Qu=({state:e,dispatch:t})=>Zu(e,t,!1),Yu=({state:e,dispatch:t})=>Zu(e,t,!0);function ed(e,t,i){if(e.readOnly)return!1;let n=[];for(let r of Xu(e))i?n.push({from:r.from,insert:e.doc.slice(r.from,r.to)+e.lineBreak}):n.push({from:r.to,insert:e.lineBreak+e.doc.slice(r.from,r.to)});return t(e.update({changes:n,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const td=({state:e,dispatch:t})=>ed(e,t,!1),id=({state:e,dispatch:t})=>ed(e,t,!0),nd=e=>{if(e.state.readOnly)return!1;let{state:t}=e,i=t.changes(Xu(t).map(({from:e,to:i})=>(e>0?e--:i<t.doc.length&&i++,{from:e,to:i}))),n=Nc(t.selection,t=>e.moveVertically(t,!0)).map(i);return e.dispatch({changes:i,selection:n,scrollIntoView:!0,userEvent:"delete.line"}),!0};function rd(e,t){if(/\(\)|\[\]|\{\}/.test(e.sliceDoc(t-1,t+1)))return{from:t,to:t};let i,n=Ca(e).resolveInner(t),r=n.childBefore(t),s=n.childAfter(t);return r&&s&&r.to<=t&&s.from>=t&&(i=r.type.prop(wl.closedBy))&&i.indexOf(s.name)>-1&&e.doc.lineAt(r.to).from==e.doc.lineAt(s.from).from?{from:r.to,to:s.from}:null}const sd=ld(!1),od=ld(!0);function ld(e){return({state:t,dispatch:i})=>{if(t.readOnly)return!1;let r=t.changeByRange(i=>{let{from:r,to:s}=i,o=t.doc.lineAt(r),l=!e&&r==s&&rd(t,r);e&&(r=s=(s<=o.to?o:t.doc.lineAt(s)).to);let a=new _a(t,{simulateBreak:r,simulateDoubleBreak:!!l}),h=za(a,r);null==h&&(h=/^\s*/.exec(t.doc.lineAt(r).text)[0].length);while(s<o.to&&/\s/.test(o.text[s-o.from]))s++;l?({from:r,to:s}=l):r>o.from&&r<o.from+100&&!/\S/.test(o.text.slice(0,r))&&(r=o.from);let c=["",Pa(t,h)];return l&&c.push(Pa(t,a.lineIndent(o.from,-1))),{changes:{from:r,to:s,insert:n.of(c)},range:N.cursor(r+1+c[1].length)}});return i(t.update(r,{scrollIntoView:!0,userEvent:"input"})),!0}}function ad(e,t){let i=-1;return e.changeByRange(n=>{let r=[];for(let o=n.from;o<=n.to;){let s=e.doc.lineAt(o);s.number>i&&(n.empty||n.to>s.from)&&(t(s,r,n),i=s.number),o=s.to+1}let s=e.changes(r);return{changes:r,range:N.range(s.mapPos(n.anchor,1),s.mapPos(n.head,1))}})}const hd=({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=Object.create(null),n=new _a(e,{overrideIndentation:e=>{let t=i[e];return null==t?-1:t}}),r=ad(e,(t,r,s)=>{let o=za(n,t.from);if(null==o)return;/\S/.test(t.text)||(o=0);let l=/^\s*/.exec(t.text)[0],a=Pa(e,o);(l!=a||s.from<t.from+l.length)&&(i[t.from]=o,r.push({from:t.from,to:t.from+l.length,insert:a}))});return r.changes.empty||t(e.update(r,{userEvent:"indent"})),!0},cd=({state:e,dispatch:t})=>!e.readOnly&&(t(e.update(ad(e,(t,i)=>{i.push({from:t.from,insert:e.facet(Ia)})}),{userEvent:"input.indent"})),!0),ud=({state:e,dispatch:t})=>!e.readOnly&&(t(e.update(ad(e,(t,i)=>{let n=/^\s*/.exec(t.text)[0];if(!n)return;let r=Ge(n,e.tabSize),s=0,o=Pa(e,Math.max(0,r-Na(e)));while(s<n.length&&s<o.length&&n.charCodeAt(s)==o.charCodeAt(s))s++;i.push({from:t.from+s,to:t.from+n.length,insert:o.slice(s)})}),{userEvent:"delete.dedent"})),!0),dd=[{key:"Ctrl-b",run:Vc,shift:fu,preventDefault:!0},{key:"Ctrl-f",run:Wc,shift:pu},{key:"Ctrl-p",run:Qc,shift:yu},{key:"Ctrl-n",run:Yc,shift:ku},{key:"Ctrl-a",run:lu,shift:Mu},{key:"Ctrl-e",run:au,shift:Bu},{key:"Ctrl-d",run:Vu},{key:"Ctrl-h",run:qu},{key:"Ctrl-k",run:Uu},{key:"Ctrl-Alt-h",run:ju},{key:"Ctrl-o",run:Ku},{key:"Ctrl-t",run:Ju},{key:"Ctrl-v",run:nu}],fd=[{key:"ArrowLeft",run:Vc,shift:fu,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:$c,shift:gu},{mac:"Cmd-ArrowLeft",run:ou,shift:Eu},{key:"ArrowRight",run:Wc,shift:pu,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:Uc,shift:vu},{mac:"Cmd-ArrowRight",run:su,shift:Au},{key:"ArrowUp",run:Qc,shift:yu,preventDefault:!0},{mac:"Cmd-ArrowUp",run:Fu,shift:Ou},{mac:"Ctrl-ArrowUp",run:iu,shift:Cu},{key:"ArrowDown",run:Yc,shift:ku,preventDefault:!0},{mac:"Cmd-ArrowDown",run:Tu,shift:Lu},{mac:"Ctrl-ArrowDown",run:nu,shift:Su},{key:"PageUp",run:iu,shift:Cu},{key:"PageDown",run:nu,shift:Su},{key:"Home",run:ou,shift:Eu,preventDefault:!0},{key:"Mod-Home",run:Fu,shift:Ou},{key:"End",run:su,shift:Au,preventDefault:!0},{key:"Mod-End",run:Tu,shift:Lu},{key:"Enter",run:sd},{key:"Mod-a",run:Ru},{key:"Backspace",run:qu,shift:qu},{key:"Delete",run:Vu},{key:"Mod-Backspace",mac:"Alt-Backspace",run:ju},{key:"Mod-Delete",mac:"Alt-Delete",run:$u},{mac:"Mod-Backspace",run:Gu},{mac:"Mod-Delete",run:Uu}].concat(dd.map(e=>({mac:e.key,run:e.run,shift:e.shift}))),pd=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:Jc,shift:xu},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:Xc,shift:wu},{key:"Alt-ArrowUp",run:Qu},{key:"Shift-Alt-ArrowUp",run:td},{key:"Alt-ArrowDown",run:Yu},{key:"Shift-Alt-ArrowDown",run:id},{key:"Escape",run:Pu},{key:"Mod-Enter",run:od},{key:"Alt-l",mac:"Ctrl-l",run:Iu},{key:"Mod-i",run:Nu,preventDefault:!0},{key:"Mod-[",run:ud},{key:"Mod-]",run:cd},{key:"Mod-Alt-\\",run:hd},{key:"Shift-Mod-k",run:nd},{key:"Shift-Mod-\\",run:cu},{key:"Mod-/",run:Yh},{key:"Alt-A",run:ic}].concat(fd);function md(){var e=arguments[0];"string"==typeof e&&(e=document.createElement(e));var t=1,i=arguments[1];if(i&&"object"==typeof i&&null==i.nodeType&&!Array.isArray(i)){for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)){var r=i[n];"string"==typeof r?e.setAttribute(n,r):null!=r&&(e[n]=r)}t++}for(;t<arguments.length;t++)gd(e,arguments[t]);return e}function gd(e,t){if("string"==typeof t)e.appendChild(document.createTextNode(t));else if(null==t);else if(null!=t.nodeType)e.appendChild(t);else{if(!Array.isArray(t))throw new RangeError("Unsupported child node: "+t);for(var i=0;i<t.length;i++)gd(e,t[i])}}const vd="function"==typeof String.prototype.normalize?e=>e.normalize("NFKD"):e=>e;class xd{constructor(e,t,i=0,n=e.length,r){this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=e.iterRange(i,n),this.bufferStart=i,this.normalize=r?e=>r(vd(e)):vd,this.query=this.normalize(t)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return k(this.buffer,this.bufferPos)}next(){while(this.matches.length)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let e=this.peek();if(e<0)return this.done=!0,this;let t=D(e),i=this.bufferStart+this.bufferPos;this.bufferPos+=C(e);let n=this.normalize(t);for(let r=0,s=i;;r++){let e=n.charCodeAt(r),o=this.match(e,s);if(o)return this.value=o,this;if(r==n.length-1)break;s==i&&r<t.length&&t.charCodeAt(r)==e&&s++}}}match(e,t){let i=null;for(let n=0;n<this.matches.length;n+=2){let r=this.matches[n],s=!1;this.query.charCodeAt(r)==e&&(r==this.query.length-1?i={from:this.matches[n+1],to:t+1}:(this.matches[n]++,s=!0)),s||(this.matches.splice(n,2),n-=2)}return this.query.charCodeAt(0)==e&&(1==this.query.length?i={from:t,to:t+1}:this.matches.push(1,t)),i}}"undefined"!=typeof Symbol&&(xd.prototype[Symbol.iterator]=function(){return this});const wd={from:-1,to:-1,match:/.*/.exec("")},bd="gm"+(null==/x/.unicode?"":"u");class yd{constructor(e,t,i,n=0,r=e.length){if(this.to=r,this.curLine="",this.done=!1,this.value=wd,/\\[sWDnr]|\n|\r|\[\^/.test(t))return new Cd(e,t,i,n,r);this.re=new RegExp(t,bd+((null===i||void 0===i?void 0:i.ignoreCase)?"i":"")),this.iter=e.iter();let s=e.lineAt(n);this.curLineStart=s.from,this.matchPos=n,this.getLine(this.curLineStart)}getLine(e){this.iter.next(e),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let e=this.matchPos-this.curLineStart;;){this.re.lastIndex=e;let t=this.matchPos<=this.to&&this.re.exec(this.curLine);if(t){let i=this.curLineStart+t.index,n=i+t[0].length;if(this.matchPos=n+(i==n?1:0),i==this.curLine.length&&this.nextLine(),i<n||i>this.value.to)return this.value={from:i,to:n,match:t},this;e=this.matchPos-this.curLineStart}else{if(!(this.curLineStart+this.curLine.length<this.to))return this.done=!0,this;this.nextLine(),e=0}}}}const kd=new WeakMap;class Dd{constructor(e,t){this.from=e,this.text=t}get to(){return this.from+this.text.length}static get(e,t,i){let n=kd.get(e);if(!n||n.from>=i||n.to<=t){let n=new Dd(t,e.sliceString(t,i));return kd.set(e,n),n}if(n.from==t&&n.to==i)return n;let{text:r,from:s}=n;return s>t&&(r=e.sliceString(t,s)+r,s=t),n.to<i&&(r+=e.sliceString(n.to,i)),kd.set(e,new Dd(s,r)),new Dd(t,r.slice(t-s,i-s))}}class Cd{constructor(e,t,i,n,r){this.text=e,this.to=r,this.done=!1,this.value=wd,this.matchPos=n,this.re=new RegExp(t,bd+((null===i||void 0===i?void 0:i.ignoreCase)?"i":"")),this.flat=Dd.get(e,n,this.chunkEnd(n+5e3))}chunkEnd(e){return e>=this.to?this.to:this.text.lineAt(e).to}next(){for(;;){let e=this.re.lastIndex=this.matchPos-this.flat.from,t=this.re.exec(this.flat.text);if(t&&!t[0]&&t.index==e&&(this.re.lastIndex=e+1,t=this.re.exec(this.flat.text)),t&&this.flat.to<this.to&&t.index+t[0].length>this.flat.text.length-10&&(t=null),t){let e=this.flat.from+t.index,i=e+t[0].length;return this.value={from:e,to:i,match:t},this.matchPos=i+(e==i?1:0),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=Dd.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+2*this.flat.text.length))}}}function Sd(e){try{return new RegExp(e,bd),!0}catch(t){return!1}}function Ad(e){let t=md("input",{class:"cm-textfield",name:"line"}),i=md("form",{class:"cm-gotoLine",onkeydown:t=>{27==t.keyCode?(t.preventDefault(),e.dispatch({effects:Ed.of(!1)}),e.focus()):13==t.keyCode&&(t.preventDefault(),n())},onsubmit:e=>{e.preventDefault(),n()}},md("label",e.state.phrase("Go to line"),": ",t)," ",md("button",{class:"cm-button",type:"submit"},e.state.phrase("go")));function n(){let i=/^([+-])?(\d+)?(:\d+)?(%)?$/.exec(t.value);if(!i)return;let{state:n}=e,r=n.doc.lineAt(n.selection.main.head),[,s,o,l,a]=i,h=l?+l.slice(1):0,c=o?+o:r.number;if(o&&a){let e=c/100;s&&(e=e*("-"==s?-1:1)+r.number/n.doc.lines),c=Math.round(n.doc.lines*e)}else o&&s&&(c=c*("-"==s?-1:1)+r.number);let u=n.doc.line(Math.max(1,Math.min(n.doc.lines,c)));e.dispatch({effects:Ed.of(!1),selection:N.cursor(u.from+Math.max(0,Math.min(h,u.length))),scrollIntoView:!0}),e.focus()}return{dom:i}}"undefined"!=typeof Symbol&&(yd.prototype[Symbol.iterator]=Cd.prototype[Symbol.iterator]=function(){return this});const Ed=fe.define(),Md=U.define({create(){return!0},update(e,t){for(let i of t.effects)i.is(Ed)&&(e=i.value);return e},provide:e=>$o.from(e,e=>e?Ad:null)}),Bd=e=>{let t=qo(e,Ad);if(!t){let i=[Ed.of(!0)];null==e.state.field(Md,!1)&&i.push(fe.appendConfig.of([Md,Fd])),e.dispatch({effects:i}),t=qo(e,Ad)}return t&&t.dom.querySelector("input").focus(),!0},Fd=ms.baseTheme({".cm-panel.cm-gotoLine":{padding:"2px 6px 4px","& label":{fontSize:"80%"}}}),Td={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},Od=_.define({combine(e){return Be(e,Td,{highlightWordAroundCursor:(e,t)=>e||t,minSelectionLength:Math.min,maxMatches:Math.min})}});function Ld(e){let t=[_d,zd];return e&&t.push(Od.of(e)),t}const Rd=vi.mark({class:"cm-selectionMatch"}),Id=vi.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function Nd(e,t,i,n){return(0==i||e(t.sliceDoc(i-1,i))!=De.Word)&&(n==t.doc.length||e(t.sliceDoc(n,n+1))!=De.Word)}function Pd(e,t,i,n){return e(t.sliceDoc(i,i+1))==De.Word&&e(t.sliceDoc(n-1,n))==De.Word}const zd=Vi.fromClass(class{constructor(e){this.decorations=this.getDeco(e)}update(e){(e.selectionSet||e.docChanged||e.viewportChanged)&&(this.decorations=this.getDeco(e.view))}getDeco(e){let t=e.state.facet(Od),{state:i}=e,n=i.selection;if(n.ranges.length>1)return vi.none;let r,s=n.main,o=null;if(s.empty){if(!t.highlightWordAroundCursor)return vi.none;let e=i.wordAt(s.head);if(!e)return vi.none;o=i.charCategorizer(s.head),r=i.sliceDoc(e.from,e.to)}else{let e=s.to-s.from;if(e<t.minSelectionLength||e>200)return vi.none;if(t.wholeWords){if(r=i.sliceDoc(s.from,s.to),o=i.charCategorizer(s.head),!Nd(o,i,s.from,s.to)||!Pd(o,i,s.from,s.to))return vi.none}else if(r=i.sliceDoc(s.from,s.to).trim(),!r)return vi.none}let l=[];for(let a of e.visibleRanges){let e=new xd(i.doc,r,a.from,a.to);while(!e.next().done){let{from:n,to:r}=e.value;if((!o||Nd(o,i,n,r))&&(s.empty&&n<=s.from&&r>=s.to?l.push(Id.range(n,r)):(n>=s.to||r<=s.from)&&l.push(Rd.range(n,r)),l.length>t.maxMatches))return vi.none}}return vi.set(l)}},{decorations:e=>e.decorations}),_d=ms.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}}),Hd=({state:e,dispatch:t})=>{let{selection:i}=e,n=N.create(i.ranges.map(t=>e.wordAt(t.head)||N.cursor(t.head)),i.mainIndex);return!n.eq(i)&&(t(e.update({selection:n})),!0)};function qd(e,t){let{main:i,ranges:n}=e.selection,r=e.wordAt(i.head),s=r&&r.from==i.from&&r.to==i.to;for(let o=!1,l=new xd(e.doc,t,n[n.length-1].to);;){if(l.next(),!l.done){if(o&&n.some(e=>e.from==l.value.from))continue;if(s){let t=e.wordAt(l.value.from);if(!t||t.from!=l.value.from||t.to!=l.value.to)continue}return l.value}if(o)return null;l=new xd(e.doc,t,0,Math.max(0,n[n.length-1].from-1)),o=!0}}const Vd=({state:e,dispatch:t})=>{let{ranges:i}=e.selection;if(i.some(e=>e.from===e.to))return Hd({state:e,dispatch:t});let n=e.sliceDoc(i[0].from,i[0].to);if(e.selection.ranges.some(t=>e.sliceDoc(t.from,t.to)!=n))return!1;let r=qd(e,n);return!!r&&(t(e.update({selection:e.selection.addRange(N.range(r.from,r.to),!1),effects:ms.scrollIntoView(r.to)})),!0)},Wd=_.define({combine(e){var t;return{top:e.reduce((e,t)=>null!==e&&void 0!==e?e:t.top,void 0)||!1,caseSensitive:e.reduce((e,t)=>null!==e&&void 0!==e?e:t.caseSensitive,void 0)||!1,createPanel:(null===(t=e.find(e=>e.createPanel))||void 0===t?void 0:t.createPanel)||(e=>new gf(e))}}});class jd{constructor(e){this.search=e.search,this.caseSensitive=!!e.caseSensitive,this.regexp=!!e.regexp,this.replace=e.replace||"",this.valid=!!this.search&&(!this.regexp||Sd(this.search)),this.unquoted=e.literal?this.search:this.search.replace(/\\([nrt\\])/g,(e,t)=>"n"==t?"\n":"r"==t?"\r":"t"==t?"\t":"\\")}eq(e){return this.search==e.search&&this.replace==e.replace&&this.caseSensitive==e.caseSensitive&&this.regexp==e.regexp}create(){return this.regexp?new Jd(this):new Gd(this)}getCursor(e,t=0,i=e.length){return this.regexp?Kd(this,e,t,i):Ud(this,e,t,i)}}class $d{constructor(e){this.spec=e}}function Ud(e,t,i,n){return new xd(t,e.unquoted,i,n,e.caseSensitive?void 0:e=>e.toLowerCase())}class Gd extends $d{constructor(e){super(e)}nextMatch(e,t,i){let n=Ud(this.spec,e,i,e.length).nextOverlapping();return n.done&&(n=Ud(this.spec,e,0,t).nextOverlapping()),n.done?null:n.value}prevMatchInRange(e,t,i){for(let n=i;;){let i=Math.max(t,n-1e4-this.spec.unquoted.length),r=Ud(this.spec,e,i,n),s=null;while(!r.nextOverlapping().done)s=r.value;if(s)return s;if(i==t)return null;n-=1e4}}prevMatch(e,t,i){return this.prevMatchInRange(e,0,t)||this.prevMatchInRange(e,i,e.length)}getReplacement(e){return this.spec.replace}matchAll(e,t){let i=Ud(this.spec,e,0,e.length),n=[];while(!i.next().done){if(n.length>=t)return null;n.push(i.value)}return n}highlight(e,t,i,n){let r=Ud(this.spec,e,Math.max(0,t-this.spec.unquoted.length),Math.min(i+this.spec.unquoted.length,e.length));while(!r.next().done)n(r.value.from,r.value.to)}}function Kd(e,t,i,n){return new yd(t,e.search,e.caseSensitive?void 0:{ignoreCase:!0},i,n)}class Jd extends $d{nextMatch(e,t,i){let n=Kd(this.spec,e,i,e.length).next();return n.done&&(n=Kd(this.spec,e,0,t).next()),n.done?null:n.value}prevMatchInRange(e,t,i){for(let n=1;;n++){let r=Math.max(t,i-1e4*n),s=Kd(this.spec,e,r,i),o=null;while(!s.next().done)o=s.value;if(o&&(r==t||o.from>r+10))return o;if(r==t)return null}}prevMatch(e,t,i){return this.prevMatchInRange(e,0,t)||this.prevMatchInRange(e,i,e.length)}getReplacement(e){return this.spec.replace.replace(/\$([$&\d+])/g,(t,i)=>"$"==i?"$":"&"==i?e.match[0]:"0"!=i&&+i<e.match.length?e.match[i]:t)}matchAll(e,t){let i=Kd(this.spec,e,0,e.length),n=[];while(!i.next().done){if(n.length>=t)return null;n.push(i.value)}return n}highlight(e,t,i,n){let r=Kd(this.spec,e,Math.max(0,t-250),Math.min(i+250,e.length));while(!r.next().done)n(r.value.from,r.value.to)}}const Xd=fe.define(),Zd=fe.define(),Qd=U.define({create(e){return new Yd(df(e).create(),null)},update(e,t){for(let i of t.effects)i.is(Xd)?e=new Yd(i.value.create(),e.panel):i.is(Zd)&&(e=new Yd(e.query,i.value?uf:null));return e},provide:e=>$o.from(e,e=>e.panel)});class Yd{constructor(e,t){this.query=e,this.panel=t}}const ef=vi.mark({class:"cm-searchMatch"}),tf=vi.mark({class:"cm-searchMatch cm-searchMatch-selected"}),nf=Vi.fromClass(class{constructor(e){this.view=e,this.decorations=this.highlight(e.state.field(Qd))}update(e){let t=e.state.field(Qd);(t!=e.startState.field(Qd)||e.docChanged||e.selectionSet||e.viewportChanged)&&(this.decorations=this.highlight(t))}highlight({query:e,panel:t}){if(!t||!e.spec.valid)return vi.none;let{view:i}=this,n=new Ne;for(let r=0,s=i.visibleRanges,o=s.length;r<o;r++){let{from:t,to:l}=s[r];while(r<o-1&&l>s[r+1].from-500)l=s[++r].to;e.highlight(i.state.doc,t,l,(e,t)=>{let r=i.state.selection.ranges.some(i=>i.from==e&&i.to==t);n.add(e,t,r?tf:ef)})}return n.finish()}},{decorations:e=>e.decorations});function rf(e){return t=>{let i=t.state.field(Qd,!1);return i&&i.query.spec.valid?e(t,i):ff(t)}}const sf=rf((e,{query:t})=>{let{from:i,to:n}=e.state.selection.main,r=t.nextMatch(e.state.doc,i,n);return!(!r||r.from==i&&r.to==n)&&(e.dispatch({selection:{anchor:r.from,head:r.to},scrollIntoView:!0,effects:bf(e,r),userEvent:"select.search"}),!0)}),of=rf((e,{query:t})=>{let{state:i}=e,{from:n,to:r}=i.selection.main,s=t.prevMatch(i.doc,n,r);return!!s&&(e.dispatch({selection:{anchor:s.from,head:s.to},scrollIntoView:!0,effects:bf(e,s),userEvent:"select.search"}),!0)}),lf=rf((e,{query:t})=>{let i=t.matchAll(e.state.doc,1e3);return!(!i||!i.length)&&(e.dispatch({selection:N.create(i.map(e=>N.range(e.from,e.to))),userEvent:"select.search.matches"}),!0)}),af=({state:e,dispatch:t})=>{let i=e.selection;if(i.ranges.length>1||i.main.empty)return!1;let{from:n,to:r}=i.main,s=[],o=0;for(let l=new xd(e.doc,e.sliceDoc(n,r));!l.next().done;){if(s.length>1e3)return!1;l.value.from==n&&(o=s.length),s.push(N.range(l.value.from,l.value.to))}return t(e.update({selection:N.create(s,o),userEvent:"select.search.matches"})),!0},hf=rf((e,{query:t})=>{let{state:i}=e,{from:n,to:r}=i.selection.main;if(i.readOnly)return!1;let s=t.nextMatch(i.doc,n,n);if(!s)return!1;let o,l,a=[],h=[];if(s.from==n&&s.to==r&&(l=i.toText(t.getReplacement(s)),a.push({from:s.from,to:s.to,insert:l}),s=t.nextMatch(i.doc,s.from,s.to),h.push(ms.announce.of(i.phrase("replaced match on line $",i.doc.lineAt(n).number)+"."))),s){let t=0==a.length||a[0].from>=s.to?0:s.to-s.from-l.length;o={anchor:s.from-t,head:s.to-t},h.push(bf(e,s))}return e.dispatch({changes:a,selection:o,scrollIntoView:!!o,effects:h,userEvent:"input.replace"}),!0}),cf=rf((e,{query:t})=>{if(e.state.readOnly)return!1;let i=t.matchAll(e.state.doc,1e9).map(e=>{let{from:i,to:n}=e;return{from:i,to:n,insert:t.getReplacement(e)}});if(!i.length)return!1;let n=e.state.phrase("replaced $ matches",i.length)+".";return e.dispatch({changes:i,effects:ms.announce.of(n),userEvent:"input.replace.all"}),!0});function uf(e){return e.state.facet(Wd).createPanel(e)}function df(e,t){var i;let n=e.selection.main,r=n.empty||n.to>n.from+100?"":e.sliceDoc(n.from,n.to),s=null!==(i=null===t||void 0===t?void 0:t.caseSensitive)&&void 0!==i?i:e.facet(Wd).caseSensitive;return t&&!r?t:new jd({search:r.replace(/\n/g,"\\n"),caseSensitive:s})}const ff=e=>{let t=e.state.field(Qd,!1);if(t&&t.panel){let i=qo(e,uf);if(!i)return!1;let n=i.dom.querySelector("[main-field]");if(n&&n!=e.root.activeElement){let i=df(e.state,t.query.spec);i.valid&&e.dispatch({effects:Xd.of(i)}),n.focus(),n.select()}}else e.dispatch({effects:[Zd.of(!0),t?Xd.of(df(e.state,t.query.spec)):fe.appendConfig.of(kf)]});return!0},pf=e=>{let t=e.state.field(Qd,!1);if(!t||!t.panel)return!1;let i=qo(e,uf);return i&&i.dom.contains(e.root.activeElement)&&e.focus(),e.dispatch({effects:Zd.of(!1)}),!0},mf=[{key:"Mod-f",run:ff,scope:"editor search-panel"},{key:"F3",run:sf,shift:of,scope:"editor search-panel",preventDefault:!0},{key:"Mod-g",run:sf,shift:of,scope:"editor search-panel",preventDefault:!0},{key:"Escape",run:pf,scope:"editor search-panel"},{key:"Mod-Shift-l",run:af},{key:"Alt-g",run:Bd},{key:"Mod-d",run:Vd,preventDefault:!0}];class gf{constructor(e){this.view=e;let t=this.query=e.state.field(Qd).query.spec;function i(e,t,i){return md("button",{class:"cm-button",name:e,onclick:t,type:"button"},i)}this.commit=this.commit.bind(this),this.searchField=md("input",{value:t.search,placeholder:vf(e,"Find"),"aria-label":vf(e,"Find"),class:"cm-textfield",name:"search","main-field":"true",onchange:this.commit,onkeyup:this.commit}),this.replaceField=md("input",{value:t.replace,placeholder:vf(e,"Replace"),"aria-label":vf(e,"Replace"),class:"cm-textfield",name:"replace",onchange:this.commit,onkeyup:this.commit}),this.caseField=md("input",{type:"checkbox",name:"case",checked:t.caseSensitive,onchange:this.commit}),this.reField=md("input",{type:"checkbox",name:"re",checked:t.regexp,onchange:this.commit}),this.dom=md("div",{onkeydown:e=>this.keydown(e),class:"cm-search"},[this.searchField,i("next",()=>sf(e),[vf(e,"next")]),i("prev",()=>of(e),[vf(e,"previous")]),i("select",()=>lf(e),[vf(e,"all")]),md("label",null,[this.caseField,vf(e,"match case")]),md("label",null,[this.reField,vf(e,"regexp")]),...e.state.readOnly?[]:[md("br"),this.replaceField,i("replace",()=>hf(e),[vf(e,"replace")]),i("replaceAll",()=>cf(e),[vf(e,"replace all")]),md("button",{name:"close",onclick:()=>pf(e),"aria-label":vf(e,"close"),type:"button"},["×"])]])}commit(){let e=new jd({search:this.searchField.value,caseSensitive:this.caseField.checked,regexp:this.reField.checked,replace:this.replaceField.value});e.eq(this.query)||(this.query=e,this.view.dispatch({effects:Xd.of(e)}))}keydown(e){Es(this.view,e,"search-panel")?e.preventDefault():13==e.keyCode&&e.target==this.searchField?(e.preventDefault(),(e.shiftKey?of:sf)(this.view)):13==e.keyCode&&e.target==this.replaceField&&(e.preventDefault(),hf(this.view))}update(e){for(let t of e.transactions)for(let e of t.effects)e.is(Xd)&&!e.value.eq(this.query)&&this.setQuery(e.value)}setQuery(e){this.query=e,this.searchField.value=e.search,this.replaceField.value=e.replace,this.caseField.checked=e.caseSensitive,this.reField.checked=e.regexp}mount(){this.searchField.select()}get pos(){return 80}get top(){return this.view.state.facet(Wd).top}}function vf(e,t){return e.state.phrase(t)}const xf=30,wf=/[\s\.,:;?!]/;function bf(e,{from:t,to:i}){let n=e.state.doc.lineAt(t),r=e.state.doc.lineAt(i).to,s=Math.max(n.from,t-xf),o=Math.min(r,i+xf),l=e.state.sliceDoc(s,o);if(s!=n.from)for(let a=0;a<xf;a++)if(!wf.test(l[a+1])&&wf.test(l[a])){l=l.slice(a);break}if(o!=r)for(let a=l.length-1;a>l.length-xf;a--)if(!wf.test(l[a-1])&&wf.test(l[a])){l=l.slice(0,a);break}return ms.announce.of(`${e.state.phrase("current match")}. ${l} ${e.state.phrase("on line")} ${n.number}.`)}const yf=ms.baseTheme({".cm-panel.cm-search":{padding:"2px 6px 4px",position:"relative","& [name=close]":{position:"absolute",top:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:0,margin:0},"& input, & button, & label":{margin:".2em .6em .2em 0"},"& input[type=checkbox]":{marginRight:".2em"},"& label":{fontSize:"80%",whiteSpace:"pre"}},"&light .cm-searchMatch":{backgroundColor:"#ffff0054"},"&dark .cm-searchMatch":{backgroundColor:"#00ffff8a"},"&light .cm-searchMatch-selected":{backgroundColor:"#ff6a0054"},"&dark .cm-searchMatch-selected":{backgroundColor:"#ff00ff8a"}}),kf=[Qd,J.lowest(nf),yf];class Df{constructor(e,t,i){this.state=e,this.pos=t,this.explicit=i,this.abortListeners=[]}tokenBefore(e){let t=Ca(this.state).resolveInner(this.pos,-1);while(t&&e.indexOf(t.name)<0)t=t.parent;return t?{from:t.from,to:this.pos,text:this.state.sliceDoc(t.from,this.pos),type:t.type}:null}matchBefore(e){let t=this.state.doc.lineAt(this.pos),i=Math.max(t.from,this.pos-250),n=t.text.slice(i-t.from,this.pos-t.from),r=n.search(Bf(e,!1));return r<0?null:{from:i+r,to:this.pos,text:n.slice(r)}}get aborted(){return null==this.abortListeners}addEventListener(e,t){"abort"==e&&this.abortListeners&&this.abortListeners.push(t)}}function Cf(e){let t=Object.keys(e).join(""),i=/\w/.test(t);return i&&(t=t.replace(/\w/g,"")),`[${i?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}function Sf(e){let t=Object.create(null),i=Object.create(null);for(let{label:r}of e){t[r[0]]=!0;for(let e=1;e<r.length;e++)i[r[e]]=!0}let n=Cf(t)+Cf(i)+"*$";return[new RegExp("^"+n),new RegExp(n)]}function Af(e){let t=e.map(e=>"string"==typeof e?{label:e}:e),[i,n]=t.every(e=>/^\w+$/.test(e.label))?[/\w*$/,/\w+$/]:Sf(t);return e=>{let r=e.matchBefore(n);return r||e.explicit?{from:r?r.from:e.pos,options:t,validFor:i}:null}}class Ef{constructor(e,t,i){this.completion=e,this.source=t,this.match=i}}function Mf(e){return e.selection.main.head}function Bf(e,t){var i;let{source:n}=e,r=t&&"^"!=n[0],s="$"!=n[n.length-1];return r||s?new RegExp(`${r?"^":""}(?:${n})${s?"$":""}`,null!==(i=e.flags)&&void 0!==i?i:e.ignoreCase?"i":""):e}function Ff(e,t,i,n){return Object.assign(Object.assign({},e.changeByRange(r=>{if(r==e.selection.main)return{changes:{from:i,to:n,insert:t},range:N.cursor(i+t.length)};let s=n-i;return!r.empty||s&&e.sliceDoc(r.from-s,r.from)!=e.sliceDoc(i,n)?{range:r}:{changes:{from:r.from-s,to:r.from,insert:t},range:N.cursor(r.from-s+t.length)}})),{userEvent:"input.complete"})}function Tf(e,t){const i=t.completion.apply||t.completion.label;let n=t.source;"string"==typeof i?e.dispatch(Ff(e.state,i,n.from,n.to)):i(e,t.completion,n.from,n.to)}const Of=new WeakMap;function Lf(e){if(!Array.isArray(e))return e;let t=Of.get(e);return t||Of.set(e,t=Af(e)),t}class Rf{constructor(e){this.pattern=e,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[];for(let t=0;t<e.length;){let i=k(e,t),n=C(i);this.chars.push(i);let r=e.slice(t,t+n),s=r.toUpperCase();this.folded.push(k(s==r?r.toLowerCase():s,0)),t+=n}this.astral=e.length!=this.chars.length}match(e){if(0==this.pattern.length)return[0];if(e.length<this.pattern.length)return null;let{chars:t,folded:i,any:n,precise:r,byWord:s}=this;if(1==t.length){let n=k(e,0);return n==t[0]?[0,0,C(n)]:n==i[0]?[-200,0,C(n)]:null}let o=e.indexOf(this.pattern);if(0==o)return[0,0,this.pattern.length];let l=t.length,a=0;if(o<0){for(let r=0,s=Math.min(e.length,200);r<s&&a<l;){let s=k(e,r);s!=t[a]&&s!=i[a]||(n[a++]=r),r+=C(s)}if(a<l)return null}let h=0,c=0,u=!1,d=0,f=-1,p=-1,m=/[a-z]/.test(e),g=!0;for(let v=0,x=Math.min(e.length,200),w=0;v<x&&c<l;){let n=k(e,v);o<0&&(h<l&&n==t[h]&&(r[h++]=v),d<l&&(n==t[d]||n==i[d]?(0==d&&(f=v),p=v+1,d++):d=0));let a,x=n<255?n>=48&&n<=57||n>=97&&n<=122?2:n>=65&&n<=90?1:0:(a=D(n))!=a.toLowerCase()?1:a!=a.toUpperCase()?2:0;(!v||1==x&&m||0==w&&0!=x)&&(t[c]==n||i[c]==n&&(u=!0)?s[c++]=v:s.length&&(g=!1)),w=x,v+=C(n)}return c==l&&0==s[0]&&g?this.result((u?-200:0)-100,s,e):d==l&&0==f?[-200-e.length,0,p]:o>-1?[-700-e.length,o,o+this.pattern.length]:d==l?[-900-e.length,f,p]:c==l?this.result((u?-200:0)-100-700+(g?0:-1100),s,e):2==t.length?null:this.result((n[0]?-700:0)-200-1100,n,e)}result(e,t,i){let n=[e-i.length],r=1;for(let s of t){let e=s+(this.astral?C(k(i,s)):1);r>1&&n[r-1]==s?n[r-1]=e:(n[r++]=s,n[r++]=e)}return n}}const If=_.define({combine(e){return Be(e,{activateOnTyping:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[]},{defaultKeymap:(e,t)=>e&&t,closeOnBlur:(e,t)=>e&&t,icons:(e,t)=>e&&t,optionClass:(e,t)=>i=>Nf(e(i),t(i)),addToOptions:(e,t)=>e.concat(t)})}});function Nf(e,t){return e?t?e+" "+t:e:t}function Pf(e){let t=e.addToOptions.slice();return e.icons&&t.push({render(e){let t=document.createElement("div");return t.classList.add("cm-completionIcon"),e.type&&t.classList.add(...e.type.split(/\s+/g).map(e=>"cm-completionIcon-"+e)),t.setAttribute("aria-hidden","true"),t},position:20}),t.push({render(e,t,i){let n=document.createElement("span");n.className="cm-completionLabel";let{label:r}=e,s=0;for(let o=1;o<i.length;){let e=i[o++],t=i[o++];e>s&&n.appendChild(document.createTextNode(r.slice(s,e)));let l=n.appendChild(document.createElement("span"));l.appendChild(document.createTextNode(r.slice(e,t))),l.className="cm-completionMatchedText",s=t}return s<r.length&&n.appendChild(document.createTextNode(r.slice(s))),n},position:50},{render(e){if(!e.detail)return null;let t=document.createElement("span");return t.className="cm-completionDetail",t.textContent=e.detail,t},position:80}),t.sort((e,t)=>e.position-t.position).map(e=>e.render)}function zf(e,t,i){if(e<=i)return{from:0,to:e};if(t<=e>>1){let e=Math.floor(t/i);return{from:e*i,to:(e+1)*i}}let n=Math.floor((e-t)/i);return{from:e-(n+1)*i,to:e-n*i}}class _f{constructor(e,t){this.view=e,this.stateField=t,this.info=null,this.placeInfo={read:()=>this.measureInfo(),write:e=>this.positionInfo(e),key:this};let i=e.state.field(t),{options:n,selected:r}=i.open,s=e.state.facet(If);this.optionContent=Pf(s),this.optionClass=s.optionClass,this.range=zf(n.length,r,s.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.dom.addEventListener("mousedown",t=>{for(let i,r=t.target;r&&r!=this.dom;r=r.parentNode)if("LI"==r.nodeName&&(i=/-(\d+)$/.exec(r.id))&&+i[1]<n.length)return Tf(e,n[+i[1]]),void t.preventDefault()}),this.list=this.dom.appendChild(this.createListBox(n,i.id,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfo)})}mount(){this.updateSel()}update(e){e.state.field(this.stateField)!=e.startState.field(this.stateField)&&this.updateSel()}positioned(){this.info&&this.view.requestMeasure(this.placeInfo)}updateSel(){let e=this.view.state.field(this.stateField),t=e.open;if((t.selected<this.range.from||t.selected>=this.range.to)&&(this.range=zf(t.options.length,t.selected,this.view.state.facet(If).maxRenderedOptions),this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t.options,e.id,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfo)})),this.updateSelectedOption(t.selected)){this.info&&(this.info.remove(),this.info=null);let{completion:i}=t.options[t.selected],{info:n}=i;if(!n)return;let r="string"===typeof n?document.createTextNode(n):n(i);if(!r)return;"then"in r?r.then(t=>{t&&this.view.state.field(this.stateField,!1)==e&&this.addInfoPane(t)}).catch(e=>zi(this.view.state,e,"completion info")):this.addInfoPane(r)}}addInfoPane(e){let t=this.info=document.createElement("div");t.className="cm-tooltip cm-completionInfo",t.appendChild(e),this.dom.appendChild(t),this.view.requestMeasure(this.placeInfo)}updateSelectedOption(e){let t=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)n==e?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),t=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return t&&qf(this.list,t),t}measureInfo(){let e=this.dom.querySelector("[aria-selected]");if(!e||!this.info)return null;let t=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=e.getBoundingClientRect();if(n.top>Math.min(innerHeight,t.bottom)-10||n.bottom<Math.max(0,t.top)+10)return null;let r=Math.max(0,Math.min(n.top,innerHeight-i.height))-t.top,s=this.view.textDirection==Qi.RTL,o=t.left,l=innerWidth-t.right;return s&&o<Math.min(i.width,l)?s=!1:!s&&l<Math.min(i.width,o)&&(s=!0),{top:r,left:s}}positionInfo(e){this.info&&(this.info.style.top=(e?e.top:-1e6)+"px",e&&(this.info.classList.toggle("cm-completionInfo-left",e.left),this.info.classList.toggle("cm-completionInfo-right",!e.left)))}createListBox(e,t,i){const n=document.createElement("ul");n.id=t,n.setAttribute("role","listbox"),n.setAttribute("aria-expanded","true"),n.setAttribute("aria-label",this.view.state.phrase("Completions"));for(let r=i.from;r<i.to;r++){let{completion:i,match:s}=e[r];const o=n.appendChild(document.createElement("li"));o.id=t+"-"+r,o.setAttribute("role","option");let l=this.optionClass(i);l&&(o.className=l);for(let e of this.optionContent){let t=e(i,this.view.state,s);t&&o.appendChild(t)}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<e.length&&n.classList.add("cm-completionListIncompleteBottom"),n}}function Hf(e){return t=>new _f(t,e)}function qf(e,t){let i=e.getBoundingClientRect(),n=t.getBoundingClientRect();n.top<i.top?e.scrollTop-=i.top-n.top:n.bottom>i.bottom&&(e.scrollTop+=n.bottom-i.bottom)}function Vf(e){return 100*(e.boost||0)+(e.apply?10:0)+(e.info?5:0)+(e.type?1:0)}function Wf(e,t){let i=[],n=0;for(let o of e)if(o.hasResult())if(!1===o.result.filter){let e=o.result.getMatch;for(let t of o.result.options){let r=[1e9-n++];if(e)for(let i of e(t))r.push(i);i.push(new Ef(t,o,r))}}else{let e,n=new Rf(t.sliceDoc(o.from,o.to));for(let t of o.result.options)(e=n.match(t.label))&&(null!=t.boost&&(e[0]+=t.boost),i.push(new Ef(t,o,e)))}let r=[],s=null;for(let o of i.sort(Xf))!s||s.label!=o.completion.label||s.detail!=o.completion.detail||null!=s.type&&null!=o.completion.type&&s.type!=o.completion.type||s.apply!=o.completion.apply?r.push(o):Vf(o.completion)>Vf(s)&&(r[r.length-1]=o),s=o.completion;return r}class jf{constructor(e,t,i,n,r){this.options=e,this.attrs=t,this.tooltip=i,this.timestamp=n,this.selected=r}setSelected(e,t){return e==this.selected||e>=this.options.length?this:new jf(this.options,Kf(t,e),this.tooltip,this.timestamp,e)}static build(e,t,i,n,r){let s=Wf(e,t);if(!s.length)return null;let o=0;if(n&&n.selected){let e=n.options[n.selected].completion;for(let t=0;t<s.length;t++)if(s[t].completion==e){o=t;break}}return new jf(s,Kf(i,o),{pos:e.reduce((e,t)=>t.hasResult()?Math.min(e,t.from):e,1e8),create:Hf(sp),above:r.aboveCursor},n?n.timestamp:Date.now(),o)}map(e){return new jf(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:e.mapPos(this.tooltip.pos)}),this.timestamp,this.selected)}}class $f{constructor(e,t,i){this.active=e,this.id=t,this.open=i}static start(){return new $f(Jf,"cm-ac-"+Math.floor(2e6*Math.random()).toString(36),null)}update(e){let{state:t}=e,i=t.facet(If),n=i.override||t.languageDataAt("autocomplete",Mf(t)).map(Lf),r=n.map(t=>{let n=this.active.find(e=>e.source==t)||new Qf(t,this.active.some(e=>0!=e.state)?1:0);return n.update(e,i)});r.length==this.active.length&&r.every((e,t)=>e==this.active[t])&&(r=this.active);let s=e.selection||r.some(t=>t.hasResult()&&e.changes.touchesRange(t.from,t.to))||!Uf(r,this.active)?jf.build(r,t,this.id,this.open,i):this.open&&e.docChanged?this.open.map(e.changes):this.open;!s&&r.every(e=>1!=e.state)&&r.some(e=>e.hasResult())&&(r=r.map(e=>e.hasResult()?new Qf(e.source,0):e));for(let o of e.effects)o.is(rp)&&(s=s&&s.setSelected(o.value,this.id));return r==this.active&&s==this.open?this:new $f(r,this.id,s)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:Gf}}function Uf(e,t){if(e==t)return!0;for(let i=0,n=0;;){while(i<e.length&&!e[i].hasResult)i++;while(n<t.length&&!t[n].hasResult)n++;let r=i==e.length,s=n==t.length;if(r||s)return r==s;if(e[i++].result!=t[n++].result)return!1}}const Gf={"aria-autocomplete":"list"};function Kf(e,t){return{"aria-autocomplete":"list","aria-haspopup":"listbox","aria-activedescendant":e+"-"+t,"aria-controls":e}}const Jf=[];function Xf(e,t){let i=t.match[0]-e.match[0];return i||e.completion.label.localeCompare(t.completion.label)}function Zf(e){return e.isUserEvent("input.type")?"input":e.isUserEvent("delete.backward")?"delete":null}class Qf{constructor(e,t,i=-1){this.source=e,this.state=t,this.explicitPos=i}hasResult(){return!1}update(e,t){let i=Zf(e),n=this;i?n=n.handleUserEvent(e,i,t):e.docChanged?n=n.handleChange(e):e.selection&&0!=n.state&&(n=new Qf(n.source,0));for(let r of e.effects)if(r.is(tp))n=new Qf(n.source,1,r.value?Mf(e.state):-1);else if(r.is(ip))n=new Qf(n.source,0);else if(r.is(np))for(let e of r.value)e.source==n.source&&(n=e);return n}handleUserEvent(e,t,i){return"delete"!=t&&i.activateOnTyping?new Qf(this.source,1):this.map(e.changes)}handleChange(e){return e.changes.touchesRange(Mf(e.startState))?new Qf(this.source,0):this.map(e.changes)}map(e){return e.empty||this.explicitPos<0?this:new Qf(this.source,this.state,e.mapPos(this.explicitPos))}}class Yf extends Qf{constructor(e,t,i,n,r){super(e,2,t),this.result=i,this.from=n,this.to=r}hasResult(){return!0}handleUserEvent(e,t,i){var n;let r=e.changes.mapPos(this.from),s=e.changes.mapPos(this.to,1),o=Mf(e.state);if((this.explicitPos<0?o<=r:o<this.from)||o>s||"delete"==t&&Mf(e.startState)==this.from)return new Qf(this.source,"input"==t&&i.activateOnTyping?1:0);let l,a=this.explicitPos<0?-1:e.changes.mapPos(this.explicitPos);return ep(this.result.validFor,e.state,r,s)?new Yf(this.source,a,this.result,r,s):this.result.update&&(l=this.result.update(this.result,r,s,new Df(e.state,o,a>=0)))?new Yf(this.source,a,l,l.from,null!==(n=l.to)&&void 0!==n?n:Mf(e.state)):new Qf(this.source,1,a)}handleChange(e){return e.changes.touchesRange(this.from,this.to)?new Qf(this.source,0):this.map(e.changes)}map(e){return e.empty?this:new Yf(this.source,this.explicitPos<0?-1:e.mapPos(this.explicitPos),this.result,e.mapPos(this.from),e.mapPos(this.to,1))}}function ep(e,t,i,n){if(!e)return!1;let r=t.sliceDoc(i,n);return"function"==typeof e?e(r,i,n,t):Bf(e,!0).test(r)}const tp=fe.define(),ip=fe.define(),np=fe.define({map(e,t){return e.map(e=>e.map(t))}}),rp=fe.define(),sp=U.define({create(){return $f.start()},update(e,t){return e.update(t)},provide:e=>[Fo.from(e,e=>e.tooltip),ms.contentAttributes.from(e,e=>e.attrs)]}),op=75;function lp(e,t="option"){return i=>{let n=i.state.field(sp,!1);if(!n||!n.open||Date.now()-n.open.timestamp<op)return!1;let r,s=1;"page"==t&&(r=zo(i,n.open.tooltip))&&(s=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let o=n.open.selected+s*(e?1:-1),{length:l}=n.open.options;return o<0?o="page"==t?0:l-1:o>=l&&(o="page"==t?l-1:0),i.dispatch({effects:rp.of(o)}),!0}}const ap=e=>{let t=e.state.field(sp,!1);return!(e.state.readOnly||!t||!t.open||Date.now()-t.open.timestamp<op)&&(Tf(e,t.open.options[t.open.selected]),!0)},hp=e=>{let t=e.state.field(sp,!1);return!!t&&(e.dispatch({effects:tp.of(!0)}),!0)},cp=e=>{let t=e.state.field(sp,!1);return!(!t||!t.active.some(e=>0!=e.state))&&(e.dispatch({effects:ip.of(null)}),!0)};class up{constructor(e,t){this.active=e,this.context=t,this.time=Date.now(),this.updates=[],this.done=void 0}}const dp=50,fp=50,pp=1e3,mp=Vi.fromClass(class{constructor(e){this.view=e,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.composing=0;for(let t of e.state.field(sp).active)1==t.state&&this.startQuery(t)}update(e){let t=e.state.field(sp);if(!e.selectionSet&&!e.docChanged&&e.startState.field(sp)==t)return;let i=e.transactions.some(e=>(e.selection||e.docChanged)&&!Zf(e));for(let r=0;r<this.running.length;r++){let t=this.running[r];if(i||t.updates.length+e.transactions.length>fp&&Date.now()-t.time>pp){for(let e of t.context.abortListeners)try{e()}catch(n){zi(this.view.state,n)}t.context.abortListeners=null,this.running.splice(r--,1)}else t.updates.push(...e.transactions)}if(this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),this.debounceUpdate=t.active.some(e=>1==e.state&&!this.running.some(t=>t.active.source==e.source))?setTimeout(()=>this.startUpdate(),dp):-1,0!=this.composing)for(let r of e.transactions)"input"==Zf(r)?this.composing=2:2==this.composing&&r.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1;let{state:e}=this.view,t=e.field(sp);for(let i of t.active)1!=i.state||this.running.some(e=>e.active.source==i.source)||this.startQuery(i)}startQuery(e){let{state:t}=this.view,i=Mf(t),n=new Df(t,i,e.explicitPos==i),r=new up(e,n);this.running.push(r),Promise.resolve(e.source(n)).then(e=>{r.context.aborted||(r.done=e||null,this.scheduleAccept())},e=>{this.view.dispatch({effects:ip.of(null)}),zi(this.view.state,e)})}scheduleAccept(){this.running.every(e=>void 0!==e.done)?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout(()=>this.accept(),dp))}accept(){var e;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let t=[],i=this.view.state.facet(If);for(let n=0;n<this.running.length;n++){let r=this.running[n];if(void 0===r.done)continue;if(this.running.splice(n--,1),r.done){let n=new Yf(r.active.source,r.active.explicitPos,r.done,r.done.from,null!==(e=r.done.to)&&void 0!==e?e:Mf(r.updates.length?r.updates[0].startState:this.view.state));for(let e of r.updates)n=n.update(e,i);if(n.hasResult()){t.push(n);continue}}let s=this.view.state.field(sp).active.find(e=>e.source==r.active.source);if(s&&1==s.state)if(null==r.done){let e=new Qf(r.active.source,0);for(let t of r.updates)e=e.update(t,i);1!=e.state&&t.push(e)}else this.startQuery(s)}t.length&&this.view.dispatch({effects:np.of(t)})}},{eventHandlers:{blur(){let e=this.view.state.field(sp,!1);e&&e.tooltip&&this.view.state.facet(If).closeOnBlur&&this.view.dispatch({effects:ip.of(null)})},compositionstart(){this.composing=1},compositionend(){3==this.composing&&setTimeout(()=>this.view.dispatch({effects:tp.of(!1)}),20),this.composing=0}}}),gp=ms.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",listStyle:"none",margin:0,padding:0,"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer",padding:"1px 3px",lineHeight:1.2}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"300px"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});const vp={brackets:["(","[","{","'",'"'],before:")]}:;>"},xp=fe.define({map(e,t){let i=t.mapPos(e,-1,A.TrackAfter);return null==i?void 0:i}}),wp=fe.define({map(e,t){return t.mapPos(e)}}),bp=new class extends Fe{};bp.startSide=1,bp.endSide=-1;const yp=U.define({create(){return Re.empty},update(e,t){if(t.selection){let i=t.state.doc.lineAt(t.selection.main.head).from,n=t.startState.doc.lineAt(t.startState.selection.main.head).from;i!=t.changes.mapPos(n,-1)&&(e=Re.empty)}e=e.map(t.changes);for(let i of t.effects)i.is(xp)?e=e.update({add:[bp.range(i.value,i.value+1)]}):i.is(wp)&&(e=e.update({filter:e=>e!=i.value}));return e}});function kp(){return[Ep,yp]}const Dp="()[]{}<>";function Cp(e){for(let t=0;t<Dp.length;t+=2)if(Dp.charCodeAt(t)==e)return Dp.charAt(t+1);return D(e<128?e:e+1)}function Sp(e,t){return e.languageDataAt("closeBrackets",t)[0]||vp}const Ap="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),Ep=ms.inputHandler.of((e,t,i,n)=>{if((Ap?e.composing:e.compositionStarted)||e.state.readOnly)return!1;let r=e.state.selection.main;if(n.length>2||2==n.length&&1==C(k(n,0))||t!=r.from||i!=r.to)return!1;let s=Fp(e.state,n);return!!s&&(e.dispatch(s),!0)}),Mp=({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=Sp(e,e.selection.main.head),n=i.brackets||vp.brackets,r=null,s=e.changeByRange(t=>{if(t.empty){let i=Lp(e.doc,t.head);for(let r of n)if(r==i&&Op(e.doc,t.head)==Cp(k(r,0)))return{changes:{from:t.head-r.length,to:t.head+r.length},range:N.cursor(t.head-r.length),userEvent:"delete.backward"}}return{range:r=t}});return r||t(e.update(s,{scrollIntoView:!0})),!r},Bp=[{key:"Backspace",run:Mp}];function Fp(e,t){let i=Sp(e,e.selection.main.head),n=i.brackets||vp.brackets;for(let r of n){let s=Cp(k(r,0));if(t==r)return s==r?Np(e,r,n.indexOf(r+r+r)>-1):Rp(e,r,s,i.before||vp.before);if(t==s&&Tp(e,e.selection.main.from))return Ip(e,r,s)}return null}function Tp(e,t){let i=!1;return e.field(yp).between(0,e.doc.length,e=>{e==t&&(i=!0)}),i}function Op(e,t){let i=e.sliceString(t,t+2);return i.slice(0,C(k(i,0)))}function Lp(e,t){let i=e.sliceString(t-2,t);return C(k(i,0))==i.length?i:i.slice(1)}function Rp(e,t,i,n){let r=null,s=e.changeByRange(s=>{if(!s.empty)return{changes:[{insert:t,from:s.from},{insert:i,from:s.to}],effects:xp.of(s.to+t.length),range:N.range(s.anchor+t.length,s.head+t.length)};let o=Op(e.doc,s.head);return!o||/\s/.test(o)||n.indexOf(o)>-1?{changes:{insert:t+i,from:s.head},effects:xp.of(s.head+t.length),range:N.cursor(s.head+t.length)}:{range:r=s}});return r?null:e.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function Ip(e,t,i){let n=null,r=e.selection.ranges.map(t=>t.empty&&Op(e.doc,t.head)==i?N.cursor(t.head+i.length):n=t);return n?null:e.update({selection:N.create(r,e.selection.mainIndex),scrollIntoView:!0,effects:e.selection.ranges.map(({from:e})=>wp.of(e))})}function Np(e,t,i){let n=null,r=e.changeByRange(r=>{if(!r.empty)return{changes:[{insert:t,from:r.from},{insert:t,from:r.to}],effects:xp.of(r.to+t.length),range:N.range(r.anchor+t.length,r.head+t.length)};let s=r.head,o=Op(e.doc,s);if(o==t){if(Pp(e,s))return{changes:{insert:t+t,from:s},effects:xp.of(s+t.length),range:N.cursor(s+t.length)};if(Tp(e,s)){let n=i&&e.sliceDoc(s,s+3*t.length)==t+t+t;return{range:N.cursor(s+t.length*(n?3:1)),effects:wp.of(s)}}}else{if(i&&e.sliceDoc(s-2*t.length,s)==t+t&&Pp(e,s-2*t.length))return{changes:{insert:t+t+t+t,from:s},effects:xp.of(s+t.length),range:N.cursor(s+t.length)};if(e.charCategorizer(s)(o)!=De.Word){let i=e.sliceDoc(s-1,s);if(i!=t&&e.charCategorizer(s)(i)!=De.Word&&!zp(e,s,t))return{changes:{insert:t+t,from:s},effects:xp.of(s+t.length),range:N.cursor(s+t.length)}}}return{range:n=r}});return n?null:e.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function Pp(e,t){let i=Ca(e).resolveInner(t+1);return i.parent&&i.from==t}function zp(e,t,i){let n=Ca(e).resolveInner(t,-1);for(let r=0;r<5;r++){if(e.sliceDoc(n.from,n.from+i.length)==i)return!0;let r=n.to==t&&n.parent;if(!r)break;n=r}return!1}function _p(e={}){return[sp,If.of(e),mp,qp,gp]}const Hp=[{key:"Ctrl-Space",run:hp},{key:"Escape",run:cp},{key:"ArrowDown",run:lp(!0)},{key:"ArrowUp",run:lp(!1)},{key:"PageDown",run:lp(!0,"page")},{key:"PageUp",run:lp(!1,"page")},{key:"Enter",run:ap}],qp=J.highest(Cs.computeN([If],e=>e.facet(If).defaultKeymap?[Hp]:[]));class Vp{constructor(e,t,i){this.from=e,this.to=t,this.diagnostic=i}}class Wp{constructor(e,t,i){this.diagnostics=e,this.panel=t,this.selected=i}static init(e,t,i){let n=e,r=i.facet(om).markerFilter;r&&(n=r(n));let s=vi.set(n.map(e=>e.from==e.to||e.from==e.to-1&&i.doc.lineAt(e.from).to==e.from?vi.widget({widget:new hm(e),diagnostic:e}).range(e.from):vi.mark({attributes:{class:"cm-lintRange cm-lintRange-"+e.severity},diagnostic:e}).range(e.from,e.to)),!0);return new Wp(s,t,jp(s))}}function jp(e,t=null,i=0){let n=null;return e.between(i,1e9,(e,i,{spec:r})=>{if(!t||r.diagnostic==t)return n=new Vp(e,i,r.diagnostic),!1}),n}function $p(e,t){return!(!e.effects.some(e=>e.is(Kp))&&!e.changes.touchesRange(t.pos))}function Up(e,t){return e.field(Zp,!1)?t:t.concat(fe.appendConfig.of([Zp,ms.decorations.compute([Zp],e=>{let{selected:t,panel:i}=e.field(Zp);return t&&i&&t.from!=t.to?vi.set([Qp.range(t.from,t.to)]):vi.none}),Po(Yp,{hideOn:$p}),pm]))}function Gp(e,t){return{effects:Up(e,[Kp.of(t)])}}const Kp=fe.define(),Jp=fe.define(),Xp=fe.define(),Zp=U.define({create(){return new Wp(vi.none,null,null)},update(e,t){if(t.docChanged){let i=e.diagnostics.map(t.changes),n=null;if(e.selected){let r=t.changes.mapPos(e.selected.from,1);n=jp(i,e.selected.diagnostic,r)||jp(i,null,r)}e=new Wp(i,e.panel,n)}for(let i of t.effects)i.is(Kp)?e=Wp.init(i.value,e.panel,t.state):i.is(Jp)?e=new Wp(e.diagnostics,i.value?um.open:null,e.selected):i.is(Xp)&&(e=new Wp(e.diagnostics,e.panel,i.value));return e},provide:e=>[$o.from(e,e=>e.panel),ms.decorations.from(e,e=>e.diagnostics)]});const Qp=vi.mark({class:"cm-lintRange cm-lintRange-active"});function Yp(e,t,i){let{diagnostics:n}=e.state.field(Zp),r=[],s=2e8,o=0;n.between(t-(i<0?1:0),t+(i>0?1:0),(e,n,{spec:l})=>{t>=e&&t<=n&&(e==n||(t>e||i>0)&&(t<n||i<0))&&(r.push(l.diagnostic),s=Math.min(e,s),o=Math.max(n,o))});let l=e.state.facet(om).tooltipFilter;return l&&(r=l(r)),r.length?{pos:s,end:o,above:e.state.doc.lineAt(s).to<o,create(){return{dom:em(e,r)}}}:null}function em(e,t){return md("ul",{class:"cm-tooltip-lint"},t.map(t=>am(e,t,!1)))}const tm=e=>{let t=e.state.field(Zp,!1);t&&t.panel||e.dispatch({effects:Up(e.state,[Jp.of(!0)])});let i=qo(e,um.open);return i&&i.dom.querySelector(".cm-panel-lint ul").focus(),!0},im=e=>{let t=e.state.field(Zp,!1);return!(!t||!t.panel)&&(e.dispatch({effects:Jp.of(!1)}),!0)},nm=e=>{let t=e.state.field(Zp,!1);if(!t)return!1;let i=e.state.selection.main,n=t.diagnostics.iter(i.to+1);return!(!n.value&&(n=t.diagnostics.iter(0),!n.value||n.from==i.from&&n.to==i.to))&&(e.dispatch({selection:{anchor:n.from,head:n.to},scrollIntoView:!0}),!0)},rm=[{key:"Mod-Shift-m",run:tm},{key:"F8",run:nm}],sm=Vi.fromClass(class{constructor(e){this.view=e,this.timeout=-1,this.set=!0;let{delay:t}=e.state.facet(om);this.lintTime=Date.now()+t,this.run=this.run.bind(this),this.timeout=setTimeout(this.run,t)}run(){let e=Date.now();if(e<this.lintTime-10)setTimeout(this.run,this.lintTime-e);else{this.set=!1;let{state:e}=this.view,{sources:t}=e.facet(om);Promise.all(t.map(e=>Promise.resolve(e(this.view)))).then(t=>{let i=t.reduce((e,t)=>e.concat(t));this.view.state.doc==e.doc&&this.view.dispatch(Gp(this.view.state,i))},e=>{zi(this.view.state,e)})}}update(e){let t=e.state.facet(om);(e.docChanged||t!=e.startState.facet(om))&&(this.lintTime=Date.now()+t.delay,this.set||(this.set=!0,this.timeout=setTimeout(this.run,t.delay)))}force(){this.set&&(this.lintTime=Date.now(),this.run())}destroy(){clearTimeout(this.timeout)}}),om=_.define({combine(e){return Object.assign({sources:e.map(e=>e.source)},Be(e.map(e=>e.config),{delay:750,markerFilter:null,tooltipFilter:null}))},enables:sm});function lm(e){let t=[];if(e)e:for(let{name:i}of e){for(let e=0;e<i.length;e++){let n=i[e];if(/[a-zA-Z]/.test(n)&&!t.some(e=>e.toLowerCase()==n.toLowerCase())){t.push(n);continue e}}t.push("")}return t}function am(e,t,i){var n;let r=i?lm(t.actions):[];return md("li",{class:"cm-diagnostic cm-diagnostic-"+t.severity},md("span",{class:"cm-diagnosticText"},t.renderMessage?t.renderMessage():t.message),null===(n=t.actions)||void 0===n?void 0:n.map((i,n)=>{let s=n=>{n.preventDefault();let r=jp(e.state.field(Zp).diagnostics,t);r&&i.apply(e,r.from,r.to)},{name:o}=i,l=r[n]?o.indexOf(r[n]):-1,a=l<0?o:[o.slice(0,l),md("u",o.slice(l,l+1)),o.slice(l+1)];return md("button",{type:"button",class:"cm-diagnosticAction",onclick:s,onmousedown:s,"aria-label":` Action: ${o}${l<0?"":` (access key "${r[n]})"`}.`},a)}),t.source&&md("div",{class:"cm-diagnosticSource"},t.source))}class hm extends mi{constructor(e){super(),this.diagnostic=e}eq(e){return e.diagnostic==this.diagnostic}toDOM(){return md("span",{class:"cm-lintPoint cm-lintPoint-"+this.diagnostic.severity})}}class cm{constructor(e,t){this.diagnostic=t,this.id="item_"+Math.floor(4294967295*Math.random()).toString(16),this.dom=am(e,t,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class um{constructor(e){this.view=e,this.items=[];let t=t=>{if(27==t.keyCode)im(this.view),this.view.focus();else if(38==t.keyCode||33==t.keyCode)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(40==t.keyCode||34==t.keyCode)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(36==t.keyCode)this.moveSelection(0);else if(35==t.keyCode)this.moveSelection(this.items.length-1);else if(13==t.keyCode)this.view.focus();else{if(!(t.keyCode>=65&&t.keyCode<=90&&this.selectedIndex>=0))return;{let{diagnostic:i}=this.items[this.selectedIndex],n=lm(i.actions);for(let r=0;r<n.length;r++)if(n[r].toUpperCase().charCodeAt(0)==t.keyCode){let t=jp(this.view.state.field(Zp).diagnostics,i);t&&i.actions[r].apply(e,t.from,t.to)}}}t.preventDefault()},i=e=>{for(let t=0;t<this.items.length;t++)this.items[t].dom.contains(e.target)&&this.moveSelection(t)};this.list=md("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:t,onclick:i}),this.dom=md("div",{class:"cm-panel-lint"},this.list,md("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>im(this.view)},"×")),this.update()}get selectedIndex(){let e=this.view.state.field(Zp).selected;if(!e)return-1;for(let t=0;t<this.items.length;t++)if(this.items[t].diagnostic==e.diagnostic)return t;return-1}update(){let{diagnostics:e,selected:t}=this.view.state.field(Zp),i=0,n=!1,r=null;e.between(0,this.view.state.doc.length,(e,s,{spec:o})=>{let l,a=-1;for(let t=i;t<this.items.length;t++)if(this.items[t].diagnostic==o.diagnostic){a=t;break}a<0?(l=new cm(this.view,o.diagnostic),this.items.splice(i,0,l),n=!0):(l=this.items[a],a>i&&(this.items.splice(i,a-i),n=!0)),t&&l.diagnostic==t.diagnostic?l.dom.hasAttribute("aria-selected")||(l.dom.setAttribute("aria-selected","true"),r=l):l.dom.hasAttribute("aria-selected")&&l.dom.removeAttribute("aria-selected"),i++});while(i<this.items.length&&!(1==this.items.length&&this.items[0].diagnostic.from<0))n=!0,this.items.pop();0==this.items.length&&(this.items.push(new cm(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),n=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:e,panel:t})=>{e.top<t.top?this.list.scrollTop-=t.top-e.top:e.bottom>t.bottom&&(this.list.scrollTop+=e.bottom-t.bottom)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),n&&this.sync()}sync(){let e=this.list.firstChild;function t(){let t=e;e=t.nextSibling,t.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){while(e!=i.dom)t();e=i.dom.nextSibling}else this.list.insertBefore(i.dom,e);while(e)t()}moveSelection(e){if(this.selectedIndex<0)return;let t=this.view.state.field(Zp),i=jp(t.diagnostics,this.items[e].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:Xp.of(i)})}static open(e){return new um(e)}}function dm(e,t='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${t}>${encodeURIComponent(e)}</svg>')`}function fm(e){return dm(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${e}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const pm=ms.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:fm("#d11")},".cm-lintRange-warning":{backgroundImage:fm("orange")},".cm-lintRange-info":{backgroundImage:fm("#999")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});i.d(t,"basicSetup",(function(){return mm})),i.d(t,"minimalSetup",(function(){return gm})),i.d(t,"EditorView",(function(){return ms}));const mm=[ul(),ml(),no(),gc(),Dh(),Rs(),Gs(),Me.allowMultipleSelections.of(!0),Za(),Bh(Oh,{fallback:!0}),Vh(),kp(),_p(),wo(),ko(),co(),Ld(),Cs.of([...Bp,...pd,...mf,...Ic,...gh,...Hp,...rm])],gm=[no(),gc(),Rs(),Bh(Oh,{fallback:!0}),Cs.of([...pd,...Ic])]},b116:function(e,t,i){},bf7d:function(e,t,i){var n=i("a2f9");n.commands.tabAndIndentMarkdownList=function(e){var t=e.listSelections(),i=t[0].head,n=e.getStateAfter(i.line),r=!1!==n.list;if(r)e.execCommand("indentMore");else if(e.options.indentWithTabs)e.execCommand("insertTab");else{var s=Array(e.options.tabSize+1).join(" ");e.replaceSelection(s)}},n.commands.shiftTabAndUnindentMarkdownList=function(e){var t=e.listSelections(),i=t[0].head,n=e.getStateAfter(i.line),r=!1!==n.list;if(r)e.execCommand("indentLess");else if(e.options.indentWithTabs)e.execCommand("insertTab");else{var s=Array(e.options.tabSize+1).join(" ");e.replaceSelection(s)}}},d5e0:function(e,t,i){(function(e){e(i("56b3"))})((function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},i={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,caseFold:!1};e.defineMode("xml",(function(n,r){var s,o,l=n.indentUnit,a={},h=r.htmlMode?t:i;for(var c in h)a[c]=h[c];for(var c in r)a[c]=r[c];function u(e,t){function i(i){return t.tokenize=i,i(e,t)}var n,r=e.next();return"<"==r?e.eat("!")?e.eat("[")?e.match("CDATA[")?i(p("atom","]]>")):null:e.match("--")?i(p("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),i(m(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=p("meta","?>"),"meta"):(s=e.eat("/")?"closeTag":"openTag",t.tokenize=d,"tag bracket"):"&"==r?(n=e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"),n?"atom":"error"):(e.eatWhile(/[^&<]/),null)}function d(e,t){var i=e.next();if(">"==i||"/"==i&&e.eat(">"))return t.tokenize=u,s=">"==i?"endTag":"selfcloseTag","tag bracket";if("="==i)return s="equals",null;if("<"==i){t.tokenize=u,t.state=w,t.tagName=t.tagStart=null;var n=t.tokenize(e,t);return n?n+" tag error":"tag error"}return/[\'\"]/.test(i)?(t.tokenize=f(i),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function f(e){var t=function(t,i){while(!t.eol())if(t.next()==e){i.tokenize=d;break}return"string"};return t.isInAttribute=!0,t}function p(e,t){return function(i,n){while(!i.eol()){if(i.match(t)){n.tokenize=u;break}i.next()}return e}}function m(e){return function(t,i){var n;while(null!=(n=t.next())){if("<"==n)return i.tokenize=m(e+1),i.tokenize(t,i);if(">"==n){if(1==e){i.tokenize=u;break}return i.tokenize=m(e-1),i.tokenize(t,i)}}return"meta"}}function g(e,t,i){this.prev=e.context,this.tagName=t,this.indent=e.indented,this.startOfLine=i,(a.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function v(e){e.context&&(e.context=e.context.prev)}function x(e,t){var i;while(1){if(!e.context)return;if(i=e.context.tagName,!a.contextGrabbers.hasOwnProperty(i)||!a.contextGrabbers[i].hasOwnProperty(t))return;v(e)}}function w(e,t,i){return"openTag"==e?(i.tagStart=t.column(),b):"closeTag"==e?y:w}function b(e,t,i){return"word"==e?(i.tagName=t.current(),o="tag",C):(o="error",b)}function y(e,t,i){if("word"==e){var n=t.current();return i.context&&i.context.tagName!=n&&a.implicitlyClosed.hasOwnProperty(i.context.tagName)&&v(i),i.context&&i.context.tagName==n||!1===a.matchClosing?(o="tag",k):(o="tag error",D)}return o="error",D}function k(e,t,i){return"endTag"!=e?(o="error",k):(v(i),w)}function D(e,t,i){return o="error",k(e,t,i)}function C(e,t,i){if("word"==e)return o="attribute",S;if("endTag"==e||"selfcloseTag"==e){var n=i.tagName,r=i.tagStart;return i.tagName=i.tagStart=null,"selfcloseTag"==e||a.autoSelfClosers.hasOwnProperty(n)?x(i,n):(x(i,n),i.context=new g(i,n,r==i.indented)),w}return o="error",C}function S(e,t,i){return"equals"==e?A:(a.allowMissing||(o="error"),C(e,t,i))}function A(e,t,i){return"string"==e?E:"word"==e&&a.allowUnquoted?(o="string",C):(o="error",C(e,t,i))}function E(e,t,i){return"string"==e?E:C(e,t,i)}return u.isInText=!0,{startState:function(e){var t={tokenize:u,state:w,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;s=null;var i=t.tokenize(e,t);return(i||s)&&"comment"!=i&&(o=null,t.state=t.state(s||i,e,t),o&&(i="error"==o?i+" error":o)),i},indent:function(t,i,n){var r=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+l;if(r&&r.noIndent)return e.Pass;if(t.tokenize!=d&&t.tokenize!=u)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==a.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+l*(a.multilineTagIndentFactor||1);if(a.alignCDATA&&/<!\[CDATA\[/.test(i))return 0;var s=i&&/^<(\/)?([\w_:\.-]*)/.exec(i);if(s&&s[1])while(r){if(r.tagName==s[2]){r=r.prev;break}if(!a.implicitlyClosed.hasOwnProperty(r.tagName))break;r=r.prev}else if(s)while(r){var o=a.contextGrabbers[r.tagName];if(!o||!o.hasOwnProperty(s[2]))break;r=r.prev}while(r&&r.prev&&!r.startOfLine)r=r.prev;return r?r.indent+l:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:a.htmlMode?"html":"xml",helperType:a.htmlMode?"html":"xml",skipAttribute:function(e){e.state==A&&(e.state=C)}}})),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})}))},d7d5:function(e,t,i){(function(e){e(i("56b3"))})((function(e){function t(e){e.state.placeholder&&(e.state.placeholder.parentNode.removeChild(e.state.placeholder),e.state.placeholder=null)}function i(e){t(e);var i=e.state.placeholder=document.createElement("pre");i.style.cssText="height: 0; overflow: visible",i.className="CodeMirror-placeholder";var n=e.getOption("placeholder");"string"==typeof n&&(n=document.createTextNode(n)),i.appendChild(n),e.display.lineSpace.insertBefore(i,e.display.lineSpace.firstChild)}function n(e){s(e)&&i(e)}function r(e){var n=e.getWrapperElement(),r=s(e);n.className=n.className.replace(" CodeMirror-empty","")+(r?" CodeMirror-empty":""),r?i(e):t(e)}function s(e){return 1===e.lineCount()&&""===e.getLine(0)}e.defineOption("placeholder","",(function(i,s,o){var l=o&&o!=e.Init;if(s&&!l)i.on("blur",n),i.on("change",r),i.on("swapDoc",r),r(i);else if(!s&&l){i.off("blur",n),i.off("change",r),i.off("swapDoc",r),t(i);var a=i.getWrapperElement();a.className=a.className.replace(" CodeMirror-empty","")}s&&!i.hasFocus()&&n(i)}))}))},ea65:function(e,t,i){"use strict";var n=i("4a5d");function r(e){e=e||{},"function"===typeof e.codeMirrorInstance&&"function"===typeof e.codeMirrorInstance.defineMode?(String.prototype.includes||(String.prototype.includes=function(){return-1!==String.prototype.indexOf.apply(this,arguments)}),e.codeMirrorInstance.defineMode("spell-checker",(function(t){if(!r.aff_loading){r.aff_loading=!0;var i=new XMLHttpRequest;i.open("GET","https://cdn.jsdelivr.net/codemirror.spell-checker/latest/en_US.aff",!0),i.onload=function(){4===i.readyState&&200===i.status&&(r.aff_data=i.responseText,r.num_loaded++,2==r.num_loaded&&(r.typo=new n("en_US",r.aff_data,r.dic_data,{platform:"any"})))},i.send(null)}if(!r.dic_loading){r.dic_loading=!0;var s=new XMLHttpRequest;s.open("GET","https://cdn.jsdelivr.net/codemirror.spell-checker/latest/en_US.dic",!0),s.onload=function(){4===s.readyState&&200===s.status&&(r.dic_data=s.responseText,r.num_loaded++,2==r.num_loaded&&(r.typo=new n("en_US",r.aff_data,r.dic_data,{platform:"any"})))},s.send(null)}var o='!"#$%&()*+,-./:;<=>?@[\\]^_`{|}~ ',l={token:function(e){var t=e.peek(),i="";if(o.includes(t))return e.next(),null;while(null!=(t=e.peek())&&!o.includes(t))i+=t,e.next();return r.typo&&!r.typo.check(i)?"spell-error":null}},a=e.codeMirrorInstance.getMode(t,t.backdrop||"text/plain");return e.codeMirrorInstance.overlayMode(a,l,!0)}))):console.log("CodeMirror Spell Checker: You must provide an instance of CodeMirror via the option `codeMirrorInstance`")}r.num_loaded=0,r.aff_loading=!1,r.dic_loading=!1,r.aff_data="",r.dic_data="",r.typo,e.exports=r},ebfb:function(e,t,i){"use strict";var n=i("a2f9");i("1ef7"),i("bf7d"),i("6d78"),i("959b"),i("9eb9"),i("d7d5"),i("9948"),i("44a0"),i("d5e0");var r=i("ea65"),s=i("7c5c"),o=/Mac/.test(navigator.platform),l={toggleBold:x,toggleItalic:w,drawLink:T,toggleHeadingSmaller:D,toggleHeadingBigger:C,drawImage:O,toggleBlockquote:k,toggleOrderedList:B,toggleUnorderedList:M,toggleCodeBlock:y,togglePreview:z,toggleStrikethrough:b,toggleHeading1:S,toggleHeading2:A,toggleHeading3:E,cleanBlock:F,drawTable:L,drawHorizontalRule:R,undo:I,redo:N,toggleSideBySide:P,toggleFullScreen:v},a={toggleBold:"Cmd-B",toggleItalic:"Cmd-I",drawLink:"Cmd-K",toggleHeadingSmaller:"Cmd-H",toggleHeadingBigger:"Shift-Cmd-H",cleanBlock:"Cmd-E",drawImage:"Cmd-Alt-I",toggleBlockquote:"Cmd-'",toggleOrderedList:"Cmd-Alt-L",toggleUnorderedList:"Cmd-L",toggleCodeBlock:"Cmd-Alt-C",togglePreview:"Cmd-P",toggleSideBySide:"F9",toggleFullScreen:"F11"},h=function(e){for(var t in l)if(l[t]===e)return t;return null},c=function(){var e=!1;return function(t){(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4)))&&(e=!0)}(navigator.userAgent||navigator.vendor||window.opera),e};function u(e){return e=o?e.replace("Ctrl","Cmd"):e.replace("Cmd","Ctrl"),e}function d(e,t,i){e=e||{};var n=document.createElement("a");return t=void 0==t||t,e.title&&t&&(n.title=p(e.title,e.action,i),o&&(n.title=n.title.replace("Ctrl","⌘"),n.title=n.title.replace("Alt","⌥"))),n.tabIndex=-1,n.className=e.className,n}function f(){var e=document.createElement("i");return e.className="separator",e.innerHTML="|",e}function p(e,t,i){var n,r=e;return t&&(n=h(t),i[n]&&(r+=" ("+u(i[n])+")")),r}function m(e,t){t=t||e.getCursor("start");var i=e.getTokenAt(t);if(!i.type)return{};for(var n,r,s=i.type.split(" "),o={},l=0;l<s.length;l++)n=s[l],"strong"===n?o.bold=!0:"variable-2"===n?(r=e.getLine(t.line),/^\s*\d+\.\s/.test(r)?o["ordered-list"]=!0:o["unordered-list"]=!0):"atom"===n?o.quote=!0:"em"===n?o.italic=!0:"quote"===n?o.quote=!0:"strikethrough"===n?o.strikethrough=!0:"comment"===n?o.code=!0:"link"===n?o.link=!0:"tag"===n?o.image=!0:n.match(/^header(\-[1-6])?$/)&&(o[n.replace("header","heading")]=!0);return o}var g="";function v(e){var t=e.codemirror;t.setOption("fullScreen",!t.getOption("fullScreen")),t.getOption("fullScreen")?(g=document.body.style.overflow,document.body.style.overflow="hidden"):document.body.style.overflow=g;var i=t.getWrapperElement();/fullscreen/.test(i.previousSibling.className)?i.previousSibling.className=i.previousSibling.className.replace(/\s*fullscreen\b/,""):i.previousSibling.className+=" fullscreen";var n=e.toolbarElements.fullscreen;/active/.test(n.className)?n.className=n.className.replace(/\s*active\s*/g,""):n.className+=" active";var r=t.getWrapperElement().nextSibling;/editor-preview-active-side/.test(r.className)&&P(e)}function x(e){V(e,"bold",e.options.blockStyles.bold)}function w(e){V(e,"italic",e.options.blockStyles.italic)}function b(e){V(e,"strikethrough","~~")}function y(e){var t=e.options.blockStyles.code;function i(e){if("object"!==typeof e)throw"fencing_line() takes a 'line' object (not a line number, or line text).  Got: "+typeof e+": "+e;return e.styles&&e.styles[2]&&-1!==e.styles[2].indexOf("formatting-code-block")}function n(e){return e.state.base.base||e.state.base}function r(e,t,r,s,o){r=r||e.getLineHandle(t),s=s||e.getTokenAt({line:t,ch:1}),o=o||!!r.text&&e.getTokenAt({line:t,ch:r.text.length-1});var l=s.type?s.type.split(" "):[];return o&&n(o).indentedCode?"indented":-1!==l.indexOf("comment")&&(n(s).fencedChars||n(o).fencedChars||i(r)?"fenced":"single")}function s(e,t,i,n){var r=t.line+1,s=i.line+1,o=t.line!==i.line,l=n+"\n",a="\n"+n;o&&s++,o&&0===i.ch&&(a=n+"\n",s--),_(e,!1,[l,a]),e.setSelection({line:r,ch:0},{line:s,ch:0})}var o,l,a,h=e.codemirror,c=h.getCursor("start"),u=h.getCursor("end"),d=h.getTokenAt({line:c.line,ch:c.ch||1}),f=h.getLineHandle(c.line),p=r(h,c.line,f,d);if("single"===p){var m=f.text.slice(0,c.ch).replace("`",""),g=f.text.slice(c.ch).replace("`","");h.replaceRange(m+g,{line:c.line,ch:0},{line:c.line,ch:99999999999999}),c.ch--,c!==u&&u.ch--,h.setSelection(c,u),h.focus()}else if("fenced"===p)if(c.line!==u.line||c.ch!==u.ch){for(o=c.line;o>=0;o--)if(f=h.getLineHandle(o),i(f))break;var v,x,w,b,y=h.getTokenAt({line:o,ch:1}),k=n(y).fencedChars;i(h.getLineHandle(c.line))?(v="",x=c.line):i(h.getLineHandle(c.line-1))?(v="",x=c.line-1):(v=k+"\n",x=c.line),i(h.getLineHandle(u.line))?(w="",b=u.line,0===u.ch&&(b+=1)):0!==u.ch&&i(h.getLineHandle(u.line+1))?(w="",b=u.line+1):(w=k+"\n",b=u.line+1),0===u.ch&&(b-=1),h.operation((function(){h.replaceRange(w,{line:b,ch:0},{line:b+(w?0:1),ch:0}),h.replaceRange(v,{line:x,ch:0},{line:x+(v?0:1),ch:0})})),h.setSelection({line:x+(v?1:0),ch:0},{line:b+(v?1:-1),ch:0}),h.focus()}else{var D=c.line;if(i(h.getLineHandle(c.line))&&("fenced"===r(h,c.line+1)?(o=c.line,D=c.line+1):(l=c.line,D=c.line-1)),void 0===o)for(o=D;o>=0;o--)if(f=h.getLineHandle(o),i(f))break;if(void 0===l)for(a=h.lineCount(),l=D;l<a;l++)if(f=h.getLineHandle(l),i(f))break;h.operation((function(){h.replaceRange("",{line:o,ch:0},{line:o+1,ch:0}),h.replaceRange("",{line:l-1,ch:0},{line:l,ch:0})})),h.focus()}else if("indented"===p){if(c.line!==u.line||c.ch!==u.ch)o=c.line,l=u.line,0===u.ch&&l--;else{for(o=c.line;o>=0;o--)if(f=h.getLineHandle(o),!f.text.match(/^\s*$/)&&"indented"!==r(h,o,f)){o+=1;break}for(a=h.lineCount(),l=c.line;l<a;l++)if(f=h.getLineHandle(l),!f.text.match(/^\s*$/)&&"indented"!==r(h,l,f)){l-=1;break}}var C=h.getLineHandle(l+1),S=C&&h.getTokenAt({line:l+1,ch:C.text.length-1}),A=S&&n(S).indentedCode;A&&h.replaceRange("\n",{line:l+1,ch:0});for(var E=o;E<=l;E++)h.indentLine(E,"subtract");h.focus()}else{var M=c.line===u.line&&c.ch===u.ch&&0===c.ch,B=c.line!==u.line;M||B?s(h,c,u,t):_(h,!1,["`","`"])}}function k(e){var t=e.codemirror;q(t,"quote")}function D(e){var t=e.codemirror;H(t,"smaller")}function C(e){var t=e.codemirror;H(t,"bigger")}function S(e){var t=e.codemirror;H(t,void 0,1)}function A(e){var t=e.codemirror;H(t,void 0,2)}function E(e){var t=e.codemirror;H(t,void 0,3)}function M(e){var t=e.codemirror;q(t,"unordered-list")}function B(e){var t=e.codemirror;q(t,"ordered-list")}function F(e){var t=e.codemirror;W(t)}function T(e){var t=e.codemirror,i=m(t),n=e.options,r="http://";if(n.promptURLs&&(r=prompt(n.promptTexts.link),!r))return!1;_(t,i.link,n.insertTexts.link,r)}function O(e){var t=e.codemirror,i=m(t),n=e.options,r="http://";if(n.promptURLs&&(r=prompt(n.promptTexts.image),!r))return!1;_(t,i.image,n.insertTexts.image,r)}function L(e){var t=e.codemirror,i=m(t),n=e.options;_(t,i.table,n.insertTexts.table)}function R(e){var t=e.codemirror,i=m(t),n=e.options;_(t,i.image,n.insertTexts.horizontalRule)}function I(e){var t=e.codemirror;t.undo(),t.focus()}function N(e){var t=e.codemirror;t.redo(),t.focus()}function P(e){var t=e.codemirror,i=t.getWrapperElement(),n=i.nextSibling,r=e.toolbarElements["side-by-side"],s=!1;/editor-preview-active-side/.test(n.className)?(n.className=n.className.replace(/\s*editor-preview-active-side\s*/g,""),r.className=r.className.replace(/\s*active\s*/g,""),i.className=i.className.replace(/\s*CodeMirror-sided\s*/g," ")):(setTimeout((function(){t.getOption("fullScreen")||v(e),n.className+=" editor-preview-active-side"}),1),r.className+=" active",i.className+=" CodeMirror-sided",s=!0);var o=i.lastChild;if(/editor-preview-active/.test(o.className)){o.className=o.className.replace(/\s*editor-preview-active\s*/g,"");var l=e.toolbarElements.preview,a=i.previousSibling;l.className=l.className.replace(/\s*active\s*/g,""),a.className=a.className.replace(/\s*disabled-for-preview*/g,"")}var h=function(){n.innerHTML=e.options.previewRender(e.value(),n)};t.sideBySideRenderingFunction||(t.sideBySideRenderingFunction=h),s?(n.innerHTML=e.options.previewRender(e.value(),n),t.on("update",t.sideBySideRenderingFunction)):t.off("update",t.sideBySideRenderingFunction),t.refresh()}function z(e){var t=e.codemirror,i=t.getWrapperElement(),n=i.previousSibling,r=!!e.options.toolbar&&e.toolbarElements.preview,s=i.lastChild;s&&/editor-preview/.test(s.className)||(s=document.createElement("div"),s.className="editor-preview",i.appendChild(s)),/editor-preview-active/.test(s.className)?(s.className=s.className.replace(/\s*editor-preview-active\s*/g,""),r&&(r.className=r.className.replace(/\s*active\s*/g,""),n.className=n.className.replace(/\s*disabled-for-preview*/g,""))):(setTimeout((function(){s.className+=" editor-preview-active"}),1),r&&(r.className+=" active",n.className+=" disabled-for-preview")),s.innerHTML=e.options.previewRender(e.value(),s);var o=t.getWrapperElement().nextSibling;/editor-preview-active-side/.test(o.className)&&P(e)}function _(e,t,i,n){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){var r,s=i[0],o=i[1],l=e.getCursor("start"),a=e.getCursor("end");n&&(o=o.replace("#url#",n)),t?(r=e.getLine(l.line),s=r.slice(0,l.ch),o=r.slice(l.ch),e.replaceRange(s+o,{line:l.line,ch:0})):(r=e.getSelection(),e.replaceSelection(s+r+o),l.ch+=s.length,l!==a&&(a.ch+=s.length)),e.setSelection(l,a),e.focus()}}function H(e,t,i){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){for(var n=e.getCursor("start"),r=e.getCursor("end"),s=n.line;s<=r.line;s++)(function(n){var r=e.getLine(n),s=r.search(/[^#]/);r=void 0!==t?s<=0?"bigger"==t?"###### "+r:"# "+r:6==s&&"smaller"==t?r.substr(7):1==s&&"bigger"==t?r.substr(2):"bigger"==t?r.substr(1):"#"+r:1==i?s<=0?"# "+r:s==i?r.substr(s+1):"# "+r.substr(s+1):2==i?s<=0?"## "+r:s==i?r.substr(s+1):"## "+r.substr(s+1):s<=0?"### "+r:s==i?r.substr(s+1):"### "+r.substr(s+1),e.replaceRange(r,{line:n,ch:0},{line:n,ch:99999999999999})})(s);e.focus()}}function q(e,t){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){for(var i=m(e),n=e.getCursor("start"),r=e.getCursor("end"),s={quote:/^(\s*)\>\s+/,"unordered-list":/^(\s*)(\*|\-|\+)\s+/,"ordered-list":/^(\s*)\d+\.\s+/},o={quote:"> ","unordered-list":"* ","ordered-list":"1. "},l=n.line;l<=r.line;l++)(function(n){var r=e.getLine(n);r=i[t]?r.replace(s[t],"$1"):o[t]+r,e.replaceRange(r,{line:n,ch:0},{line:n,ch:99999999999999})})(l);e.focus()}}function V(e,t,i,n){if(!/editor-preview-active/.test(e.codemirror.getWrapperElement().lastChild.className)){n="undefined"===typeof n?i:n;var r,s=e.codemirror,o=m(s),l=i,a=n,h=s.getCursor("start"),c=s.getCursor("end");o[t]?(r=s.getLine(h.line),l=r.slice(0,h.ch),a=r.slice(h.ch),"bold"==t?(l=l.replace(/(\*\*|__)(?![\s\S]*(\*\*|__))/,""),a=a.replace(/(\*\*|__)/,"")):"italic"==t?(l=l.replace(/(\*|_)(?![\s\S]*(\*|_))/,""),a=a.replace(/(\*|_)/,"")):"strikethrough"==t&&(l=l.replace(/(\*\*|~~)(?![\s\S]*(\*\*|~~))/,""),a=a.replace(/(\*\*|~~)/,"")),s.replaceRange(l+a,{line:h.line,ch:0},{line:h.line,ch:99999999999999}),"bold"==t||"strikethrough"==t?(h.ch-=2,h!==c&&(c.ch-=2)):"italic"==t&&(h.ch-=1,h!==c&&(c.ch-=1))):(r=s.getSelection(),"bold"==t?(r=r.split("**").join(""),r=r.split("__").join("")):"italic"==t?(r=r.split("*").join(""),r=r.split("_").join("")):"strikethrough"==t&&(r=r.split("~~").join("")),s.replaceSelection(l+r+a),h.ch+=i.length,c.ch=h.ch+r.length),s.setSelection(h,c),s.focus()}}function W(e){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className))for(var t,i=e.getCursor("start"),n=e.getCursor("end"),r=i.line;r<=n.line;r++)t=e.getLine(r),t=t.replace(/^[ ]*([# ]+|\*|\-|[> ]+|[0-9]+(.|\)))[ ]*/,""),e.replaceRange(t,{line:r,ch:0},{line:r,ch:99999999999999})}function j(e,t){for(var i in t)t.hasOwnProperty(i)&&(t[i]instanceof Array?e[i]=t[i].concat(e[i]instanceof Array?e[i]:[]):null!==t[i]&&"object"===typeof t[i]&&t[i].constructor===Object?e[i]=j(e[i]||{},t[i]):e[i]=t[i]);return e}function $(e){for(var t=1;t<arguments.length;t++)e=j(e,arguments[t]);return e}function U(e){var t=/[a-zA-Z0-9_\u0392-\u03c9\u0410-\u04F9]+|[\u4E00-\u9FFF\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\uac00-\ud7af]+/g,i=e.match(t),n=0;if(null===i)return n;for(var r=0;r<i.length;r++)i[r].charCodeAt(0)>=19968?n+=i[r].length:n+=1;return n}var G={bold:{name:"bold",action:x,className:"fa fa-bold",title:"Bold",default:!0},italic:{name:"italic",action:w,className:"fa fa-italic",title:"Italic",default:!0},strikethrough:{name:"strikethrough",action:b,className:"fa fa-strikethrough",title:"Strikethrough"},heading:{name:"heading",action:D,className:"fa fa-header",title:"Heading",default:!0},"heading-smaller":{name:"heading-smaller",action:D,className:"fa fa-header fa-header-x fa-header-smaller",title:"Smaller Heading"},"heading-bigger":{name:"heading-bigger",action:C,className:"fa fa-header fa-header-x fa-header-bigger",title:"Bigger Heading"},"heading-1":{name:"heading-1",action:S,className:"fa fa-header fa-header-x fa-header-1",title:"Big Heading"},"heading-2":{name:"heading-2",action:A,className:"fa fa-header fa-header-x fa-header-2",title:"Medium Heading"},"heading-3":{name:"heading-3",action:E,className:"fa fa-header fa-header-x fa-header-3",title:"Small Heading"},"separator-1":{name:"separator-1"},code:{name:"code",action:y,className:"fa fa-code",title:"Code"},quote:{name:"quote",action:k,className:"fa fa-quote-left",title:"Quote",default:!0},"unordered-list":{name:"unordered-list",action:M,className:"fa fa-list-ul",title:"Generic List",default:!0},"ordered-list":{name:"ordered-list",action:B,className:"fa fa-list-ol",title:"Numbered List",default:!0},"clean-block":{name:"clean-block",action:F,className:"fa fa-eraser fa-clean-block",title:"Clean block"},"separator-2":{name:"separator-2"},link:{name:"link",action:T,className:"fa fa-link",title:"Create Link",default:!0},image:{name:"image",action:O,className:"fa fa-picture-o",title:"Insert Image",default:!0},table:{name:"table",action:L,className:"fa fa-table",title:"Insert Table"},"horizontal-rule":{name:"horizontal-rule",action:R,className:"fa fa-minus",title:"Insert Horizontal Line"},"separator-3":{name:"separator-3"},preview:{name:"preview",action:z,className:"fa fa-eye no-disable",title:"Toggle Preview",default:!0},"side-by-side":{name:"side-by-side",action:P,className:"fa fa-columns no-disable no-mobile",title:"Toggle Side by Side",default:!0},fullscreen:{name:"fullscreen",action:v,className:"fa fa-arrows-alt no-disable no-mobile",title:"Toggle Fullscreen",default:!0},"separator-4":{name:"separator-4"},guide:{name:"guide",action:"https://simplemde.com/markdown-guide",className:"fa fa-question-circle",title:"Markdown Guide",default:!0},"separator-5":{name:"separator-5"},undo:{name:"undo",action:I,className:"fa fa-undo no-disable",title:"Undo"},redo:{name:"redo",action:N,className:"fa fa-repeat no-disable",title:"Redo"}},K={link:["[","](#url#)"],image:["![](","#url#)"],table:["","\n\n| Column 1 | Column 2 | Column 3 |\n| -------- | -------- | -------- |\n| Text     | Text     | Text     |\n\n"],horizontalRule:["","\n\n-----\n\n"]},J={link:"URL for the link:",image:"URL of the image:"},X={bold:"**",code:"```",italic:"*"};function Z(e){e=e||{},e.parent=this;var t=!0;if(!1===e.autoDownloadFontAwesome&&(t=!1),!0!==e.autoDownloadFontAwesome)for(var i=document.styleSheets,n=0;n<i.length;n++)i[n].href&&i[n].href.indexOf("//maxcdn.bootstrapcdn.com/font-awesome/")>-1&&(t=!1);if(t){var r=document.createElement("link");r.rel="stylesheet",r.href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css",document.getElementsByTagName("head")[0].appendChild(r)}if(e.element)this.element=e.element;else if(null===e.element)return void console.log("SimpleMDE: Error. No element was found.");if(void 0===e.toolbar)for(var s in e.toolbar=[],G)G.hasOwnProperty(s)&&(-1!=s.indexOf("separator-")&&e.toolbar.push("|"),(!0===G[s].default||e.showIcons&&e.showIcons.constructor===Array&&-1!=e.showIcons.indexOf(s))&&e.toolbar.push(s));e.hasOwnProperty("status")||(e.status=["autosave","lines","words","cursor"]),e.previewRender||(e.previewRender=function(e){return this.parent.markdown(e)}),e.parsingConfig=$({highlightFormatting:!0},e.parsingConfig||{}),e.insertTexts=$({},K,e.insertTexts||{}),e.promptTexts=J,e.blockStyles=$({},X,e.blockStyles||{}),e.shortcuts=$({},a,e.shortcuts||{}),void 0!=e.autosave&&void 0!=e.autosave.unique_id&&""!=e.autosave.unique_id&&(e.autosave.uniqueId=e.autosave.unique_id),this.options=e,this.render(),!e.initialValue||this.options.autosave&&!0===this.options.autosave.foundSavedValue||this.value(e.initialValue)}function Q(){if("object"!==typeof localStorage)return!1;try{localStorage.setItem("smde_localStorage",1),localStorage.removeItem("smde_localStorage")}catch(e){return!1}return!0}Z.prototype.markdown=function(e){if(s){var t={};return this.options&&this.options.renderingConfig&&!1===this.options.renderingConfig.singleLineBreaks?t.breaks=!1:t.breaks=!0,this.options&&this.options.renderingConfig&&!0===this.options.renderingConfig.codeSyntaxHighlighting&&window.hljs&&(t.highlight=function(e){return window.hljs.highlightAuto(e).value}),s.setOptions(t),s(e)}},Z.prototype.render=function(e){if(e||(e=this.element||document.getElementsByTagName("textarea")[0]),!this._rendered||this._rendered!==e){this.element=e;var t,i,s=this.options,o=this,a={};for(var h in s.shortcuts)null!==s.shortcuts[h]&&null!==l[h]&&function(e){a[u(s.shortcuts[e])]=function(){l[e](o)}}(h);if(a["Enter"]="newlineAndIndentContinueMarkdownList",a["Tab"]="tabAndIndentMarkdownList",a["Shift-Tab"]="shiftTabAndUnindentMarkdownList",a["Esc"]=function(e){e.getOption("fullScreen")&&v(o)},document.addEventListener("keydown",(function(e){e=e||window.event,27==e.keyCode&&o.codemirror.getOption("fullScreen")&&v(o)}),!1),!1!==s.spellChecker?(t="spell-checker",i=s.parsingConfig,i.name="gfm",i.gitHubSpice=!1,r({codeMirrorInstance:n})):(t=s.parsingConfig,t.name="gfm",t.gitHubSpice=!1),this.codemirror=n.fromTextArea(e,{mode:t,backdrop:i,theme:"paper",tabSize:void 0!=s.tabSize?s.tabSize:2,indentUnit:void 0!=s.tabSize?s.tabSize:2,indentWithTabs:!1!==s.indentWithTabs,lineNumbers:!1,autofocus:!0===s.autofocus,extraKeys:a,lineWrapping:!1!==s.lineWrapping,allowDropFileTypes:["text/plain"],placeholder:s.placeholder||e.getAttribute("placeholder")||"",styleSelectedText:void 0==s.styleSelectedText||s.styleSelectedText}),!0===s.forceSync){var c=this.codemirror;c.on("change",(function(){c.save()}))}this.gui={},!1!==s.toolbar&&(this.gui.toolbar=this.createToolbar()),!1!==s.status&&(this.gui.statusbar=this.createStatusbar()),void 0!=s.autosave&&!0===s.autosave.enabled&&this.autosave(),this.gui.sideBySide=this.createSideBySide(),this._rendered=this.element;var d=this.codemirror;setTimeout(function(){d.refresh()}.bind(d),0)}},Z.prototype.autosave=function(){if(Q()){var e=this;if(void 0==this.options.autosave.uniqueId||""==this.options.autosave.uniqueId)return void console.log("SimpleMDE: You must set a uniqueId to use the autosave feature");null!=e.element.form&&void 0!=e.element.form&&e.element.form.addEventListener("submit",(function(){localStorage.removeItem("smde_"+e.options.autosave.uniqueId)})),!0!==this.options.autosave.loaded&&("string"==typeof localStorage.getItem("smde_"+this.options.autosave.uniqueId)&&""!=localStorage.getItem("smde_"+this.options.autosave.uniqueId)&&(this.codemirror.setValue(localStorage.getItem("smde_"+this.options.autosave.uniqueId)),this.options.autosave.foundSavedValue=!0),this.options.autosave.loaded=!0),localStorage.setItem("smde_"+this.options.autosave.uniqueId,e.value());var t=document.getElementById("autosaved");if(null!=t&&void 0!=t&&""!=t){var i=new Date,n=i.getHours(),r=i.getMinutes(),s="am",o=n;o>=12&&(o=n-12,s="pm"),0==o&&(o=12),r=r<10?"0"+r:r,t.innerHTML="Autosaved: "+o+":"+r+" "+s}this.autosaveTimeoutId=setTimeout((function(){e.autosave()}),this.options.autosave.delay||1e4)}else console.log("SimpleMDE: localStorage not available, cannot autosave")},Z.prototype.clearAutosavedValue=function(){if(Q()){if(void 0==this.options.autosave||void 0==this.options.autosave.uniqueId||""==this.options.autosave.uniqueId)return void console.log("SimpleMDE: You must set a uniqueId to clear the autosave value");localStorage.removeItem("smde_"+this.options.autosave.uniqueId)}else console.log("SimpleMDE: localStorage not available, cannot autosave")},Z.prototype.createSideBySide=function(){var e=this.codemirror,t=e.getWrapperElement(),i=t.nextSibling;i&&/editor-preview-side/.test(i.className)||(i=document.createElement("div"),i.className="editor-preview-side",t.parentNode.insertBefore(i,t.nextSibling));var n=!1,r=!1;return e.on("scroll",(function(e){if(n)n=!1;else{r=!0;var t=e.getScrollInfo().height-e.getScrollInfo().clientHeight,s=parseFloat(e.getScrollInfo().top)/t,o=(i.scrollHeight-i.clientHeight)*s;i.scrollTop=o}})),i.onscroll=function(){if(r)r=!1;else{n=!0;var t=i.scrollHeight-i.clientHeight,s=parseFloat(i.scrollTop)/t,o=(e.getScrollInfo().height-e.getScrollInfo().clientHeight)*s;e.scrollTo(0,o)}},i},Z.prototype.createToolbar=function(e){if(e=e||this.options.toolbar,e&&0!==e.length){var t;for(t=0;t<e.length;t++)void 0!=G[e[t]]&&(e[t]=G[e[t]]);var i=document.createElement("div");i.className="editor-toolbar";var n=this,r={};for(n.toolbar=e,t=0;t<e.length;t++)if(("guide"!=e[t].name||!1!==n.options.toolbarGuideIcon)&&(!n.options.hideIcons||-1==n.options.hideIcons.indexOf(e[t].name))&&("fullscreen"!=e[t].name&&"side-by-side"!=e[t].name||!c())){if("|"===e[t]){for(var s=!1,o=t+1;o<e.length;o++)"|"===e[o]||n.options.hideIcons&&-1!=n.options.hideIcons.indexOf(e[o].name)||(s=!0);if(!s)continue}(function(e){var t;t="|"===e?f():d(e,n.options.toolbarTips,n.options.shortcuts),e.action&&("function"===typeof e.action?t.onclick=function(t){t.preventDefault(),e.action(n)}:"string"===typeof e.action&&(t.href=e.action,t.target="_blank")),r[e.name||e]=t,i.appendChild(t)})(e[t])}n.toolbarElements=r;var l=this.codemirror;l.on("cursorActivity",(function(){var e=m(l);for(var t in r)(function(t){var i=r[t];e[t]?i.className+=" active":"fullscreen"!=t&&"side-by-side"!=t&&(i.className=i.className.replace(/\s*active\s*/g,""))})(t)}));var a=l.getWrapperElement();return a.parentNode.insertBefore(i,a),i}},Z.prototype.createStatusbar=function(e){e=e||this.options.status;var t=this.options,i=this.codemirror;if(e&&0!==e.length){var n,r,s,o=[];for(n=0;n<e.length;n++)if(r=void 0,s=void 0,"object"===typeof e[n])o.push({className:e[n].className,defaultValue:e[n].defaultValue,onUpdate:e[n].onUpdate});else{var l=e[n];"words"===l?(s=function(e){e.innerHTML=U(i.getValue())},r=function(e){e.innerHTML=U(i.getValue())}):"lines"===l?(s=function(e){e.innerHTML=i.lineCount()},r=function(e){e.innerHTML=i.lineCount()}):"cursor"===l?(s=function(e){e.innerHTML="0:0"},r=function(e){var t=i.getCursor();e.innerHTML=t.line+":"+t.ch}):"autosave"===l&&(s=function(e){void 0!=t.autosave&&!0===t.autosave.enabled&&e.setAttribute("id","autosaved")}),o.push({className:l,defaultValue:s,onUpdate:r})}var a=document.createElement("div");for(a.className="editor-statusbar",n=0;n<o.length;n++){var h=o[n],c=document.createElement("span");c.className=h.className,"function"===typeof h.defaultValue&&h.defaultValue(c),"function"===typeof h.onUpdate&&this.codemirror.on("update",function(e,t){return function(){t.onUpdate(e)}}(c,h)),a.appendChild(c)}var u=this.codemirror.getWrapperElement();return u.parentNode.insertBefore(a,u.nextSibling),a}},Z.prototype.value=function(e){return void 0===e?this.codemirror.getValue():(this.codemirror.getDoc().setValue(e),this)},Z.toggleBold=x,Z.toggleItalic=w,Z.toggleStrikethrough=b,Z.toggleBlockquote=k,Z.toggleHeadingSmaller=D,Z.toggleHeadingBigger=C,Z.toggleHeading1=S,Z.toggleHeading2=A,Z.toggleHeading3=E,Z.toggleCodeBlock=y,Z.toggleUnorderedList=M,Z.toggleOrderedList=B,Z.cleanBlock=F,Z.drawLink=T,Z.drawImage=O,Z.drawTable=L,Z.drawHorizontalRule=R,Z.undo=I,Z.redo=N,Z.togglePreview=z,Z.toggleSideBySide=P,Z.toggleFullScreen=v,Z.prototype.toggleBold=function(){x(this)},Z.prototype.toggleItalic=function(){w(this)},Z.prototype.toggleStrikethrough=function(){b(this)},Z.prototype.toggleBlockquote=function(){k(this)},Z.prototype.toggleHeadingSmaller=function(){D(this)},Z.prototype.toggleHeadingBigger=function(){C(this)},Z.prototype.toggleHeading1=function(){S(this)},Z.prototype.toggleHeading2=function(){A(this)},Z.prototype.toggleHeading3=function(){E(this)},Z.prototype.toggleCodeBlock=function(){y(this)},Z.prototype.toggleUnorderedList=function(){M(this)},Z.prototype.toggleOrderedList=function(){B(this)},Z.prototype.cleanBlock=function(){F(this)},Z.prototype.drawLink=function(){T(this)},Z.prototype.drawImage=function(){O(this)},Z.prototype.drawTable=function(){L(this)},Z.prototype.drawHorizontalRule=function(){R(this)},Z.prototype.undo=function(){I(this)},Z.prototype.redo=function(){N(this)},Z.prototype.togglePreview=function(){z(this)},Z.prototype.toggleSideBySide=function(){P(this)},Z.prototype.toggleFullScreen=function(){v(this)},Z.prototype.isPreviewActive=function(){var e=this.codemirror,t=e.getWrapperElement(),i=t.lastChild;return/editor-preview-active/.test(i.className)},Z.prototype.isSideBySideActive=function(){var e=this.codemirror,t=e.getWrapperElement(),i=t.nextSibling;return/editor-preview-active-side/.test(i.className)},Z.prototype.isFullscreenActive=function(){var e=this.codemirror;return e.getOption("fullScreen")},Z.prototype.getState=function(){var e=this.codemirror;return m(e)},Z.prototype.toTextArea=function(){var e=this.codemirror,t=e.getWrapperElement();t.parentNode&&(this.gui.toolbar&&t.parentNode.removeChild(this.gui.toolbar),this.gui.statusbar&&t.parentNode.removeChild(this.gui.statusbar),this.gui.sideBySide&&t.parentNode.removeChild(this.gui.sideBySide)),e.toTextArea(),this.autosaveTimeoutId&&(clearTimeout(this.autosaveTimeoutId),this.autosaveTimeoutId=void 0,this.clearAutosavedValue())},e.exports=Z},f040:function(e,t,i){(function(e){e(i("56b3"))})((function(e){"use strict";e.modeInfo=[{name:"APL",mime:"text/apl",mode:"apl",ext:["dyalog","apl"]},{name:"PGP",mimes:["application/pgp","application/pgp-keys","application/pgp-signature"],mode:"asciiarmor",ext:["pgp"]},{name:"ASN.1",mime:"text/x-ttcn-asn",mode:"asn.1",ext:["asn","asn1"]},{name:"Asterisk",mime:"text/x-asterisk",mode:"asterisk",file:/^extensions\.conf$/i},{name:"Brainfuck",mime:"text/x-brainfuck",mode:"brainfuck",ext:["b","bf"]},{name:"C",mime:"text/x-csrc",mode:"clike",ext:["c","h"]},{name:"C++",mime:"text/x-c++src",mode:"clike",ext:["cpp","c++","cc","cxx","hpp","h++","hh","hxx"],alias:["cpp"]},{name:"Cobol",mime:"text/x-cobol",mode:"cobol",ext:["cob","cpy"]},{name:"C#",mime:"text/x-csharp",mode:"clike",ext:["cs"],alias:["csharp"]},{name:"Clojure",mime:"text/x-clojure",mode:"clojure",ext:["clj","cljc","cljx"]},{name:"ClojureScript",mime:"text/x-clojurescript",mode:"clojure",ext:["cljs"]},{name:"Closure Stylesheets (GSS)",mime:"text/x-gss",mode:"css",ext:["gss"]},{name:"CMake",mime:"text/x-cmake",mode:"cmake",ext:["cmake","cmake.in"],file:/^CMakeLists.txt$/},{name:"CoffeeScript",mime:"text/x-coffeescript",mode:"coffeescript",ext:["coffee"],alias:["coffee","coffee-script"]},{name:"Common Lisp",mime:"text/x-common-lisp",mode:"commonlisp",ext:["cl","lisp","el"],alias:["lisp"]},{name:"Cypher",mime:"application/x-cypher-query",mode:"cypher",ext:["cyp","cypher"]},{name:"Cython",mime:"text/x-cython",mode:"python",ext:["pyx","pxd","pxi"]},{name:"Crystal",mime:"text/x-crystal",mode:"crystal",ext:["cr"]},{name:"CSS",mime:"text/css",mode:"css",ext:["css"]},{name:"CQL",mime:"text/x-cassandra",mode:"sql",ext:["cql"]},{name:"D",mime:"text/x-d",mode:"d",ext:["d"]},{name:"Dart",mimes:["application/dart","text/x-dart"],mode:"dart",ext:["dart"]},{name:"diff",mime:"text/x-diff",mode:"diff",ext:["diff","patch"]},{name:"Django",mime:"text/x-django",mode:"django"},{name:"Dockerfile",mime:"text/x-dockerfile",mode:"dockerfile",file:/^Dockerfile$/},{name:"DTD",mime:"application/xml-dtd",mode:"dtd",ext:["dtd"]},{name:"Dylan",mime:"text/x-dylan",mode:"dylan",ext:["dylan","dyl","intr"]},{name:"EBNF",mime:"text/x-ebnf",mode:"ebnf"},{name:"ECL",mime:"text/x-ecl",mode:"ecl",ext:["ecl"]},{name:"edn",mime:"application/edn",mode:"clojure",ext:["edn"]},{name:"Eiffel",mime:"text/x-eiffel",mode:"eiffel",ext:["e"]},{name:"Elm",mime:"text/x-elm",mode:"elm",ext:["elm"]},{name:"Embedded Javascript",mime:"application/x-ejs",mode:"htmlembedded",ext:["ejs"]},{name:"Embedded Ruby",mime:"application/x-erb",mode:"htmlembedded",ext:["erb"]},{name:"Erlang",mime:"text/x-erlang",mode:"erlang",ext:["erl"]},{name:"Factor",mime:"text/x-factor",mode:"factor",ext:["factor"]},{name:"FCL",mime:"text/x-fcl",mode:"fcl"},{name:"Forth",mime:"text/x-forth",mode:"forth",ext:["forth","fth","4th"]},{name:"Fortran",mime:"text/x-fortran",mode:"fortran",ext:["f","for","f77","f90"]},{name:"F#",mime:"text/x-fsharp",mode:"mllike",ext:["fs"],alias:["fsharp"]},{name:"Gas",mime:"text/x-gas",mode:"gas",ext:["s"]},{name:"Gherkin",mime:"text/x-feature",mode:"gherkin",ext:["feature"]},{name:"GitHub Flavored Markdown",mime:"text/x-gfm",mode:"gfm",file:/^(readme|contributing|history).md$/i},{name:"Go",mime:"text/x-go",mode:"go",ext:["go"]},{name:"Groovy",mime:"text/x-groovy",mode:"groovy",ext:["groovy","gradle"],file:/^Jenkinsfile$/},{name:"HAML",mime:"text/x-haml",mode:"haml",ext:["haml"]},{name:"Haskell",mime:"text/x-haskell",mode:"haskell",ext:["hs"]},{name:"Haskell (Literate)",mime:"text/x-literate-haskell",mode:"haskell-literate",ext:["lhs"]},{name:"Haxe",mime:"text/x-haxe",mode:"haxe",ext:["hx"]},{name:"HXML",mime:"text/x-hxml",mode:"haxe",ext:["hxml"]},{name:"ASP.NET",mime:"application/x-aspx",mode:"htmlembedded",ext:["aspx"],alias:["asp","aspx"]},{name:"HTML",mime:"text/html",mode:"htmlmixed",ext:["html","htm"],alias:["xhtml"]},{name:"HTTP",mime:"message/http",mode:"http"},{name:"IDL",mime:"text/x-idl",mode:"idl",ext:["pro"]},{name:"Pug",mime:"text/x-pug",mode:"pug",ext:["jade","pug"],alias:["jade"]},{name:"Java",mime:"text/x-java",mode:"clike",ext:["java"]},{name:"Java Server Pages",mime:"application/x-jsp",mode:"htmlembedded",ext:["jsp"],alias:["jsp"]},{name:"JavaScript",mimes:["text/javascript","text/ecmascript","application/javascript","application/x-javascript","application/ecmascript"],mode:"javascript",ext:["js"],alias:["ecmascript","js","node"]},{name:"JSON",mimes:["application/json","application/x-json"],mode:"javascript",ext:["json","map"],alias:["json5"]},{name:"JSON-LD",mime:"application/ld+json",mode:"javascript",ext:["jsonld"],alias:["jsonld"]},{name:"JSX",mime:"text/jsx",mode:"jsx",ext:["jsx"]},{name:"Jinja2",mime:"null",mode:"jinja2"},{name:"Julia",mime:"text/x-julia",mode:"julia",ext:["jl"]},{name:"Kotlin",mime:"text/x-kotlin",mode:"clike",ext:["kt"]},{name:"LESS",mime:"text/x-less",mode:"css",ext:["less"]},{name:"LiveScript",mime:"text/x-livescript",mode:"livescript",ext:["ls"],alias:["ls"]},{name:"Lua",mime:"text/x-lua",mode:"lua",ext:["lua"]},{name:"Markdown",mime:"text/x-markdown",mode:"markdown",ext:["markdown","md","mkd"]},{name:"mIRC",mime:"text/mirc",mode:"mirc"},{name:"MariaDB SQL",mime:"text/x-mariadb",mode:"sql"},{name:"Mathematica",mime:"text/x-mathematica",mode:"mathematica",ext:["m","nb"]},{name:"Modelica",mime:"text/x-modelica",mode:"modelica",ext:["mo"]},{name:"MUMPS",mime:"text/x-mumps",mode:"mumps",ext:["mps"]},{name:"MS SQL",mime:"text/x-mssql",mode:"sql"},{name:"mbox",mime:"application/mbox",mode:"mbox",ext:["mbox"]},{name:"MySQL",mime:"text/x-mysql",mode:"sql"},{name:"Nginx",mime:"text/x-nginx-conf",mode:"nginx",file:/nginx.*\.conf$/i},{name:"NSIS",mime:"text/x-nsis",mode:"nsis",ext:["nsh","nsi"]},{name:"NTriples",mime:"text/n-triples",mode:"ntriples",ext:["nt"]},{name:"Objective C",mime:"text/x-objectivec",mode:"clike",ext:["m","mm"],alias:["objective-c","objc"]},{name:"OCaml",mime:"text/x-ocaml",mode:"mllike",ext:["ml","mli","mll","mly"]},{name:"Octave",mime:"text/x-octave",mode:"octave",ext:["m"]},{name:"Oz",mime:"text/x-oz",mode:"oz",ext:["oz"]},{name:"Pascal",mime:"text/x-pascal",mode:"pascal",ext:["p","pas"]},{name:"PEG.js",mime:"null",mode:"pegjs",ext:["jsonld"]},{name:"Perl",mime:"text/x-perl",mode:"perl",ext:["pl","pm"]},{name:"PHP",mime:"application/x-httpd-php",mode:"php",ext:["php","php3","php4","php5","phtml"]},{name:"Pig",mime:"text/x-pig",mode:"pig",ext:["pig"]},{name:"Plain Text",mime:"text/plain",mode:"null",ext:["txt","text","conf","def","list","log"]},{name:"PLSQL",mime:"text/x-plsql",mode:"sql",ext:["pls"]},{name:"PowerShell",mime:"application/x-powershell",mode:"powershell",ext:["ps1","psd1","psm1"]},{name:"Properties files",mime:"text/x-properties",mode:"properties",ext:["properties","ini","in"],alias:["ini","properties"]},{name:"ProtoBuf",mime:"text/x-protobuf",mode:"protobuf",ext:["proto"]},{name:"Python",mime:"text/x-python",mode:"python",ext:["BUILD","bzl","py","pyw"],file:/^(BUCK|BUILD)$/},{name:"Puppet",mime:"text/x-puppet",mode:"puppet",ext:["pp"]},{name:"Q",mime:"text/x-q",mode:"q",ext:["q"]},{name:"R",mime:"text/x-rsrc",mode:"r",ext:["r","R"],alias:["rscript"]},{name:"reStructuredText",mime:"text/x-rst",mode:"rst",ext:["rst"],alias:["rst"]},{name:"RPM Changes",mime:"text/x-rpm-changes",mode:"rpm"},{name:"RPM Spec",mime:"text/x-rpm-spec",mode:"rpm",ext:["spec"]},{name:"Ruby",mime:"text/x-ruby",mode:"ruby",ext:["rb"],alias:["jruby","macruby","rake","rb","rbx"]},{name:"Rust",mime:"text/x-rustsrc",mode:"rust",ext:["rs"]},{name:"SAS",mime:"text/x-sas",mode:"sas",ext:["sas"]},{name:"Sass",mime:"text/x-sass",mode:"sass",ext:["sass"]},{name:"Scala",mime:"text/x-scala",mode:"clike",ext:["scala"]},{name:"Scheme",mime:"text/x-scheme",mode:"scheme",ext:["scm","ss"]},{name:"SCSS",mime:"text/x-scss",mode:"css",ext:["scss"]},{name:"Shell",mime:"text/x-sh",mode:"shell",ext:["sh","ksh","bash"],alias:["bash","sh","zsh"],file:/^PKGBUILD$/},{name:"Sieve",mime:"application/sieve",mode:"sieve",ext:["siv","sieve"]},{name:"Slim",mimes:["text/x-slim","application/x-slim"],mode:"slim",ext:["slim"]},{name:"Smalltalk",mime:"text/x-stsrc",mode:"smalltalk",ext:["st"]},{name:"Smarty",mime:"text/x-smarty",mode:"smarty",ext:["tpl"]},{name:"Solr",mime:"text/x-solr",mode:"solr"},{name:"Soy",mime:"text/x-soy",mode:"soy",ext:["soy"],alias:["closure template"]},{name:"SPARQL",mime:"application/sparql-query",mode:"sparql",ext:["rq","sparql"],alias:["sparul"]},{name:"Spreadsheet",mime:"text/x-spreadsheet",mode:"spreadsheet",alias:["excel","formula"]},{name:"SQL",mime:"text/x-sql",mode:"sql",ext:["sql"]},{name:"SQLite",mime:"text/x-sqlite",mode:"sql"},{name:"Squirrel",mime:"text/x-squirrel",mode:"clike",ext:["nut"]},{name:"Stylus",mime:"text/x-styl",mode:"stylus",ext:["styl"]},{name:"Swift",mime:"text/x-swift",mode:"swift",ext:["swift"]},{name:"sTeX",mime:"text/x-stex",mode:"stex"},{name:"LaTeX",mime:"text/x-latex",mode:"stex",ext:["text","ltx"],alias:["tex"]},{name:"SystemVerilog",mime:"text/x-systemverilog",mode:"verilog",ext:["v"]},{name:"Tcl",mime:"text/x-tcl",mode:"tcl",ext:["tcl"]},{name:"Textile",mime:"text/x-textile",mode:"textile",ext:["textile"]},{name:"TiddlyWiki ",mime:"text/x-tiddlywiki",mode:"tiddlywiki"},{name:"Tiki wiki",mime:"text/tiki",mode:"tiki"},{name:"TOML",mime:"text/x-toml",mode:"toml",ext:["toml"]},{name:"Tornado",mime:"text/x-tornado",mode:"tornado"},{name:"troff",mime:"text/troff",mode:"troff",ext:["1","2","3","4","5","6","7","8","9"]},{name:"TTCN",mime:"text/x-ttcn",mode:"ttcn",ext:["ttcn","ttcn3","ttcnpp"]},{name:"TTCN_CFG",mime:"text/x-ttcn-cfg",mode:"ttcn-cfg",ext:["cfg"]},{name:"Turtle",mime:"text/turtle",mode:"turtle",ext:["ttl"]},{name:"TypeScript",mime:"application/typescript",mode:"javascript",ext:["ts"],alias:["ts"]},{name:"TypeScript-JSX",mime:"text/typescript-jsx",mode:"jsx",ext:["tsx"],alias:["tsx"]},{name:"Twig",mime:"text/x-twig",mode:"twig"},{name:"Web IDL",mime:"text/x-webidl",mode:"webidl",ext:["webidl"]},{name:"VB.NET",mime:"text/x-vb",mode:"vb",ext:["vb"]},{name:"VBScript",mime:"text/vbscript",mode:"vbscript",ext:["vbs"]},{name:"Velocity",mime:"text/velocity",mode:"velocity",ext:["vtl"]},{name:"Verilog",mime:"text/x-verilog",mode:"verilog",ext:["v"]},{name:"VHDL",mime:"text/x-vhdl",mode:"vhdl",ext:["vhd","vhdl"]},{name:"Vue.js Component",mimes:["script/x-vue","text/x-vue"],mode:"vue",ext:["vue"]},{name:"XML",mimes:["application/xml","text/xml"],mode:"xml",ext:["xml","xsl","xsd","svg"],alias:["rss","wsdl","xsd"]},{name:"XQuery",mime:"application/xquery",mode:"xquery",ext:["xy","xquery"]},{name:"Yacas",mime:"text/x-yacas",mode:"yacas",ext:["ys"]},{name:"YAML",mimes:["text/x-yaml","text/yaml"],mode:"yaml",ext:["yaml","yml"],alias:["yml"]},{name:"Z80",mime:"text/x-z80",mode:"z80",ext:["z80"]},{name:"mscgen",mime:"text/x-mscgen",mode:"mscgen",ext:["mscgen","mscin","msc"]},{name:"xu",mime:"text/x-xu",mode:"mscgen",ext:["xu"]},{name:"msgenny",mime:"text/x-msgenny",mode:"mscgen",ext:["msgenny"]}];for(var t=0;t<e.modeInfo.length;t++){var i=e.modeInfo[t];i.mimes&&(i.mime=i.mimes[0])}e.findModeByMIME=function(t){t=t.toLowerCase();for(var i=0;i<e.modeInfo.length;i++){var n=e.modeInfo[i];if(n.mime==t)return n;if(n.mimes)for(var r=0;r<n.mimes.length;r++)if(n.mimes[r]==t)return n}return/\+xml$/.test(t)?e.findModeByMIME("application/xml"):/\+json$/.test(t)?e.findModeByMIME("application/json"):void 0},e.findModeByExtension=function(t){for(var i=0;i<e.modeInfo.length;i++){var n=e.modeInfo[i];if(n.ext)for(var r=0;r<n.ext.length;r++)if(n.ext[r]==t)return n}},e.findModeByFileName=function(t){for(var i=0;i<e.modeInfo.length;i++){var n=e.modeInfo[i];if(n.file&&n.file.test(t))return n}var r=t.lastIndexOf("."),s=r>-1&&t.substring(r+1,t.length);if(s)return e.findModeByExtension(s)},e.findModeByName=function(t){t=t.toLowerCase();for(var i=0;i<e.modeInfo.length;i++){var n=e.modeInfo[i];if(n.name.toLowerCase()==t)return n;if(n.alias)for(var r=0;r<n.alias.length;r++)if(n.alias[r].toLowerCase()==t)return n}}}))}}]);