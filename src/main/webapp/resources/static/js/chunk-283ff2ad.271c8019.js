(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-283ff2ad"],{"03b2":function(t,e,n){},"28a5":function(t,e,n){"use strict";var r=n("aae3"),i=n("cb7c"),a=n("ebd6"),s=n("0390"),l=n("9def"),o=n("5f1b"),u=n("520a"),c=n("79e5"),d=Math.min,f=[].push,p="split",m="length",h="lastIndex",v=4294967295,g=!c((function(){RegExp(v,"y")}));n("214f")("split",2,(function(t,e,n,c){var b;return b="c"=="abbc"[p](/(b)*/)[1]||4!="test"[p](/(?:)/,-1)[m]||2!="ab"[p](/(?:ab)*/)[m]||4!="."[p](/(.?)(.?)/)[m]||"."[p](/()()/)[m]>1||""[p](/.?/)[m]?function(t,e){var i=String(this);if(void 0===t&&0===e)return[];if(!r(t))return n.call(i,t,e);var a,s,l,o=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,p=void 0===e?v:e>>>0,g=new RegExp(t.source,c+"g");while(a=u.call(g,i)){if(s=g[h],s>d&&(o.push(i.slice(d,a.index)),a[m]>1&&a.index<i[m]&&f.apply(o,a.slice(1)),l=a[0][m],d=s,o[m]>=p))break;g[h]===a.index&&g[h]++}return d===i[m]?!l&&g.test("")||o.push(""):o.push(i.slice(d)),o[m]>p?o.slice(0,p):o}:"0"[p](void 0,0)[m]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,r){var i=t(this),a=void 0==n?void 0:n[e];return void 0!==a?a.call(n,i,r):b.call(String(i),n,r)},function(t,e){var r=c(b,t,this,e,b!==n);if(r.done)return r.value;var u=i(t),f=String(this),p=a(u,RegExp),m=u.unicode,h=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(g?"y":"g"),y=new p(g?u:"^(?:"+u.source+")",h),w=void 0===e?v:e>>>0;if(0===w)return[];if(0===f.length)return null===o(y,f)?[f]:[];var _=0,x=0,j=[];while(x<f.length){y.lastIndex=g?x:0;var k,O=o(y,g?f:f.slice(x));if(null===O||(k=d(l(y.lastIndex+(g?0:x)),f.length))===_)x=s(f,x,m);else{if(j.push(f.slice(_,x)),j.length===w)return j;for(var S=1;S<=O.length-1;S++)if(j.push(O[S]),j.length===w)return j;x=_=k}}return j.push(f.slice(_)),j}]}))},4917:function(t,e,n){"use strict";var r=n("cb7c"),i=n("9def"),a=n("0390"),s=n("5f1b");n("214f")("match",1,(function(t,e,n,l){return[function(n){var r=t(this),i=void 0==n?void 0:n[e];return void 0!==i?i.call(n,r):new RegExp(n)[e](String(r))},function(t){var e=l(n,t,this);if(e.done)return e.value;var o=r(t),u=String(this);if(!o.global)return s(o,u);var c=o.unicode;o.lastIndex=0;var d,f=[],p=0;while(null!==(d=s(o,u))){var m=String(d[0]);f[p]=m,""===m&&(o.lastIndex=a(u,i(o.lastIndex),c)),p++}return 0===p?null:f}]}))},6724:function(t,e,n){"use strict";n("8d41");var r={bind:function(t,e){t.addEventListener("click",(function(n){var r=Object.assign({},e.value),i=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},r),a=i.ele;if(a){a.style.position="relative",a.style.overflow="hidden";var s=a.getBoundingClientRect(),l=a.querySelector(".waves-ripple");switch(l?l.className="waves-ripple":(l=document.createElement("span"),l.className="waves-ripple",l.style.height=l.style.width=Math.max(s.width,s.height)+"px",a.appendChild(l)),i.type){case"center":l.style.top=s.height/2-l.offsetHeight/2+"px",l.style.left=s.width/2-l.offsetWidth/2+"px";break;default:l.style.top=n.pageY-s.top-l.offsetHeight/2-document.body.scrollTop+"px",l.style.left=n.pageX-s.left-l.offsetWidth/2-document.body.scrollLeft+"px"}return l.style.backgroundColor=i.color,l.className="waves-ripple z-active",!1}}),!1)}},i=function(t){t.directive("waves",r)};window.Vue&&(window.waves=r,Vue.use(i)),r.install=i;e["a"]=r},"8d41":function(t,e,n){},b9a5:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container calendar-list-container"},[n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"请输入角色名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.title,callback:function(e){t.$set(t.listQuery,"title",e)},expression:"listQuery.title"}}),t._v(" "),t._e(),t._v(" "),t._e(),t._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{size:"small",type:"primary",icon:"search"},on:{click:t.handleFilter}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"filter-item el-icon-plus",staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"success"},on:{click:t.handleCreate}},[t._v("添加")]),t._v(" "),t._e(),t._v(" "),t._e()],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],key:t.tableKey,staticStyle:{width:"100%"},attrs:{data:t.list,"element-loading-text":"给我一点时间",stripe:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{align:"center",label:"序号",width:"65px",type:"index"}}),t._v(" "),n("el-table-column",{attrs:{"min-width":"150px",align:"center",label:"名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{on:{click:function(n){return t.handleUpdate(e.row)}}},[t._v(t._s(e.row.name))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"180px",align:"center",label:"时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("parseTime")(e.row.createTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),t._v(" "),t.showPassword?n("el-table-column",{attrs:{width:"110px",align:"center",label:"密码"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.password))])]}}],null,!1,1657276601)}):t._e(),t._v(" "),t.showAuditor?n("el-table-column",{attrs:{width:"110px",align:"center",label:"审核人"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{staticStyle:{color:"red"}},[t._v(t._s(e.row.auditor))])]}}],null,!1,1430385659)}):t._e(),t._v(" "),n("el-table-column",{attrs:{"class-name":"status-col",label:"状态",width:"90px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-tag",{attrs:{type:t._f("statusFilter")(e.row.status)}},[t._v(t._s(e.row.status))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"center",label:"操作",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return["draft"!=e.row.status?n("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(n){return t.handleModifyStatus(e.row,"draft")}}},[t._v("开启\n\t\t\t\t\t")]):t._e(),t._v(" "),"published"!=e.row.status?n("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(n){return t.handleModifyStatus(e.row,"published")}}},[t._v("禁用\n\t\t\t\t\t")]):t._e()]}}])})],1),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:!t.listLoading,expression:"!listLoading"}],staticClass:"pagination-container"},[n("el-pagination",{attrs:{"current-page":t.listQuery.page,"page-sizes":[10,15,20,30],"page-size":t.listQuery.limit,layout:" sizes, prev, pager, next,total, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.listQuery,"page",e)},"update:current-page":function(e){return t.$set(t.listQuery,"page",e)}}})],1),t._v(" "),n("el-dialog",{staticClass:"ma-small",attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[n("el-form",{ref:"temp",staticClass:"small-space",staticStyle:{width:"400px","margin-left":"50px"},attrs:{model:t.temp,rules:t.rules,"label-position":"left","label-width":"70px"}},[n("el-form-item",{staticClass:"ma_role",attrs:{label:"角色名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入角色名称"},model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name",e)},expression:"temp.name"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"权限"}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"330px"},attrs:{multiple:!0,placeholder:"选择权限"},on:{change:t.handleFilter},model:{value:t.permission,callback:function(e){t.permission=e},expression:"permission"}},t._l(t.permissions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"状态"}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"330px"},attrs:{placeholder:"请选择"},model:{value:t.temp.status,callback:function(e){t.$set(t.temp,"status",e)},expression:"temp.status"}},t._l(t.statusOptions,(function(t,e){return n("el-option",{key:t,attrs:{label:t,value:e}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"描述"}},[n("el-input",{attrs:{type:"textarea",autosize:{minRows:4,maxRows:4},placeholder:"请输入内容"},model:{value:t.temp.remark,callback:function(e){t.$set(t.temp,"remark",e)},expression:"temp.remark"}})],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},["create"==t.dialogStatus?n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.create("temp")}}},[t._v("确 定")]):n("el-button",{attrs:{type:"primary"},on:{click:t.update}},[t._v("确 定")]),t._v(" "),n("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")])],1)],1),t._v(" "),n("el-dialog",{staticClass:"ma-smalldel",attrs:{visible:t.userVisible},on:{"update:visible":function(e){t.userVisible=e}}},[n("div",{staticClass:"deltip mb_30"},[t._v("确定删除该角色？")]),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},[n("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.handleRole("deleted")}}},[t._v("删 除")]),t._v(" "),n("el-button",{on:{click:function(e){t.userVisible=!1}}},[t._v("取 消")])],1)])],1)},i=[],a=(n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("c24f")),s=n("6724"),l=n("ed08");function o(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=u(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return s=t.done,t},e:function(t){l=!0,a=t},f:function(){try{s||null==n.return||n.return()}finally{if(l)throw a}}}}function u(t,e){if(t){if("string"===typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var d=[{key:"CN",display_name:"中国"},{key:"US",display_name:"美国"},{key:"JP",display_name:"日本"},{key:"EU",display_name:"欧元区"}],f=d.reduce((function(t,e){return t[e.key]=e.display_name,t}),{}),p={name:"siteManager",directives:{waves:s["a"]},data:function(){return{list:[],total:null,listLoading:!0,listQuery:{page:0,limit:15,importance:void 0,title:void 0,type:void 0,sort:"+id"},temp:{id:void 0,importance:0,remark:"",timestamp:0,username:"",password:"",email:"",phone:"",type:0,status:0,deleteUid:""},rules:{name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:10,message:"长度在 2到 10 个字符",trigger:"blur"}]},permissions:[],permission:"",importanceOptions:[1,2,3],calendarTypeOptions:d,sortOptions:[{label:"按ID升序列",key:"+id"},{label:"按ID降序",key:"-id"}],statusOptions:["启用","禁用"],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"编辑",create:"添加角色"},dialogPvVisible:!1,userVisible:!1,pvData:[],showAuditor:!1,tableKey:0,showPassword:!1}},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]},typeFilter:function(t){return f[t]}},created:function(){this.getList(),this.getPer()},methods:{getList:function(){var t=this;this.listLoading=!0,Object(a["r"])(this.listQuery.page).then((function(e){t.list=e.obj.content,t.total=e.obj.totalElements,t.listLoading=!1}))},getPer:function(){var t=this;Object(a["q"])().then((function(e){t.permissions=e.obj.content}))},handleFilter:function(){this.listQuery.page=0,this.getList()},handleSizeChange:function(t){this.listQuery.limit=t,this.getList()},handleCurrentChange:function(t){this.listQuery.page=t-1,this.getList()},timeFilter:function(t){if(!t[0])return this.listQuery.start=void 0,void(this.listQuery.end=void 0);this.listQuery.start=parseInt(+t[0]/1e3),this.listQuery.end=parseInt((+t[1]+864e5)/1e3)},delRole:function(t){this.userVisible=!0,this.deleteUid=t},handleRole:function(t){this.$message({message:"操作成功",type:"success"}),this.deleteUid.status=t,this.userVisible=!1},handleModifyStatus:function(t,e){this.$message({message:"操作成功",type:"success"}),t.status=e},handleCreate:function(){this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0},handleUpdate:function(t){this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0},handleDelete:function(t){this.$notify({title:"成功",message:"删除成功",type:"success",duration:2e3});var e=this.list.indexOf(t);this.list.splice(e,1)},successTip:function(){this.dialogFormVisible=!1,this.$notify({title:"成功",message:"创建成功",type:"success",duration:2e3})},failTip:function(){this.$notify({title:"失败",message:"添加失败",type:"fail",duration:2e3})},create:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return e.failTip(),!1;Object(a["a"])(e.temp).then((function(t){e.temp.id=parseInt(100*Math.random())+1024,e.temp.timestamp=+new Date,e.list.unshift(e.temp),e.successTip(),e.getList()})).catch((function(t){e.failTip(t)}))}))},update:function(){this.temp.timestamp=+this.temp.timestamp;var t,e=o(this.list);try{for(e.s();!(t=e.n()).done;){var n=t.value;if(n.id===this.temp.id){var r=this.list.indexOf(n);this.list.splice(r,1,this.temp);break}}}catch(i){e.e(i)}finally{e.f()}this.dialogFormVisible=!1,this.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})},resetTemp:function(){this.temp={id:void 0,importance:0,remark:"",timestamp:0,title:"",status:0,type:0}},handleDownload:function(){},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return"timestamp"===t?Object(l["f"])(e[t]):e[t]}))}))}}},m=p,h=(n("dfb6"),n("2877")),v=Object(h["a"])(m,r,i,!1,null,null,null);e["default"]=v.exports},c24f:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"i",(function(){return a})),n.d(e,"l",(function(){return s})),n.d(e,"u",(function(){return l})),n.d(e,"g",(function(){return o})),n.d(e,"r",(function(){return u})),n.d(e,"k",(function(){return c})),n.d(e,"q",(function(){return d})),n.d(e,"a",(function(){return f})),n.d(e,"n",(function(){return p})),n.d(e,"d",(function(){return m})),n.d(e,"s",(function(){return h})),n.d(e,"p",(function(){return v})),n.d(e,"c",(function(){return g})),n.d(e,"j",(function(){return b})),n.d(e,"h",(function(){return y})),n.d(e,"w",(function(){return w})),n.d(e,"t",(function(){return _})),n.d(e,"o",(function(){return x})),n.d(e,"x",(function(){return j})),n.d(e,"m",(function(){return k})),n.d(e,"v",(function(){return O})),n.d(e,"f",(function(){return S})),n.d(e,"e",(function(){return C}));var r=n("1c1e");function i(t){return Object(r["a"])({url:"user/add",method:"post",params:t})}function a(t){return Object(r["a"])({url:"user/edit",method:"post",params:t})}function s(t){return Object(r["a"])({url:"user/bind",method:"post",params:t})}function l(t){return Object(r["a"])({url:"user",method:"post",params:t})}function o(t){return Object(r["a"])({url:"user/del",method:"post",params:{id:t}})}function u(t){return Object(r["a"])({url:"role",method:"post",params:{page:t}})}function c(t,e){return Object(r["a"])({url:"userrole/edit_user_role",method:"post",params:{userId:t,roleIds:e}})}function d(t){return Object(r["a"])({url:"resource/page",method:"post",params:t})}function f(t){return Object(r["a"])({url:"role/add",method:"post",params:t})}function p(t){return Object(r["a"])({url:"user/forbid",method:"post",params:{uid:t}})}function m(t){return Object(r["a"])({url:"user/allow",method:"post",params:{uid:t}})}function h(){return Object(r["a"])({url:"tree/gettree",method:"post"})}function v(t){return Object(r["a"])({url:"tree/getdepartment",method:"post",params:{sid:t}})}function g(t){return Object(r["a"])({url:"userjob/add",method:"post",params:t})}function b(t){return Object(r["a"])({url:"userjob/edit",method:"post",params:t})}function y(t){return Object(r["a"])({url:"userjob/delete",method:"post",params:{id:t}})}function w(t){return Object(r["a"])({url:"userjob/page",method:"post",params:t})}function _(){return Object(r["a"])({url:"userjob/list",method:"post"})}function x(){return Object(r["a"])({url:"user/areas",method:"get"})}function j(){return Object(r["a"])({url:"user/statistics"})}function k(){return Object(r["a"])({url:"user/export/profile"})}function O(t){return Object(r["a"])({url:"role/list-site-role",method:"post",params:{userId:t}})}function S(t){return Object(r["a"])({url:"userlog/page",method:"post",params:t})}function C(){return Object(r["a"])({url:"userlog/type",method:"get"})}},dfb6:function(t,e,n){"use strict";n("03b2")},ed08:function(t,e,n){"use strict";n.d(e,"f",(function(){return l})),n.d(e,"d",(function(){return o})),n.d(e,"g",(function(){return u})),n.d(e,"a",(function(){return c})),n.d(e,"b",(function(){return d})),n.d(e,"e",(function(){return f})),n.d(e,"c",(function(){return p})),n.d(e,"h",(function(){return m}));n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("28a5"),n("ac6a"),n("456d"),n("4917"),n("a481");var r=n("53ca");function i(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=a(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,l=!0,o=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return l=t.done,t},e:function(t){o=!0,s=t},f:function(){try{l||null==n.return||n.return()}finally{if(o)throw s}}}}function a(t,e){if(t){if("string"===typeof t)return s(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(t,e):void 0}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function l(t,e){if(0===arguments.length)return null;var n,i=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===Object(r["a"])(t)?n=t:(10===(""+t).length&&(t=1e3*parseInt(t)),n=new Date(t));var a={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()},s=i.replace(/{(y|m|d|h|i|s|a)+}/g,(function(t,e){var n=a[e];return"a"===e?["一","二","三","四","五","六","日"][n-1]:(t.length>0&&n<10&&(n="0"+n),n||0)}));return s}function o(t,e){return!t||t.length<=e?t:t.substring(0,e)+"......"}function u(t,e){if(t&&e){var n=t.className,r=n.indexOf(e);-1===r?n+=""+e:n=n.substr(0,r)+n.substr(r+e.length),t.className=n}}function c(t){if(!t&&"object"!==Object(r["a"])(t))throw new Error("error arguments","shallowClone");var e=t.constructor===Array?[]:{};for(var n in t)t.hasOwnProperty(n)&&(t[n]&&"object"===Object(r["a"])(t[n])?(e[n]=t[n].constructor===Array?[]:{},e[n]=c(t[n])):e[n]=t[n]);return e}function d(t,e){var n,r=i(t);try{for(r.s();!(n=r.n()).done;){var a=n.value,s=a[e];s&&0!==s.length?d(s,e):delete a[e]}}catch(l){r.e(l)}finally{r.f()}}function f(t){if(Array.isArray(t)&&t.length>0)return t[t.length-1]}function p(t,e,n,r){if(Array.isArray(t)){var a,s=i(t);try{for(s.s();!(a=s.n()).done;){var l=a.value,o=p(l,e,n,r);if(o)return o}}catch(v){s.e(v)}finally{s.f()}}if(t[r]===e){var u=t[r],c=[t[r]];return{result:u,path:c}}if(t[n]){var d,f=i(t[n]);try{for(f.s();!(d=f.n()).done;){var m=d.value,h=p(m,e,n,r);if(h)return h.path.unshift(t[r]),h}}catch(v){f.e(v)}finally{f.f()}}}function m(t){var e=[];return function t(n){for(var r=n.childNodes,i=0;i<r.length;i++)e.push(r[i].data),t(r[i])}(t),e}}}]);