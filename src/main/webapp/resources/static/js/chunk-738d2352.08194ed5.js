(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-738d2352"],{"06fa":function(e,t,n){},"0985":function(e,t,n){"use strict";n("06fa")},"6c6e":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"e",(function(){return s})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return l}));var r=n("1c1e");function i(e){return Object(r["a"])({url:"/resource/page",method:"post",params:e})}function a(e){return Object(r["a"])({url:"/resource/add",method:"post",params:e})}function s(e){return Object(r["a"])({url:"/resource/edit",method:"post",params:e})}function o(e){return Object(r["a"])({url:"/resource/tree/children",method:"post",params:{siteId:e}})}function l(e){return Object(r["a"])({url:"/resource/status",method:"post",params:{id:e}})}},a0f0:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("div",{staticStyle:{"text-align":"left"}},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"关键字",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch.apply(null,arguments)}},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}}),e._v(" "),n("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.handleAddRole}},[e._v("新建角色")])],1),e._v(" "),n("el-table",{staticStyle:{width:"100%","margin-top":"30px"},attrs:{data:e.rolesList,border:""}},[n("el-table-column",{attrs:{align:"center",label:"id",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"center",label:"角色名",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"center",label:"sn",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.sn))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"center",label:"创建日期"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("parseTime")(t.row.createTime)))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(n){return e.handleEdit(t)}}},[e._v("编辑")]),e._v(" "),"admin"!=t.row.sn?n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(n){return e.handleEditPermission(t)}}},[e._v("权限分配")]):e._e(),e._v(" "),n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(n){return e.handleUser(t)}}},[e._v("该角色下人员")]),e._v(" "),0==t.row.status&&"admin"!=t.row.sn?n("el-button",{attrs:{type:"danger",size:"small"},on:{click:function(n){return e.handleDelete(t)}}},[e._v("禁用")]):e._e(),e._v(" "),1==t.row.status&&"admin"!=t.row.sn?n("el-button",{attrs:{type:"success",size:"small"},on:{click:function(n){return e.handleDelete(t)}}},[e._v("开启")]):e._e()]}}])})],1),e._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"current-page":e.listQuery.page,"page-sizes":[10,15,20,30],"page-size":e.listQuery.size,layout:" sizes, prev, pager, next,total, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"page",t)},"update:current-page":function(t){return e.$set(e.listQuery,"page",t)}}})],1),e._v(" "),n("el-dialog",{attrs:{visible:e.dialogVisible,title:"edit"===e.dialogType?"编辑角色":"新建角色"},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("el-form",{attrs:{model:e.role,"label-width":"80px","label-position":"left"}},[n("el-form-item",{attrs:{label:"角色名"}},[n("el-input",{attrs:{placeholder:"角色名称"},model:{value:e.role.name,callback:function(t){e.$set(e.role,"name",t)},expression:"role.name"}})],1)],1),e._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"danger"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("取消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.confirmRole}},[e._v("确定")])],1)],1),e._v(" "),n("el-dialog",{attrs:{visible:e.permissionDialogVisible},on:{"update:visible":function(t){e.permissionDialogVisible=t}}},[n("el-tree",{ref:"tree",staticClass:"permission-tree",attrs:{"check-strictly":e.checkStrictly,data:e.routesData,props:e.defaultProps,"show-checkbox":"","node-key":"id"}}),e._v(" "),n("div",{staticStyle:{"text-align":"right",padding:"10px"}},[n("el-button",{attrs:{type:"danger"},on:{click:function(t){e.permissionDialogVisible=!1}}},[e._v("取消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.confirmPermission}},[e._v("确定")])],1)],1),e._v(" "),n("el-dialog",{attrs:{title:"该角色下用户",visible:e.userDialogVisible,fullscreen:!0},on:{"update:visible":function(t){e.userDialogVisible=t}}},[n("div",{attrs:{slot:"title"},slot:"title"},[n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"请输入姓名或者手机号查询！"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleUserFilter.apply(null,arguments)}},model:{value:e.listUserQuery.truename,callback:function(t){e.$set(e.listUserQuery,"truename",t)},expression:"listUserQuery.truename"}}),e._v(" "),n("el-button",{staticClass:"filter-item",attrs:{size:"small",type:"primary",icon:"el-icon-search"},on:{click:e.handleUserFilter}},[e._v("搜索")])],1)]),e._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],key:e.tableKey,staticStyle:{width:"100%"},attrs:{data:e.userList,"element-loading-text":"给我一点时间",stripe:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{align:"center",label:"ID",width:"60px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.uid))])]}}])}),e._v(" "),n("el-table-column",{attrs:{"min-width":"150px",align:"center",label:"姓名"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v(e._s(t.row.truename))])]}}])}),e._v(" "),n("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.phone))])]}}])}),e._v(" "),n("el-table-column",{attrs:{"min-width":"300px",align:"center",label:"站点"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.siteName))])]}}])}),e._v(" "),n("el-table-column",{attrs:{"min-width":"200px",align:"center",label:"部门"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.departmentName))])]}}])}),e._v(" "),n("el-table-column",{attrs:{width:"150px",align:"center",label:"注册时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.createTime,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:!e.listLoading,expression:"!listLoading"}],staticClass:"pagination-container"},[n("el-pagination",{attrs:{"current-page":e.listUserQuery.page,"page-sizes":[10,15,20,30],"page-size":e.listUserQuery.size,layout:"sizes, prev, pager, next, total, jumper",total:e.userTotal},on:{"size-change":e.userSizeChange,"current-change":e.userCurrentChange,"update:currentPage":function(t){return e.$set(e.listUserQuery,"page",t)},"update:current-page":function(t){return e.$set(e.listUserQuery,"page",t)}}})],1)],1)],1)},i=[],a=(n("8e6e"),n("456d"),n("ade3")),s=(n("7f7f"),n("2909")),o=(n("ac6a"),n("96cf"),n("1da1")),l=n("df7c"),u=n.n(l),c=n("ed08"),d=n("cc5e"),f=n("6c6e");function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){Object(a["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var g={key:"",name:"",description:"",routes:[]},m={props:["siteId"],data:function(){return{permissionDialogVisible:!1,role:Object.assign({},g),currentRoleId:null,selectedPermissions:[],routes:[],total:null,userTotal:null,listQuery:{name:"",page:1,size:10},listUserQuery:{truename:"",roleId:0,page:1,size:15},rolesList:[],dialogVisible:!1,userDialogVisible:!1,listLoading:!0,userList:[],tableKey:0,dialogType:"new",checkStrictly:!0,defaultProps:{children:"children",label:"name"}}},computed:{routesData:function(){return this.routes}},created:function(){var e=this;this.getRoutes().then((function(){e.getRoles()}))},methods:{getData:function(){var e=this;this.getRoutes().then((function(){e.getRoles()}))},handleSearch:function(){this.getRoles()},handleSizeChange:function(e){this.listQuery.size=e,this.getData()},userSizeChange:function(e){this.listUserQuery.size=e,this.getUserList(this.listUserQuery)},handleCurrentChange:function(e){this.listQuery.page=e,this.getData()},userCurrentChange:function(e){this.listUserQuery.page=e,this.getUserList(this.listUserQuery)},handleUserFilter:function(){this.listUserQuery.page=1,this.getUserList(this.listUserQuery)},handleEditPermission:function(e){var t=this;Object(d["e"])(e.row.id).then((function(n){t.checkStrictly=!0,t.currentRoleId=e.row.id;var r=n.obj.map((function(e){return e.id}));t.permissionDialogVisible=!0,t.$nextTick((function(){this.$refs.tree.setCheckedKeys(r),this.checkStrictly=!1}))}))},handleUser:function(e){this.userDialogVisible=!0,this.listUserQuery.roleId=e.row.id,this.getUserList(this.listUserQuery),this.listLoading=!1},confirmPermission:function(){var e=this,t=this.$refs.tree.getCheckedNodes(!1,!0),n={roleId:this.currentRoleId,resIds:t.map((function(e){return e.id}))};Object(d["h"])(n).then((function(t){e.permissionDialogVisible=!1})).catch((function(e){}))},getRoutes:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(f["d"])(this.siteId);case 2:t=e.sent,this.serviceRoutes=t.obj,this.routes=t.obj;case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getRoles:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.listQuery,t.siteId=this.siteId,e.next=4,Object(d["c"])(this.listQuery);case 4:n=e.sent,this.total=n.obj.totalElements,this.rolesList=n.obj.content;case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getUserList:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(d["b"])(t);case 2:n=e.sent,this.userTotal=n.obj.totalElements,this.userList=n.obj.content;case 5:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),generateArr:function(e){var t=this,n=[];return e.forEach((function(e){if(n.push(e),e.children){var r=t.generateArr(e.children);r.length>0&&(n=[].concat(Object(s["a"])(n),Object(s["a"])(r)))}})),n},handleAddRole:function(){this.role=Object.assign({},g),this.$refs.tree&&this.$refs.tree.setCheckedNodes([]),this.dialogType="new",this.dialogVisible=!0},handleEdit:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.dialogType="edit",this.dialogVisible=!0,this.checkStrictly=!0,this.role=Object(c["a"])(t.row);case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleDelete:function(e){var t=this,n=(e.$index,e.row);this.$confirm("确定进行禁用?","Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(o["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(d["f"])(n.id).then((function(e){t.getData(),t.$message({type:"success",message:e.msg})}));case 2:case"end":return e.stop()}}),e)})))).catch((function(e){console.error(e)}))},confirmRole:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n,r,i,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t="edit"===this.dialogType,!t){e.next=14;break}return e.next=4,Object(d["g"])(this.role);case 4:n=0;case 5:if(!(n<this.rolesList.length)){e.next=12;break}if(this.rolesList[n].id!==this.role.id){e.next=9;break}return this.rolesList.splice(n,1,Object.assign({},this.role)),e.abrupt("break",12);case 9:n++,e.next=5;break;case 12:e.next=21;break;case 14:return r=Object.assign({},this.role),r.siteId=this.siteId,e.next=18,Object(d["a"])(r);case 18:i=e.sent,i.data,this.getData();case 21:a=this.role,a.description,a.key,a.name,this.dialogVisible=!1,this.$notify({title:"成功",type:"success"});case 24:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),onlyOneShowingChild:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=null,r=e.filter((function(e){return!e.hidden}));return 1===r.length?(n=r[0],n.path=u.a.resolve(t.path,n.path),n):0===r.length&&(n=p(p({},t),{},{path:"",noShowingChildren:!0}),n)}}},b=m,y=(n("0985"),n("2877")),v=Object(y["a"])(b,r,i,!1,null,"e122b24c",null);t["default"]=v.exports},cc5e:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"f",(function(){return a})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return o})),n.d(t,"g",(function(){return l})),n.d(t,"e",(function(){return u})),n.d(t,"h",(function(){return c})),n.d(t,"b",(function(){return d}));var r=n("1c1e");function i(e){return Object(r["a"])({url:"role/page",method:"post",params:e})}function a(e){return Object(r["a"])({url:"role/status",method:"post",params:{id:e}})}function s(e){return Object(r["a"])({url:"/role/page",method:"post",params:e})}function o(e){return Object(r["a"])({url:"/role/add",method:"post",params:e})}function l(e){return Object(r["a"])({url:"/role/edit",method:"post",params:e})}function u(e){return Object(r["a"])({url:"/resource/get-by-role",method:"post",params:{roleId:e}})}function c(e){return Object(r["a"])({url:"/role/grant",method:"post",params:e})}function d(e){return Object(r["a"])({url:"/role/list-user",method:"post",params:e})}},ed08:function(e,t,n){"use strict";n.d(t,"f",(function(){return o})),n.d(t,"d",(function(){return l})),n.d(t,"g",(function(){return u})),n.d(t,"a",(function(){return c})),n.d(t,"b",(function(){return d})),n.d(t,"e",(function(){return f})),n.d(t,"c",(function(){return h})),n.d(t,"h",(function(){return p}));n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("28a5"),n("ac6a"),n("456d"),n("4917"),n("a481");var r=n("53ca");function i(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=a(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){l=!0,s=e},f:function(){try{o||null==n.return||n.return()}finally{if(l)throw s}}}}function a(e,t){if(e){if("string"===typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){if(0===arguments.length)return null;var n,i=t||"{y}-{m}-{d} {h}:{i}:{s}";"object"===Object(r["a"])(e)?n=e:(10===(""+e).length&&(e=1e3*parseInt(e)),n=new Date(e));var a={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()},s=i.replace(/{(y|m|d|h|i|s|a)+}/g,(function(e,t){var n=a[t];return"a"===t?["一","二","三","四","五","六","日"][n-1]:(e.length>0&&n<10&&(n="0"+n),n||0)}));return s}function l(e,t){return!e||e.length<=t?e:e.substring(0,t)+"......"}function u(e,t){if(e&&t){var n=e.className,r=n.indexOf(t);-1===r?n+=""+t:n=n.substr(0,r)+n.substr(r+t.length),e.className=n}}function c(e){if(!e&&"object"!==Object(r["a"])(e))throw new Error("error arguments","shallowClone");var t=e.constructor===Array?[]:{};for(var n in e)e.hasOwnProperty(n)&&(e[n]&&"object"===Object(r["a"])(e[n])?(t[n]=e[n].constructor===Array?[]:{},t[n]=c(e[n])):t[n]=e[n]);return t}function d(e,t){var n,r=i(e);try{for(r.s();!(n=r.n()).done;){var a=n.value,s=a[t];s&&0!==s.length?d(s,t):delete a[t]}}catch(o){r.e(o)}finally{r.f()}}function f(e){if(Array.isArray(e)&&e.length>0)return e[e.length-1]}function h(e,t,n,r){if(Array.isArray(e)){var a,s=i(e);try{for(s.s();!(a=s.n()).done;){var o=a.value,l=h(o,t,n,r);if(l)return l}}catch(m){s.e(m)}finally{s.f()}}if(e[r]===t){var u=e[r],c=[e[r]];return{result:u,path:c}}if(e[n]){var d,f=i(e[n]);try{for(f.s();!(d=f.n()).done;){var p=d.value,g=h(p,t,n,r);if(g)return g.path.unshift(e[r]),g}}catch(m){f.e(m)}finally{f.f()}}}function p(e){var t=[];return function e(n){for(var r=n.childNodes,i=0;i<r.length;i++)t.push(r[i].data),e(r[i])}(e),t}}}]);