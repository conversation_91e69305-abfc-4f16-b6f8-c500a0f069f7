(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1b23339a"],{"29c1":function(t,e,r){},8916:function(t,e,r){"use strict";r.d(e,"h",(function(){return n})),r.d(e,"f",(function(){return o})),r.d(e,"i",(function(){return c})),r.d(e,"e",(function(){return i})),r.d(e,"n",(function(){return s})),r.d(e,"a",(function(){return u})),r.d(e,"r",(function(){return l})),r.d(e,"q",(function(){return d})),r.d(e,"c",(function(){return h})),r.d(e,"g",(function(){return f})),r.d(e,"d",(function(){return m})),r.d(e,"b",(function(){return g})),r.d(e,"j",(function(){return O})),r.d(e,"m",(function(){return v})),r.d(e,"l",(function(){return b})),r.d(e,"k",(function(){return y})),r.d(e,"p",(function(){return w})),r.d(e,"o",(function(){return j}));var a=r("1c1e");function n(t){return Object(a["a"])({url:"log",method:"post",params:t})}function o(){return Object(a["a"])({url:"log/source",method:"post"})}function c(t){return Object(a["a"])({url:"log/findlogsource",method:"post",params:t})}function i(){return Object(a["a"])({url:"log/clear_log_condition",method:"post"})}function s(t,e,r){return Object(a["a"])({url:"log/set_log_condition",method:"post",params:{count:t,date:e,logSwitch:r}})}function u(t){return Object(a["a"])({url:"log/add/source",method:"post",params:t})}function l(t,e){return Object(a["a"])({url:"log/update/source/status",method:"post",params:{id:t,status:e}})}function d(t){return Object(a["a"])({url:"log/update/source",method:"post",params:t})}function h(t){return Object(a["a"])({url:"log/delete/source",method:"post",params:{id:t}})}function p(){return Object(a["a"])({url:"log/type",method:"post"})}function f(){return a["a"].all([o(),p()]).then(a["a"].spread((function(t,e){return{types:e.obj,sources:t.obj}})))}function m(t,e){return Object(a["a"])({url:"audit",method:"post",params:{page:t,size:e}})}function g(t){return Object(a["a"])({url:"audit/del",method:"post",params:{id:t}})}function O(t){return Object(a["a"])({url:"/log/login",method:"post",params:t})}function v(){return Object(a["a"])({url:"/log/login/type",method:"get"})}function b(){return Object(a["a"])({url:"/log/login/statistics/period"})}function y(t){return Object(a["a"])({url:"/log/login/statistics",method:"post",params:t})}function w(){return Object(a["a"])({url:"/log/system/statistics/period"})}function j(t){return Object(a["a"])({url:"/log/system/statistics",method:"post",params:t})}},a7a3:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container calendar-list-container"},[r("div",{staticClass:"filter-container"},[r("el-select",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{clearable:"",placeholder:"请选择时间范围"},on:{change:t.handleFilter},model:{value:t.params.period,callback:function(e){t.$set(t.params,"period",e)},expression:"params.period"}},t._l(t.periods,(function(t){return r("el-option",{key:t.code,attrs:{label:t.description,value:t.code}})})),1),t._v(" "),r("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],staticClass:"filter-item",attrs:{type:"datetimerange","picker-options":t.pickerOptions,"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"right","value-format":"yyyy-MM-dd HH:mm:ss"},on:{change:t.handleFilter},model:{value:t.params.period,callback:function(e){t.$set(t.params,"period",e)},expression:"params.period"}})],1),t._v(" "),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("ve-bar",{attrs:{data:t.charOfRank.chartData,settings:t.charOfRank.chartSettings}}),t._v(" "),r("p",{staticClass:"chart-description"},[t._v("站点日志排行")])],1),t._v(" "),r("el-col",{attrs:{span:6}},[r("ve-ring",{attrs:{data:t.charOfRank.chartData,settings:t.charOfRank.chartSettings}}),t._v(" "),r("p",{staticClass:"chart-description"},[t._v("站点日志比例")])],1),t._v(" "),r("el-col",{attrs:{span:6}},[r("ve-ring",{attrs:{data:t.charOfApi.chartData,settings:t.charOfApi.chartSettings}}),t._v(" "),r("p",{staticClass:"chart-description"},[t._v("接口调用比例")])],1)],1),t._v(" "),r("el-row",[r("el-col",{attrs:{span:24}},[r("ve-line",{attrs:{data:t.charOfHour.chartData,settings:t.charOfHour.chartSettings}})],1)],1),t._v(" "),r("el-row",[r("el-col",{attrs:{span:24}},[r("ve-histogram",{attrs:{data:t.charOfDay.chartData,settings:t.charOfDay.chartSettings}})],1)],1)],1)},n=[],o=(r("4f7f"),r("5df3"),r("1c4c"),r("456d"),r("2909")),c=(r("ac6a"),r("8916")),i=r("4be7");r("2ef0");function s(t){var e=t.hour,r=t.date,a=[];if(e.forEach((function(t){var e=t.data,r={};r.date=t.date+"点",e.forEach((function(t){r[t.title]=t.count})),a.push(r)})),a){var n=[];a.forEach((function(t){var e;(e=n).push.apply(e,Object(o["a"])(Object.keys(t)))})),n=Array.from(new Set(n)),a.forEach((function(t,e){n.forEach((function(e){t.hasOwnProperty(e)||(t[e]=0)}))}))}var c=[],i=[];r.forEach((function(t){var e=t.data,r={};e.forEach((function(t){r[t.title]=t.count})),r.date=t.date,i.push(r)}));var s=new Set;return i.forEach((function(t){var e;(e=s).add.apply(e,Object(o["a"])(Object.keys(t)))})),s=Array.from(s),i.forEach((function(t){s.forEach((function(e){t.hasOwnProperty(e)||(t[e]=0)}))})),c=i.map((function(t){s.forEach((function(e){t[e]=t[e]}));var e=s.reduce((function(e,r){return e+t[r]}),0);return t[u]=e,t})),{hourConverter:a,dateConverter:c}}var u="总数",l={data:function(){return this.typeArr=["line","histogram","pie","ring"],{resourceOptions:[],periods:[],params:{type:void 0,period:void 0},charOfDay:{index:0,chartData:{columns:["date","count"],rows:[]},chartSettings:{labelMap:{count:"接口调用次数"},dimension:["date"],dataOrder:{date:"desc"},metrics:[],legendName:{"登录数量":"接口调用次数"},type:this.typeArr[this.index]}},charOfApi:{index:0,chartData:{columns:["title","count"],rows:[]},chartSettings:{limitShowNum:10,roseType:"radius",labelMap:{count:"接口调用数量"},dimension:["title"],metrics:["count"],legendName:{"登录数量":"接口调用数量"},type:this.typeArr[this.index]}},charOfRank:{index:0,chartData:{columns:["title","count"],rows:[]},chartSettings:{labelMap:{count:"接口调用次数"},dimension:["title"],metrics:["count"],dataOrder:{label:"count",order:"desc"},legendName:{"登录数量":"接口调用次数"},type:this.typeArr[this.index]}},charOfSite:{index:0,chartData:{columns:["title","count"],rows:[]},chartSettings:{roseType:"radius",labelMap:{count:"接口调用次数"},dimension:["title"],metrics:["count"],legendName:{"登录数量":"接口调用次数"},type:this.typeArr[this.index]}},charOfHour:{chartData:{columns:[],rows:[]}},pickerOptions:{shortcuts:[{text:"一天",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-864e5),t.$emit("pick",[r,e])}},{text:"最近一周",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-6048e5),t.$emit("pick",[r,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-2592e6),t.$emit("pick",[r,e])}}]},period:[]}},computed:{sumField:function(){return this.resourceOptions.map((function(t){return t}))}},mounted:function(){var t=this;this.returnResource().then((function(e){t.getData()}))},methods:{getData:function(){var t=this;Object(c["o"])(this.params).then((function(e){t.charOfRank.chartData.rows=e.obj.rank,t.charOfApi.chartData.rows=e.obj.api;var r=s(e.obj),a=r.hourConverter,n=r.dateConverter;t.charOfHour.chartData.rows=a,t.charOfHour.chartData.rows&&(t.charOfHour.chartData.columns=Object.keys(t.charOfHour.chartData.rows[0])),t.charOfDay.chartData.rows=n,t.charOfDay.chartSettings.metrics=Object.keys(t.charOfDay.chartData.rows[0]).filter((function(t){return"date"!=t}))})).catch((function(t){console.log(t)}))},changeChartType:function(){this.index++,this.index>=this.typeArr.length&&(this.index=0),this.chartSettings={type:this.typeArr[this.index]}},returnResource:function(){var t=this,e=this;return i["Promise"].all([Object(c["f"])(),Object(c["p"])()]).then((function(r){e.resourceOptions=r[0].obj.reverse(),t.charOfDay.chartSettings.metrics=e.resourceOptions.map((function(t){return t})),t.charOfDay.chartSettings.metrics.push(u),t.periods=r[1].obj}))},handleFilter:function(){this.getData(this.params)}}},d=l,h=(r("dd82"),r("2877")),p=Object(h["a"])(d,a,n,!1,null,"0a57d546",null);e["default"]=p.exports},dd82:function(t,e,r){"use strict";r("29c1")}}]);