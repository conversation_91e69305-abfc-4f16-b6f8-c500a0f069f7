(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2163f3"],{c23a:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("div",{staticStyle:{"margin-bottom":"15px"}},[e._v("你的权限： "+e._s(e.roles))]),e._v("\n  切换权限：\n  "),r("el-radio-group",{model:{value:e.role,callback:function(t){e.role=t},expression:"role"}},[r("el-radio-button",{attrs:{label:"editor"}})],1)],1)},o=[],c=(r("8e6e"),r("ac6a"),r("456d"),r("ade3")),a=r("2f62");function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){Object(c["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var l={data:function(){return{role:""}},computed:s({},Object(a["b"])(["roles"])),watch:{role:function(e){var t=this;this.$store.dispatch("ChangeRole",e).then((function(){t.$router.push({path:"/permission/index?"+ +new Date})}))}}},u=l,p=r("2877"),b=Object(p["a"])(u,n,o,!1,null,null,null);t["default"]=b.exports}}]);