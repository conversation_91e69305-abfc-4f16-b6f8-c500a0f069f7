(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4a25b5e8"],{"0dd0":function(n,e,t){},"3f23":function(n,e){n.exports='/* Jison generated parser */\nvar jsonlint = (function(){\nvar parser = {trace: function trace() { },\nyy: {},\nsymbols_: {"error":2,"JSONString":3,"STRING":4,"JSONNumber":5,"NUMBER":6,"JSONNullLiteral":7,"NULL":8,"JSONBooleanLiteral":9,"TRUE":10,"FALSE":11,"JSONText":12,"JSONValue":13,"EOF":14,"JSONObject":15,"JSONArray":16,"{":17,"}":18,"JSONMemberList":19,"JSONMember":20,":":21,",":22,"[":23,"]":24,"JSONElementList":25,"$accept":0,"$end":1},\nterminals_: {2:"error",4:"STRING",6:"NUMBER",8:"NULL",10:"TRUE",11:"FALSE",14:"EOF",17:"{",18:"}",21:":",22:",",23:"[",24:"]"},\nproductions_: [0,[3,1],[5,1],[7,1],[9,1],[9,1],[12,2],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[15,2],[15,3],[20,3],[19,1],[19,3],[16,2],[16,3],[25,1],[25,3]],\nperformAction: function anonymous(yytext,yyleng,yylineno,yy,yystate,$$,_$) {\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1: // replace escaped characters with actual character\n          this.$ = yytext.replace(/\\\\(\\\\|")/g, "$"+"1")\n                     .replace(/\\\\n/g,\'\\n\')\n                     .replace(/\\\\r/g,\'\\r\')\n                     .replace(/\\\\t/g,\'\\t\')\n                     .replace(/\\\\v/g,\'\\v\')\n                     .replace(/\\\\f/g,\'\\f\')\n                     .replace(/\\\\b/g,\'\\b\');\n        \nbreak;\ncase 2:this.$ = Number(yytext);\nbreak;\ncase 3:this.$ = null;\nbreak;\ncase 4:this.$ = true;\nbreak;\ncase 5:this.$ = false;\nbreak;\ncase 6:return this.$ = $$[$0-1];\nbreak;\ncase 13:this.$ = {};\nbreak;\ncase 14:this.$ = $$[$0-1];\nbreak;\ncase 15:this.$ = [$$[$0-2], $$[$0]];\nbreak;\ncase 16:this.$ = {}; this.$[$$[$0][0]] = $$[$0][1];\nbreak;\ncase 17:this.$ = $$[$0-2]; $$[$0-2][$$[$0][0]] = $$[$0][1];\nbreak;\ncase 18:this.$ = [];\nbreak;\ncase 19:this.$ = $$[$0-1];\nbreak;\ncase 20:this.$ = [$$[$0]];\nbreak;\ncase 21:this.$ = $$[$0-2]; $$[$0-2].push($$[$0]);\nbreak;\n}\n},\ntable: [{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],12:1,13:2,15:7,16:8,17:[1,14],23:[1,15]},{1:[3]},{14:[1,16]},{14:[2,7],18:[2,7],22:[2,7],24:[2,7]},{14:[2,8],18:[2,8],22:[2,8],24:[2,8]},{14:[2,9],18:[2,9],22:[2,9],24:[2,9]},{14:[2,10],18:[2,10],22:[2,10],24:[2,10]},{14:[2,11],18:[2,11],22:[2,11],24:[2,11]},{14:[2,12],18:[2,12],22:[2,12],24:[2,12]},{14:[2,3],18:[2,3],22:[2,3],24:[2,3]},{14:[2,4],18:[2,4],22:[2,4],24:[2,4]},{14:[2,5],18:[2,5],22:[2,5],24:[2,5]},{14:[2,1],18:[2,1],21:[2,1],22:[2,1],24:[2,1]},{14:[2,2],18:[2,2],22:[2,2],24:[2,2]},{3:20,4:[1,12],18:[1,17],19:18,20:19},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:23,15:7,16:8,17:[1,14],23:[1,15],24:[1,21],25:22},{1:[2,6]},{14:[2,13],18:[2,13],22:[2,13],24:[2,13]},{18:[1,24],22:[1,25]},{18:[2,16],22:[2,16]},{21:[1,26]},{14:[2,18],18:[2,18],22:[2,18],24:[2,18]},{22:[1,28],24:[1,27]},{22:[2,20],24:[2,20]},{14:[2,14],18:[2,14],22:[2,14],24:[2,14]},{3:20,4:[1,12],20:29},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:30,15:7,16:8,17:[1,14],23:[1,15]},{14:[2,19],18:[2,19],22:[2,19],24:[2,19]},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:31,15:7,16:8,17:[1,14],23:[1,15]},{18:[2,17],22:[2,17]},{18:[2,15],22:[2,15]},{22:[2,21],24:[2,21]}],\ndefaultActions: {16:[2,6]},\nparseError: function parseError(str, hash) {\n    throw new Error(str);\n},\nparse: function parse(input) {\n    var self = this,\n        stack = [0],\n        vstack = [null], // semantic value stack\n        lstack = [], // location stack\n        table = this.table,\n        yytext = \'\',\n        yylineno = 0,\n        yyleng = 0,\n        recovering = 0,\n        TERROR = 2,\n        EOF = 1;\n\n    //this.reductionCount = this.shiftCount = 0;\n\n    this.lexer.setInput(input);\n    this.lexer.yy = this.yy;\n    this.yy.lexer = this.lexer;\n    if (typeof this.lexer.yylloc == \'undefined\')\n        this.lexer.yylloc = {};\n    var yyloc = this.lexer.yylloc;\n    lstack.push(yyloc);\n\n    if (typeof this.yy.parseError === \'function\')\n        this.parseError = this.yy.parseError;\n\n    function popStack (n) {\n        stack.length = stack.length - 2*n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n\n    function lex() {\n        var token;\n        token = self.lexer.lex() || 1; // $end = 1\n        // if token isn\'t its numeric value, convert\n        if (typeof token !== \'number\') {\n            token = self.symbols_[token] || token;\n        }\n        return token;\n    }\n\n    var symbol, preErrorSymbol, state, action, a, r, yyval={},p,len,newState, expected;\n    while (true) {\n        // retreive state number from top of stack\n        state = stack[stack.length-1];\n\n        // use default actions if available\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol == null)\n                symbol = lex();\n            // read action for current state and first input\n            action = table[state] && table[state][symbol];\n        }\n\n        // handle parse error\n        _handle_error:\n        if (typeof action === \'undefined\' || !action.length || !action[0]) {\n\n            if (!recovering) {\n                // Report error\n                expected = [];\n                for (p in table[state]) if (this.terminals_[p] && p > 2) {\n                    expected.push("\'"+this.terminals_[p]+"\'");\n                }\n                var errStr = \'\';\n                if (this.lexer.showPosition) {\n                    errStr = \'Parse error on line \'+(yylineno+1)+":\\n"+this.lexer.showPosition()+"\\nExpecting "+expected.join(\', \') + ", got \'" + this.terminals_[symbol]+ "\'";\n                } else {\n                    errStr = \'Parse error on line \'+(yylineno+1)+": Unexpected " +\n                                  (symbol == 1 /*EOF*/ ? "end of input" :\n                                              ("\'"+(this.terminals_[symbol] || symbol)+"\'"));\n                }\n                this.parseError(errStr,\n                    {text: this.lexer.match, token: this.terminals_[symbol] || symbol, line: this.lexer.yylineno, loc: yyloc, expected: expected});\n            }\n\n            // just recovered from another error\n            if (recovering == 3) {\n                if (symbol == EOF) {\n                    throw new Error(errStr || \'Parsing halted.\');\n                }\n\n                // discard current lookahead and grab another\n                yyleng = this.lexer.yyleng;\n                yytext = this.lexer.yytext;\n                yylineno = this.lexer.yylineno;\n                yyloc = this.lexer.yylloc;\n                symbol = lex();\n            }\n\n            // try to recover from error\n            while (1) {\n                // check for error recovery rule in this state\n                if ((TERROR.toString()) in table[state]) {\n                    break;\n                }\n                if (state == 0) {\n                    throw new Error(errStr || \'Parsing halted.\');\n                }\n                popStack(1);\n                state = stack[stack.length-1];\n            }\n\n            preErrorSymbol = symbol; // save the lookahead token\n            symbol = TERROR;         // insert generic error symbol as new lookahead\n            state = stack[stack.length-1];\n            action = table[state] && table[state][TERROR];\n            recovering = 3; // allow 3 real symbols to be shifted before reporting a new error\n        }\n\n        // this shouldn\'t happen, unless resolve defaults are off\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error(\'Parse Error: multiple actions possible at state: \'+state+\', token: \'+symbol);\n        }\n\n        switch (action[0]) {\n\n            case 1: // shift\n                //this.shiftCount++;\n\n                stack.push(symbol);\n                vstack.push(this.lexer.yytext);\n                lstack.push(this.lexer.yylloc);\n                stack.push(action[1]); // push state\n                symbol = null;\n                if (!preErrorSymbol) { // normal execution/no error\n                    yyleng = this.lexer.yyleng;\n                    yytext = this.lexer.yytext;\n                    yylineno = this.lexer.yylineno;\n                    yyloc = this.lexer.yylloc;\n                    if (recovering > 0)\n                        recovering--;\n                } else { // error just occurred, resume old lookahead f/ before error\n                    symbol = preErrorSymbol;\n                    preErrorSymbol = null;\n                }\n                break;\n\n            case 2: // reduce\n                //this.reductionCount++;\n\n                len = this.productions_[action[1]][1];\n\n                // perform semantic action\n                yyval.$ = vstack[vstack.length-len]; // default to $$ = $1\n                // default location, uses first token for firsts, last for lasts\n                yyval._$ = {\n                    first_line: lstack[lstack.length-(len||1)].first_line,\n                    last_line: lstack[lstack.length-1].last_line,\n                    first_column: lstack[lstack.length-(len||1)].first_column,\n                    last_column: lstack[lstack.length-1].last_column\n                };\n                r = this.performAction.call(yyval, yytext, yyleng, yylineno, this.yy, action[1], vstack, lstack);\n\n                if (typeof r !== \'undefined\') {\n                    return r;\n                }\n\n                // pop off stack\n                if (len) {\n                    stack = stack.slice(0,-1*len*2);\n                    vstack = vstack.slice(0, -1*len);\n                    lstack = lstack.slice(0, -1*len);\n                }\n\n                stack.push(this.productions_[action[1]][0]);    // push nonterminal (reduce)\n                vstack.push(yyval.$);\n                lstack.push(yyval._$);\n                // goto new state = table[STATE][NONTERMINAL]\n                newState = table[stack[stack.length-2]][stack[stack.length-1]];\n                stack.push(newState);\n                break;\n\n            case 3: // accept\n                return true;\n        }\n\n    }\n\n    return true;\n}};\n/* Jison generated lexer */\nvar lexer = (function(){\nvar lexer = ({EOF:1,\nparseError:function parseError(str, hash) {\n        if (this.yy.parseError) {\n            this.yy.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\nsetInput:function (input) {\n        this._input = input;\n        this._more = this._less = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \'\';\n        this.conditionStack = [\'INITIAL\'];\n        this.yylloc = {first_line:1,first_column:0,last_line:1,last_column:0};\n        return this;\n    },\ninput:function () {\n        var ch = this._input[0];\n        this.yytext+=ch;\n        this.yyleng++;\n        this.match+=ch;\n        this.matched+=ch;\n        var lines = ch.match(/\\n/);\n        if (lines) this.yylineno++;\n        this._input = this._input.slice(1);\n        return ch;\n    },\nunput:function (ch) {\n        this._input = ch + this._input;\n        return this;\n    },\nmore:function () {\n        this._more = true;\n        return this;\n    },\nless:function (n) {\n        this._input = this.match.slice(n) + this._input;\n    },\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \'...\':\'\') + past.substr(-20).replace(/\\n/g, "");\n    },\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20)+(next.length > 20 ? \'...\':\'\')).replace(/\\n/g, "");\n    },\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join("-");\n        return pre + this.upcomingInput() + "\\n" + c+"^";\n    },\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) this.done = true;\n\n        var token,\n            match,\n            tempMatch,\n            index,\n            col,\n            lines;\n        if (!this._more) {\n            this.yytext = \'\';\n            this.match = \'\';\n        }\n        var rules = this._currentRules();\n        for (var i=0;i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (!this.options.flex) break;\n            }\n        }\n        if (match) {\n            lines = match[0].match(/\\n.*/g);\n            if (lines) this.yylineno += lines.length;\n            this.yylloc = {first_line: this.yylloc.last_line,\n                           last_line: this.yylineno+1,\n                           first_column: this.yylloc.last_column,\n                           last_column: lines ? lines[lines.length-1].length-1 : this.yylloc.last_column + match[0].length}\n            this.yytext += match[0];\n            this.match += match[0];\n            this.yyleng = this.yytext.length;\n            this._more = false;\n            this._input = this._input.slice(match[0].length);\n            this.matched += match[0];\n            token = this.performAction.call(this, this.yy, this, rules[index],this.conditionStack[this.conditionStack.length-1]);\n            if (this.done && this._input) this.done = false;\n            if (token) return token;\n            else return;\n        }\n        if (this._input === "") {\n            return this.EOF;\n        } else {\n            this.parseError(\'Lexical error on line \'+(this.yylineno+1)+\'. Unrecognized text.\\n\'+this.showPosition(), \n                    {text: "", token: null, line: this.yylineno});\n        }\n    },\nlex:function lex() {\n        var r = this.next();\n        if (typeof r !== \'undefined\') {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\nbegin:function begin(condition) {\n        this.conditionStack.push(condition);\n    },\npopState:function popState() {\n        return this.conditionStack.pop();\n    },\n_currentRules:function _currentRules() {\n        return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules;\n    },\ntopState:function () {\n        return this.conditionStack[this.conditionStack.length-2];\n    },\npushState:function begin(condition) {\n        this.begin(condition);\n    }});\nlexer.options = {};\nlexer.performAction = function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\n\nvar YYSTATE=YY_START\nswitch($avoiding_name_collisions) {\ncase 0:/* skip whitespace */\nbreak;\ncase 1:return 6\nbreak;\ncase 2:yy_.yytext = yy_.yytext.substr(1,yy_.yyleng-2); return 4\nbreak;\ncase 3:return 17\nbreak;\ncase 4:return 18\nbreak;\ncase 5:return 23\nbreak;\ncase 6:return 24\nbreak;\ncase 7:return 22\nbreak;\ncase 8:return 21\nbreak;\ncase 9:return 10\nbreak;\ncase 10:return 11\nbreak;\ncase 11:return 8\nbreak;\ncase 12:return 14\nbreak;\ncase 13:return \'INVALID\'\nbreak;\n}\n};\nlexer.rules = [/^(?:\\s+)/,/^(?:(-?([0-9]|[1-9][0-9]+))(\\.[0-9]+)?([eE][-+]?[0-9]+)?\\b)/,/^(?:"(?:\\\\[\\\\"bfnrt/]|\\\\u[a-fA-F0-9]{4}|[^\\\\\\0-\\x09\\x0a-\\x1f"])*")/,/^(?:\\{)/,/^(?:\\})/,/^(?:\\[)/,/^(?:\\])/,/^(?:,)/,/^(?::)/,/^(?:true\\b)/,/^(?:false\\b)/,/^(?:null\\b)/,/^(?:$)/,/^(?:.)/];\nlexer.conditions = {"INITIAL":{"rules":[0,1,2,3,4,5,6,7,8,9,10,11,12,13],"inclusive":true}};\n\n\n;\nreturn lexer;})()\nparser.lexer = lexer;\nreturn parser;\n})();\nif (typeof require !== \'undefined\' && typeof exports !== \'undefined\') {\nexports.parser = jsonlint;\nexports.parse = function () { return jsonlint.parse.apply(jsonlint, arguments); }\nexports.main = function commonjsMain(args) {\n    if (!args[1])\n        throw new Error(\'Usage: \'+args[0]+\' FILE\');\n    if (typeof process !== \'undefined\') {\n        var source = require(\'fs\').readFileSync(require(\'path\').join(process.cwd(), args[1]), "utf8");\n    } else {\n        var cwd = require("file").path(require("file").cwd());\n        var source = cwd.join(args[1]).read({charset: "utf-8"});\n    }\n    return exports.parser.parse(source);\n}\nif (typeof module !== \'undefined\' && require.main === module) {\n  exports.main(typeof process !== \'undefined\' ? process.argv.slice(1) : require("system").args);\n}\n}'},8822:function(n,e,t){(function(n){n(t("56b3"))})((function(n){"use strict";var e="CodeMirror-lint-markers";function t(e,t){var r=document.createElement("div");function i(e){if(!r.parentNode)return n.off(document,"mousemove",i);r.style.top=Math.max(0,e.clientY-r.offsetHeight-5)+"px",r.style.left=e.clientX+5+"px"}return r.className="CodeMirror-lint-tooltip",r.appendChild(t.cloneNode(!0)),document.body.appendChild(r),n.on(document,"mousemove",i),i(e),null!=r.style.opacity&&(r.style.opacity=1),r}function r(n){n.parentNode&&n.parentNode.removeChild(n)}function i(n){n.parentNode&&(null==n.style.opacity&&r(n),n.style.opacity=0,setTimeout((function(){r(n)}),600))}function a(e,r,a){var o=t(e,r);function s(){n.off(a,"mouseout",s),o&&(i(o),o=null)}var l=setInterval((function(){if(o)for(var n=a;;n=n.parentNode){if(n&&11==n.nodeType&&(n=n.host),n==document.body)return;if(!n){s();break}}if(!o)return clearInterval(l)}),400);n.on(a,"mouseout",s)}function o(n,e,t){this.marked=[],this.options=e,this.timeout=null,this.hasGutter=t,this.onMouseOver=function(e){k(n,e)},this.waitingFor=0}function s(n,e){return e instanceof Function?{getAnnotations:e}:(e&&!0!==e||(e={}),e)}function l(n){var t=n.state.lint;t.hasGutter&&n.clearGutter(e);for(var r=0;r<t.marked.length;++r)t.marked[r].clear();t.marked.length=0}function c(e,t,r,i){var o=document.createElement("div"),s=o;return o.className="CodeMirror-lint-marker-"+t,r&&(s=o.appendChild(document.createElement("div")),s.className="CodeMirror-lint-marker-multiple"),0!=i&&n.on(s,"mouseover",(function(n){a(n,e,s)})),o}function u(n,e){return"error"==n?n:e}function f(n){for(var e=[],t=0;t<n.length;++t){var r=n[t],i=r.from.line;(e[i]||(e[i]=[])).push(r)}return e}function p(n){var e=n.severity;e||(e="error");var t=document.createElement("div");return t.className="CodeMirror-lint-message-"+e,t.appendChild(document.createTextNode(n.message)),t}function h(e,t,r){var i=e.state.lint,a=++i.waitingFor;function o(){a=-1,e.off("change",o)}e.on("change",o),t(e.getValue(),(function(t,r){e.off("change",o),i.waitingFor==a&&(r&&t instanceof n&&(t=r),y(e,t))}),r,e)}function d(e){var t=e.state.lint,r=t.options,i=r.options||r,a=r.getAnnotations||e.getHelper(n.Pos(0,0),"lint");if(a)if(r.async||a.async)h(e,a,i);else{var o=a(e.getValue(),i,e);if(!o)return;o.then?o.then((function(n){y(e,n)})):y(e,o)}}function y(n,t){l(n);for(var r=n.state.lint,i=r.options,a=f(t),o=0;o<a.length;++o){var s=a[o];if(s){for(var h=null,d=r.hasGutter&&document.createDocumentFragment(),y=0;y<s.length;++y){var m=s[y],v=m.severity;v||(v="error"),h=u(h,v),i.formatAnnotation&&(m=i.formatAnnotation(m)),r.hasGutter&&d.appendChild(p(m)),m.to&&r.marked.push(n.markText(m.from,m.to,{className:"CodeMirror-lint-mark-"+v,__annotation:m}))}r.hasGutter&&n.setGutterMarker(o,e,c(d,h,s.length>1,r.options.tooltips))}}i.onUpdateLinting&&i.onUpdateLinting(t,a,n)}function m(n){var e=n.state.lint;e&&(clearTimeout(e.timeout),e.timeout=setTimeout((function(){d(n)}),e.options.delay||500))}function v(n,e){for(var t=e.target||e.srcElement,r=document.createDocumentFragment(),i=0;i<n.length;i++){var o=n[i];r.appendChild(p(o))}a(e,r,t)}function k(n,e){var t=e.target||e.srcElement;if(/\bCodeMirror-lint-mark-/.test(t.className)){for(var r=t.getBoundingClientRect(),i=(r.left+r.right)/2,a=(r.top+r.bottom)/2,o=n.findMarksAt(n.coordsChar({left:i,top:a},"client")),s=[],l=0;l<o.length;++l){var c=o[l].__annotation;c&&s.push(c)}s.length&&v(s,e)}}n.defineOption("lint",!1,(function(t,r,i){if(i&&i!=n.Init&&(l(t),!1!==t.state.lint.options.lintOnChange&&t.off("change",m),n.off(t.getWrapperElement(),"mouseover",t.state.lint.onMouseOver),clearTimeout(t.state.lint.timeout),delete t.state.lint),r){for(var a=t.getOption("gutters"),c=!1,u=0;u<a.length;++u)a[u]==e&&(c=!0);var f=t.state.lint=new o(t,s(t,r),c);!1!==f.options.lintOnChange&&t.on("change",m),0!=f.options.tooltips&&"gutter"!=f.options.tooltips&&n.on(t.getWrapperElement(),"mouseover",f.onMouseOver),d(t)}})),n.defineExtension("performLint",(function(){this.state.lint&&d(this)}))}))},a7be:function(n,e,t){},acdf:function(n,e,t){},ae67:function(n,e,t){t("f2b5")(t("3f23"))},d2de:function(n,e,t){(function(n){n(t("56b3"))})((function(n){"use strict";n.registerHelper("lint","json",(function(e){var t=[];jsonlint.parseError=function(e,r){var i=r.loc;t.push({from:n.Pos(i.first_line-1,i.first_column),to:n.Pos(i.last_line-1,i.last_column),message:e})};try{jsonlint.parse(e)}catch(r){}return t}))}))},f2b5:function(n,e){n.exports=function(n){function e(n){"undefined"!==typeof console&&(console.error||console.log)("[Script Loader]",n)}function t(){return"undefined"!==typeof attachEvent&&"undefined"===typeof addEventListener}try{"undefined"!==typeof execScript&&t()?execScript(n):"undefined"!==typeof eval?eval.call(null,n):e("EvalError: No eval function available")}catch(r){e(r)}}},f9d4:function(n,e,t){(function(n){n(t("56b3"))})((function(n){"use strict";function e(n,e,t){return/^(?:operator|sof|keyword c|case|new|export|default|[\[{}\(,;:]|=>)$/.test(e.lastType)||"quasi"==e.lastType&&/\{\s*$/.test(n.string.slice(0,n.pos-(t||0)))}n.defineMode("javascript",(function(t,r){var i,a,o=t.indentUnit,s=r.statementIndent,l=r.jsonld,c=r.json||l,u=r.typescript,f=r.wordCharacters||/[\w$\xa1-\uffff]/,p=function(){function n(n){return{type:n,style:"keyword"}}var e=n("keyword a"),t=n("keyword b"),r=n("keyword c"),i=n("operator"),a={type:"atom",style:"atom"},o={if:n("if"),while:e,with:e,else:t,do:t,try:t,finally:t,return:r,break:r,continue:r,new:n("new"),delete:r,throw:r,debugger:r,var:n("var"),const:n("var"),let:n("var"),function:n("function"),catch:n("catch"),for:n("for"),switch:n("switch"),case:n("case"),default:n("default"),in:i,typeof:i,instanceof:i,true:a,false:a,null:a,undefined:a,NaN:a,Infinity:a,this:n("this"),class:n("class"),super:n("atom"),yield:r,export:n("export"),import:n("import"),extends:r,await:r,async:n("async")};if(u){var s={type:"variable",style:"variable-3"},l={interface:n("class"),implements:r,namespace:r,module:n("module"),enum:n("module"),public:n("modifier"),private:n("modifier"),protected:n("modifier"),abstract:n("modifier"),as:i,string:s,number:s,boolean:s,any:s};for(var c in l)o[c]=l[c]}return o}(),h=/[+\-*&%=<>!?|~^@]/,d=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function y(n){var e,t=!1,r=!1;while(null!=(e=n.next())){if(!t){if("/"==e&&!r)return;"["==e?r=!0:r&&"]"==e&&(r=!1)}t=!t&&"\\"==e}}function m(n,e,t){return i=n,a=t,e}function v(n,t){var r=n.next();if('"'==r||"'"==r)return t.tokenize=k(r),t.tokenize(n,t);if("."==r&&n.match(/^\d+(?:[eE][+\-]?\d+)?/))return m("number","number");if("."==r&&n.match(".."))return m("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return m(r);if("="==r&&n.eat(">"))return m("=>","operator");if("0"==r&&n.eat(/x/i))return n.eatWhile(/[\da-f]/i),m("number","number");if("0"==r&&n.eat(/o/i))return n.eatWhile(/[0-7]/i),m("number","number");if("0"==r&&n.eat(/b/i))return n.eatWhile(/[01]/i),m("number","number");if(/\d/.test(r))return n.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/),m("number","number");if("/"==r)return n.eat("*")?(t.tokenize=b,b(n,t)):n.eat("/")?(n.skipToEnd(),m("comment","comment")):e(n,t,1)?(y(n),n.match(/^\b(([gimyu])(?![gimyu]*\2))+\b/),m("regexp","string-2")):(n.eatWhile(h),m("operator","operator",n.current()));if("`"==r)return t.tokenize=g,g(n,t);if("#"==r)return n.skipToEnd(),m("error","error");if(h.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||n.eatWhile(h),m("operator","operator",n.current());if(f.test(r)){n.eatWhile(f);var i=n.current(),a=p.propertyIsEnumerable(i)&&p[i];return a&&"."!=t.lastType?m(a.type,a.style,i):m("variable","variable",i)}}function k(n){return function(e,t){var r,i=!1;if(l&&"@"==e.peek()&&e.match(d))return t.tokenize=v,m("jsonld-keyword","meta");while(null!=(r=e.next())){if(r==n&&!i)break;i=!i&&"\\"==r}return i||(t.tokenize=v),m("string","string")}}function b(n,e){var t,r=!1;while(t=n.next()){if("/"==t&&r){e.tokenize=v;break}r="*"==t}return m("comment","comment")}function g(n,e){var t,r=!1;while(null!=(t=n.next())){if(!r&&("`"==t||"$"==t&&n.eat("{"))){e.tokenize=v;break}r=!r&&"\\"==t}return m("quasi","string-2",n.current())}var x="([{}])";function w(n,e){e.fatArrowAt&&(e.fatArrowAt=null);var t=n.string.indexOf("=>",n.start);if(!(t<0)){if(u){var r=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(n.string.slice(n.start,t));r&&(t=r.index)}for(var i=0,a=!1,o=t-1;o>=0;--o){var s=n.string.charAt(o),l=x.indexOf(s);if(l>=0&&l<3){if(!i){++o;break}if(0==--i){"("==s&&(a=!0);break}}else if(l>=3&&l<6)++i;else if(f.test(s))a=!0;else{if(/["'\/]/.test(s))return;if(a&&!i){++o;break}}}a&&!i&&(e.fatArrowAt=o)}}var $={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,"jsonld-keyword":!0};function E(n,e,t,r,i,a){this.indented=n,this.column=e,this.type=t,this.prev=i,this.info=a,null!=r&&(this.align=r)}function _(n,e){for(var t=n.localVars;t;t=t.next)if(t.name==e)return!0;for(var r=n.context;r;r=r.prev)for(t=r.vars;t;t=t.next)if(t.name==e)return!0}function S(n,e,t,r,i){var a=n.cc;M.state=n,M.stream=i,M.marked=null,M.cc=a,M.style=e,n.lexical.hasOwnProperty("align")||(n.lexical.align=!0);while(1){var o=a.length?a.pop():c?F:L;if(o(t,r)){while(a.length&&a[a.length-1].lex)a.pop()();return M.marked?M.marked:"variable"==t&&_(n,r)?"variable-2":e}}}var M={state:null,column:null,marked:null,cc:null};function j(){for(var n=arguments.length-1;n>=0;n--)M.cc.push(arguments[n])}function N(){return j.apply(null,arguments),!0}function A(n){function e(e){for(var t=e;t;t=t.next)if(t.name==n)return!0;return!1}var t=M.state;if(M.marked="def",t.context){if(e(t.localVars))return;t.localVars={name:n,next:t.localVars}}else{if(e(t.globalVars))return;r.globalVars&&(t.globalVars={name:n,next:t.globalVars})}}var T={name:"this",next:{name:"arguments"}};function O(){M.state.context={prev:M.state.context,vars:M.state.localVars},M.state.localVars=T}function I(){M.state.localVars=M.state.context.vars,M.state.context=M.state.context.prev}function C(n,e){var t=function(){var t=M.state,r=t.indented;if("stat"==t.lexical.type)r=t.lexical.indented;else for(var i=t.lexical;i&&")"==i.type&&i.align;i=i.prev)r=i.indented;t.lexical=new E(r,M.stream.column(),n,null,t.lexical,e)};return t.lex=!0,t}function R(){var n=M.state;n.lexical.prev&&(")"==n.lexical.type&&(n.indented=n.lexical.indented),n.lexical=n.lexical.prev)}function V(n){function e(t){return t==n?N():";"==n?j():N(e)}return e}function L(n,e){return"var"==n?N(C("vardef",e.length),dn,V(";"),R):"keyword a"==n?N(C("form"),z,L,R):"keyword b"==n?N(C("form"),L,R):"{"==n?N(C("}"),sn,R):";"==n?N():"if"==n?("else"==M.state.lexical.info&&M.state.cc[M.state.cc.length-1]==R&&M.state.cc.pop()(),N(C("form"),z,L,R,bn)):"function"==n?N(_n):"for"==n?N(C("form"),gn,L,R):"variable"==n?u&&"type"==e?(M.marked="keyword",N(cn,V("operator"),cn,V(";"))):N(C("stat"),Z):"switch"==n?N(C("form"),z,V("{"),C("}","switch"),sn,R,R):"case"==n?N(F,V(":")):"default"==n?N(V(":")):"catch"==n?N(C("form"),O,V("("),Sn,V(")"),L,R,I):"class"==n?N(C("form"),jn,R):"export"==n?N(C("stat"),On,R):"import"==n?N(C("stat"),Cn,R):"module"==n?N(C("form"),yn,V("{"),C("}"),sn,R,R):"async"==n?N(L):"@"==e?N(F,L):j(C("stat"),F,V(";"),R)}function F(n){return J(n,!1)}function P(n){return J(n,!0)}function z(n){return"("!=n?j():N(C(")"),F,V(")"),R)}function J(n,e){if(M.state.fatArrowAt==M.stream.start){var t=e?D:H;if("("==n)return N(O,C(")"),an(yn,")"),R,V("=>"),t,I);if("variable"==n)return j(O,yn,V("=>"),t,I)}var r=e?W:G;return $.hasOwnProperty(n)?N(r):"function"==n?N(_n,r):"class"==n?N(C("form"),Mn,R):"keyword c"==n||"async"==n?N(e?U:q):"("==n?N(C(")"),q,V(")"),R,r):"operator"==n||"spread"==n?N(e?P:F):"["==n?N(C("]"),Pn,R,r):"{"==n?on(en,"}",null,r):"quasi"==n?j(Y,r):"new"==n?N(X(e)):N()}function q(n){return n.match(/[;\}\)\],]/)?j():j(F)}function U(n){return n.match(/[;\}\)\],]/)?j():j(P)}function G(n,e){return","==n?N(F):W(n,e,!1)}function W(n,e,t){var r=0==t?G:W,i=0==t?F:P;return"=>"==n?N(O,t?D:H,I):"operator"==n?/\+\+|--/.test(e)?N(r):"?"==e?N(F,V(":"),i):N(i):"quasi"==n?j(Y,r):";"!=n?"("==n?on(P,")","call",r):"."==n?N(nn,r):"["==n?N(C("]"),q,V("]"),R,r):void 0:void 0}function Y(n,e){return"quasi"!=n?j():"${"!=e.slice(e.length-2)?N(Y):N(F,B)}function B(n){if("}"==n)return M.marked="string-2",M.state.tokenize=g,N(Y)}function H(n){return w(M.stream,M.state),j("{"==n?L:F)}function D(n){return w(M.stream,M.state),j("{"==n?L:P)}function X(n){return function(e){return"."==e?N(n?Q:K):j(n?P:F)}}function K(n,e){if("target"==e)return M.marked="keyword",N(G)}function Q(n,e){if("target"==e)return M.marked="keyword",N(W)}function Z(n){return":"==n?N(R,L):j(G,V(";"),R)}function nn(n){if("variable"==n)return M.marked="property",N()}function en(n,e){return"async"==n?(M.marked="property",N(en)):"variable"==n||"keyword"==M.style?(M.marked="property",N("get"==e||"set"==e?tn:rn)):"number"==n||"string"==n?(M.marked=l?"property":M.style+" property",N(rn)):"jsonld-keyword"==n?N(rn):"modifier"==n?N(en):"["==n?N(F,V("]"),rn):"spread"==n?N(F):":"==n?j(rn):void 0}function tn(n){return"variable"!=n?j(rn):(M.marked="property",N(_n))}function rn(n){return":"==n?N(P):"("==n?j(_n):void 0}function an(n,e,t){function r(i,a){if(t?t.indexOf(i)>-1:","==i){var o=M.state.lexical;return"call"==o.info&&(o.pos=(o.pos||0)+1),N((function(t,r){return t==e||r==e?j():j(n)}),r)}return i==e||a==e?N():N(V(e))}return function(t,i){return t==e||i==e?N():j(n,r)}}function on(n,e,t){for(var r=3;r<arguments.length;r++)M.cc.push(arguments[r]);return N(C(e,t),an(n,e),R)}function sn(n){return"}"==n?N():j(L,sn)}function ln(n,e){if(u){if(":"==n)return N(cn);if("?"==e)return N(ln)}}function cn(n){return"variable"==n?(M.marked="variable-3",N(hn)):"string"==n||"number"==n||"atom"==n?N(hn):"{"==n?N(C("}"),an(fn,"}",",;"),R,hn):"("==n?N(an(pn,")"),un):void 0}function un(n){if("=>"==n)return N(cn)}function fn(n,e){return"variable"==n||"keyword"==M.style?(M.marked="property",N(fn)):"?"==e?N(fn):":"==n?N(cn):"["==n?N(F,ln,V("]"),fn):void 0}function pn(n){return"variable"==n?N(pn):":"==n?N(cn):void 0}function hn(n,e){return"<"==e?N(C(">"),an(cn,">"),R,hn):"|"==e||"."==n?N(cn):"["==n?N(V("]"),hn):"extends"==e?N(cn):void 0}function dn(){return j(yn,ln,vn,kn)}function yn(n,e){return"modifier"==n?N(yn):"variable"==n?(A(e),N()):"spread"==n?N(yn):"["==n?on(yn,"]"):"{"==n?on(mn,"}"):void 0}function mn(n,e){return"variable"!=n||M.stream.match(/^\s*:/,!1)?("variable"==n&&(M.marked="property"),"spread"==n?N(yn):"}"==n?j():N(V(":"),yn,vn)):(A(e),N(vn))}function vn(n,e){if("="==e)return N(P)}function kn(n){if(","==n)return N(dn)}function bn(n,e){if("keyword b"==n&&"else"==e)return N(C("form","else"),L,R)}function gn(n){if("("==n)return N(C(")"),xn,V(")"),R)}function xn(n){return"var"==n?N(dn,V(";"),$n):";"==n?N($n):"variable"==n?N(wn):j(F,V(";"),$n)}function wn(n,e){return"in"==e||"of"==e?(M.marked="keyword",N(F)):N(G,$n)}function $n(n,e){return";"==n?N(En):"in"==e||"of"==e?(M.marked="keyword",N(F)):j(F,V(";"),En)}function En(n){")"!=n&&N(F)}function _n(n,e){return"*"==e?(M.marked="keyword",N(_n)):"variable"==n?(A(e),N(_n)):"("==n?N(O,C(")"),an(Sn,")"),R,ln,L,I):u&&"<"==e?N(C(">"),an(cn,">"),R,_n):void 0}function Sn(n){return"spread"==n?N(Sn):j(yn,ln,vn)}function Mn(n,e){return"variable"==n?jn(n,e):Nn(n,e)}function jn(n,e){if("variable"==n)return A(e),N(Nn)}function Nn(n,e){return"<"==e?N(C(">"),an(cn,">"),R,Nn):"extends"==e||"implements"==e||u&&","==n?N(u?cn:F,Nn):"{"==n?N(C("}"),An,R):void 0}function An(n,e){return"variable"==n||"keyword"==M.style?("async"==e||"static"==e||"get"==e||"set"==e||u&&("public"==e||"private"==e||"protected"==e||"readonly"==e||"abstract"==e))&&M.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(M.marked="keyword",N(An)):(M.marked="property",N(u?Tn:_n,An)):"["==n?N(F,V("]"),u?Tn:_n,An):"*"==e?(M.marked="keyword",N(An)):";"==n?N(An):"}"==n?N():"@"==e?N(F,An):void 0}function Tn(n,e){return"?"==e?N(Tn):":"==n?N(cn,vn):"="==e?N(P):j(_n)}function On(n,e){return"*"==e?(M.marked="keyword",N(Fn,V(";"))):"default"==e?(M.marked="keyword",N(F,V(";"))):"{"==n?N(an(In,"}"),Fn,V(";")):j(L)}function In(n,e){return"as"==e?(M.marked="keyword",N(V("variable"))):"variable"==n?j(P,In):void 0}function Cn(n){return"string"==n?N():j(Rn,Vn,Fn)}function Rn(n,e){return"{"==n?on(Rn,"}"):("variable"==n&&A(e),"*"==e&&(M.marked="keyword"),N(Ln))}function Vn(n){if(","==n)return N(Rn,Vn)}function Ln(n,e){if("as"==e)return M.marked="keyword",N(Rn)}function Fn(n,e){if("from"==e)return M.marked="keyword",N(F)}function Pn(n){return"]"==n?N():j(an(P,"]"))}function zn(n,e){return"operator"==n.lastType||","==n.lastType||h.test(e.charAt(0))||/[,.]/.test(e.charAt(0))}return R.lex=!0,{startState:function(n){var e={tokenize:v,lastType:"sof",cc:[],lexical:new E((n||0)-o,0,"block",!1),localVars:r.localVars,context:r.localVars&&{vars:r.localVars},indented:n||0};return r.globalVars&&"object"==typeof r.globalVars&&(e.globalVars=r.globalVars),e},token:function(n,e){if(n.sol()&&(e.lexical.hasOwnProperty("align")||(e.lexical.align=!1),e.indented=n.indentation(),w(n,e)),e.tokenize!=b&&n.eatSpace())return null;var t=e.tokenize(n,e);return"comment"==i?t:(e.lastType="operator"!=i||"++"!=a&&"--"!=a?i:"incdec",S(e,t,i,a,n))},indent:function(e,t){if(e.tokenize==b)return n.Pass;if(e.tokenize!=v)return 0;var i,a=t&&t.charAt(0),l=e.lexical;if(!/^\s*else\b/.test(t))for(var c=e.cc.length-1;c>=0;--c){var u=e.cc[c];if(u==R)l=l.prev;else if(u!=bn)break}while(("stat"==l.type||"form"==l.type)&&("}"==a||(i=e.cc[e.cc.length-1])&&(i==G||i==W)&&!/^[,\.=+\-*:?[\(]/.test(t)))l=l.prev;s&&")"==l.type&&"stat"==l.prev.type&&(l=l.prev);var f=l.type,p=a==f;return"vardef"==f?l.indented+("operator"==e.lastType||","==e.lastType?l.info+1:0):"form"==f&&"{"==a?l.indented:"form"==f?l.indented+o:"stat"==f?l.indented+(zn(e,t)?s||o:0):"switch"!=l.info||p||0==r.doubleIndentSwitch?l.align?l.column+(p?0:1):l.indented+(p?0:o):l.indented+(/^(?:case|default)\b/.test(t)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:c?null:"/*",blockCommentEnd:c?null:"*/",lineComment:c?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:c?"json":"javascript",jsonldMode:l,jsonMode:c,expressionAllowed:e,skipExpression:function(n){var e=n.cc[n.cc.length-1];e!=F&&e!=P||n.cc.pop()}}})),n.registerHelper("wordChars","javascript",/[\w$]/),n.defineMIME("text/javascript","javascript"),n.defineMIME("text/ecmascript","javascript"),n.defineMIME("application/javascript","javascript"),n.defineMIME("application/x-javascript","javascript"),n.defineMIME("application/ecmascript","javascript"),n.defineMIME("application/json",{name:"javascript",json:!0}),n.defineMIME("application/x-json",{name:"javascript",json:!0}),n.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),n.defineMIME("text/typescript",{name:"javascript",typescript:!0}),n.defineMIME("application/typescript",{name:"javascript",typescript:!0})}))}}]);