(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bacc0f62"],{2423:function(t,n,e){"use strict";e.d(n,"a",(function(){return l}));var a=e("1c1e");function l(t){return Object(a["a"])({url:"/article/list",method:"get",params:t})}},ca54:function(t,n,e){"use strict";e.r(n);var a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"app-container"},[e("el-button",{staticStyle:{"margin-bottom":"20px"},attrs:{type:"primary",icon:"document",loading:t.downloadLoading},on:{click:t.handleDownload}},[t._v("导出zip")]),t._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading.body",value:t.listLoading,expression:"listLoading",modifiers:{body:!0}}],attrs:{data:t.list,"element-loading-text":"拼命加载中",border:"",fit:"","highlight-current-row":""}},[e("el-table-column",{attrs:{align:"center",label:"ID",width:"95"},scopedSlots:t._u([{key:"default",fn:function(n){return[t._v("\n        "+t._s(n.$index)+"\n      ")]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"文章标题"},scopedSlots:t._u([{key:"default",fn:function(n){return[t._v("\n        "+t._s(n.row.title)+"\n      ")]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"作者",width:"95",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-tag",[t._v(t._s(n.row.author))])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"阅读数",width:"115",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[t._v("\n        "+t._s(n.row.pageviews)+"\n      ")]}}])}),t._v(" "),e("el-table-column",{attrs:{align:"center",prop:"created_at",label:"发布时间",width:"220"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("i",{staticClass:"el-icon-time"}),t._v(" "),e("span",[t._v(t._s(n.row.display_time))])]}}])})],1)],1)},l=[],i=e("2423"),o={data:function(){return{list:null,listLoading:!0,downloadLoading:!1}},created:function(){this.fetchData()},methods:{fetchData:function(){var t=this;this.listLoading=!0,Object(i["a"])().then((function(n){t.list=n.data.items,t.listLoading=!1}))},handleDownload:function(){this.downloadLoading=!0},formatJson:function(t,n){return n.map((function(n){return t.map((function(t){return n[t]}))}))}}},r=o,s=e("2877"),c=Object(s["a"])(r,a,l,!1,null,null,null);n["default"]=c.exports}}]);