(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6d569870"],{2767:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"components-container"},[e._m(0),e._v(" "),i("pan-thumb",{attrs:{image:e.image}}),e._v(" "),i("el-button",{staticStyle:{position:"absolute",bottom:"15px","margin-left":"40px"},attrs:{type:"primary",icon:"upload"},on:{click:function(t){e.imagecropperShow=!0}}},[e._v("修改头像\n  ")]),e._v(" "),i("image-cropper",{directives:[{name:"show",rawName:"v-show",value:e.imagecropperShow,expression:"imagecropperShow"}],key:e.imagecropper<PERSON>ey,attrs:{width:300,height:300,url:"https://httpbin.org/post"},on:{close:e.close,"crop-upload-success":e.cropSuccess}})],1)},a=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("code",[e._v("这里核心代码用的是"),i("a",{staticClass:"link-type",attrs:{href:"//github.com/dai-siki/vue-image-crop-upload"}},[e._v(" vue-image-crop-upload")]),e._v("\n  由于我在使用时它只有vue@1版本，而且有些业务的需求耦合到七牛等等原因吧，自己改造了一下，如果大家要使用的话，优先还是使用官方component\n  ")])}],r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],staticClass:"vue-image-crop-upload"},[i("div",{staticClass:"vicp-wrap"},[i("div",{staticClass:"vicp-close",on:{click:e.off}},[i("i",{staticClass:"vicp-icon4"})]),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:1==e.step,expression:"step == 1"}],staticClass:"vicp-step1"},[i("div",{staticClass:"vicp-drop-area",on:{dragleave:e.preventDefault,dragover:e.preventDefault,dragenter:e.preventDefault,click:e.handleClick,drop:e.handleChange}},[i("i",{directives:[{name:"show",rawName:"v-show",value:1!=e.loading,expression:"loading != 1"}],staticClass:"vicp-icon1"},[i("i",{staticClass:"vicp-icon1-arrow"}),e._v(" "),i("i",{staticClass:"vicp-icon1-body"}),e._v(" "),i("i",{staticClass:"vicp-icon1-bottom"})]),e._v(" "),i("span",{directives:[{name:"show",rawName:"v-show",value:1!==e.loading,expression:"loading !== 1"}],staticClass:"vicp-hint"},[e._v(e._s(e.lang.hint))]),e._v(" "),i("span",{directives:[{name:"show",rawName:"v-show",value:!e.isSupported,expression:"!isSupported"}],staticClass:"vicp-no-supported-hint"},[e._v(e._s(e.lang.noSupported))]),e._v(" "),i("input",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"fileinput",attrs:{type:"file"},on:{change:e.handleChange}})]),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.hasError,expression:"hasError"}],staticClass:"vicp-error"},[i("i",{staticClass:"vicp-icon2"}),e._v(" "+e._s(e.errorMsg)+"\n            ")]),e._v(" "),i("div",{staticClass:"vicp-operate"},[i("a",{on:{click:e.off,mousedown:e.ripple}},[e._v(e._s(e.lang.btn.off))])])]),e._v(" "),2==e.step?i("div",{staticClass:"vicp-step2"},[i("div",{staticClass:"vicp-crop"},[i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}],staticClass:"vicp-crop-left"},[i("div",{staticClass:"vicp-img-container"},[i("img",{ref:"img",staticClass:"vicp-img",style:e.sourceImgStyle,attrs:{src:e.sourceImgUrl,draggable:"false"},on:{drag:e.preventDefault,dragstart:e.preventDefault,dragend:e.preventDefault,dragleave:e.preventDefault,dragover:e.preventDefault,dragenter:e.preventDefault,drop:e.preventDefault,mousedown:e.imgStartMove,mousemove:e.imgMove,mouseup:e.createImg,mouseout:e.createImg}}),e._v(" "),i("div",{staticClass:"vicp-img-shade vicp-img-shade-1",style:e.sourceImgShadeStyle}),e._v(" "),i("div",{staticClass:"vicp-img-shade vicp-img-shade-2",style:e.sourceImgShadeStyle})]),e._v(" "),i("div",{staticClass:"vicp-range"},[i("input",{attrs:{type:"range",step:"1",min:"0",max:"100"},domProps:{value:e.scale.range},on:{change:e.zoomChange}}),e._v(" "),i("i",{staticClass:"vicp-icon5",on:{mousedown:e.startZoomSub,mouseout:e.endZoomSub,mouseup:e.endZoomSub}}),e._v(" "),i("i",{staticClass:"vicp-icon6",on:{mousedown:e.startZoomAdd,mouseout:e.endZoomAdd,mouseup:e.endZoomAdd}})])]),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}],staticClass:"vicp-crop-right"},[i("div",{staticClass:"vicp-preview"},[i("div",{staticClass:"vicp-preview-item"},[i("img",{style:e.previewStyle,attrs:{src:e.createImgUrl}}),e._v(" "),i("span",[e._v(e._s(e.lang.preview))])]),e._v(" "),i("div",{staticClass:"vicp-preview-item"},[e.noCircle?e._e():i("img",{style:e.previewStyle,attrs:{src:e.createImgUrl}}),e._v(" "),i("span",[e._v(e._s(e.lang.preview))])])])])]),e._v(" "),i("div",{staticClass:"vicp-operate"},[i("a",{on:{click:function(t){return e.setStep(1)},mousedown:e.ripple}},[e._v(e._s(e.lang.btn.back))]),e._v(" "),i("a",{staticClass:"vicp-operate-btn",on:{click:e.upload,mousedown:e.ripple}},[e._v(e._s(e.lang.btn.save))])])]):e._e(),e._v(" "),3==e.step?i("div",{staticClass:"vicp-step3"},[i("div",{staticClass:"vicp-upload"},[i("span",{directives:[{name:"show",rawName:"v-show",value:1===e.loading,expression:"loading === 1"}],staticClass:"vicp-loading"},[e._v(e._s(e.lang.loading))]),e._v(" "),i("div",{staticClass:"vicp-progress-wrap"},[i("span",{directives:[{name:"show",rawName:"v-show",value:1===e.loading,expression:"loading === 1"}],staticClass:"vicp-progress",style:e.progressStyle})]),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.hasError,expression:"hasError"}],staticClass:"vicp-error"},[i("i",{staticClass:"vicp-icon2"}),e._v(" "+e._s(e.errorMsg)+"\n                ")]),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:2===e.loading,expression:"loading === 2"}],staticClass:"vicp-success"},[i("i",{staticClass:"vicp-icon3"}),e._v(" "+e._s(e.lang.success)+"\n                ")])]),e._v(" "),i("div",{staticClass:"vicp-operate"},[i("a",{on:{click:function(t){return e.setStep(2)},mousedown:e.ripple}},[e._v(e._s(e.lang.btn.back))]),e._v(" "),i("a",{on:{click:e.off,mousedown:e.ripple}},[e._v(e._s(e.lang.btn.close))])])]):e._e(),e._v(" "),i("canvas",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"canvas",attrs:{width:e.width,height:e.height}})])])},o=[],n=(i("456d"),i("ac6a"),i("53ca"));i("c5f6"),i("34ef"),i("28a5");function c(e,t){var i=Object.assign({ele:e.target,type:"hit",bgc:"rgba(0, 0, 0, 0.15)"},t),s=i.ele;if(s){var a=s.getBoundingClientRect(),r=s.querySelector(".e-ripple");switch(r?r.className="e-ripple":(r=document.createElement("span"),r.className="e-ripple",r.style.height=r.style.width=Math.max(a.width,a.height)+"px",s.appendChild(r)),i.type){case"center":r.style.top=a.height/2-r.offsetHeight/2+"px",r.style.left=a.width/2-r.offsetWidth/2+"px";break;default:r.style.top=e.pageY-a.top-r.offsetHeight/2-document.body.scrollTop+"px",r.style.left=e.pageX-a.left-r.offsetWidth/2-document.body.scrollLeft+"px"}return r.style.backgroundColor=i.bgc,r.className="e-ripple z-active",!1}}function l(e,t){e=e.split(",")[1],e=window.atob(e);for(var i=new Uint8Array(e.length),s=0;s<e.length;s++)i[s]=e.charCodeAt(s);return new Blob([i],{type:t})}var h=i("1c1e"),p={zh:{hint:"点击，或拖动图片至此处",loading:"正在上传……",noSupported:"浏览器不支持该功能，请使用IE10以上或其他现在浏览器！",success:"上传成功",fail:"图片上传失败",preview:"头像预览",btn:{off:"取消",close:"关闭",back:"上一步",save:"保存"},error:{onlyImg:"仅限图片格式",outOfSize:"单文件大小不能超过 ",lowestPx:"图片最低像素为（宽*高）："}},en:{hint:"Click, or drag the file here",loading:"Uploading……",noSupported:"Browser does not support, please use IE10+ or other browsers",success:"Upload success",fail:"Upload failed",preview:"Preview",btn:{off:"Cancel",close:"Close",back:"Back",save:"Save"},error:{onlyImg:"Image only",outOfSize:"Image exceeds size limit: ",lowestPx:"The lowest pixel in the image: "}}},u=p,g={jpg:"image/jpeg",png:"image/png",gif:"image/gif",svg:"image/svg+xml",psd:"image/photoshop"},d={props:{field:{type:String,default:"avatar"},url:{type:String,default:""},params:{type:Object,default:null},width:{type:Number,default:200},height:{type:Number,default:200},noCircle:{type:Boolean,default:!1},maxSize:{type:Number,default:10240},langType:{type:String,default:"zh"}},data:function(){var e=this,t=e.langType,i=e.width,s=e.height,a=!0,r=u[t]?u[t]:r["zh"];return"function"!=typeof FormData&&(a=!1),{show:!0,mime:g["jpg"],lang:r,isSupported:a,step:1,loading:0,progress:0,hasError:!1,errorMsg:"",ratio:i/s,sourceImg:null,sourceImgUrl:"",createImgUrl:"",sourceImgMouseDown:{on:!1,mX:0,mY:0,x:0,y:0},previewContainer:{width:100,height:100},sourceImgContainer:{width:240,height:180},scale:{zoomAddOn:!1,zoomSubOn:!1,range:1,x:0,y:0,width:0,height:0,maxWidth:0,maxHeight:0,minWidth:0,minHeight:0,naturalWidth:0,naturalHeight:0}}},computed:{progressStyle:function(){var e=this.progress;return{width:e+"%"}},sourceImgStyle:function(){var e=this.scale,t=this.sourceImgMasking;return{top:e.y+t.y+"px",left:e.x+t.x+"px",width:e.width+"px",height:e.height+"px"}},sourceImgMasking:function(){var e=this.width,t=this.height,i=this.ratio,s=this.sourceImgContainer,a=s,r=a.width/a.height,o=0,n=0,c=a.width,l=a.height,h=1;return i<r&&(h=a.height/t,c=a.height*i,o=(a.width-c)/2),i>r&&(h=a.width/e,l=a.width/i,n=(a.height-l)/2),{scale:h,x:o,y:n,width:c,height:l}},sourceImgShadeStyle:function(){var e=this.sourceImgContainer,t=this.sourceImgMasking,i=t.width==e.width?t.width:(e.width-t.width)/2,s=t.height==e.height?t.height:(e.height-t.height)/2;return{width:i+"px",height:s+"px"}},previewStyle:function(){this.width,this.height;var e=this.ratio,t=this.previewContainer,i=t,s=i.width,a=i.height,r=s/a;return e<r&&(s=i.height*e),e>r&&(a=i.width/e),{width:s+"px",height:a+"px"}}},methods:{ripple:function(e){c(e)},off:function(){this.show=!1,this.$emit("close")},setStep:function(e){var t=this;setTimeout((function(){t.step=e}),200)},preventDefault:function(e){return e.preventDefault(),!1},handleClick:function(e){1!==this.loading&&e.target!==this.$refs.fileinput&&(e.preventDefault(),document.activeElement!==this.$refs&&this.$refs.fileinput.click())},handleChange:function(e){if(e.preventDefault(),1!==this.loading){var t=e.target.files||e.dataTransfer.files;this.reset(),this.checkFile(t[0])&&this.setSourceImg(t[0])}},checkFile:function(e){var t=this,i=t.lang,s=t.maxSize;return-1===e.type.indexOf("image")?(t.hasError=!0,t.errorMsg=i.error.onlyImg,!1):(this.mime=e.type,!(e.size/1024>s)||(t.hasError=!0,t.errorMsg=i.error.outOfSize+s+"kb",!1))},reset:function(){var e=this;e.step=1,e.loading=0,e.hasError=!1,e.errorMsg="",e.progress=0},setSourceImg:function(e){var t=this,i=new FileReader;i.onload=function(e){t.sourceImgUrl=i.result,t.startCrop()},i.readAsDataURL(e)},startCrop:function(){var e=this,t=(e.width,e.height,e.ratio),i=e.scale,s=e.sourceImgUrl,a=e.sourceImgMasking,r=(e.lang,a),o=new Image;o.src=s,o.onload=function(){var s=o.naturalWidth,a=o.naturalHeight,n=s/a,c=r.width,l=r.height,h=0,p=0;t>n&&(l=c/n,p=(r.height-l)/2),t<n&&(c=l*n,h=(r.width-c)/2),i.range=0,i.x=h,i.y=p,i.width=c,i.height=l,i.minWidth=c,i.minHeight=l,i.maxWidth=s*r.scale,i.maxHeight=a*r.scale,i.naturalWidth=s,i.naturalHeight=a,e.sourceImg=o,e.createImg(),e.setStep(2)}},imgStartMove:function(e){var t=this.sourceImgMouseDown,i=this.scale,s=t;s.mX=e.screenX,s.mY=e.screenY,s.x=i.x,s.y=i.y,s.on=!0},imgMove:function(e){var t=this.sourceImgMouseDown,i=t.on,s=t.mX,a=t.mY,r=t.x,o=t.y,n=this.scale,c=this.sourceImgMasking,l=c,h=e.screenX,p=e.screenY,u=h-s,g=p-a,d=r+u,v=o+g;i&&(d>0&&(d=0),v>0&&(v=0),d<l.width-n.width&&(d=l.width-n.width),v<l.height-n.height&&(v=l.height-n.height),n.x=d,n.y=v)},startZoomAdd:function(e){var t=this,i=t.scale;function s(){if(i.zoomAddOn){var e=i.range>=100?100:++i.range;t.zoomImg(e),setTimeout((function(){s()}),60)}}i.zoomAddOn=!0,s()},endZoomAdd:function(e){this.scale.zoomAddOn=!1},startZoomSub:function(e){var t=this,i=t.scale;function s(){if(i.zoomSubOn){var e=i.range<=0?0:--i.range;t.zoomImg(e),setTimeout((function(){s()}),60)}}i.zoomSubOn=!0,s()},endZoomSub:function(e){var t=this.scale;t.zoomSubOn=!1},zoomChange:function(e){this.zoomImg(e.target.value)},zoomImg:function(e){var t=this,i=this.sourceImgMasking,s=(this.sourceImgMouseDown,this.scale),a=s.maxWidth,r=s.maxHeight,o=s.minWidth,n=s.minHeight,c=s.width,l=s.height,h=s.x,p=s.y,u=(s.range,i),g=u.width,d=u.height,v=o+(a-o)*e/100,m=n+(r-n)*e/100,f=g/2-v/c*(g/2-h),w=d/2-m/l*(d/2-p);f>0&&(f=0),w>0&&(w=0),f<g-v&&(f=g-v),w<d-m&&(w=d-m),s.x=f,s.y=w,s.width=v,s.height=m,s.range=e,setTimeout((function(){s.range==e&&t.createImg()}),300)},createImg:function(e){var t=this,i=t.mime,s=t.sourceImg,a=t.scale,r=a.x,o=a.y,n=a.width,c=a.height,l=t.sourceImgMasking.scale,h=t.$refs.canvas,p=h.getContext("2d");e&&(t.sourceImgMouseDown.on=!1),p.drawImage(s,r/l,o/l,n/l,c/l),t.createImgUrl=h.toDataURL(i)},upload:function(){var e=this,t=this.lang,i=this.imgFormat,s=this.mime,a=this.url,r=this.params,o=(this.headers,this.field),c=this.ki,p=this.createImgUrl,u=new FormData;u.append(o,l(p,s),o+"."+i),"object"==Object(n["a"])(r)&&r&&Object.keys(r).forEach((function(e){u.append(e,r[e])})),e.reset(),e.loading=1,e.setStep(3),e.$emit("crop-success",p,o,c),Object(h["a"])({url:a,method:"post",data:u}).then((function(t){e.loading=2,e.$emit("crop-upload-success",t.data)})).catch((function(i){e.value&&(e.loading=3,e.hasError=!0,e.errorMsg=t.fail,e.$emit("crop-upload-fail",i,o,c))}))}}},v=d,m=(i("67af"),i("2877")),f=Object(m["a"])(v,r,o,!1,null,"a07960cc",null),w=f.exports,y=i("3cbc"),b={components:{ImageCropper:w,PanThumb:y["a"]},data:function(){return{imagecropperShow:!1,imagecropperKey:0,image:"https://wpimg.wallstcn.com/577965b9-bb9e-4e02-9f0c-095b41417191"}},methods:{cropSuccess:function(e){this.imagecropperShow=!1,this.imagecropperKey=this.imagecropperKey+1,this.image=e.files.avatar},close:function(){this.imagecropperShow=!1}}},x=b,C=(i("b92b"),Object(m["a"])(x,s,a,!1,null,"1efe51ea",null));t["default"]=C.exports},"28a5":function(e,t,i){"use strict";var s=i("aae3"),a=i("cb7c"),r=i("ebd6"),o=i("0390"),n=i("9def"),c=i("5f1b"),l=i("520a"),h=i("79e5"),p=Math.min,u=[].push,g="split",d="length",v="lastIndex",m=4294967295,f=!h((function(){RegExp(m,"y")}));i("214f")("split",2,(function(e,t,i,h){var w;return w="c"=="abbc"[g](/(b)*/)[1]||4!="test"[g](/(?:)/,-1)[d]||2!="ab"[g](/(?:ab)*/)[d]||4!="."[g](/(.?)(.?)/)[d]||"."[g](/()()/)[d]>1||""[g](/.?/)[d]?function(e,t){var a=String(this);if(void 0===e&&0===t)return[];if(!s(e))return i.call(a,e,t);var r,o,n,c=[],h=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),p=0,g=void 0===t?m:t>>>0,f=new RegExp(e.source,h+"g");while(r=l.call(f,a)){if(o=f[v],o>p&&(c.push(a.slice(p,r.index)),r[d]>1&&r.index<a[d]&&u.apply(c,r.slice(1)),n=r[0][d],p=o,c[d]>=g))break;f[v]===r.index&&f[v]++}return p===a[d]?!n&&f.test("")||c.push(""):c.push(a.slice(p)),c[d]>g?c.slice(0,g):c}:"0"[g](void 0,0)[d]?function(e,t){return void 0===e&&0===t?[]:i.call(this,e,t)}:i,[function(i,s){var a=e(this),r=void 0==i?void 0:i[t];return void 0!==r?r.call(i,a,s):w.call(String(a),i,s)},function(e,t){var s=h(w,e,this,t,w!==i);if(s.done)return s.value;var l=a(e),u=String(this),g=r(l,RegExp),d=l.unicode,v=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(f?"y":"g"),y=new g(f?l:"^(?:"+l.source+")",v),b=void 0===t?m:t>>>0;if(0===b)return[];if(0===u.length)return null===c(y,u)?[u]:[];var x=0,C=0,_=[];while(C<u.length){y.lastIndex=f?C:0;var S,I=c(y,f?u:u.slice(C));if(null===I||(S=p(n(y.lastIndex+(f?0:C)),u.length))===x)C=o(u,C,d);else{if(_.push(u.slice(x,C)),_.length===b)return _;for(var k=1;k<=I.length-1;k++)if(_.push(I[k]),_.length===b)return _;C=x=S}}return _.push(u.slice(x)),_}]}))},"3cbc":function(e,t,i){"use strict";var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"pan-item",style:{zIndex:e.zIndex,height:e.height,width:e.width}},[i("div",{staticClass:"pan-info"},[i("div",{staticClass:"pan-info-roles-container"},[e._t("default")],2)]),e._v(" "),i("img",{staticClass:"pan-thumb",attrs:{src:e.image}})])},a=[],r=(i("c5f6"),{name:"PanThumb",props:{image:{type:String,required:!0},zIndex:{type:Number,default:100},width:{type:String,default:"150px"},height:{type:String,default:"150px"}}}),o=r,n=(i("d103"),i("2877")),c=Object(n["a"])(o,s,a,!1,null,"530f5f14",null);t["a"]=c.exports},"67af":function(e,t,i){"use strict";i("c47c")},"8fa2":function(e,t,i){},9087:function(e,t,i){},b92b:function(e,t,i){"use strict";i("9087")},c47c:function(e,t,i){},d103:function(e,t,i){"use strict";i("8fa2")}}]);