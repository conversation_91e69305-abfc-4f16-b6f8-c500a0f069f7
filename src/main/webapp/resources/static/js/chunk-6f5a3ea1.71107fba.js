(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6f5a3ea1"],{"1b78":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container calendar-list-container"},[n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"请输入角色名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}}),t._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"请输入角色sn"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.sn,callback:function(e){t.$set(t.listQuery,"sn",e)},expression:"listQuery.sn"}}),t._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{size:"small",type:"primary",icon:"search"},on:{click:t.handleFilter}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"filter-item el-icon-plus",staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"success"},on:{click:t.handleCreate}},[t._v("添加")]),t._v(" "),t._e(),t._v(" "),t._e()],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],key:t.tableKey,staticStyle:{width:"100%"},attrs:{data:t.list,"element-loading-text":"给我一点时间",stripe:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{align:"center",label:"序号",width:"65px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.id))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"150px",align:"center",label:"名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{on:{click:function(n){return t.handleUpdate(e.row)}}},[t._v(t._s(e.row.name))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"100px",align:"center",label:"角色sn"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.sn))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"150px",align:"center",label:"所属系统"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.siteName))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"180px",align:"center",label:"时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("parseTime")(e.row.createTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"class-name":"status-col",label:"状态",width:"90px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-tag",{attrs:{type:t._f("statusFilter")(e.row.status)}},[t._v(t._s(t._f("statusText")(e.row.status)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"center",label:"操作","min-width":"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.status&&"admin"!=e.row.sn?n("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(n){return t.handleModifyStatus(e.row,1)}}},[t._v("禁用\n\t\t\t\t\t")]):t._e(),t._v(" "),1==e.row.status&&"admin"!=e.row.sn?n("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(n){return t.handleModifyStatus(e.row,0)}}},[t._v("开启\n\t\t\t\t\t")]):t._e(),t._v(" "),n("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(n){return t.handleUser(e.row.id)}}},[t._v("该角色用户\n          ")])]}}])})],1),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:!t.listLoading,expression:"!listLoading"}],staticClass:"pagination-container"},[n("el-pagination",{attrs:{"current-page":t.listQuery.page,"page-sizes":[10,15,20,30],"page-size":t.listQuery.size,layout:" sizes, prev, pager, next,total, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.listQuery,"page",e)},"update:current-page":function(e){return t.$set(t.listQuery,"page",e)}}})],1),t._v(" "),n("el-dialog",{staticClass:"ma-small",attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible,width:"80%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[n("el-form",{ref:"temp",staticClass:"small-space",staticStyle:{width:"400px","margin-left":"50px"},attrs:{model:t.temp,rules:t.rules,"label-position":"left","label-width":"70px"}},[n("el-form-item",{staticClass:"ma_role",attrs:{label:"角色名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入角色名称"},model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name",e)},expression:"temp.name"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"权限"}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"330px"},attrs:{multiple:!0,placeholder:"选择权限"},on:{change:t.handleFilter},model:{value:t.permission,callback:function(e){t.permission=e},expression:"permission"}},t._l(t.permissions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},["create"==t.dialogStatus?n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.create("temp")}}},[t._v("确 定")]):n("el-button",{attrs:{type:"primary"},on:{click:t.update}},[t._v("确 定")]),t._v(" "),n("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")])],1)],1),t._v(" "),n("el-dialog",{staticClass:"ma-smalldel",attrs:{visible:t.userVisible},on:{"update:visible":function(e){t.userVisible=e}}},[n("div",{staticClass:"deltip mb_30"},[t._v("确定删除该角色？")]),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},[n("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.handleRole("deleted")}}},[t._v("删 除")]),t._v(" "),n("el-button",{on:{click:function(e){t.userVisible=!1}}},[t._v("取 消")])],1)])],1)},r=[],a=(n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("cc5e")),s=n("6724"),l=n("ed08");function o(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=u(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return s=t.done,t},e:function(t){l=!0,a=t},f:function(){try{s||null==n.return||n.return()}finally{if(l)throw a}}}}function u(t,e){if(t){if("string"===typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var d={name:"siteRoleManager",directives:{waves:s["a"]},data:function(){return{list:[],total:null,listLoading:!0,listQuery:{page:0,size:15,name:"",siteId:this.$route.query.siteId,sn:""},temp:{id:void 0,importance:0,remark:"",timestamp:0,username:"",password:"",email:"",phone:"",type:0,status:0,deleteUid:""},rules:{name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:10,message:"长度在 2到 10 个字符",trigger:"blur"}]},permissions:[],permission:"",importanceOptions:[1,2,3],sortOptions:[{label:"按ID升序列",key:"+id"},{label:"按ID降序",key:"-id"}],statusOptions:["启用","禁用"],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"编辑",create:"添加角色"},dialogPvVisible:!1,userVisible:!1,pvData:[],showAuditor:!1,tableKey:0,showPassword:!1}},filters:{statusFilter:function(t){var e={0:"success",1:"gray"};return e[t]},statusText:function(t){var e={0:"启用",1:"禁用"};return e[t]},typeFilter:function(t){return calendarTypeKeyValue[t]}},created:function(){this.getList(),this.getPer()},methods:{getList:function(){var t=this;this.listLoading=!0,Object(a["d"])(this.listQuery).then((function(e){t.list=e.obj.content,t.total=e.obj.totalElements,t.listLoading=!1}))},handleUser:function(t){this.$router.push({path:"/user/user",query:{roleId:t}})},getPer:function(){},handleFilter:function(){this.listQuery.page=0,this.getList()},handleSizeChange:function(t){this.listQuery.size=t,this.getList()},handleCurrentChange:function(t){this.listQuery.page=t-1,this.getList()},delRole:function(t){this.userVisible=!0,this.deleteUid=t},handleRole:function(t){this.$message({message:"操作成功",type:"success"}),this.deleteUid.status=t,this.userVisible=!1},handleModifyStatus:function(t,e){var n=this;Object(a["f"])(t.id).then((function(i){n.successTip(),t.status=e})).catch((function(t){n.failTip()}))},handleCreate:function(){this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0},handleUpdate:function(t){this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0},handleDelete:function(t){this.$notify({title:"成功",message:"删除成功",type:"success",duration:2e3});var e=this.list.indexOf(t);this.list.splice(e,1)},successTip:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"成功";this.dialogFormVisible=!1,this.$notify({title:"成功",message:t,type:"success",duration:2e3})},failTip:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"失败";this.$notify({title:"失败",message:t,type:"fail",duration:2e3})},create:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return e.failTip(),!1;Object(a["a"])(e.temp).then((function(t){e.temp.id=parseInt(100*Math.random())+1024,e.temp.timestamp=+new Date,e.list.unshift(e.temp),e.successTip(),e.getList()})).catch((function(t){e.failTip(t)}))}))},update:function(){this.temp.timestamp=+this.temp.timestamp;var t,e=o(this.list);try{for(e.s();!(t=e.n()).done;){var n=t.value;if(n.id===this.temp.id){var i=this.list.indexOf(n);this.list.splice(i,1,this.temp);break}}}catch(r){e.e(r)}finally{e.f()}this.dialogFormVisible=!1,this.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})},resetTemp:function(){this.temp={id:void 0,importance:0,remark:"",timestamp:0,title:"",status:0,type:0}},handleDownload:function(){},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return"timestamp"===t?Object(l["f"])(e[t]):e[t]}))}))}}},f=d,p=(n("fd1c"),n("2877")),h=Object(p["a"])(f,i,r,!1,null,null,null);e["default"]=h.exports},"28a5":function(t,e,n){"use strict";var i=n("aae3"),r=n("cb7c"),a=n("ebd6"),s=n("0390"),l=n("9def"),o=n("5f1b"),u=n("520a"),c=n("79e5"),d=Math.min,f=[].push,p="split",h="length",m="lastIndex",v=4294967295,g=!c((function(){RegExp(v,"y")}));n("214f")("split",2,(function(t,e,n,c){var y;return y="c"=="abbc"[p](/(b)*/)[1]||4!="test"[p](/(?:)/,-1)[h]||2!="ab"[p](/(?:ab)*/)[h]||4!="."[p](/(.?)(.?)/)[h]||"."[p](/()()/)[h]>1||""[p](/.?/)[h]?function(t,e){var r=String(this);if(void 0===t&&0===e)return[];if(!i(t))return n.call(r,t,e);var a,s,l,o=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,p=void 0===e?v:e>>>0,g=new RegExp(t.source,c+"g");while(a=u.call(g,r)){if(s=g[m],s>d&&(o.push(r.slice(d,a.index)),a[h]>1&&a.index<r[h]&&f.apply(o,a.slice(1)),l=a[0][h],d=s,o[h]>=p))break;g[m]===a.index&&g[m]++}return d===r[h]?!l&&g.test("")||o.push(""):o.push(r.slice(d)),o[h]>p?o.slice(0,p):o}:"0"[p](void 0,0)[h]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,i){var r=t(this),a=void 0==n?void 0:n[e];return void 0!==a?a.call(n,r,i):y.call(String(r),n,i)},function(t,e){var i=c(y,t,this,e,y!==n);if(i.done)return i.value;var u=r(t),f=String(this),p=a(u,RegExp),h=u.unicode,m=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(g?"y":"g"),b=new p(g?u:"^(?:"+u.source+")",m),w=void 0===e?v:e>>>0;if(0===w)return[];if(0===f.length)return null===o(b,f)?[f]:[];var _=0,x=0,k=[];while(x<f.length){b.lastIndex=g?x:0;var S,O=o(b,g?f:f.slice(x));if(null===O||(S=d(l(b.lastIndex+(g?0:x)),f.length))===_)x=s(f,x,h);else{if(k.push(f.slice(_,x)),k.length===w)return k;for(var C=1;C<=O.length-1;C++)if(k.push(O[C]),k.length===w)return k;x=_=S}}return k.push(f.slice(_)),k}]}))},4917:function(t,e,n){"use strict";var i=n("cb7c"),r=n("9def"),a=n("0390"),s=n("5f1b");n("214f")("match",1,(function(t,e,n,l){return[function(n){var i=t(this),r=void 0==n?void 0:n[e];return void 0!==r?r.call(n,i):new RegExp(n)[e](String(i))},function(t){var e=l(n,t,this);if(e.done)return e.value;var o=i(t),u=String(this);if(!o.global)return s(o,u);var c=o.unicode;o.lastIndex=0;var d,f=[],p=0;while(null!==(d=s(o,u))){var h=String(d[0]);f[p]=h,""===h&&(o.lastIndex=a(u,r(o.lastIndex),c)),p++}return 0===p?null:f}]}))},6724:function(t,e,n){"use strict";n("8d41");var i={bind:function(t,e){t.addEventListener("click",(function(n){var i=Object.assign({},e.value),r=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),a=r.ele;if(a){a.style.position="relative",a.style.overflow="hidden";var s=a.getBoundingClientRect(),l=a.querySelector(".waves-ripple");switch(l?l.className="waves-ripple":(l=document.createElement("span"),l.className="waves-ripple",l.style.height=l.style.width=Math.max(s.width,s.height)+"px",a.appendChild(l)),r.type){case"center":l.style.top=s.height/2-l.offsetHeight/2+"px",l.style.left=s.width/2-l.offsetWidth/2+"px";break;default:l.style.top=n.pageY-s.top-l.offsetHeight/2-document.body.scrollTop+"px",l.style.left=n.pageX-s.left-l.offsetWidth/2-document.body.scrollLeft+"px"}return l.style.backgroundColor=r.color,l.className="waves-ripple z-active",!1}}),!1)}},r=function(t){t.directive("waves",i)};window.Vue&&(window.waves=i,Vue.use(r)),i.install=r;e["a"]=i},"8d41":function(t,e,n){},abc7:function(t,e,n){},cc5e:function(t,e,n){"use strict";n.d(e,"d",(function(){return r})),n.d(e,"f",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return l})),n.d(e,"g",(function(){return o})),n.d(e,"e",(function(){return u})),n.d(e,"h",(function(){return c})),n.d(e,"b",(function(){return d}));var i=n("1c1e");function r(t){return Object(i["a"])({url:"role/page",method:"post",params:t})}function a(t){return Object(i["a"])({url:"role/status",method:"post",params:{id:t}})}function s(t){return Object(i["a"])({url:"/role/page",method:"post",params:t})}function l(t){return Object(i["a"])({url:"/role/add",method:"post",params:t})}function o(t){return Object(i["a"])({url:"/role/edit",method:"post",params:t})}function u(t){return Object(i["a"])({url:"/resource/get-by-role",method:"post",params:{roleId:t}})}function c(t){return Object(i["a"])({url:"/role/grant",method:"post",params:t})}function d(t){return Object(i["a"])({url:"/role/list-user",method:"post",params:t})}},ed08:function(t,e,n){"use strict";n.d(e,"f",(function(){return l})),n.d(e,"d",(function(){return o})),n.d(e,"g",(function(){return u})),n.d(e,"a",(function(){return c})),n.d(e,"b",(function(){return d})),n.d(e,"e",(function(){return f})),n.d(e,"c",(function(){return p})),n.d(e,"h",(function(){return h}));n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("28a5"),n("ac6a"),n("456d"),n("4917"),n("a481");var i=n("53ca");function r(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=a(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,l=!0,o=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return l=t.done,t},e:function(t){o=!0,s=t},f:function(){try{l||null==n.return||n.return()}finally{if(o)throw s}}}}function a(t,e){if(t){if("string"===typeof t)return s(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(t,e):void 0}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function l(t,e){if(0===arguments.length)return null;var n,r=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===Object(i["a"])(t)?n=t:(10===(""+t).length&&(t=1e3*parseInt(t)),n=new Date(t));var a={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()},s=r.replace(/{(y|m|d|h|i|s|a)+}/g,(function(t,e){var n=a[e];return"a"===e?["一","二","三","四","五","六","日"][n-1]:(t.length>0&&n<10&&(n="0"+n),n||0)}));return s}function o(t,e){return!t||t.length<=e?t:t.substring(0,e)+"......"}function u(t,e){if(t&&e){var n=t.className,i=n.indexOf(e);-1===i?n+=""+e:n=n.substr(0,i)+n.substr(i+e.length),t.className=n}}function c(t){if(!t&&"object"!==Object(i["a"])(t))throw new Error("error arguments","shallowClone");var e=t.constructor===Array?[]:{};for(var n in t)t.hasOwnProperty(n)&&(t[n]&&"object"===Object(i["a"])(t[n])?(e[n]=t[n].constructor===Array?[]:{},e[n]=c(t[n])):e[n]=t[n]);return e}function d(t,e){var n,i=r(t);try{for(i.s();!(n=i.n()).done;){var a=n.value,s=a[e];s&&0!==s.length?d(s,e):delete a[e]}}catch(l){i.e(l)}finally{i.f()}}function f(t){if(Array.isArray(t)&&t.length>0)return t[t.length-1]}function p(t,e,n,i){if(Array.isArray(t)){var a,s=r(t);try{for(s.s();!(a=s.n()).done;){var l=a.value,o=p(l,e,n,i);if(o)return o}}catch(v){s.e(v)}finally{s.f()}}if(t[i]===e){var u=t[i],c=[t[i]];return{result:u,path:c}}if(t[n]){var d,f=r(t[n]);try{for(f.s();!(d=f.n()).done;){var h=d.value,m=p(h,e,n,i);if(m)return m.path.unshift(t[i]),m}}catch(v){f.e(v)}finally{f.f()}}}function h(t){var e=[];return function t(n){for(var i=n.childNodes,r=0;r<i.length;r++)e.push(i[r].data),t(i[r])}(t),e}},fd1c:function(t,e,n){"use strict";n("abc7")}}]);