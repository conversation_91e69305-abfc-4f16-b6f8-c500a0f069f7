(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-88a929e4"],{"28a5":function(t,e,n){"use strict";var i=n("aae3"),r=n("cb7c"),s=n("ebd6"),a=n("0390"),l=n("9def"),o=n("5f1b"),u=n("520a"),c=n("79e5"),d=Math.min,f=[].push,p="split",m="length",h="lastIndex",v=4294967295,y=!c((function(){RegExp(v,"y")}));n("214f")("split",2,(function(t,e,n,c){var g;return g="c"=="abbc"[p](/(b)*/)[1]||4!="test"[p](/(?:)/,-1)[m]||2!="ab"[p](/(?:ab)*/)[m]||4!="."[p](/(.?)(.?)/)[m]||"."[p](/()()/)[m]>1||""[p](/.?/)[m]?function(t,e){var r=String(this);if(void 0===t&&0===e)return[];if(!i(t))return n.call(r,t,e);var s,a,l,o=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,p=void 0===e?v:e>>>0,y=new RegExp(t.source,c+"g");while(s=u.call(y,r)){if(a=y[h],a>d&&(o.push(r.slice(d,s.index)),s[m]>1&&s.index<r[m]&&f.apply(o,s.slice(1)),l=s[0][m],d=a,o[m]>=p))break;y[h]===s.index&&y[h]++}return d===r[m]?!l&&y.test("")||o.push(""):o.push(r.slice(d)),o[m]>p?o.slice(0,p):o}:"0"[p](void 0,0)[m]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,i){var r=t(this),s=void 0==n?void 0:n[e];return void 0!==s?s.call(n,r,i):g.call(String(r),n,i)},function(t,e){var i=c(g,t,this,e,g!==n);if(i.done)return i.value;var u=r(t),f=String(this),p=s(u,RegExp),m=u.unicode,h=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(y?"y":"g"),b=new p(y?u:"^(?:"+u.source+")",h),w=void 0===e?v:e>>>0;if(0===w)return[];if(0===f.length)return null===o(b,f)?[f]:[];var _=0,x=0,k=[];while(x<f.length){b.lastIndex=y?x:0;var S,O=o(b,y?f:f.slice(x));if(null===O||(S=d(l(b.lastIndex+(y?0:x)),f.length))===_)x=a(f,x,m);else{if(k.push(f.slice(_,x)),k.length===w)return k;for(var j=1;j<=O.length-1;j++)if(k.push(O[j]),k.length===w)return k;x=_=S}}return k.push(f.slice(_)),k}]}))},"3d5b":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container calendar-list-container"},[n("div",{staticClass:"filter-container"},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"请选择系统",clearable:""},on:{change:t.handleFilter},model:{value:t.listQuery.siteId,callback:function(e){t.$set(t.listQuery,"siteId",e)},expression:"listQuery.siteId"}},t._l(t.siteOptions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.description,value:t.id}})})),1),t._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入权限名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}}),t._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"请输入授权字符"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.permission,callback:function(e){t.$set(t.listQuery,"permission",e)},expression:"listQuery.permission"}}),t._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",size:"small",icon:"search"},on:{click:t.handleFilter}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"filter-item el-icon-plus",staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"success"},on:{click:t.handleCreate}},[t._v("添加")]),t._v(" "),t._e(),t._v(" "),t._e()],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],key:t.tableKey,staticStyle:{width:"100%"},attrs:{data:t.list,"element-loading-text":"给我一点时间",stripe:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{align:"center",label:"序号","min-width":"45px",type:"index"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.id))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"150px",align:"center",label:"名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{on:{click:function(n){return t.handleUpdate(e.row)}}},[t._v(t._s(e.row.name))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"150px",align:"center",label:"父级名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{on:{click:function(n){return t.handleUpdate(e.row)}}},[t._v(t._s(e.row.pname))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"130px",align:"center",label:"权限字符串"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.permission))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"100px",align:"center",label:"路径"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.url))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"100px",align:"center",label:"站点"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.siteName))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"180px",align:"center",label:"创建时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("parseTime")(e.row.createTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),t._v(" "),t.showAuditor?n("el-table-column",{attrs:{width:"110px",align:"center",label:"审核人"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{staticStyle:{color:"red"}},[t._v(t._s(e.row.auditor))])]}}],null,!1,1430385659)}):t._e(),t._v(" "),n("el-table-column",{attrs:{"class-name":"status-col",label:"状态",width:"90px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-tag",{attrs:{type:t._f("statusFilter")(e.row.status)}},[t._v(t._s(t._f("statusText")(e.row.status)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"center",label:"操作","min-width":"150px"},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.status?n("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(n){return t.handleModifyStatus(e.row,1)}}},[t._v("禁用\n        ")]):t._e(),t._v(" "),1==e.row.status?n("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(n){return t.handleModifyStatus(e.row,0)}}},[t._v("开启\n        ")]):t._e()]}}])})],1),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:!t.listLoading,expression:"!listLoading"}],staticClass:"pagination-container"},[n("el-pagination",{attrs:{"current-page":t.listQuery.page,"page-sizes":[10,15,20,30],"page-size":t.listQuery.size,layout:"sizes, prev, pager, next,total, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.listQuery,"page",e)},"update:current-page":function(e){return t.$set(t.listQuery,"page",e)}}})],1),t._v(" "),n("el-dialog",{staticClass:"ma-small",attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[n("el-form",{ref:"temp",staticClass:"small-space",staticStyle:{width:"400px","margin-left":"50px"},attrs:{model:t.temp,rules:t.rules,"label-position":"left","label-width":"80px"}},[n("el-form-item",{attrs:{label:"所属系统",prop:"siteId"}},[n("el-select",{staticStyle:{width:"320px"},attrs:{placeholder:"请选择系统",disabled:t.siteDisables},model:{value:t.temp.siteId,callback:function(e){t.$set(t.temp,"siteId",e)},expression:"temp.siteId"}},t._l(t.siteOptions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.description,value:t.id}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"权限名称",prop:"name"}},[n("el-input",{model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name","string"===typeof e?e.trim():e)},expression:"temp.name"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"权限字符",prop:"permission"}},[n("el-input",{model:{value:t.temp.permission,callback:function(e){t.$set(t.temp,"permission",e)},expression:"temp.permission"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"权限路径",prop:"url"}},[n("el-input",{model:{value:t.temp.url,callback:function(e){t.$set(t.temp,"url",e)},expression:"temp.url"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"上级权限"}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"330px"},attrs:{placeholder:"请选择"},model:{value:t.temp.status,callback:function(e){t.$set(t.temp,"status",e)},expression:"temp.status"}},t._l(t.statusOptions,(function(t,e){return n("el-option",{key:t,attrs:{label:t,value:e}})})),1)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},["create"==t.dialogStatus?n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.create("temp")}}},[t._v("确 定")]):n("el-button",{attrs:{type:"primary"},on:{click:t.update}},[t._v("确 定")]),t._v(" "),n("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")])],1)],1),t._v(" "),n("el-dialog",{staticClass:"ma-smalldel",attrs:{visible:t.userVisible},on:{"update:visible":function(e){t.userVisible=e}}},[n("div",{staticClass:"deltip mb_30"},[t._v("确定删除该权限？")]),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},[n("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.handlepermission("deleted")}}},[t._v("删 除")]),t._v(" "),n("el-button",{on:{click:function(e){t.userVisible=!1}}},[t._v("取 消")])],1)])],1)},r=[],s=(n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("6c6e")),a=n("571f"),l=n("6724"),o=n("ed08");function u(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=c(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){l=!0,s=t},f:function(){try{a||null==n.return||n.return()}finally{if(l)throw s}}}}function c(t,e){if(t){if("string"===typeof t)return d(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var f=[{key:"CN",display_name:"中国"},{key:"US",display_name:"美国"},{key:"JP",display_name:"日本"},{key:"EU",display_name:"欧元区"}],p=f.reduce((function(t,e){return t[e.key]=e.display_name,t}),{}),m={name:"siteManager",directives:{waves:l["a"]},data:function(){return{list:[],total:null,listLoading:!0,listQuery:{page:0,size:15,name:"",permission:"",siteId:null},siteOptions:[],temp:{name:"",url:"",permission:"",pid:0,siteId:0},rules:{name:[{required:!0,message:"请输入活动名称",trigger:"blur"},{min:3,max:8,message:"长度在 3 到 8 个字符",trigger:"blur"}],permission:[{required:!0,message:"请输入权限",trigger:"blur"},{min:1,max:15,message:"长度在 3 到 8 个字符",trigger:"blur"}],siteId:[{required:!0,message:"请选择所属系统",trigger:"blur"}]},siteDisables:!0,importanceOptions:[1,2,3],calendarTypeOptions:f,sortOptions:[{label:"按ID升序列",key:"+id"},{label:"按ID降序",key:"-id"}],statusOptions:["启用","禁用"],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"编辑",create:"添加权限"},dialogPvVisible:!1,pvData:[],showAuditor:!1,tableKey:0,userVisible:!1}},filters:{statusFilter:function(t){var e={0:"success",1:"gray"};return e[t]},statusText:function(t){var e={0:"启用",1:"禁用"};return e[t]},typeFilter:function(t){return p[t]}},created:function(){this.getList(),this.returnSite()},methods:{getList:function(){var t=this;this.listLoading=!0,Object(s["b"])(this.listQuery).then((function(e){t.list=e.obj.content,t.total=e.obj.totalElements,t.listLoading=!1}))},returnSite:function(){var t=this;Object(a["e"])().then((function(e){t.siteOptions=e.obj})).catch((function(t){}))},handleFilter:function(){this.listQuery.page=0,this.getList()},handleSizeChange:function(t){this.listQuery.size=t,this.getList()},handleCurrentChange:function(t){this.listQuery.page=t-1,this.getList()},timeFilter:function(t){if(!t[0])return this.listQuery.start=void 0,void(this.listQuery.end=void 0);this.listQuery.start=parseInt(+t[0]/1e3),this.listQuery.end=parseInt((+t[1]+864e5)/1e3)},delpermission:function(t){this.userVisible=!0,this.deleteUid=t},handlepermission:function(t){this.$message({message:"操作成功",type:"success"}),this.deleteUid.status=t,this.userVisible=!1},handleModifyStatus:function(t,e){var n=this;Object(s["c"])(t.id).then((function(i){n.successTip(),t.status=e})).catch((function(t){n.failTip()}))},handleCreate:function(){this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.siteDisables=!1},handleUpdate:function(t){this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0,this.siteDisables=!0},handleDelete:function(t){this.$notify({title:"成功",message:"删除成功",type:"success",duration:2e3});var e=this.list.indexOf(t);this.list.splice(e,1)},successTip:function(){this.dialogFormVisible=!1,this.$notify({title:"成功",message:"创建成功",type:"success",duration:2e3})},failTip:function(){this.$notify({title:"失败",message:"添加失败",type:"fail",duration:2e3})},create:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return e.failTip(),!1;addPermission(e.temp).then((function(t){e.list.unshift(e.temp),e.successTip(),e.getList()})).catch((function(t){e.failTip()}))}))},update:function(){this.temp.timestamp=+this.temp.timestamp;var t,e=u(this.list);try{for(e.s();!(t=e.n()).done;){var n=t.value;if(n.id===this.temp.id){var i=this.list.indexOf(n);this.list.splice(i,1,this.temp);break}}}catch(r){e.e(r)}finally{e.f()}this.dialogFormVisible=!1,this.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})},resetTemp:function(){this.temp={name:"",url:"",permission:"",status:0}},handleDownload:function(){},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return"timestamp"===t?Object(o["f"])(e[t]):e[t]}))}))}}},h=m,v=(n("426a"),n("2877")),y=Object(v["a"])(h,i,r,!1,null,null,null);e["default"]=y.exports},"426a":function(t,e,n){"use strict";n("5c69")},4917:function(t,e,n){"use strict";var i=n("cb7c"),r=n("9def"),s=n("0390"),a=n("5f1b");n("214f")("match",1,(function(t,e,n,l){return[function(n){var i=t(this),r=void 0==n?void 0:n[e];return void 0!==r?r.call(n,i):new RegExp(n)[e](String(i))},function(t){var e=l(n,t,this);if(e.done)return e.value;var o=i(t),u=String(this);if(!o.global)return a(o,u);var c=o.unicode;o.lastIndex=0;var d,f=[],p=0;while(null!==(d=a(o,u))){var m=String(d[0]);f[p]=m,""===m&&(o.lastIndex=s(u,r(o.lastIndex),c)),p++}return 0===p?null:f}]}))},"571f":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"c",(function(){return s})),n.d(e,"f",(function(){return a})),n.d(e,"e",(function(){return l})),n.d(e,"b",(function(){return o})),n.d(e,"g",(function(){return u})),n.d(e,"h",(function(){return c})),n.d(e,"d",(function(){return d}));var i=n("1c1e");function r(t){return Object(i["a"])({url:"site/add",method:"post",params:t})}function s(t){return Object(i["a"])({url:"site/edit",method:"post",params:t})}function a(t){return Object(i["a"])({url:"site",method:"post",params:t})}function l(){return Object(i["a"])({url:"site/list",method:"post"})}function o(t){return Object(i["a"])({url:"site/delete",method:"post",params:{id:t}})}function u(t){return Object(i["a"])({url:"site/forbid",method:"post",params:{id:t}})}function c(t){return Object(i["a"])({url:"site/getusersite",method:"post",params:{userId:t}})}function d(t,e){return Object(i["a"])({url:"site/add_user_site",method:"post",params:{userId:t,sid:e}})}},"5c69":function(t,e,n){},6724:function(t,e,n){"use strict";n("8d41");var i={bind:function(t,e){t.addEventListener("click",(function(n){var i=Object.assign({},e.value),r=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),s=r.ele;if(s){s.style.position="relative",s.style.overflow="hidden";var a=s.getBoundingClientRect(),l=s.querySelector(".waves-ripple");switch(l?l.className="waves-ripple":(l=document.createElement("span"),l.className="waves-ripple",l.style.height=l.style.width=Math.max(a.width,a.height)+"px",s.appendChild(l)),r.type){case"center":l.style.top=a.height/2-l.offsetHeight/2+"px",l.style.left=a.width/2-l.offsetWidth/2+"px";break;default:l.style.top=n.pageY-a.top-l.offsetHeight/2-document.body.scrollTop+"px",l.style.left=n.pageX-a.left-l.offsetWidth/2-document.body.scrollLeft+"px"}return l.style.backgroundColor=r.color,l.className="waves-ripple z-active",!1}}),!1)}},r=function(t){t.directive("waves",i)};window.Vue&&(window.waves=i,Vue.use(r)),i.install=r;e["a"]=i},"6c6e":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"a",(function(){return s})),n.d(e,"e",(function(){return a})),n.d(e,"d",(function(){return l})),n.d(e,"c",(function(){return o}));var i=n("1c1e");function r(t){return Object(i["a"])({url:"/resource/page",method:"post",params:t})}function s(t){return Object(i["a"])({url:"/resource/add",method:"post",params:t})}function a(t){return Object(i["a"])({url:"/resource/edit",method:"post",params:t})}function l(t){return Object(i["a"])({url:"/resource/tree/children",method:"post",params:{siteId:t}})}function o(t){return Object(i["a"])({url:"/resource/status",method:"post",params:{id:t}})}},"8d41":function(t,e,n){},ed08:function(t,e,n){"use strict";n.d(e,"f",(function(){return l})),n.d(e,"d",(function(){return o})),n.d(e,"g",(function(){return u})),n.d(e,"a",(function(){return c})),n.d(e,"b",(function(){return d})),n.d(e,"e",(function(){return f})),n.d(e,"c",(function(){return p})),n.d(e,"h",(function(){return m}));n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("28a5"),n("ac6a"),n("456d"),n("4917"),n("a481");var i=n("53ca");function r(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=s(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,l=!0,o=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return l=t.done,t},e:function(t){o=!0,a=t},f:function(){try{l||null==n.return||n.return()}finally{if(o)throw a}}}}function s(t,e){if(t){if("string"===typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(t,e):void 0}}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function l(t,e){if(0===arguments.length)return null;var n,r=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===Object(i["a"])(t)?n=t:(10===(""+t).length&&(t=1e3*parseInt(t)),n=new Date(t));var s={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()},a=r.replace(/{(y|m|d|h|i|s|a)+}/g,(function(t,e){var n=s[e];return"a"===e?["一","二","三","四","五","六","日"][n-1]:(t.length>0&&n<10&&(n="0"+n),n||0)}));return a}function o(t,e){return!t||t.length<=e?t:t.substring(0,e)+"......"}function u(t,e){if(t&&e){var n=t.className,i=n.indexOf(e);-1===i?n+=""+e:n=n.substr(0,i)+n.substr(i+e.length),t.className=n}}function c(t){if(!t&&"object"!==Object(i["a"])(t))throw new Error("error arguments","shallowClone");var e=t.constructor===Array?[]:{};for(var n in t)t.hasOwnProperty(n)&&(t[n]&&"object"===Object(i["a"])(t[n])?(e[n]=t[n].constructor===Array?[]:{},e[n]=c(t[n])):e[n]=t[n]);return e}function d(t,e){var n,i=r(t);try{for(i.s();!(n=i.n()).done;){var s=n.value,a=s[e];a&&0!==a.length?d(a,e):delete s[e]}}catch(l){i.e(l)}finally{i.f()}}function f(t){if(Array.isArray(t)&&t.length>0)return t[t.length-1]}function p(t,e,n,i){if(Array.isArray(t)){var s,a=r(t);try{for(a.s();!(s=a.n()).done;){var l=s.value,o=p(l,e,n,i);if(o)return o}}catch(v){a.e(v)}finally{a.f()}}if(t[i]===e){var u=t[i],c=[t[i]];return{result:u,path:c}}if(t[n]){var d,f=r(t[n]);try{for(f.s();!(d=f.n()).done;){var m=d.value,h=p(m,e,n,i);if(h)return h.path.unshift(t[i]),h}}catch(v){f.e(v)}finally{f.f()}}}function m(t){var e=[];return function t(n){for(var i=n.childNodes,r=0;r<i.length;r++)e.push(i[r].data),t(i[r])}(t),e}}}]);