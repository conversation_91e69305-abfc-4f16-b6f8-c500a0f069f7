(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7e4e8fc1","chunk-aeaeae9e"],{"147e":function(n,e,t){"use strict";t.r(e);var o=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("div",{staticClass:"social-signup-container"},[t("div",{staticClass:"sign-btn",on:{click:function(e){return n.wechatHandleClick("wechat")}}},[t("span",{staticClass:"wx-svg-container"},[t("icon-svg",{staticClass:"icon",attrs:{"icon-class":"wechat"}})],1),n._v(" 微信\n  ")]),n._v(" "),t("div",{staticClass:"sign-btn",on:{click:function(e){return n.tencentHandleClick("tencent")}}},[t("span",{staticClass:"qq-svg-container"},[t("icon-svg",{staticClass:"icon",attrs:{"icon-class":"qq"}})],1),n._v(" QQ\n  ")])])},i=[];function s(n,e,t,o){var i=void 0!==window.screenLeft?window.screenLeft:screen.left,s=void 0!==window.screenTop?window.screenTop:screen.top,r=window.innerWidth?window.innerWidth:document.documentElement.clientWidth?document.documentElement.clientWidth:screen.width,a=window.innerHeight?window.innerHeight:document.documentElement.clientHeight?document.documentElement.clientHeight:screen.height,c=r/2-t/2+i,l=a/2-o/2+s,d=window.open(n,e,"toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=yes, copyhistory=no, width="+t+", height="+o+", top="+l+", left="+c);window.focus&&d.focus()}var r={name:"social-signin",methods:{wechatHandleClick:function(n){this.$store.commit("SET_AUTH_TYPE",n);var e="xxxxx",t=encodeURIComponent("xxx/redirect?redirect="+window.location.origin+"/authredirect"),o="https://open.weixin.qq.com/connect/qrconnect?appid="+e+"&redirect_uri="+t+"&response_type=code&slot-scope=snsapi_login#wechat_redirect";s(o,n,540,540)},tencentHandleClick:function(n){this.$store.commit("SET_AUTH_TYPE",n);var e="xxxxx",t=encodeURIComponent("xxx/redirect?redirect="+window.location.origin+"/authredirect"),o="https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id="+e+"&redirect_uri="+t;s(o,n,540,540)}}},a=r,c=(t("4543"),t("2877")),l=Object(c["a"])(a,o,i,!1,null,"3e96f51c",null);e["default"]=l.exports},4543:function(n,e,t){"use strict";t("a6bf")},"735d":function(n,e,t){"use strict";t("ef08")},"9ed6":function(n,e,t){"use strict";t.r(e);var o=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("div",{staticClass:"login-container"},[t("el-form",{ref:"loginForm",staticClass:"card-box login-form",attrs:{autoComplete:"on",model:n.loginForm,rules:n.loginRules,"label-position":"left"}},[t("h3",{staticClass:"title"},[n._v("sso管理系统登录")]),n._v(" "),t("el-form-item",{attrs:{prop:"username"}},[t("span",{staticClass:"svg-container svg-container_login"},[t("icon-svg",{attrs:{"icon-class":"user"}})],1),n._v(" "),t("el-input",{attrs:{name:"username",type:"text",autoComplete:"on",placeholder:"邮箱"},model:{value:n.loginForm.username,callback:function(e){n.$set(n.loginForm,"username",e)},expression:"loginForm.username"}})],1),n._v(" "),t("el-form-item",{attrs:{prop:"password"}},[t("span",{staticClass:"svg-container"},[t("icon-svg",{attrs:{"icon-class":"password"}})],1),n._v(" "),t("el-input",{attrs:{name:"password",type:n.pwdType,autoComplete:"on",placeholder:"密码"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&n._k(e.keyCode,"enter",13,e.key,"Enter")?null:n.handleLogin.apply(null,arguments)}},model:{value:n.loginForm.password,callback:function(e){n.$set(n.loginForm,"password",e)},expression:"loginForm.password"}}),n._v(" "),t("span",{staticClass:"show-pwd",on:{click:n.showPwd}},[t("icon-svg",{attrs:{"icon-class":"eye"}})],1)],1),n._v(" "),t("el-button",{staticStyle:{width:"100%","margin-bottom":"30px"},attrs:{type:"primary",loading:n.loading},nativeOn:{click:function(e){return e.preventDefault(),n.handleLogin.apply(null,arguments)}}},[n._v("登录")]),n._v(" "),n._e()],1),n._v(" "),t("el-dialog",{attrs:{title:"第三方验证",visible:n.showDialog},on:{"update:visible":function(e){n.showDialog=e}}},[n._v("\n    本地不能模拟，请结合自己业务进行模拟！！！"),t("br"),t("br"),t("br"),n._v("\n    邮箱登录成功,请选择第三方验证"),t("br"),n._v(" "),t("social-sign")],1)],1)},i=[];function s(n){return n&&n.trim().length>4}var r=t("147e"),a={components:{socialSign:r["default"]},name:"login",data:function(){var n=function(n,e,t){s(e)?t():t(new Error("请输入正确的用户名"))},e=function(n,e,t){e.length<6?t(new Error("密码不能小于6位")):t()};return{loginForm:{username:"",password:""},loginRules:{username:[{required:!0,trigger:"blur",validator:n}],password:[{required:!0,trigger:"blur",validator:e}]},pwdType:"password",loading:!1,showDialog:!1}},methods:{showPwd:function(){"password"===this.pwdType?this.pwdType="":this.pwdType="password"},handleLogin:function(){var n=this;this.$refs.loginForm.validate((function(e){if(!e)return console.log("error submit!!"),!1;n.loading=!0,n.$store.dispatch("LoginByUsername",n.loginForm).then((function(){n.loading=!1,n.$router.push({path:"/"})})).catch((function(e){n.$message.error("登录失败"),n.loading=!1}))}))},afterQRScan:function(){}},created:function(){},destroyed:function(){}},c=a,l=(t("735d"),t("2877")),d=Object(l["a"])(c,o,i,!1,null,null,null);e["default"]=d.exports},a6bf:function(n,e,t){},ef08:function(n,e,t){}}]);