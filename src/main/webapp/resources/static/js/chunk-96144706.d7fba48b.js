(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-96144706"],{"04bc":function(t,e,n){},"28a5":function(t,e,n){"use strict";var i=n("aae3"),r=n("cb7c"),a=n("ebd6"),s=n("0390"),o=n("9def"),l=n("5f1b"),u=n("520a"),c=n("79e5"),d=Math.min,p=[].push,f="split",h="length",m="lastIndex",b=4294967295,g=!c((function(){RegExp(b,"y")}));n("214f")("split",2,(function(t,e,n,c){var v;return v="c"=="abbc"[f](/(b)*/)[1]||4!="test"[f](/(?:)/,-1)[h]||2!="ab"[f](/(?:ab)*/)[h]||4!="."[f](/(.?)(.?)/)[h]||"."[f](/()()/)[h]>1||""[f](/.?/)[h]?function(t,e){var r=String(this);if(void 0===t&&0===e)return[];if(!i(t))return n.call(r,t,e);var a,s,o,l=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,f=void 0===e?b:e>>>0,g=new RegExp(t.source,c+"g");while(a=u.call(g,r)){if(s=g[m],s>d&&(l.push(r.slice(d,a.index)),a[h]>1&&a.index<r[h]&&p.apply(l,a.slice(1)),o=a[0][h],d=s,l[h]>=f))break;g[m]===a.index&&g[m]++}return d===r[h]?!o&&g.test("")||l.push(""):l.push(r.slice(d)),l[h]>f?l.slice(0,f):l}:"0"[f](void 0,0)[h]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,i){var r=t(this),a=void 0==n?void 0:n[e];return void 0!==a?a.call(n,r,i):v.call(String(r),n,i)},function(t,e){var i=c(v,t,this,e,v!==n);if(i.done)return i.value;var u=r(t),p=String(this),f=a(u,RegExp),h=u.unicode,m=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(g?"y":"g"),y=new f(g?u:"^(?:"+u.source+")",m),_=void 0===e?b:e>>>0;if(0===_)return[];if(0===p.length)return null===l(y,p)?[p]:[];var j=0,O=0,w=[];while(O<p.length){y.lastIndex=g?O:0;var k,x=l(y,g?p:p.slice(O));if(null===x||(k=d(o(y.lastIndex+(g?0:O)),p.length))===j)O=s(p,O,h);else{if(w.push(p.slice(j,O)),w.length===_)return w;for(var S=1;S<=x.length-1;S++)if(w.push(x[S]),w.length===_)return w;O=j=k}}return w.push(p.slice(j)),w}]}))},4917:function(t,e,n){"use strict";var i=n("cb7c"),r=n("9def"),a=n("0390"),s=n("5f1b");n("214f")("match",1,(function(t,e,n,o){return[function(n){var i=t(this),r=void 0==n?void 0:n[e];return void 0!==r?r.call(n,i):new RegExp(n)[e](String(i))},function(t){var e=o(n,t,this);if(e.done)return e.value;var l=i(t),u=String(this);if(!l.global)return s(l,u);var c=l.unicode;l.lastIndex=0;var d,p=[],f=0;while(null!==(d=s(l,u))){var h=String(d[0]);p[f]=h,""===h&&(l.lastIndex=a(u,r(l.lastIndex),c)),f++}return 0===f?null:p}]}))},"504c":function(t,e,n){var i=n("9e1e"),r=n("0d58"),a=n("6821"),s=n("52a7").f;t.exports=function(t){return function(e){var n,o=a(e),l=r(o),u=l.length,c=0,d=[];while(u>c)n=l[c++],i&&!s.call(o,n)||d.push(t?[n,o[n]]:o[n]);return d}}},"571f":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"f",(function(){return s})),n.d(e,"e",(function(){return o})),n.d(e,"b",(function(){return l})),n.d(e,"g",(function(){return u})),n.d(e,"h",(function(){return c})),n.d(e,"d",(function(){return d}));var i=n("1c1e");function r(t){return Object(i["a"])({url:"site/add",method:"post",params:t})}function a(t){return Object(i["a"])({url:"site/edit",method:"post",params:t})}function s(t){return Object(i["a"])({url:"site",method:"post",params:t})}function o(){return Object(i["a"])({url:"site/list",method:"post"})}function l(t){return Object(i["a"])({url:"site/delete",method:"post",params:{id:t}})}function u(t){return Object(i["a"])({url:"site/forbid",method:"post",params:{id:t}})}function c(t){return Object(i["a"])({url:"site/getusersite",method:"post",params:{userId:t}})}function d(t,e){return Object(i["a"])({url:"site/add_user_site",method:"post",params:{userId:t,sid:e}})}},6724:function(t,e,n){"use strict";n("8d41");var i={bind:function(t,e){t.addEventListener("click",(function(n){var i=Object.assign({},e.value),r=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),a=r.ele;if(a){a.style.position="relative",a.style.overflow="hidden";var s=a.getBoundingClientRect(),o=a.querySelector(".waves-ripple");switch(o?o.className="waves-ripple":(o=document.createElement("span"),o.className="waves-ripple",o.style.height=o.style.width=Math.max(s.width,s.height)+"px",a.appendChild(o)),r.type){case"center":o.style.top=s.height/2-o.offsetHeight/2+"px",o.style.left=s.width/2-o.offsetWidth/2+"px";break;default:o.style.top=n.pageY-s.top-o.offsetHeight/2-document.body.scrollTop+"px",o.style.left=n.pageX-s.left-o.offsetWidth/2-document.body.scrollLeft+"px"}return o.style.backgroundColor=r.color,o.className="waves-ripple z-active",!1}}),!1)}},r=function(t){t.directive("waves",i)};window.Vue&&(window.waves=i,Vue.use(r)),i.install=r;e["a"]=i},8615:function(t,e,n){var i=n("5ca1"),r=n("504c")(!1);i(i.S,"Object",{values:function(t){return r(t)}})},8916:function(t,e,n){"use strict";n.d(e,"h",(function(){return r})),n.d(e,"f",(function(){return a})),n.d(e,"i",(function(){return s})),n.d(e,"e",(function(){return o})),n.d(e,"n",(function(){return l})),n.d(e,"a",(function(){return u})),n.d(e,"r",(function(){return c})),n.d(e,"q",(function(){return d})),n.d(e,"c",(function(){return p})),n.d(e,"g",(function(){return h})),n.d(e,"d",(function(){return m})),n.d(e,"b",(function(){return b})),n.d(e,"j",(function(){return g})),n.d(e,"m",(function(){return v})),n.d(e,"l",(function(){return y})),n.d(e,"k",(function(){return _})),n.d(e,"p",(function(){return j})),n.d(e,"o",(function(){return O}));var i=n("1c1e");function r(t){return Object(i["a"])({url:"log",method:"post",params:t})}function a(){return Object(i["a"])({url:"log/source",method:"post"})}function s(t){return Object(i["a"])({url:"log/findlogsource",method:"post",params:t})}function o(){return Object(i["a"])({url:"log/clear_log_condition",method:"post"})}function l(t,e,n){return Object(i["a"])({url:"log/set_log_condition",method:"post",params:{count:t,date:e,logSwitch:n}})}function u(t){return Object(i["a"])({url:"log/add/source",method:"post",params:t})}function c(t,e){return Object(i["a"])({url:"log/update/source/status",method:"post",params:{id:t,status:e}})}function d(t){return Object(i["a"])({url:"log/update/source",method:"post",params:t})}function p(t){return Object(i["a"])({url:"log/delete/source",method:"post",params:{id:t}})}function f(){return Object(i["a"])({url:"log/type",method:"post"})}function h(){return i["a"].all([a(),f()]).then(i["a"].spread((function(t,e){return{types:e.obj,sources:t.obj}})))}function m(t,e){return Object(i["a"])({url:"audit",method:"post",params:{page:t,size:e}})}function b(t){return Object(i["a"])({url:"audit/del",method:"post",params:{id:t}})}function g(t){return Object(i["a"])({url:"/log/login",method:"post",params:t})}function v(){return Object(i["a"])({url:"/log/login/type",method:"get"})}function y(){return Object(i["a"])({url:"/log/login/statistics/period"})}function _(t){return Object(i["a"])({url:"/log/login/statistics",method:"post",params:t})}function j(){return Object(i["a"])({url:"/log/system/statistics/period"})}function O(t){return Object(i["a"])({url:"/log/system/statistics",method:"post",params:t})}},"8d41":function(t,e,n){},bee6:function(t,e,n){"use strict";n("04bc")},c24f:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"i",(function(){return a})),n.d(e,"l",(function(){return s})),n.d(e,"u",(function(){return o})),n.d(e,"g",(function(){return l})),n.d(e,"r",(function(){return u})),n.d(e,"k",(function(){return c})),n.d(e,"q",(function(){return d})),n.d(e,"a",(function(){return p})),n.d(e,"n",(function(){return f})),n.d(e,"d",(function(){return h})),n.d(e,"s",(function(){return m})),n.d(e,"p",(function(){return b})),n.d(e,"c",(function(){return g})),n.d(e,"j",(function(){return v})),n.d(e,"h",(function(){return y})),n.d(e,"w",(function(){return _})),n.d(e,"t",(function(){return j})),n.d(e,"o",(function(){return O})),n.d(e,"x",(function(){return w})),n.d(e,"m",(function(){return k})),n.d(e,"v",(function(){return x})),n.d(e,"f",(function(){return S})),n.d(e,"e",(function(){return C}));var i=n("1c1e");function r(t){return Object(i["a"])({url:"user/add",method:"post",params:t})}function a(t){return Object(i["a"])({url:"user/edit",method:"post",params:t})}function s(t){return Object(i["a"])({url:"user/bind",method:"post",params:t})}function o(t){return Object(i["a"])({url:"user",method:"post",params:t})}function l(t){return Object(i["a"])({url:"user/del",method:"post",params:{id:t}})}function u(t){return Object(i["a"])({url:"role",method:"post",params:{page:t}})}function c(t,e){return Object(i["a"])({url:"userrole/edit_user_role",method:"post",params:{userId:t,roleIds:e}})}function d(t){return Object(i["a"])({url:"resource/page",method:"post",params:t})}function p(t){return Object(i["a"])({url:"role/add",method:"post",params:t})}function f(t){return Object(i["a"])({url:"user/forbid",method:"post",params:{uid:t}})}function h(t){return Object(i["a"])({url:"user/allow",method:"post",params:{uid:t}})}function m(){return Object(i["a"])({url:"tree/gettree",method:"post"})}function b(t){return Object(i["a"])({url:"tree/getdepartment",method:"post",params:{sid:t}})}function g(t){return Object(i["a"])({url:"userjob/add",method:"post",params:t})}function v(t){return Object(i["a"])({url:"userjob/edit",method:"post",params:t})}function y(t){return Object(i["a"])({url:"userjob/delete",method:"post",params:{id:t}})}function _(t){return Object(i["a"])({url:"userjob/page",method:"post",params:t})}function j(){return Object(i["a"])({url:"userjob/list",method:"post"})}function O(){return Object(i["a"])({url:"user/areas",method:"get"})}function w(){return Object(i["a"])({url:"user/statistics"})}function k(){return Object(i["a"])({url:"user/export/profile"})}function x(t){return Object(i["a"])({url:"role/list-site-role",method:"post",params:{userId:t}})}function S(t){return Object(i["a"])({url:"userlog/page",method:"post",params:t})}function C(){return Object(i["a"])({url:"userlog/type",method:"get"})}},ed08:function(t,e,n){"use strict";n.d(e,"f",(function(){return o})),n.d(e,"d",(function(){return l})),n.d(e,"g",(function(){return u})),n.d(e,"a",(function(){return c})),n.d(e,"b",(function(){return d})),n.d(e,"e",(function(){return p})),n.d(e,"c",(function(){return f})),n.d(e,"h",(function(){return h}));n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("28a5"),n("ac6a"),n("456d"),n("4917"),n("a481");var i=n("53ca");function r(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=a(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return o=t.done,t},e:function(t){l=!0,s=t},f:function(){try{o||null==n.return||n.return()}finally{if(l)throw s}}}}function a(t,e){if(t){if("string"===typeof t)return s(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(t,e):void 0}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function o(t,e){if(0===arguments.length)return null;var n,r=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===Object(i["a"])(t)?n=t:(10===(""+t).length&&(t=1e3*parseInt(t)),n=new Date(t));var a={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()},s=r.replace(/{(y|m|d|h|i|s|a)+}/g,(function(t,e){var n=a[e];return"a"===e?["一","二","三","四","五","六","日"][n-1]:(t.length>0&&n<10&&(n="0"+n),n||0)}));return s}function l(t,e){return!t||t.length<=e?t:t.substring(0,e)+"......"}function u(t,e){if(t&&e){var n=t.className,i=n.indexOf(e);-1===i?n+=""+e:n=n.substr(0,i)+n.substr(i+e.length),t.className=n}}function c(t){if(!t&&"object"!==Object(i["a"])(t))throw new Error("error arguments","shallowClone");var e=t.constructor===Array?[]:{};for(var n in t)t.hasOwnProperty(n)&&(t[n]&&"object"===Object(i["a"])(t[n])?(e[n]=t[n].constructor===Array?[]:{},e[n]=c(t[n])):e[n]=t[n]);return e}function d(t,e){var n,i=r(t);try{for(i.s();!(n=i.n()).done;){var a=n.value,s=a[e];s&&0!==s.length?d(s,e):delete a[e]}}catch(o){i.e(o)}finally{i.f()}}function p(t){if(Array.isArray(t)&&t.length>0)return t[t.length-1]}function f(t,e,n,i){if(Array.isArray(t)){var a,s=r(t);try{for(s.s();!(a=s.n()).done;){var o=a.value,l=f(o,e,n,i);if(l)return l}}catch(b){s.e(b)}finally{s.f()}}if(t[i]===e){var u=t[i],c=[t[i]];return{result:u,path:c}}if(t[n]){var d,p=r(t[n]);try{for(p.s();!(d=p.n()).done;){var h=d.value,m=f(h,e,n,i);if(m)return m.path.unshift(t[i]),m}}catch(b){p.e(b)}finally{p.f()}}}function h(t){var e=[];return function t(n){for(var i=n.childNodes,r=0;r<i.length;r++)e.push(i[r].data),t(i[r])}(t),e}},f6bf:function(t,e,n){"use strict";n.r(e);var i,r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container calendar-list-container"},[n("div",{staticClass:"filter-container"},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"请选择可访问系统",clearable:""},on:{change:t.handleFilter},model:{value:t.listQuery.siteId,callback:function(e){t.$set(t.listQuery,"siteId",e)},expression:"listQuery.siteId"}},t._l(t.siteOptions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.description,value:t.id}})})),1),t._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{placeholder:"请输入姓名或者手机号查询！"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.truename,callback:function(e){t.$set(t.listQuery,"truename",e)},expression:"listQuery.truename"}}),t._v(" "),t._e(),t._v(" "),n("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{placeholder:"状态"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.flag,callback:function(e){t.$set(t.listQuery,"flag",e)},expression:"listQuery.flag"}},t._l(t.flagOptions,(function(t){return n("el-option",{key:t.key,attrs:{label:t.label,value:t.key}})})),1),t._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",icon:"search",size:"small"},on:{click:t.handleFilter}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"filter-item el-icon-plus",staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"success"},on:{click:t.handleCreate}},[t._v("添加")]),t._v(" "),t._e()],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],key:t.tableKey,staticStyle:{width:"100%"},attrs:{data:t.list,"element-loading-text":"给我一点时间",stripe:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{align:"center",label:"ID",width:"60px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.uid))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"min-width":"150px",align:"center",label:"姓名"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{on:{click:function(n){return t.handleUpdate(e.row)}}},[t._v(t._s(e.row.truename))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"120px",align:"center",label:"手机号"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.phone))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"200px",align:"center",label:"单位"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.company))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"300px",align:"center",label:"权限"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.permissions))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"300px",align:"center",label:"签约服务时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.startTime&&e.row.endTime?n("span",[t._v(t._s(t._f("parseTime")(e.row.startTime,"{y}-{m}-{d} {h}:{i}"))+"-"+t._s(t._f("parseTime")(e.row.endTime,"{y}-{m}-{d} {h}:{i}")))]):t._e()]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"100px",align:"center",label:"签约是否到期"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.isEffect?n("span",[t._v("是")]):t._e(),t._v(" "),0==e.row.isEffect?n("span",[t._v("否")]):t._e()]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"100px",align:"center",label:"商务跟进人"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.businessManager))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"200px",align:"center",label:"商务联系方式"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.businessManagerPhone))])]}}])}),t._v(" "),t._e(),t._v(" "),n("el-table-column",{attrs:{width:"150px",align:"center",label:"添加时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("parseTime")(e.row.createTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),t._v(" "),n("el-table-column",{attrs:{"class-name":"status-col",label:"状态","min-width":"80px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-tag",{attrs:{type:t._f("statusFilter")(e.row.flag)}},[t._v(t._s(t._f("statusTextFilter")(e.row.flag)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"center",label:"操作","min-width":"300px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(n){return t.handleAddSites(e.row)}}},[t._v("修改系统")]),t._v(" "),1!=e.row.flag?n("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(n){return t.handleModifyStatus(e.row,1)}}},[t._v("禁用")]):t._e(),t._v(" "),0!=e.row.flag?n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.handleModifyStatus(e.row,0)}}},[t._v("开启")]):t._e(),t._v(" "),n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["user:delete"],expression:"['user:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(n){return t.handleUser(e.row)}}},[t._v("删除")])]}}])})],1),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:!t.listLoading,expression:"!listLoading"}],staticClass:"pagination-container"},[n("el-pagination",{attrs:{"current-page":t.listQuery.page,"page-sizes":[10,15,20,30],"page-size":t.listQuery.size,layout:" sizes, prev, pager, next,total, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){return t.$set(t.listQuery,"page",e)},"update:current-page":function(e){return t.$set(t.listQuery,"page",e)}}})],1),t._v(" "),n("el-dialog",{staticClass:"ma-small",attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible,"lock-scroll":!0},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[n("el-form",{ref:"temp",staticClass:"small-space",staticStyle:{width:"400px","margin-left":"50px"},attrs:{model:t.temp,rules:t.rules,"label-position":"right","label-width":"78px"}},[n("el-form-item",{attrs:{label:"姓名",prop:"truename","label-width":"100px"}},[n("el-input",{model:{value:t.temp.truename,callback:function(e){t.$set(t.temp,"truename","string"===typeof e?e.trim():e)},expression:"temp.truename"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"手机号",prop:"phone","label-width":"100px"}},[n("el-input",{model:{value:t.temp.phone,callback:function(e){t.$set(t.temp,"phone","string"===typeof e?e.trim():e)},expression:"temp.phone"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"单 位",prop:"company","label-width":"100px"}},[n("el-input",{model:{value:t.temp.company,callback:function(e){t.$set(t.temp,"company","string"===typeof e?e.trim():e)},expression:"temp.company"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"账号绑定","label-width":"100px"}},[n("el-input",{staticClass:"hei400",attrs:{type:"textarea",rows:4,autosize:{minRows:4,maxRows:20}},model:{value:t.temp.thirdAccountInfo,callback:function(e){t.$set(t.temp,"thirdAccountInfo","string"===typeof e?e.trim():e)},expression:"temp.thirdAccountInfo"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"开始时间","label-width":"100px"}},[n("el-col",[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期","value-format":"timestamp"},model:{value:t.temp.startTime,callback:function(e){t.$set(t.temp,"startTime",e)},expression:"temp.startTime"}})],1)],1),t._v(" "),n("el-form-item",{attrs:{label:"结束时间","label-width":"100px"}},[n("el-col",[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期","value-format":"timestamp"},model:{value:t.temp.endTime,callback:function(e){t.$set(t.temp,"endTime",e)},expression:"temp.endTime"}})],1)],1),t._v(" "),n("el-form-item",{attrs:{label:"商务跟进人",prop:"businessManager","label-width":"100px"}},[n("el-input",{model:{value:t.temp.businessManager,callback:function(e){t.$set(t.temp,"businessManager","string"===typeof e?e.trim():e)},expression:"temp.businessManager"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"商务联系方式",prop:"businessManagerPhone","label-width":"100px"}},[n("el-input",{model:{value:t.temp.businessManagerPhone,callback:function(e){t.$set(t.temp,"businessManagerPhone","string"===typeof e?e.trim():e)},expression:"temp.businessManagerPhone"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"行政区域","label-width":"100px"}},[n("el-cascader",{attrs:{options:t.areaOptions,props:{checkStrictly:!0,label:"name",value:"id"},clearable:""},model:{value:t.temp.areaCode,callback:function(e){t.$set(t.temp,"areaCode",e)},expression:"temp.areaCode"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"状 态","label-width":"100px"}},[n("el-select",{staticClass:"filter-item",staticStyle:{width:"320px"},attrs:{placeholder:"请选择"},model:{value:t.temp.flag,callback:function(e){t.$set(t.temp,"flag",e)},expression:"temp.flag"}},t._l(t.statusOptions,(function(t,e){return n("el-option",{key:e,attrs:{label:t,value:e}})})),1)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},["create"==t.dialogStatus?n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.create("temp")}}},[t._v("确 定")]):n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.update("temp")}}},[t._v("确 定")]),t._v(" "),n("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")])],1)],1),t._v(" "),n("el-dialog",{staticClass:"ma-smalldel",attrs:{visible:t.userVisible},on:{"update:visible":function(e){t.userVisible=e}}},[n("div",{staticClass:"deltip mb_30"},[t._v("确定删除该客户？")]),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},[n("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.handleDelete()}}},[t._v("删 除")]),t._v(" "),n("el-button",{on:{click:function(e){t.userVisible=!1}}},[t._v("取 消")])],1)]),t._v(" "),n("el-dialog",{staticClass:"ma-small",attrs:{title:"添加可访问系统",visible:t.siteAddDialogFormVisible},on:{"update:visible":function(e){t.siteAddDialogFormVisible=e}}},[n("el-checkbox-group",{model:{value:t.checkSites,callback:function(e){t.checkSites=e},expression:"checkSites"}},t._l(t.sites,(function(e){return n("el-checkbox",{key:e.id,attrs:{label:e.id}},[t._v(t._s(e.description))])})),1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addSite()}}},[t._v("添 加")]),t._v(" "),n("el-button",{on:{click:function(e){t.siteAddDialogFormVisible=!1}}},[t._v("取 消")])],1)],1),t._v(" "),n("el-dialog",{staticClass:"ma-small",attrs:{title:"添加角色",visible:t.roleAddDialog},on:{"update:visible":function(e){t.roleAddDialog=e}}},[n("el-collapse",t._l(t.roles,(function(e){return n("el-collapse-item",{attrs:{title:e.description}},[n("el-checkbox-group",{model:{value:t.checkRoles,callback:function(e){t.checkRoles=e},expression:"checkRoles"}},t._l(e.roleList,(function(e){return n("el-checkbox",{key:e.id,attrs:{label:e.id}},[t._v(t._s(e.name))])})),1)],1)})),1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addRole()}}},[t._v("添 加")]),t._v(" "),n("el-button",{on:{click:function(e){t.roleAddDialog=!1}}},[t._v("取 消")])],1)],1)],1)},a=[],s=(n("7f7f"),n("ac6a"),n("8615"),n("ade3")),o=n("c24f"),l=(n("8916"),n("571f")),u=n("6724"),c=n("ed08"),d=[{key:"CN",display_name:"中国"},{key:"US",display_name:"美国"},{key:"JP",display_name:"日本"},{key:"EU",display_name:"欧元区"}],p=d.reduce((function(t,e){return t[e.key]=e.display_name,t}),{}),f={name:"siteManager",directives:{waves:u["a"]},data:function(){return{clickStats:!0,list:[],logs:[],total:null,listLoading:!0,listQuery:{page:1,size:15,type:0,importance:void 0,truename:void 0,flag:void 0,sid:void 0,tempsid:[],organization:void 0,siteId:void 0},temp:{uid:void 0,importance:0,remark:"",timestamp:0,username:"",truename:"",tempsid:[],areaCode:"",sid:"1",site:"1",thirdAccountInfo:"",job:void 0,organization:0,password:"",email:"",phone:"",type:0,flag:0,sex:0,deleteUid:"",businessManager:"",businessManagerPhone:"",startTime:"",endTime:"",company:""},areaOptions:[],siteOptions:[],rules:{truename:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],phone:[{required:!0,message:"请输入电话号",trigger:"blur"}],company:[{required:!0,message:"请输入单位名称",trigger:"blur"}],sid:[{required:!0,message:"请选择站点",trigger:"change"}],organization:[{required:!0,message:"请填写部门",trigger:"blur"}],email:[{type:"email",message:"请输入正确地址",trigger:"blur"}]},importanceOptions:[1,2,3],calendarTypeOptions:d,flagOptions:[{label:"请选择状态",key:void 0},{label:"开启",key:0},{label:"禁用",key:1}],statusOptions:["启用","禁用"],departOptions:[],departOption:[],jobOptions:[],sexOptions:["男","女"],dialogFormVisible:!1,userVisible:!1,siteAddDialogFormVisible:!1,userOperationsDialogVisible:!1,userLogDialogVisible:!1,roleAddDialog:!1,dialogStatus:"",textMap:{update:"编辑",create:"添加客户"},dialogPvVisible:!1,pvData:[],showAuditor:!1,tableKey:0,showPassword:!1,checkSites:[],checkRoles:[],sites:[],roles:[],currentUid:0,showtree:!1,tree:[],treeOptions:[],defaultProps:{children:"children",label:"name",value:"id"}}},filters:(i={cutFilter:function(t,e){return Object(c["d"])(t,e)},statusFilter:function(t){var e={0:"success",1:"danger"};return e[t]},statusTextFilter:function(t){var e={0:"启用",1:"禁用"};return e[t]}},Object(s["a"])(i,"statusTextFilter",(function(t){var e={0:"启用",1:"禁用"};return e[t]})),Object(s["a"])(i,"typeFilter",(function(t){return p[t]})),i),created:function(){this.getList(),this.returnTree(),this.returnJobs(),this.getAreas(),this.returnSite()},methods:{getList:function(){var t=this;this.listLoading=!0,Object(o["u"])(this.listQuery).then((function(e){t.list=e.obj.content,t.total=e.obj.totalElements,t.listLoading=!1})).catch((function(e){t.failTip()}))},returnJobs:function(){var t=this;Object(o["t"])().then((function(e){t.jobOptions=e.obj})).catch((function(t){}))},returnDepartment:function(t){var e=this;this.temp.sid=Object(c["e"])(t),this.departOptions=[],this.temp.organization=0,Object(o["p"])(this.temp.tempsid[this.temp.tempsid.length-1]).then((function(t){e.departOptions=t.obj}))},returnOptionsDepartment:function(t){var e=this;this.listQuery.sid=Object(c["e"])(t),this.departOption=[],this.listQuery.organization=void 0,Object(o["p"])(this.listQuery.tempsid[this.listQuery.tempsid.length-1]).then((function(t){e.departOption=t.obj}))},getDepartmentBySite:function(){var t=this;this.departOptions=[],Object(c["e"])(this.temp.tempsid)&&Object(o["p"])(Object(c["e"])(this.temp.tempsid)).then((function(e){t.departOptions=e.obj}))},returnTree:function(){var t=this;this.showtree=!0,Object(o["s"])().then((function(e){0==t.tree.length&&(t.tree=e.obj,Object(c["b"])(t.tree,"children")),0==t.treeOptions.length&&(t.treeOptions=e.obj,Object(c["b"])(t.treeOptions,"children"))})).catch((function(t){}))},getAreas:function(){var t=this;Object(o["o"])().then((function(e){t.areaOptions=e.obj}))},returnSite:function(){var t=this;Object(l["e"])().then((function(e){t.siteOptions=e.obj})).catch((function(t){}))},handleFilter:function(){this.listQuery.page=1,this.getList()},handleSizeChange:function(t){this.listQuery.size=t,this.getList()},handleCurrentChange:function(t){this.listQuery.page=t,this.getList()},timeFilter:function(t){if(!t[0])return this.listQuery.start=void 0,void(this.listQuery.end=void 0);this.listQuery.start=parseInt(+t[0]/1e3),this.listQuery.end=parseInt((+t[1]+864e5)/1e3)},handleModifyStatus:function(t,e){1===e?Object(o["n"])(t.uid):0==e&&Object(o["d"])(t.uid),this.$message({message:"操作成功",type:"success"}),t.flag=e},handleCreate:function(){this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0},handleUpdate:function(t){this.temp=Object.assign({},t);var e=Object(c["c"])(this.tree,this.temp.sid,"children","id");e&&(this.temp.tempsid=e["path"]),this.temp.thirdAccountInfo=decodeURIComponent(atob(this.temp.thirdAccountInfo)),this.getDepartmentBySite(),0===this.temp.job&&(this.temp.job=void 0);var n=Object(c["c"])(this.areaOptions,this.temp.areaCode,"children","id");n&&(this.temp.areaCode=n["path"]),this.dialogStatus="update",this.dialogFormVisible=!0},handleUser:function(t){this.userVisible=!0,this.deleteUid=t},handleDelete:function(){var t=this;Object(o["g"])(this.deleteUid.uid).then((function(e){t.successTip("删除成功");var n=t.list.indexOf(t.deleteUid);t.list.splice(n,1),t.userVisible=!1})).catch((function(e){t.failTip("删除失败")}))},handleAddSites:function(t){var e=this;this.currentUid=t.uid,this.siteAddDialogFormVisible=!0;var n=this;Object(l["e"])().then((function(t){Object(l["h"])(n.currentUid).then((function(e){n.sites=t.obj,n.checkSites=e.obj.map((function(t){return t.id}))})).catch((function(t){}))})).catch((function(t){e.failTip()}))},handleUserOperations:function(t){this.$router.push({path:"/log/index",query:{userId:t.uid}})},handleUserLogs:function(t){this.$router.push({path:"/user/change_record/".concat(t.uid)})},handleAddRoles:function(t){var e=this;this.currentUid=t.uid,this.roleAddDialog=!0;var n=this;n.checkRoles=[],Object(o["v"])(t.uid).then((function(t){n.roles=t.obj,t.obj.map((function(t){t.roleList.map((function(t){1==t.checked&&n.checkRoles.push(t.id)}))}))})).catch((function(t){e.failTip()}))},addSite:function(){var t=this;Object(l["d"])(this.currentUid,this.checkSites).then((function(e){t.successTip()})).catch((function(e){t.failTip()}))},addRole:function(){var t=this;Object(o["k"])(this.currentUid,this.checkRoles).then((function(e){t.successTip()})).catch((function(e){t.failTip()}))},successTip:function(t){this.dialogFormVisible=!1,this.siteAddDialogFormVisible=!1,this.roleAddDialog=!1,this.$notify({title:"成功",message:t||"创建成功",type:"success",duration:2e3})},failTip:function(t){this.listLoading=!1,this.$notify({title:"失败",message:t||"添加失败",type:"fail",duration:2e3})},create:function(t){var e=this;this.$refs[t].validate((function(t){if(e.temp.thirdAccountInfo=btoa(encodeURIComponent(e.temp.thirdAccountInfo)),!t)return e.failTip(),!1;Object(o["b"])(e.temp).then((function(t){e.temp.timestamp=+new Date,e.list.unshift(e.temp),e.successTip(),e.getList()})).catch((function(t){e.failTip(t)}))}))},update:function(t){var e=this;this.$refs[t].validate((function(t){if(e.temp.thirdAccountInfo=btoa(encodeURIComponent(e.temp.thirdAccountInfo)),!t)return e.failTip("校验失败"),!1;Object(o["i"])(e.temp).then((function(t){e.successTip(),e.getList()})).catch((function(t){e.failTip(t)}))}))},resetTemp:function(){this.temp={id:void 0,importance:0,remark:"",timestamp:0,username:"",truename:"",sid:"",site:"",organization:0,job:void 0,password:"",email:"",phone:"",type:0,flag:0,sex:1,businessManager:"",businessManagerPhone:"",startTime:"",endTime:"",company:""}},handleDownload:function(){var t=this;Object(o["x"])().then((function(t){return t.obj.site})).then((function(t){Promise.all([n.e("chunk-c7e393f8"),n.e("chunk-b27dbcdc"),n.e("chunk-1fd85336")]).then(n.bind(null,"4bf8d")).then((function(e){var n=["站点","客户数量"],i=t.map((function(t){return Object.values(t)}));e.export_json_to_excel(n,i,"客户站点统计")}))})),Object(o["m"])().then((function(t){return t.obj})).then((function(e){return Promise.all([n.e("chunk-c7e393f8"),n.e("chunk-b27dbcdc"),n.e("chunk-1fd85336")]).then(n.bind(null,"4bf8d")).then((function(n){var i=["手机号","姓名","注册时间","站点","部门","系统","状态","性别"],r=["phone","username","createTime","siteName","company","sites","flag","sex"],a=t.formatJson(r,e);n.export_json_to_excel(i,a,"客户数据")}))}))},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return"createTime"===t?Object(c["f"])(e[t]):"flag"==t?0==e[t]?"启用":"禁用":"sex"==t?0==e[t]?"男":"女":"sites"==t?e[t]=e[t].filter((function(t){return"local"!=t.name})).reduce((function(t,e){return t+" "+e.description}),""):"operateDate"===t?Object(c["f"])(e[t]):e[t]}))}))}},watch:{siteAddDialogFormVisible:function(){this.checkSites=[]}}},h=f,m=(n("bee6"),n("2877")),b=Object(m["a"])(h,r,a,!1,null,null,null);e["default"]=b.exports}}]);