(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2881fc0e"],{"244b":function(n,t,e){"use strict";e.r(t);e("386d");var i,o,r={name:"authredirect",created:function(){var n=window.location.search.slice(1);window.opener.location.href=window.location.origin+"/login#"+n,window.close()}},c=r,a=e("2877"),u=Object(a["a"])(c,i,o,!1,null,null,null);t["default"]=u.exports},"386d":function(n,t,e){"use strict";var i=e("cb7c"),o=e("83a1"),r=e("5f1b");e("214f")("search",1,(function(n,t,e,c){return[function(e){var i=n(this),o=void 0==e?void 0:e[t];return void 0!==o?o.call(e,i):new RegExp(e)[t](String(i))},function(n){var t=c(e,n,this);if(t.done)return t.value;var a=i(n),u=String(this),l=a.lastIndex;o(l,0)||(a.lastIndex=0);var s=r(a,u);return o(a.lastIndex,l)||(a.lastIndex=l),null===s?-1:s.index}]}))},"83a1":function(n,t){n.exports=Object.is||function(n,t){return n===t?0!==n||1/n===1/t:n!=n&&t!=t}}}]);