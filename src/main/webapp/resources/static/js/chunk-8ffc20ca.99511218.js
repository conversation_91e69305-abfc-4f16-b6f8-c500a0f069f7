(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8ffc20ca"],{"44cf":function(e,t,n){},"6c6e":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"e",(function(){return o})),n.d(t,"d",(function(){return l})),n.d(t,"c",(function(){return s}));var r=n("1c1e");function i(e){return Object(r["a"])({url:"/resource/page",method:"post",params:e})}function a(e){return Object(r["a"])({url:"/resource/add",method:"post",params:e})}function o(e){return Object(r["a"])({url:"/resource/edit",method:"post",params:e})}function l(e){return Object(r["a"])({url:"/resource/tree/children",method:"post",params:{siteId:e}})}function s(e){return Object(r["a"])({url:"/resource/status",method:"post",params:{id:e}})}},"7aaa":function(e,t,n){"use strict";n("44cf")},bd93:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("div",{staticStyle:{"text-align":"right"}},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"230px"},attrs:{size:"small",placeholder:"权限名或权限字符串进行查询"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch.apply(null,arguments)}},model:{value:e.listQuery.keyword,callback:function(t){e.$set(e.listQuery,"keyword",t)},expression:"listQuery.keyword"}}),e._v(" "),n("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleSearch}},[e._v("搜索")]),e._v(" "),n("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.handleAddRole}},[e._v("新建权限")])],1),e._v(" "),n("el-row",{staticStyle:{"margin-top":"30px"},attrs:{gutter:20}},[n("el-col",{attrs:{span:4}},[n("el-tree",{ref:"tree",staticClass:"permission-tree",attrs:{"check-strictly":e.checkStrictly,data:e.permissionTree,props:e.defaultProps,"highlight-current":"","node-key":"id"},on:{"node-click":e.changeCurrentPermission}})],1),e._v(" "),n("el-col",{attrs:{span:20}},[n("el-table",{staticStyle:{width:"100%"},attrs:{data:e.permissionList,border:""}},[n("el-table-column",{attrs:{align:"center",label:"id",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.id))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"center",label:"权限名",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"header-center",label:"资源地址"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.url))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"header-center",label:"权限字符"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.permission))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"header-center",label:"创建日期"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("parseTime")(t.row.createTime)))]}}])}),e._v(" "),n("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(n){return e.handleEdit(t)}}},[e._v("编辑")]),e._v(" "),0==t.row.status?n("el-button",{attrs:{type:"danger",size:"small"},on:{click:function(n){return e.handleDelete(t)}}},[e._v("禁用")]):e._e(),e._v(" "),1==t.row.status?n("el-button",{attrs:{type:"success",size:"small"},on:{click:function(n){return e.handleDelete(t)}}},[e._v("开启")]):e._e()]}}])})],1),e._v(" "),n("div",{staticClass:"pagination-container"},[n("el-pagination",{attrs:{"current-page":e.listQuery.page,"page-sizes":[10,15,20,30],"page-size":e.listQuery.size,layout:" sizes, prev, pager, next,total, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.listQuery,"page",t)},"update:current-page":function(t){return e.$set(e.listQuery,"page",t)}}})],1)],1)],1),e._v(" "),n("el-dialog",{attrs:{visible:e.dialogVisible,title:"edit"===e.dialogType?"编辑":"新建"},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("el-form",{attrs:{model:e.role,"label-width":"80px","label-position":"left"}},[n("el-form-item",{attrs:{label:"名称"}},[n("el-input",{attrs:{placeholder:"权限名"},model:{value:e.role.name,callback:function(t){e.$set(e.role,"name",t)},expression:"role.name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"资源地址"}},[n("el-input",{attrs:{placeholder:"资源地址"},model:{value:e.role.url,callback:function(t){e.$set(e.role,"url",t)},expression:"role.url"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"权限字符"}},[n("el-input",{attrs:{placeholder:"权限字符"},model:{value:e.role.permission,callback:function(t){e.$set(e.role,"permission",t)},expression:"role.permission"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"上级权限"}},[n("treeselect",{attrs:{placeholder:"选择父权限",multiple:!1,options:e.permissionTree,normalizer:e.normalizer},model:{value:e.parentPermission,callback:function(t){e.parentPermission=t},expression:"parentPermission"}})],1)],1),e._v(" "),n("div",{staticStyle:{"text-align":"right",padding:"10px"}},[n("el-button",{attrs:{type:"danger"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("取消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.confirmRole}},[e._v("确定")])],1)],1)],1)},i=[],a=(n("8e6e"),n("ac6a"),n("456d"),n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("6b54"),n("ade3")),o=(n("6762"),n("2fdb"),n("96cf"),n("1da1")),l=(n("7f7f"),n("df7c")),s=n.n(l),c=n("ed08"),u=n("6c6e");function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){Object(a["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function h(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=p(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){l=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(l)throw a}}}}function p(e,t){if(e){if("string"===typeof e)return m(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var g={key:"",name:"",description:"",permission:"",uri:"",routes:[]},y={props:["siteId"],data:function(){return{listQuery:{keyword:"",page:1,size:15},total:null,parentPermission:null,permissionTree:[],currentSelectPermisson:null,role:Object.assign({},g),routes:[],permissionList:[],dialogVisible:!1,dialogType:"new",checkStrictly:!1,defaultProps:{children:"children",label:"name"}}},computed:{routesData:function(){return this.routes}},created:function(){var e=this;this.getTree(this.siteId).then((function(t){e.getRoles()}))},methods:{handleSearch:function(){this.getRoles()},handleSizeChange:function(e){this.listQuery.size=e,this.getRoles()},handleCurrentChange:function(e){this.listQuery.page=e,this.getRoles()},changeCurrentPermission:function(e,t,n){this.currentSelectPermisson=t,this.permissionList=Object(c["h"])(t)},normalizer:function(e){return{id:e.id,label:e.name,children:e.children}},getRoles:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=Object.assign({},this.listQuery),t.siteId=this.siteId,e.next=4,Object(u["b"])(t);case 4:n=e.sent,this.permissionList=n.obj.content,this.total=n.obj.totalElements;case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getTree:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(u["d"])(this.siteId);case 2:t=e.sent,n=t.obj,Object(c["b"])(n),this.permissionTree=n;case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleAddRole:function(){this.role=Object.assign({},g),this.dialogType="new",this.dialogVisible=!0},handleEdit:function(e){this.dialogType="edit",this.dialogVisible=!0,this.checkStrictly=!0,this.role=Object(c["a"])(e.row),this.parentPermission=0==e.row.pid?null:e.row.pid},handleDelete:function(e){var t=this,n=(e.$index,e.row);this.$confirm("确认禁用该权限","确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(o["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(u["c"])(n.id).then((function(e){t.getRoles(),t.$message({type:"success",message:e.msg})}));case 2:case"end":return e.stop()}}),e)})))).catch((function(e){console.error(e)}))},generateTree:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",r=arguments.length>2?arguments[2]:void 0,i=[],a=h(e);try{for(a.s();!(t=a.n()).done;){var o=t.value,l=s.a.resolve(n,o.path);o.children&&(o.children=this.generateTree(o.children,l,r)),(r.includes(l)||o.children&&o.children.length>=1)&&i.push(o)}}catch(c){a.e(c)}finally{a.f()}return i},confirmRole:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,n,r,i=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t="edit"===this.dialogType,n=this.parentPermission,this.role.pid=n,!t){e.next=8;break}return e.next=6,Object(u["e"])(this.role).then((function(e){i.getTree(i.siteId).then((function(e){i.getRoles()})),i.dialogVisible=!1,i.$notify({title:"成功",type:"success",duration:1e3})})).catch((function(e){i.$notify({title:"失败",message:"失败",type:"fail",duration:2e3})}));case 6:e.next=12;break;case 8:return r=Object.assign({},this.role),r.siteId=this.siteId,e.next=12,Object(u["a"])(r).then((function(e){i.getTree(i.siteId).then((function(e){i.getRoles()})),i.dialogVisible=!1,i.$notify({title:"成功",type:"success",duration:1e3})})).catch((function(e){i.$notify({title:"失败",message:"失败",type:"fail",duration:2e3})}));case 12:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),onlyOneShowingChild:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=null,r=e.filter((function(e){return!e.hidden}));return 1===r.length?(n=r[0],n.path=s.a.resolve(t.path,n.path),n):0===r.length&&(n=d(d({},t),{},{path:"",noShowingChildren:!0}),n)}}},b=y,v=(n("7aaa"),n("2877")),w=Object(v["a"])(b,r,i,!1,null,"1962cab5",null);t["default"]=w.exports},ed08:function(e,t,n){"use strict";n.d(t,"f",(function(){return l})),n.d(t,"d",(function(){return s})),n.d(t,"g",(function(){return c})),n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return f})),n.d(t,"e",(function(){return d})),n.d(t,"c",(function(){return h})),n.d(t,"h",(function(){return p}));n("ac4d"),n("8a81"),n("5df3"),n("1c4c"),n("7f7f"),n("6b54"),n("28a5"),n("ac6a"),n("456d"),n("4917"),n("a481");var r=n("53ca");function i(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=a(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){s=!0,o=e},f:function(){try{l||null==n.return||n.return()}finally{if(s)throw o}}}}function a(e,t){if(e){if("string"===typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function l(e,t){if(0===arguments.length)return null;var n,i=t||"{y}-{m}-{d} {h}:{i}:{s}";"object"===Object(r["a"])(e)?n=e:(10===(""+e).length&&(e=1e3*parseInt(e)),n=new Date(e));var a={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()},o=i.replace(/{(y|m|d|h|i|s|a)+}/g,(function(e,t){var n=a[t];return"a"===t?["一","二","三","四","五","六","日"][n-1]:(e.length>0&&n<10&&(n="0"+n),n||0)}));return o}function s(e,t){return!e||e.length<=t?e:e.substring(0,t)+"......"}function c(e,t){if(e&&t){var n=e.className,r=n.indexOf(t);-1===r?n+=""+t:n=n.substr(0,r)+n.substr(r+t.length),e.className=n}}function u(e){if(!e&&"object"!==Object(r["a"])(e))throw new Error("error arguments","shallowClone");var t=e.constructor===Array?[]:{};for(var n in e)e.hasOwnProperty(n)&&(e[n]&&"object"===Object(r["a"])(e[n])?(t[n]=e[n].constructor===Array?[]:{},t[n]=u(e[n])):t[n]=e[n]);return t}function f(e,t){var n,r=i(e);try{for(r.s();!(n=r.n()).done;){var a=n.value,o=a[t];o&&0!==o.length?f(o,t):delete a[t]}}catch(l){r.e(l)}finally{r.f()}}function d(e){if(Array.isArray(e)&&e.length>0)return e[e.length-1]}function h(e,t,n,r){if(Array.isArray(e)){var a,o=i(e);try{for(o.s();!(a=o.n()).done;){var l=a.value,s=h(l,t,n,r);if(s)return s}}catch(g){o.e(g)}finally{o.f()}}if(e[r]===t){var c=e[r],u=[e[r]];return{result:c,path:u}}if(e[n]){var f,d=i(e[n]);try{for(d.s();!(f=d.n()).done;){var p=f.value,m=h(p,t,n,r);if(m)return m.path.unshift(e[r]),m}}catch(g){d.e(g)}finally{d.f()}}}function p(e){var t=[];return function e(n){for(var r=n.childNodes,i=0;i<r.length;i++)t.push(r[i].data),e(r[i])}(e),t}}}]);