(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0b96a2"],{"339e":function(e,r,t){var n;/*! showdown 02-06-2017 */
(function(){function s(e){"use strict";var r={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Specify a prefix to generated header ids",type:"string"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(r));var t={};for(var n in r)r.hasOwnProperty(n)&&(t[n]=r[n].defaultValue);return t}function a(){"use strict";var e=s(!0),r={};for(var t in e)e.hasOwnProperty(t)&&(r[t]=!0);return r}var i={},o={},c={},l=s(!0),u="vanilla",p={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:s(!0),allOn:a()};function h(e,r){"use strict";var t=r?"Error in "+r+" extension->":"Error in unnamed extension",n={valid:!0,error:""};i.helper.isArray(e)||(e=[e]);for(var s=0;s<e.length;++s){var a=t+" sub-extension "+s+": ",o=e[s];if("object"!==typeof o)return n.valid=!1,n.error=a+"must be an object, but "+typeof o+" given",n;if(!i.helper.isString(o.type))return n.valid=!1,n.error=a+'property "type" must be a string, but '+typeof o.type+" given",n;var c=o.type=o.type.toLowerCase();if("language"===c&&(c=o.type="lang"),"html"===c&&(c=o.type="output"),"lang"!==c&&"output"!==c&&"listener"!==c)return n.valid=!1,n.error=a+"type "+c+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',n;if("listener"===c){if(i.helper.isUndefined(o.listeners))return n.valid=!1,n.error=a+'. Extensions of type "listener" must have a property called "listeners"',n}else if(i.helper.isUndefined(o.filter)&&i.helper.isUndefined(o.regex))return n.valid=!1,n.error=a+c+' extensions must define either a "regex" property or a "filter" method',n;if(o.listeners){if("object"!==typeof o.listeners)return n.valid=!1,n.error=a+'"listeners" property must be an object but '+typeof o.listeners+" given",n;for(var l in o.listeners)if(o.listeners.hasOwnProperty(l)&&"function"!==typeof o.listeners[l])return n.valid=!1,n.error=a+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+l+" must be a function but "+typeof o.listeners[l]+" given",n}if(o.filter){if("function"!==typeof o.filter)return n.valid=!1,n.error=a+'"filter" must be a function, but '+typeof o.filter+" given",n}else if(o.regex){if(i.helper.isString(o.regex)&&(o.regex=new RegExp(o.regex,"g")),!(o.regex instanceof RegExp))return n.valid=!1,n.error=a+'"regex" property must either be a string or a RegExp object, but '+typeof o.regex+" given",n;if(i.helper.isUndefined(o.replace))return n.valid=!1,n.error=a+'"regex" extensions must implement a replace string or function',n}}return n}function d(e,r){"use strict";var t=r.charCodeAt(0);return"¨E"+t+"E"}i.helper={},i.extensions={},i.setOption=function(e,r){"use strict";return l[e]=r,this},i.getOption=function(e){"use strict";return l[e]},i.getOptions=function(){"use strict";return l},i.resetOptions=function(){"use strict";l=s(!0)},i.setFlavor=function(e){"use strict";if(!p.hasOwnProperty(e))throw Error(e+" flavor was not found");i.resetOptions();var r=p[e];for(var t in u=e,r)r.hasOwnProperty(t)&&(l[t]=r[t])},i.getFlavor=function(){"use strict";return u},i.getFlavorOptions=function(e){"use strict";if(p.hasOwnProperty(e))return p[e]},i.getDefaultOptions=function(e){"use strict";return s(e)},i.subParser=function(e,r){"use strict";if(i.helper.isString(e)){if("undefined"===typeof r){if(o.hasOwnProperty(e))return o[e];throw Error("SubParser named "+e+" not registered!")}o[e]=r}},i.extension=function(e,r){"use strict";if(!i.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=i.helper.stdExtName(e),i.helper.isUndefined(r)){if(!c.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return c[e]}"function"===typeof r&&(r=r()),i.helper.isArray(r)||(r=[r]);var t=h(r,e);if(!t.valid)throw Error(t.error);c[e]=r},i.getAllExtensions=function(){"use strict";return c},i.removeExtension=function(e){"use strict";delete c[e]},i.resetExtensions=function(){"use strict";c={}},i.validateExtension=function(e){"use strict";var r=h(e,null);return!!r.valid||(console.warn(r.error),!1)},i.hasOwnProperty("helper")||(i.helper={}),i.helper.isString=function(e){"use strict";return"string"===typeof e||e instanceof String},i.helper.isFunction=function(e){"use strict";var r={};return e&&"[object Function]"===r.toString.call(e)},i.helper.isArray=function(e){"use strict";return e.constructor===Array},i.helper.isUndefined=function(e){"use strict";return"undefined"===typeof e},i.helper.forEach=function(e,r){"use strict";if(i.helper.isUndefined(e))throw new Error("obj param is required");if(i.helper.isUndefined(r))throw new Error("callback param is required");if(!i.helper.isFunction(r))throw new Error("callback param must be a function/closure");if("function"===typeof e.forEach)e.forEach(r);else if(i.helper.isArray(e))for(var t=0;t<e.length;t++)r(e[t],t,e);else{if("object"!==typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var n in e)e.hasOwnProperty(n)&&r(e[n],n,e)}},i.helper.stdExtName=function(e){"use strict";return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},i.helper.escapeCharactersCallback=d,i.helper.escapeCharacters=function(e,r,t){"use strict";var n="(["+r.replace(/([\[\]\\])/g,"\\$1")+"])";t&&(n="\\\\"+n);var s=new RegExp(n,"g");return e=e.replace(s,d),e};var f=function(e,r,t,n){"use strict";var s,a,i,o,c,l=n||"",u=l.indexOf("g")>-1,p=new RegExp(r+"|"+t,"g"+l.replace(/g/g,"")),h=new RegExp(r,l.replace(/g/g,"")),d=[];do{s=0;while(i=p.exec(e))if(h.test(i[0]))s++||(a=p.lastIndex,o=a-i[0].length);else if(s&&!--s){c=i.index+i[0].length;var f={left:{start:o,end:a},match:{start:a,end:i.index},right:{start:i.index,end:c},wholeMatch:{start:o,end:c}};if(d.push(f),!u)return d}}while(s&&(p.lastIndex=a));return d};i.helper.matchRecursiveRegExp=function(e,r,t,n){"use strict";for(var s=f(e,r,t,n),a=[],i=0;i<s.length;++i)a.push([e.slice(s[i].wholeMatch.start,s[i].wholeMatch.end),e.slice(s[i].match.start,s[i].match.end),e.slice(s[i].left.start,s[i].left.end),e.slice(s[i].right.start,s[i].right.end)]);return a},i.helper.replaceRecursiveRegExp=function(e,r,t,n,s){"use strict";if(!i.helper.isFunction(r)){var a=r;r=function(){return a}}var o=f(e,t,n,s),c=e,l=o.length;if(l>0){var u=[];0!==o[0].wholeMatch.start&&u.push(e.slice(0,o[0].wholeMatch.start));for(var p=0;p<l;++p)u.push(r(e.slice(o[p].wholeMatch.start,o[p].wholeMatch.end),e.slice(o[p].match.start,o[p].match.end),e.slice(o[p].left.start,o[p].left.end),e.slice(o[p].right.start,o[p].right.end))),p<l-1&&u.push(e.slice(o[p].wholeMatch.end,o[p+1].wholeMatch.start));o[l-1].wholeMatch.end<e.length&&u.push(e.slice(o[l-1].wholeMatch.end)),c=u.join("")}return c},i.helper.regexIndexOf=function(e,r,t){"use strict";if(!i.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(r instanceof RegExp===!1)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var n=e.substring(t||0).search(r);return n>=0?n+(t||0):n},i.helper.splitAtIndex=function(e,r){"use strict";if(!i.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,r),e.substring(r)]},i.helper.encodeEmailAddress=function(e){"use strict";var r=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e=e.replace(/./g,(function(e){if("@"===e)e=r[Math.floor(2*Math.random())](e);else{var t=Math.random();e=t>.9?r[2](e):t>.45?r[1](e):r[0](e)}return e})),e},"undefined"===typeof console&&(console={warn:function(e){"use strict";alert(e)},log:function(e){"use strict";alert(e)},error:function(e){"use strict";throw e}}),i.helper.regexes={asteriskAndDash:/([*_])/g},i.Converter=function(e){"use strict";var r={},t=[],n=[],s={},a=u;function o(){for(var t in e=e||{},l)l.hasOwnProperty(t)&&(r[t]=l[t]);if("object"!==typeof e)throw Error("Converter expects the passed parameter to be an object, but "+typeof e+" was passed instead.");for(var n in e)e.hasOwnProperty(n)&&(r[n]=e[n]);r.extensions&&i.helper.forEach(r.extensions,d)}function d(e,r){if(r=r||null,i.helper.isString(e)){if(e=i.helper.stdExtName(e),r=e,i.extensions[e])return console.warn("DEPRECATION WARNING: "+e+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void f(i.extensions[e],e);if(i.helper.isUndefined(c[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=c[e]}"function"===typeof e&&(e=e()),i.helper.isArray(e)||(e=[e]);var s=h(e,r);if(!s.valid)throw Error(s.error);for(var a=0;a<e.length;++a){switch(e[a].type){case"lang":t.push(e[a]);break;case"output":n.push(e[a]);break}if(e[a].hasOwnProperty("listeners"))for(var o in e[a].listeners)e[a].listeners.hasOwnProperty(o)&&g(o,e[a].listeners[o])}}function f(e,r){"function"===typeof e&&(e=e(new i.Converter)),i.helper.isArray(e)||(e=[e]);var s=h(e,r);if(!s.valid)throw Error(s.error);for(var a=0;a<e.length;++a)switch(e[a].type){case"lang":t.push(e[a]);break;case"output":n.push(e[a]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}function g(e,r){if(!i.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!==typeof r)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof r+" given");s.hasOwnProperty(e)||(s[e]=[]),s[e].push(r)}function b(e){var r=e.match(/^\s*/)[0].length,t=new RegExp("^\\s{0,"+r+"}","gm");return e.replace(t,"")}o(),this._dispatch=function(e,r,t,n){if(s.hasOwnProperty(e))for(var a=0;a<s[e].length;++a){var i=s[e][a](e,r,this,t,n);i&&"undefined"!==typeof i&&(r=i)}return r},this.listen=function(e,r){return g(e,r),this},this.makeHtml=function(e){if(!e)return e;var s={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:t,outputModifiers:n,converter:this,ghCodeBlocks:[]};return e=e.replace(/¨/g,"¨T"),e=e.replace(/\$/g,"¨D"),e=e.replace(/\r\n/g,"\n"),e=e.replace(/\r/g,"\n"),e=e.replace(/\u00A0/g," "),r.smartIndentationFix&&(e=b(e)),e="\n\n"+e+"\n\n",e=i.subParser("detab")(e,r,s),e=e.replace(/^[ \t]+$/gm,""),i.helper.forEach(t,(function(t){e=i.subParser("runExtension")(t,e,r,s)})),e=i.subParser("hashPreCodeTags")(e,r,s),e=i.subParser("githubCodeBlocks")(e,r,s),e=i.subParser("hashHTMLBlocks")(e,r,s),e=i.subParser("hashCodeTags")(e,r,s),e=i.subParser("stripLinkDefinitions")(e,r,s),e=i.subParser("blockGamut")(e,r,s),e=i.subParser("unhashHTMLSpans")(e,r,s),e=i.subParser("unescapeSpecialChars")(e,r,s),e=e.replace(/¨D/g,"$$"),e=e.replace(/¨T/g,"¨"),i.helper.forEach(n,(function(t){e=i.subParser("runExtension")(t,e,r,s)})),e},this.setOption=function(e,t){r[e]=t},this.getOption=function(e){return r[e]},this.getOptions=function(){return r},this.addExtension=function(e,r){r=r||null,d(e,r)},this.useExtension=function(e){d(e)},this.setFlavor=function(e){if(!p.hasOwnProperty(e))throw Error(e+" flavor was not found");var t=p[e];for(var n in a=e,t)t.hasOwnProperty(n)&&(r[n]=t[n])},this.getFlavor=function(){return a},this.removeExtension=function(e){i.helper.isArray(e)||(e=[e]);for(var r=0;r<e.length;++r){for(var s=e[r],a=0;a<t.length;++a)t[a]===s&&t[a].splice(a,1);for(var o=0;o<n.length;++a)n[o]===s&&n[o].splice(a,1)}},this.getAllExtensions=function(){return{language:t,output:n}}},i.subParser("anchors",(function(e,r,t){"use strict";e=t.converter._dispatch("anchors.before",e,r,t);var n=function(e,n,s,a,o,c,l){if(i.helper.isUndefined(l)&&(l=""),s=s.toLowerCase(),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)a="";else if(!a){if(s||(s=n.toLowerCase().replace(/ ?\n/g," ")),a="#"+s,i.helper.isUndefined(t.gUrls[s]))return e;a=t.gUrls[s],i.helper.isUndefined(t.gTitles[s])||(l=t.gTitles[s])}a=a.replace(i.helper.regexes.asteriskAndDash,i.helper.escapeCharactersCallback);var u='<a href="'+a+'"';return""!==l&&null!==l&&(l=l.replace(/"/g,"&quot;"),l=l.replace(i.helper.regexes.asteriskAndDash,i.helper.escapeCharactersCallback),u+=' title="'+l+'"'),r.openLinksInNewWindow&&(u+=' target="¨E95Eblank"'),u+=">"+n+"</a>",u};return e=e.replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,n),e=e.replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n),e=e.replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n),e=e.replace(/\[([^\[\]]+)]()()()()()/g,n),r.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d\-]+))(?=[.!?;,[\]()]|\s|$)/gim,(function(e,t,n,s,a){if("\\"===n)return t+s;if(!i.helper.isString(r.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var o=r.ghMentionsLink.replace(/\{u}/g,a);return t+'<a href="'+o+'">'+s+"</a>"}))),e=t.converter._dispatch("anchors.after",e,r,t),e}));var g=/\b(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+)()(?=\s|$)(?!["<>])/gi,b=/\b(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]]?)(?=\s|$)(?!["<>])/gi,m=/<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>/gi,v=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,k=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,w=function(e){"use strict";return function(r,t,n,s,a){var i=t,o="",c="";return/^www\./i.test(t)&&(t=t.replace(/^www\./i,"http://www.")),e.excludeTrailingPunctuationFromURLs&&a&&(o=a),e.openLinksInNewWindow&&(c=' target="¨E95Eblank"'),'<a href="'+t+'"'+c+">"+i+"</a>"+o}},x=function(e,r){"use strict";return function(t,n,s){var a="mailto:";return n=n||"",s=i.subParser("unescapeSpecialChars")(s,e,r),e.encodeEmails?(a=i.helper.encodeEmailAddress(a+s),s=i.helper.encodeEmailAddress(s)):a+=s,n+'<a href="'+a+'">'+s+"</a>"}};i.subParser("autoLinks",(function(e,r,t){"use strict";return e=t.converter._dispatch("autoLinks.before",e,r,t),e=e.replace(m,w(r)),e=e.replace(k,x(r,t)),e=t.converter._dispatch("autoLinks.after",e,r,t),e})),i.subParser("simplifiedAutoLinks",(function(e,r,t){"use strict";return r.simplifiedAutoLink?(e=t.converter._dispatch("simplifiedAutoLinks.before",e,r,t),e=r.excludeTrailingPunctuationFromURLs?e.replace(b,w(r)):e.replace(g,w(r)),e=e.replace(v,x(r,t)),e=t.converter._dispatch("simplifiedAutoLinks.after",e,r,t),e):e})),i.subParser("blockGamut",(function(e,r,t){"use strict";return e=t.converter._dispatch("blockGamut.before",e,r,t),e=i.subParser("blockQuotes")(e,r,t),e=i.subParser("headers")(e,r,t),e=i.subParser("horizontalRule")(e,r,t),e=i.subParser("lists")(e,r,t),e=i.subParser("codeBlocks")(e,r,t),e=i.subParser("tables")(e,r,t),e=i.subParser("hashHTMLBlocks")(e,r,t),e=i.subParser("paragraphs")(e,r,t),e=t.converter._dispatch("blockGamut.after",e,r,t),e})),i.subParser("blockQuotes",(function(e,r,t){"use strict";return e=t.converter._dispatch("blockQuotes.before",e,r,t),e=e.replace(/((^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+)/gm,(function(e,n){var s=n;return s=s.replace(/^[ \t]*>[ \t]?/gm,"¨0"),s=s.replace(/¨0/g,""),s=s.replace(/^[ \t]+$/gm,""),s=i.subParser("githubCodeBlocks")(s,r,t),s=i.subParser("blockGamut")(s,r,t),s=s.replace(/(^|\n)/g,"$1  "),s=s.replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,(function(e,r){var t=r;return t=t.replace(/^  /gm,"¨0"),t=t.replace(/¨0/g,""),t})),i.subParser("hashBlock")("<blockquote>\n"+s+"\n</blockquote>",r,t)})),e=t.converter._dispatch("blockQuotes.after",e,r,t),e})),i.subParser("codeBlocks",(function(e,r,t){"use strict";e=t.converter._dispatch("codeBlocks.before",e,r,t),e+="¨0";var n=/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g;return e=e.replace(n,(function(e,n,s){var a=n,o=s,c="\n";return a=i.subParser("outdent")(a,r,t),a=i.subParser("encodeCode")(a,r,t),a=i.subParser("detab")(a,r,t),a=a.replace(/^\n+/g,""),a=a.replace(/\n+$/g,""),r.omitExtraWLInCodeBlocks&&(c=""),a="<pre><code>"+a+c+"</code></pre>",i.subParser("hashBlock")(a,r,t)+o})),e=e.replace(/¨0/,""),e=t.converter._dispatch("codeBlocks.after",e,r,t),e})),i.subParser("codeSpans",(function(e,r,t){"use strict";return e=t.converter._dispatch("codeSpans.before",e,r,t),"undefined"===typeof e&&(e=""),e=e.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,(function(e,n,s,a){var o=a;return o=o.replace(/^([ \t]*)/g,""),o=o.replace(/[ \t]*$/g,""),o=i.subParser("encodeCode")(o,r,t),n+"<code>"+o+"</code>"})),e=t.converter._dispatch("codeSpans.after",e,r,t),e})),i.subParser("detab",(function(e,r,t){"use strict";return e=t.converter._dispatch("detab.before",e,r,t),e=e.replace(/\t(?=\t)/g,"    "),e=e.replace(/\t/g,"¨A¨B"),e=e.replace(/¨B(.+?)¨A/g,(function(e,r){for(var t=r,n=4-t.length%4,s=0;s<n;s++)t+=" ";return t})),e=e.replace(/¨A/g,"    "),e=e.replace(/¨B/g,""),e=t.converter._dispatch("detab.after",e,r,t),e})),i.subParser("encodeAmpsAndAngles",(function(e,r,t){"use strict";return e=t.converter._dispatch("encodeAmpsAndAngles.before",e,r,t),e=e.replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;"),e=e.replace(/<(?![a-z\/?$!])/gi,"&lt;"),e=e.replace(/</g,"&lt;"),e=e.replace(/>/g,"&gt;"),e=t.converter._dispatch("encodeAmpsAndAngles.after",e,r,t),e})),i.subParser("encodeBackslashEscapes",(function(e,r,t){"use strict";return e=t.converter._dispatch("encodeBackslashEscapes.before",e,r,t),e=e.replace(/\\(\\)/g,i.helper.escapeCharactersCallback),e=e.replace(/\\([`*_{}\[\]()>#+.!~=|-])/g,i.helper.escapeCharactersCallback),e=t.converter._dispatch("encodeBackslashEscapes.after",e,r,t),e})),i.subParser("encodeCode",(function(e,r,t){"use strict";return e=t.converter._dispatch("encodeCode.before",e,r,t),e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,i.helper.escapeCharactersCallback),e=t.converter._dispatch("encodeCode.after",e,r,t),e})),i.subParser("escapeSpecialCharsWithinTagAttributes",(function(e,r,t){"use strict";e=t.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,r,t);var n=/(<[a-z\/!$]("[^"]*"|'[^']*'|[^'">])*>|<!(--.*?--\s*)+>)/gi;return e=e.replace(n,(function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,i.helper.escapeCharactersCallback)})),e=t.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,r,t),e})),i.subParser("githubCodeBlocks",(function(e,r,t){"use strict";return r.ghCodeBlocks?(e=t.converter._dispatch("githubCodeBlocks.before",e,r,t),e+="¨0",e=e.replace(/(?:^|\n)```(.*)\n([\s\S]*?)\n```/g,(function(e,n,s){var a=r.omitExtraWLInCodeBlocks?"":"\n";return s=i.subParser("encodeCode")(s,r,t),s=i.subParser("detab")(s,r,t),s=s.replace(/^\n+/g,""),s=s.replace(/\n+$/g,""),s="<pre><code"+(n?' class="'+n+" language-"+n+'"':"")+">"+s+a+"</code></pre>",s=i.subParser("hashBlock")(s,r,t),"\n\n¨G"+(t.ghCodeBlocks.push({text:e,codeblock:s})-1)+"G\n\n"})),e=e.replace(/¨0/,""),t.converter._dispatch("githubCodeBlocks.after",e,r,t)):e})),i.subParser("hashBlock",(function(e,r,t){"use strict";return e=t.converter._dispatch("hashBlock.before",e,r,t),e=e.replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(t.gHtmlBlocks.push(e)-1)+"K\n\n",e=t.converter._dispatch("hashBlock.after",e,r,t),e})),i.subParser("hashCodeTags",(function(e,r,t){"use strict";e=t.converter._dispatch("hashCodeTags.before",e,r,t);var n=function(e,n,s,a){var o=s+i.subParser("encodeCode")(n,r,t)+a;return"¨C"+(t.gHtmlSpans.push(o)-1)+"C"};return e=i.helper.replaceRecursiveRegExp(e,n,"<code\\b[^>]*>","</code>","gim"),e=t.converter._dispatch("hashCodeTags.after",e,r,t),e})),i.subParser("hashElement",(function(e,r,t){"use strict";return function(e,r){var n=r;return n=n.replace(/\n\n/g,"\n"),n=n.replace(/^\n/,""),n=n.replace(/\n+$/g,""),n="\n\n¨K"+(t.gHtmlBlocks.push(n)-1)+"K\n\n",n}})),i.subParser("hashHTMLBlocks",(function(e,r,t){"use strict";e=t.converter._dispatch("hashHTMLBlocks.before",e,r,t);for(var n=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],s=function(e,r,n,s){var a=e;return-1!==n.search(/\bmarkdown\b/)&&(a=n+t.converter.makeHtml(r)+s),"\n\n¨K"+(t.gHtmlBlocks.push(a)-1)+"K\n\n"},a=0;a<n.length;++a){var o,c=new RegExp("^ {0,3}<"+n[a]+"\\b[^>]*>","im"),l="<"+n[a]+"\\b[^>]*>",u="</"+n[a]+">";while(-1!==(o=i.helper.regexIndexOf(e,c))){var p=i.helper.splitAtIndex(e,o),h=i.helper.replaceRecursiveRegExp(p[1],s,l,u,"im");if(h===p[1])break;e=p[0].concat(h)}}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,i.subParser("hashElement")(e,r,t)),e=i.helper.replaceRecursiveRegExp(e,(function(e){return"\n\n¨K"+(t.gHtmlBlocks.push(e)-1)+"K\n\n"}),"^ {0,3}\x3c!--","--\x3e","gm"),e=e.replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,i.subParser("hashElement")(e,r,t)),e=t.converter._dispatch("hashHTMLBlocks.after",e,r,t),e})),i.subParser("hashHTMLSpans",(function(e,r,t){"use strict";function n(e){return"¨C"+(t.gHtmlSpans.push(e)-1)+"C"}return e=t.converter._dispatch("hashHTMLSpans.before",e,r,t),e=e.replace(/<[^>]+?\/>/gi,(function(e){return n(e)})),e=e.replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,(function(e){return n(e)})),e=e.replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,(function(e){return n(e)})),e=e.replace(/<[^>]+?>/gi,(function(e){return n(e)})),e=t.converter._dispatch("hashHTMLSpans.after",e,r,t),e})),i.subParser("unhashHTMLSpans",(function(e,r,t){"use strict";e=t.converter._dispatch("unhashHTMLSpans.before",e,r,t);for(var n=0;n<t.gHtmlSpans.length;++n){var s=t.gHtmlSpans[n],a=0;while(/¨C(\d+)C/.test(s)){var i=RegExp.$1;if(s=s.replace("¨C"+i+"C",t.gHtmlSpans[i]),10===a)break;++a}e=e.replace("¨C"+n+"C",s)}return e=t.converter._dispatch("unhashHTMLSpans.after",e,r,t),e})),i.subParser("hashPreCodeTags",(function(e,r,t){"use strict";e=t.converter._dispatch("hashPreCodeTags.before",e,r,t);var n=function(e,n,s,a){var o=s+i.subParser("encodeCode")(n,r,t)+a;return"\n\n¨G"+(t.ghCodeBlocks.push({text:e,codeblock:o})-1)+"G\n\n"};return e=i.helper.replaceRecursiveRegExp(e,n,"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),e=t.converter._dispatch("hashPreCodeTags.after",e,r,t),e})),i.subParser("headers",(function(e,r,t){"use strict";e=t.converter._dispatch("headers.before",e,r,t);var n=isNaN(parseInt(r.headerLevelStart))?1:parseInt(r.headerLevelStart),s=r.ghCompatibleHeaderId,a=r.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,o=r.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;e=e.replace(a,(function(e,s){var a=i.subParser("spanGamut")(s,r,t),o=r.noHeaderId?"":' id="'+l(s)+'"',c=n,u="<h"+c+o+">"+a+"</h"+c+">";return i.subParser("hashBlock")(u,r,t)})),e=e.replace(o,(function(e,s){var a=i.subParser("spanGamut")(s,r,t),o=r.noHeaderId?"":' id="'+l(s)+'"',c=n+1,u="<h"+c+o+">"+a+"</h"+c+">";return i.subParser("hashBlock")(u,r,t)}));var c=r.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function l(e){var n;if(r.customizedHeaderId){var a=e.match(/\{([^{]+?)}\s*$/);a&&a[1]&&(e=a[1])}return n=i.helper.isString(r.prefixHeaderId)?r.prefixHeaderId+e:!0===r.prefixHeaderId?"section "+e:e,n=s?n.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():n.replace(/[^\w]/g,"").toLowerCase(),t.hashLinkCounts[n]?n=n+"-"+t.hashLinkCounts[n]++:t.hashLinkCounts[n]=1,n}return e=e.replace(c,(function(e,s,a){var o=a;r.customizedHeaderId&&(o=a.replace(/\s?\{([^{]+?)}\s*$/,""));var c=i.subParser("spanGamut")(o,r,t),u=r.noHeaderId?"":' id="'+l(a)+'"',p=n-1+s.length,h="<h"+p+u+">"+c+"</h"+p+">";return i.subParser("hashBlock")(h,r,t)})),e=t.converter._dispatch("headers.after",e,r,t),e})),i.subParser("horizontalRule",(function(e,r,t){"use strict";e=t.converter._dispatch("horizontalRule.before",e,r,t);var n=i.subParser("hashBlock")("<hr />",r,t);return e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,n),e=e.replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,n),e=e.replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,n),e=t.converter._dispatch("horizontalRule.after",e,r,t),e})),i.subParser("images",(function(e,r,t){"use strict";e=t.converter._dispatch("images.before",e,r,t);var n=/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,s=/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,a=/!\[([^\]]*?)] ?(?:\n *)?\[(.*?)]()()()()()/g,o=/!\[([^\[\]]+)]()()()()()/g;function c(e,r,n,s,a,o,c,l){var u=t.gUrls,p=t.gTitles,h=t.gDimensions;if(n=n.toLowerCase(),l||(l=""),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)s="";else if(""===s||null===s){if(""!==n&&null!==n||(n=r.toLowerCase().replace(/ ?\n/g," ")),s="#"+n,i.helper.isUndefined(u[n]))return e;s=u[n],i.helper.isUndefined(p[n])||(l=p[n]),i.helper.isUndefined(h[n])||(a=h[n].width,o=h[n].height)}r=r.replace(/"/g,"&quot;").replace(i.helper.regexes.asteriskAndDash,i.helper.escapeCharactersCallback),s=s.replace(i.helper.regexes.asteriskAndDash,i.helper.escapeCharactersCallback);var d='<img src="'+s+'" alt="'+r+'"';return l&&(l=l.replace(/"/g,"&quot;").replace(i.helper.regexes.asteriskAndDash,i.helper.escapeCharactersCallback),d+=' title="'+l+'"'),a&&o&&(a="*"===a?"auto":a,o="*"===o?"auto":o,d+=' width="'+a+'"',d+=' height="'+o+'"'),d+=" />",d}return e=e.replace(a,c),e=e.replace(s,c),e=e.replace(n,c),e=e.replace(o,c),e=t.converter._dispatch("images.after",e,r,t),e})),i.subParser("italicsAndBold",(function(e,r,t){"use strict";function n(e,n,s){return r.simplifiedAutoLink&&(e=i.subParser("simplifiedAutoLinks")(e,r,t)),n+e+s}return e=t.converter._dispatch("italicsAndBold.before",e,r,t),r.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*)___\b/g,(function(e,r){return n(r,"<strong><em>","</em></strong>")})),e=e.replace(/\b__(\S[\s\S]*)__\b/g,(function(e,r){return n(r,"<strong>","</strong>")})),e=e.replace(/\b_(\S[\s\S]*?)_\b/g,(function(e,r){return n(r,"<em>","</em>")}))):(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,r){return/\S$/.test(r)?n(r,"<strong><em>","</em></strong>"):e})),e=e.replace(/__(\S[\s\S]*?)__/g,(function(e,r){return/\S$/.test(r)?n(r,"<strong>","</strong>"):e})),e=e.replace(/_([^\s_][\s\S]*?)_/g,(function(e,r){return/\S$/.test(r)?n(r,"<em>","</em>"):e}))),r.literalMidWordAsterisks?(e=e.trim().replace(/(?:^| +)\*{3}(\S[\s\S]*?)\*{3}(?: +|$)/g,(function(e,r){return n(r," <strong><em>","</em></strong> ")})),e=e.trim().replace(/(?:^| +)\*{2}(\S[\s\S]*?)\*{2}(?: +|$)/g,(function(e,r){return n(r," <strong>","</strong> ")})),e=e.trim().replace(/(?:^| +)\*{1}(\S[\s\S]*?)\*{1}(?: +|$)/g,(function(e,r){return n(r," <em>","</em>"+(" "===e.slice(-1)?" ":""))}))):(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,(function(e,r){return/\S$/.test(r)?n(r,"<strong><em>","</em></strong>"):e})),e=e.replace(/\*\*(\S[\s\S]*?)\*\*/g,(function(e,r){return/\S$/.test(r)?n(r,"<strong>","</strong>"):e})),e=e.replace(/\*([^\s*][\s\S]*?)\*/g,(function(e,r){return/\S$/.test(r)?n(r,"<em>","</em>"):e}))),e=t.converter._dispatch("italicsAndBold.after",e,r,t),e})),i.subParser("lists",(function(e,r,t){"use strict";function n(e,n){t.gListLevel++,e=e.replace(/\n{2,}$/,"\n"),e+="¨0";var s=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,a=/\n[ \t]*\n(?!¨0)/.test(e);return r.disableForced4SpacesIndentedSublists&&(s=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=e.replace(s,(function(e,n,s,o,c,l,u){u=u&&""!==u.trim();var p=i.subParser("outdent")(c,r,t),h="";return l&&r.tasklists&&(h=' class="task-list-item" style="list-style-type: none;"',p=p.replace(/^[ \t]*\[(x|X| )?]/m,(function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return u&&(e+=" checked"),e+=">",e}))),p=p.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,(function(e){return"¨A"+e})),n||p.search(/\n{2,}/)>-1?(p=i.subParser("githubCodeBlocks")(p,r,t),p=i.subParser("blockGamut")(p,r,t)):(p=i.subParser("lists")(p,r,t),p=p.replace(/\n$/,""),p=i.subParser("hashHTMLBlocks")(p,r,t),p=p.replace(/\n\n+/g,"\n\n"),p=p.replace(/\n\n/g,"¨B"),p=a?i.subParser("paragraphs")(p,r,t):i.subParser("spanGamut")(p,r,t),p=p.replace(/¨B/g,"\n\n")),p=p.replace("¨A",""),p="<li"+h+">"+p+"</li>\n",p})),e=e.replace(/¨0/g,""),t.gListLevel--,n&&(e=e.replace(/\s+$/,"")),e}function s(e,t,s){var a=r.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,i=r.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,o="ul"===t?a:i,c="";return-1!==e.search(o)?function e(r){var l=r.search(o);-1!==l?(c+="\n<"+t+">\n"+n(r.slice(0,l),!!s)+"</"+t+">\n",t="ul"===t?"ol":"ul",o="ul"===t?a:i,e(r.slice(l))):c+="\n<"+t+">\n"+n(r,!!s)+"</"+t+">\n"}(e):c="\n<"+t+">\n"+n(e,!!s)+"</"+t+">\n",c}return e=t.converter._dispatch("lists.before",e,r,t),e+="¨0",e=t.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,r,t){var n=t.search(/[*+-]/g)>-1?"ul":"ol";return s(r,n,!0)})):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,r,t,n){var a=n.search(/[*+-]/g)>-1?"ul":"ol";return s(t,a,!1)})),e=e.replace(/¨0/,""),e=t.converter._dispatch("lists.after",e,r,t),e})),i.subParser("outdent",(function(e,r,t){"use strict";return e=t.converter._dispatch("outdent.before",e,r,t),e=e.replace(/^(\t|[ ]{1,4})/gm,"¨0"),e=e.replace(/¨0/g,""),e=t.converter._dispatch("outdent.after",e,r,t),e})),i.subParser("paragraphs",(function(e,r,t){"use strict";e=t.converter._dispatch("paragraphs.before",e,r,t),e=e.replace(/^\n+/g,""),e=e.replace(/\n+$/g,"");for(var n=e.split(/\n{2,}/g),s=[],a=n.length,o=0;o<a;o++){var c=n[o];c.search(/¨(K|G)(\d+)\1/g)>=0?s.push(c):c.search(/\S/)>=0&&(c=i.subParser("spanGamut")(c,r,t),c=c.replace(/^([ \t]*)/g,"<p>"),c+="</p>",s.push(c))}for(a=s.length,o=0;o<a;o++){var l="",u=s[o],p=!1;while(/¨(K|G)(\d+)\1/.test(u)){var h=RegExp.$1,d=RegExp.$2;l="K"===h?t.gHtmlBlocks[d]:p?i.subParser("encodeCode")(t.ghCodeBlocks[d].text,r,t):t.ghCodeBlocks[d].codeblock,l=l.replace(/\$/g,"$$$$"),u=u.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,l),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(u)&&(p=!0)}s[o]=u}return e=s.join("\n"),e=e.replace(/^\n+/g,""),e=e.replace(/\n+$/g,""),t.converter._dispatch("paragraphs.after",e,r,t)})),i.subParser("runExtension",(function(e,r,t,n){"use strict";if(e.filter)r=e.filter(r,n.converter,t);else if(e.regex){var s=e.regex;s instanceof RegExp||(s=new RegExp(s,"g")),r=r.replace(s,e.replace)}return r})),i.subParser("spanGamut",(function(e,r,t){"use strict";return e=t.converter._dispatch("spanGamut.before",e,r,t),e=i.subParser("codeSpans")(e,r,t),e=i.subParser("escapeSpecialCharsWithinTagAttributes")(e,r,t),e=i.subParser("encodeBackslashEscapes")(e,r,t),e=i.subParser("images")(e,r,t),e=i.subParser("anchors")(e,r,t),e=i.subParser("autoLinks")(e,r,t),e=i.subParser("italicsAndBold")(e,r,t),e=i.subParser("strikethrough")(e,r,t),e=i.subParser("simplifiedAutoLinks")(e,r,t),e=i.subParser("hashHTMLSpans")(e,r,t),e=i.subParser("encodeAmpsAndAngles")(e,r,t),e=r.simpleLineBreaks?e.replace(/\n/g,"<br />\n"):e.replace(/  +\n/g,"<br />\n"),e=t.converter._dispatch("spanGamut.after",e,r,t),e})),i.subParser("strikethrough",(function(e,r,t){"use strict";function n(e){return r.simplifiedAutoLink&&(e=i.subParser("simplifiedAutoLinks")(e,r,t)),"<del>"+e+"</del>"}return r.strikethrough&&(e=t.converter._dispatch("strikethrough.before",e,r,t),e=e.replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,(function(e,r){return n(r)})),e=t.converter._dispatch("strikethrough.after",e,r,t)),e})),i.subParser("stripLinkDefinitions",(function(e,r,t){"use strict";var n=/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm;return e+="¨0",e=e.replace(n,(function(e,n,s,a,o,c,l){return n=n.toLowerCase(),t.gUrls[n]=i.subParser("encodeAmpsAndAngles")(s,r,t),c?c+l:(l&&(t.gTitles[n]=l.replace(/"|'/g,"&quot;")),r.parseImgDimensions&&a&&o&&(t.gDimensions[n]={width:a,height:o}),"")})),e=e.replace(/¨0/,""),e})),i.subParser("tables",(function(e,r,t){"use strict";if(!r.tables)return e;var n=/^ {0,3}\|?.+\|.+\n[ \t]{0,3}\|?[ \t]*:?[ \t]*(?:-|=){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:-|=){2,}[\s\S]+?(?:\n\n|¨0)/gm;function s(e){return/^:[ \t]*--*$/.test(e)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(e)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(e)?' style="text-align:center;"':""}function a(e,n){var s="";return e=e.trim(),r.tableHeaderId&&(s=' id="'+e.replace(/ /g,"_").toLowerCase()+'"'),e=i.subParser("spanGamut")(e,r,t),"<th"+s+n+">"+e+"</th>\n"}function o(e,n){var s=i.subParser("spanGamut")(e,r,t);return"<td"+n+">"+s+"</td>\n"}function c(e,r){for(var t="<table>\n<thead>\n<tr>\n",n=e.length,s=0;s<n;++s)t+=e[s];for(t+="</tr>\n</thead>\n<tbody>\n",s=0;s<r.length;++s){t+="<tr>\n";for(var a=0;a<n;++a)t+=r[s][a];t+="</tr>\n"}return t+="</tbody>\n</table>\n",t}return e=t.converter._dispatch("tables.before",e,r,t),e=e.replace(/\\(\|)/g,i.helper.escapeCharactersCallback),e=e.replace(n,(function(e){var r,t=e.split("\n");for(r=0;r<t.length;++r)/^ {0,3}\|/.test(t[r])&&(t[r]=t[r].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(t[r])&&(t[r]=t[r].replace(/\|[ \t]*$/,""));var n=t[0].split("|").map((function(e){return e.trim()})),l=t[1].split("|").map((function(e){return e.trim()})),u=[],p=[],h=[],d=[];for(t.shift(),t.shift(),r=0;r<t.length;++r)""!==t[r].trim()&&u.push(t[r].split("|").map((function(e){return e.trim()})));if(n.length<l.length)return e;for(r=0;r<l.length;++r)h.push(s(l[r]));for(r=0;r<n.length;++r)i.helper.isUndefined(h[r])&&(h[r]=""),p.push(a(n[r],h[r]));for(r=0;r<u.length;++r){for(var f=[],g=0;g<p.length;++g)i.helper.isUndefined(u[r][g]),f.push(o(u[r][g],h[g]));d.push(f)}return c(p,d)})),e=t.converter._dispatch("tables.after",e,r,t),e})),i.subParser("unescapeSpecialChars",(function(e,r,t){"use strict";return e=t.converter._dispatch("unescapeSpecialChars.before",e,r,t),e=e.replace(/¨E(\d+)E/g,(function(e,r){var t=parseInt(r);return String.fromCharCode(t)})),e=t.converter._dispatch("unescapeSpecialChars.after",e,r,t),e}));e.exports?e.exports=i:(n=function(){"use strict";return i}.call(r,t,r,e),void 0===n||(e.exports=n))}).call(this)}}]);