(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c8f68"],{"56b3":function(e,t,r){(function(t,r){e.exports=r()})(0,(function(){"use strict";var e=navigator.userAgent,t=navigator.platform,r=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),l=n||i||o,s=l&&(n?document.documentMode||6:+(o||i)[1]),a=!o&&/WebKit\//.test(e),u=a&&/Qt\/\d+\.\d+/.test(e),c=!o&&/Chrome\//.test(e),h=/Opera\//.test(e),f=/Apple Computer/.test(navigator.vendor),d=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),p=/PhantomJS/.test(e),g=!o&&/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),v=/Android/.test(e),m=g||v||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),y=g||/Mac/.test(t),b=/\bCrOS\b/.test(e),w=/win/i.test(t),x=h&&e.match(/Version\/(\d*\.\d*)/);x&&(x=Number(x[1])),x&&x>=15&&(h=!1,a=!0);var C=y&&(u||h&&(null==x||x<12.11)),S=r||l&&s>=9;function L(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var k,T=function(e,t){var r=e.className,n=L(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function M(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function N(e,t){return M(e).appendChild(t)}function O(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function A(e,t,r,n){var i=O(e,t,r,n);return i.setAttribute("role","presentation"),i}function W(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function D(){var e;try{e=document.activeElement}catch(t){e=document.body||null}while(e&&e.shadowRoot&&e.shadowRoot.activeElement)e=e.shadowRoot.activeElement;return e}function H(e,t){var r=e.className;L(t).test(r)||(e.className+=(r?" ":"")+t)}function E(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!L(r[n]).test(t)&&(t+=" "+r[n]);return t}k=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(i){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var P=function(e){e.select()};function F(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function I(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function z(e,t,r,n,i){null==t&&(t=e.search(/[^\s\u00a0]/),-1==t&&(t=e.length));for(var o=n||0,l=i||0;;){var s=e.indexOf("\t",o);if(s<0||s>=t)return l+(t-o);l+=s-o,l+=r-l%r,o=s+1}}g?P=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:l&&(P=function(e){try{e.select()}catch(t){}});var R=function(){this.id=null};function B(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}R.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var G=30,U={toString:function(){return"CodeMirror.Pass"}},V={scroll:!1},K={origin:"*mouse"},j={origin:"+move"};function X(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var l=o-n;if(o==e.length||i+l>=t)return n+Math.min(l,t-i);if(i+=o-n,i+=r-i%r,n=o+1,i>=t)return n}}var Y=[""];function _(e){while(Y.length<=e)Y.push($(Y)+" ");return Y[e]}function $(e){return e[e.length-1]}function q(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function Z(e,t,r){var n=0,i=r(t);while(n<e.length&&r(e[n])<=i)n++;e.splice(n,0,t)}function J(){}function Q(e,t){var r;return Object.create?r=Object.create(e):(J.prototype=e,r=new J),t&&I(t,r),r}var ee=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function te(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||ee.test(e))}function re(e,t){return t?!!(t.source.indexOf("\\w")>-1&&te(e))||t.test(e):te(e)}function ne(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ie=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function oe(e){return e.charCodeAt(0)>=768&&ie.test(e)}function le(e,t,r){while((r<0?t>0:t<e.length)&&oe(e.charAt(t)))t+=r;return t}function se(e,t,r){for(;;){if(Math.abs(t-r)<=1)return e(t)?t:r;var n=Math.floor((t+r)/2);e(n)?r=n:t=n}}function ae(e,t,n){var i=this;this.input=n,i.scrollbarFiller=O("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=O("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=A("div",null,"CodeMirror-code"),i.selectionDiv=O("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=O("div",null,"CodeMirror-cursors"),i.measure=O("div",null,"CodeMirror-measure"),i.lineMeasure=O("div",null,"CodeMirror-measure"),i.lineSpace=A("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=A("div",[i.lineSpace],"CodeMirror-lines");i.mover=O("div",[o],null,"position: relative"),i.sizer=O("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=O("div",null,null,"position: absolute; height: "+G+"px; width: 1px;"),i.gutters=O("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=O("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=O("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),l&&s<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),a||r&&m||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,n.init(i)}function ue(e,t){if(t-=e.first,t<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");var r=e;while(!r.lines)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function ce(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,(function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i})),n}function he(e,t,r){var n=[];return e.iter(t,r,(function(e){n.push(e.text)})),n}function fe(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function de(e){if(null==e.parent)return null;for(var t=e.parent,r=B(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;;++i){if(n.children[i]==t)break;r+=n.children[i].chunkSize()}return r+t.first}function pe(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var l=0;l<e.lines.length;++l){var s=e.lines[l],a=s.height;if(t<a)break;t-=a}return r+l}function ge(e,t){return t>=e.first&&t<e.first+e.size}function ve(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function me(e,t,r){if(void 0===r&&(r=null),!(this instanceof me))return new me(e,t,r);this.line=e,this.ch=t,this.sticky=r}function ye(e,t){return e.line-t.line||e.ch-t.ch}function be(e,t){return e.sticky==t.sticky&&0==ye(e,t)}function we(e){return me(e.line,e.ch)}function xe(e,t){return ye(e,t)<0?t:e}function Ce(e,t){return ye(e,t)<0?e:t}function Se(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function Le(e,t){if(t.line<e.first)return me(e.first,0);var r=e.first+e.size-1;return t.line>r?me(r,ue(e,r).text.length):ke(t,ue(e,t.line).text.length)}function ke(e,t){var r=e.ch;return null==r||r>t?me(e.line,t):r<0?me(e.line,0):e}function Te(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=Le(e,t[n]);return r}var Me=!1,Ne=!1;function Oe(){Me=!0}function Ae(){Ne=!0}function We(e,t,r){this.marker=e,this.from=t,this.to=r}function De(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function He(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Ee(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}function Pe(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,s=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);if(s||o.from==t&&"bookmark"==l.type&&(!r||!o.marker.insertLeft)){var a=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new We(l,o.from,a?null:o.to))}}return n}function Fe(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,s=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);if(s||o.from==t&&"bookmark"==l.type&&(!r||o.marker.insertLeft)){var a=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new We(l,a?null:o.from-t,null==o.to?null:o.to-t))}}return n}function Ie(e,t){if(t.full)return null;var r=ge(e,t.from.line)&&ue(e,t.from.line).markedSpans,n=ge(e,t.to.line)&&ue(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,l=0==ye(t.from,t.to),s=Pe(r,i,l),a=Fe(n,o,l),u=1==t.text.length,c=$(t.text).length+(u?i:0);if(s)for(var h=0;h<s.length;++h){var f=s[h];if(null==f.to){var d=De(a,f.marker);d?u&&(f.to=null==d.to?null:d.to+c):f.to=i}}if(a)for(var p=0;p<a.length;++p){var g=a[p];if(null!=g.to&&(g.to+=c),null==g.from){var v=De(s,g.marker);v||(g.from=c,u&&(s||(s=[])).push(g))}else g.from+=c,u&&(s||(s=[])).push(g)}s&&(s=ze(s)),a&&a!=s&&(a=ze(a));var m=[s];if(!u){var y,b=t.text.length-2;if(b>0&&s)for(var w=0;w<s.length;++w)null==s[w].to&&(y||(y=[])).push(new We(s[w].marker,null,null));for(var x=0;x<b;++x)m.push(y);m.push(a)}return m}function ze(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Re(e,t,r){var n=null;if(e.iter(t.line,r.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=B(n,r)||(n||(n=[])).push(r)}})),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var l=n[o],s=l.find(0),a=0;a<i.length;++a){var u=i[a];if(!(ye(u.to,s.from)<0||ye(u.from,s.to)>0)){var c=[a,1],h=ye(u.from,s.from),f=ye(u.to,s.to);(h<0||!l.inclusiveLeft&&!h)&&c.push({from:u.from,to:s.from}),(f>0||!l.inclusiveRight&&!f)&&c.push({from:s.to,to:u.to}),i.splice.apply(i,c),a+=c.length-3}}return i}function Be(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function Ge(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function Ue(e){return e.inclusiveLeft?-1:0}function Ve(e){return e.inclusiveRight?1:0}function Ke(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=ye(n.from,i.from)||Ue(e)-Ue(t);if(o)return-o;var l=ye(n.to,i.to)||Ve(e)-Ve(t);return l||t.id-e.id}function je(e,t){var r,n=Ne&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)i=n[o],i.marker.collapsed&&null==(t?i.from:i.to)&&(!r||Ke(r,i.marker)<0)&&(r=i.marker);return r}function Xe(e){return je(e,!0)}function Ye(e){return je(e,!1)}function _e(e,t,r,n,i){var o=ue(e,t),l=Ne&&o.markedSpans;if(l)for(var s=0;s<l.length;++s){var a=l[s];if(a.marker.collapsed){var u=a.marker.find(0),c=ye(u.from,r)||Ue(a.marker)-Ue(i),h=ye(u.to,n)||Ve(a.marker)-Ve(i);if(!(c>=0&&h<=0||c<=0&&h>=0)&&(c<=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?ye(u.to,r)>=0:ye(u.to,r)>0)||c>=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?ye(u.from,n)<=0:ye(u.from,n)<0)))return!0}}}function $e(e){var t;while(t=Xe(e))e=t.find(-1,!0).line;return e}function qe(e){var t;while(t=Ye(e))e=t.find(1,!0).line;return e}function Ze(e){var t,r;while(t=Ye(e))e=t.find(1,!0).line,(r||(r=[])).push(e);return r}function Je(e,t){var r=ue(e,t),n=$e(r);return r==n?t:de(n)}function Qe(e,t){if(t>e.lastLine())return t;var r,n=ue(e,t);if(!et(e,n))return t;while(r=Ye(n))n=r.find(1,!0).line;return de(n)+1}function et(e,t){var r=Ne&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if(n=r[i],n.marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&tt(e,t,n))return!0}}function tt(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return tt(e,n.line,De(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if(i=t.markedSpans[o],i.marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&tt(e,t,i))return!0}function rt(e){e=$e(e);for(var t=0,r=e.parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;r=o,o=r.parent)for(var l=0;l<o.children.length;++l){var s=o.children[l];if(s==r)break;t+=s.height}return t}function nt(e){if(0==e.height)return 0;var t,r=e.text.length,n=e;while(t=Xe(n)){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}n=e;while(t=Ye(n)){var o=t.find(0,!0);r-=n.text.length-o.from.ch,n=o.to.line,r+=n.text.length-o.to.ch}return r}function it(e){var t=e.display,r=e.doc;t.maxLine=ue(r,r.first),t.maxLineLength=nt(t.maxLine),t.maxLineChanged=!0,r.iter((function(e){var r=nt(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)}))}function ot(e,t,r,n){if(!e)return n(t,r,"ltr");for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<r&&l.to>t||t==r&&l.to==t)&&(n(Math.max(l.from,t),Math.min(l.to,r),1==l.level?"rtl":"ltr"),i=!0)}i||n(t,r,"ltr")}var lt=null;function st(e,t,r){var n;lt=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:lt=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:lt=i)}return null!=n?n:lt}var at=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function r(r){return r<=247?e.charAt(r):1424<=r&&r<=1524?"R":1536<=r&&r<=1785?t.charAt(r-1536):1774<=r&&r<=2220?"r":8192<=r&&r<=8203?"w":8204==r?"b":"L"}var n=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,l=/[Lb1n]/,s=/[1n]/;function a(e,t,r){this.level=e,this.from=t,this.to=r}return function(e,t){var u="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!n.test(e))return!1;for(var c=e.length,h=[],f=0;f<c;++f)h.push(r(e.charCodeAt(f)));for(var d=0,p=u;d<c;++d){var g=h[d];"m"==g?h[d]=p:p=g}for(var v=0,m=u;v<c;++v){var y=h[v];"1"==y&&"r"==m?h[v]="n":o.test(y)&&(m=y,"r"==y&&(h[v]="R"))}for(var b=1,w=h[0];b<c-1;++b){var x=h[b];"+"==x&&"1"==w&&"1"==h[b+1]?h[b]="1":","!=x||w!=h[b+1]||"1"!=w&&"n"!=w||(h[b]=w),w=x}for(var C=0;C<c;++C){var S=h[C];if(","==S)h[C]="N";else if("%"==S){var L=void 0;for(L=C+1;L<c&&"%"==h[L];++L);for(var k=C&&"!"==h[C-1]||L<c&&"1"==h[L]?"1":"N",T=C;T<L;++T)h[T]=k;C=L-1}}for(var M=0,N=u;M<c;++M){var O=h[M];"L"==N&&"1"==O?h[M]="L":o.test(O)&&(N=O)}for(var A=0;A<c;++A)if(i.test(h[A])){var W=void 0;for(W=A+1;W<c&&i.test(h[W]);++W);for(var D="L"==(A?h[A-1]:u),H="L"==(W<c?h[W]:u),E=D==H?D?"L":"R":u,P=A;P<W;++P)h[P]=E;A=W-1}for(var F,I=[],z=0;z<c;)if(l.test(h[z])){var R=z;for(++z;z<c&&l.test(h[z]);++z);I.push(new a(0,R,z))}else{var B=z,G=I.length;for(++z;z<c&&"L"!=h[z];++z);for(var U=B;U<z;)if(s.test(h[U])){B<U&&I.splice(G,0,new a(1,B,U));var V=U;for(++U;U<z&&s.test(h[U]);++U);I.splice(G,0,new a(2,V,U)),B=U}else++U;B<z&&I.splice(G,0,new a(1,B,z))}return 1==I[0].level&&(F=e.match(/^\s+/))&&(I[0].from=F[0].length,I.unshift(new a(0,0,F[0].length))),1==$(I).level&&(F=e.match(/\s+$/))&&($(I).to-=F[0].length,I.push(new a(0,c-F[0].length,c))),"rtl"==t?I.reverse():I}}();function ut(e,t){var r=e.order;return null==r&&(r=e.order=at(e.text,t)),r}function ct(e,t,r){var n=le(e.text,t+r,r);return n<0||n>e.text.length?null:n}function ht(e,t,r){var n=ct(e,t.ch,r);return null==n?null:new me(t.line,n,r<0?"after":"before")}function ft(e,t,r,n,i){if(e){var o=ut(r,t.doc.direction);if(o){var l,s=i<0?$(o):o[0],a=i<0==(1==s.level),u=a?"after":"before";if(s.level>0){var c=en(t,r);l=i<0?r.text.length-1:0;var h=tn(t,c,l).top;l=se((function(e){return tn(t,c,e).top==h}),i<0==(1==s.level)?s.from:s.to-1,l),"before"==u&&(l=ct(r,l,1))}else l=i<0?s.to:s.from;return new me(n,l,u)}}return new me(n,i<0?r.text.length:0,i<0?"before":"after")}function dt(e,t,r,n){var i=ut(t,e.doc.direction);if(!i)return ht(t,r,n);r.ch>=t.text.length?(r.ch=t.text.length,r.sticky="before"):r.ch<=0&&(r.ch=0,r.sticky="after");var o=st(i,r.ch,r.sticky),l=i[o];if("ltr"==e.doc.direction&&l.level%2==0&&(n>0?l.to>r.ch:l.from<r.ch))return ht(t,r,n);var s,a=function(e,r){return ct(t,e instanceof me?e.ch:e,r)},u=function(r){return e.options.lineWrapping?(s=s||en(e,t),Cn(e,t,s,r)):{begin:0,end:t.text.length}},c=u("before"==r.sticky?a(r,-1):r.ch);if("rtl"==e.doc.direction||1==l.level){var h=1==l.level==n<0,f=a(r,h?1:-1);if(null!=f&&(h?f<=l.to&&f<=c.end:f>=l.from&&f>=c.begin)){var d=h?"before":"after";return new me(r.line,f,d)}}var p=function(e,t,n){for(var o=function(e,t){return t?new me(r.line,a(e,1),"before"):new me(r.line,e,"after")};e>=0&&e<i.length;e+=t){var l=i[e],s=t>0==(1!=l.level),u=s?n.begin:a(n.end,-1);if(l.from<=u&&u<l.to)return o(u,s);if(u=s?l.from:a(l.to,-1),n.begin<=u&&u<n.end)return o(u,s)}},g=p(o+n,n,c);if(g)return g;var v=n>0?c.end:a(c.begin,-1);return null==v||n>0&&v==t.text.length||(g=p(n>0?0:i.length-1,n,u(v)),!g)?null:g}var pt=[],gt=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||pt).concat(r)}};function vt(e,t){return e._handlers&&e._handlers[t]||pt}function mt(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=B(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function yt(e,t){var r=vt(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function bt(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),yt(e,r||t.type,e,t),kt(t)||t.codemirrorIgnore}function wt(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==B(r,t[n])&&r.push(t[n])}function xt(e,t){return vt(e,t).length>0}function Ct(e){e.prototype.on=function(e,t){gt(this,e,t)},e.prototype.off=function(e,t){mt(this,e,t)}}function St(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Lt(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function kt(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Tt(e){St(e),Lt(e)}function Mt(e){return e.target||e.srcElement}function Nt(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),y&&e.ctrlKey&&1==t&&(t=3),t}var Ot,At,Wt=function(){if(l&&s<9)return!1;var e=O("div");return"draggable"in e||"dragDrop"in e}();function Dt(e){if(null==Ot){var t=O("span","​");N(e,O("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Ot=t.offsetWidth<=1&&t.offsetHeight>2&&!(l&&s<8))}var r=Ot?O("span","​"):O("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function Ht(e){if(null!=At)return At;var t=N(e,document.createTextNode("AخA")),r=k(t,0,1).getBoundingClientRect(),n=k(t,1,2).getBoundingClientRect();return M(e),!(!r||r.left==r.right)&&(At=n.right-r.right<3)}var Et=3!="\n\nb".split(/\n/).length?function(e){var t=0,r=[],n=e.length;while(t<=n){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(r.push(o.slice(0,l)),t+=l+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},Pt=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(t){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(r){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Ft=function(){var e=O("div");return"oncopy"in e||(e.setAttribute("oncopy","return;"),"function"==typeof e.oncopy)}(),It=null;function zt(e){if(null!=It)return It;var t=N(e,O("span","x")),r=t.getBoundingClientRect(),n=k(t,0,1).getBoundingClientRect();return It=Math.abs(r.left-n.left)>1}var Rt={},Bt={};function Gt(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Rt[e]=t}function Ut(e,t){Bt[e]=t}function Vt(e){if("string"==typeof e&&Bt.hasOwnProperty(e))e=Bt[e];else if(e&&"string"==typeof e.name&&Bt.hasOwnProperty(e.name)){var t=Bt[e.name];"string"==typeof t&&(t={name:t}),e=Q(t,e),e.name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Vt("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Vt("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Kt(e,t){t=Vt(t);var r=Rt[t.name];if(!r)return Kt(e,"text/plain");var n=r(e,t);if(jt.hasOwnProperty(t.name)){var i=jt[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)n[l]=t.modeProps[l];return n}var jt={};function Xt(e,t){var r=jt.hasOwnProperty(e)?jt[e]:jt[e]={};I(t,r)}function Yt(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function _t(e,t){var r;while(e.innerMode){if(r=e.innerMode(t),!r||r.mode==e)break;t=r.state,e=r.mode}return r||{mode:e,state:t}}function $t(e,t,r){return!e.startState||e.startState(t,r)}var qt=function(e,t){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0};function Zt(e,t,r,n){var i=[e.state.modeGen],o={};or(e,t.text,e.doc.mode,r,(function(e,t){return i.push(e,t)}),o,n);for(var l=function(r){var n=e.state.overlays[r],l=1,s=0;or(e,t.text,n.mode,!0,(function(e,t){var r=l;while(s<e){var o=i[l];o>e&&i.splice(l,1,e,i[l+1],o),l+=2,s=Math.min(e,o)}if(t)if(n.opaque)i.splice(r,l-r,e,"overlay "+t),l=r+2;else for(;r<l;r+=2){var a=i[r+1];i[r+1]=(a?a+" ":"")+"overlay "+t}}),o)},s=0;s<e.state.overlays.length;++s)l(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function Jt(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=Qt(e,de(t)),i=Zt(e,t,t.text.length>e.options.maxHighlightLength?Yt(e.doc.mode,n):n);t.stateAfter=n,t.styles=i.styles,i.classes?t.styleClasses=i.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.frontier&&e.doc.frontier++}return t.styles}function Qt(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return!0;var o=lr(e,t,r),l=o>n.first&&ue(n,o-1).stateAfter;return l=l?Yt(n.mode,l):$t(n.mode),n.iter(o,t,(function(r){er(e,r.text,l);var s=o==t-1||o%5==0||o>=i.viewFrom&&o<i.viewTo;r.stateAfter=s?Yt(n.mode,l):null,++o})),r&&(n.frontier=o),l}function er(e,t,r,n){var i=e.doc.mode,o=new qt(t,e.options.tabSize);o.start=o.pos=n||0,""==t&&tr(i,r);while(!o.eol())rr(i,o,r),o.start=o.pos}function tr(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=_t(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function rr(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=_t(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}function nr(e,t,r,n){var i,o=function(e){return{start:h.start,end:h.pos,string:h.current(),type:i||null,state:e?Yt(l.mode,c):c}},l=e.doc,s=l.mode;t=Le(l,t);var a,u=ue(l,t.line),c=Qt(e,t.line,r),h=new qt(u.text,e.options.tabSize);n&&(a=[]);while((n||h.pos<t.ch)&&!h.eol())h.start=h.pos,i=rr(s,h,c),n&&a.push(o(!0));return n?a:o()}function ir(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|s)"+r[2]+"(?:$|s)").test(t[n])||(t[n]+=" "+r[2])}return e}function or(e,t,r,n,i,o,l){var s=r.flattenSpans;null==s&&(s=e.options.flattenSpans);var a,u=0,c=null,h=new qt(t,e.options.tabSize),f=e.options.addModeClass&&[null];""==t&&ir(tr(r,n),o);while(!h.eol()){if(h.pos>e.options.maxHighlightLength?(s=!1,l&&er(e,t,n,h.pos),h.pos=t.length,a=null):a=ir(rr(r,h,n,f),o),f){var d=f[0].name;d&&(a="m-"+(a?d+" "+a:d))}if(!s||c!=a){while(u<h.start)u=Math.min(h.start,u+5e3),i(u,c);c=a}h.start=h.pos}while(u<h.pos){var p=Math.min(h.pos,u+5e3);i(p,c),u=p}}function lr(e,t,r){for(var n,i,o=e.doc,l=r?-1:t-(e.doc.mode.innerMode?1e3:100),s=t;s>l;--s){if(s<=o.first)return o.first;var a=ue(o,s-1);if(a.stateAfter&&(!r||s<=o.frontier))return s;var u=z(a.text,null,e.options.tabSize);(null==i||n>u)&&(i=s-1,n=u)}return i}qt.prototype.eol=function(){return this.pos>=this.string.length},qt.prototype.sol=function(){return this.pos==this.lineStart},qt.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},qt.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},qt.prototype.eat=function(e){var t,r=this.string.charAt(this.pos);if(t="string"==typeof e?r==e:r&&(e.test?e.test(r):e(r)),t)return++this.pos,r},qt.prototype.eatWhile=function(e){var t=this.pos;while(this.eat(e));return this.pos>t},qt.prototype.eatSpace=function(){var e=this,t=this.pos;while(/[\s\u00a0]/.test(this.string.charAt(this.pos)))++e.pos;return this.pos>t},qt.prototype.skipToEnd=function(){this.pos=this.string.length},qt.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},qt.prototype.backUp=function(e){this.pos-=e},qt.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=z(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?z(this.string,this.lineStart,this.tabSize):0)},qt.prototype.indentation=function(){return z(this.string,null,this.tabSize)-(this.lineStart?z(this.string,this.lineStart,this.tabSize):0)},qt.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e},o=this.string.substr(this.pos,e.length);if(i(o)==i(e))return!1!==t&&(this.pos+=e.length),!0},qt.prototype.current=function(){return this.string.slice(this.start,this.pos)},qt.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}};var sr=function(e,t,r){this.text=e,Ge(this,t),this.height=r?r(this):1};function ar(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Be(e),Ge(e,r);var i=n?n(e):1;i!=e.height&&fe(e,i)}function ur(e){e.parent=null,Be(e)}sr.prototype.lineNo=function(){return de(this)},Ct(sr);var cr={},hr={};function fr(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?hr:cr;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function dr(e,t){var r=A("span",null,null,a?"padding-right: .1px":null),n={pre:A("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:(l||a)&&e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,s=void 0;n.pos=0,n.addToken=gr,Ht(e.display.measure)&&(s=ut(o,e.doc.direction))&&(n.addToken=mr(n.addToken,s)),n.map=[];var u=t!=e.display.externalMeasured&&de(o);br(o,n,Jt(e,o,u)),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=E(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=E(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(Dt(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(a){var c=n.content.lastChild;(/\bcm-tab\b/.test(c.className)||c.querySelector&&c.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return yt(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=E(n.pre.className,n.textClass||"")),n}function pr(e){var t=O("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function gr(e,t,r,n,i,o,a){if(t){var u,c=e.splitSpaces?vr(t,e.trailingSpace):t,h=e.cm.state.specialChars,f=!1;if(h.test(t)){u=document.createDocumentFragment();var d=0;while(1){h.lastIndex=d;var p=h.exec(t),g=p?p.index-d:t.length-d;if(g){var v=document.createTextNode(c.slice(d,d+g));l&&s<9?u.appendChild(O("span",[v])):u.appendChild(v),e.map.push(e.pos,e.pos+g,v),e.col+=g,e.pos+=g}if(!p)break;d+=g+1;var m=void 0;if("\t"==p[0]){var y=e.cm.options.tabSize,b=y-e.col%y;m=u.appendChild(O("span",_(b),"cm-tab")),m.setAttribute("role","presentation"),m.setAttribute("cm-text","\t"),e.col+=b}else"\r"==p[0]||"\n"==p[0]?(m=u.appendChild(O("span","\r"==p[0]?"␍":"␤","cm-invalidchar")),m.setAttribute("cm-text",p[0]),e.col+=1):(m=e.cm.options.specialCharPlaceholder(p[0]),m.setAttribute("cm-text",p[0]),l&&s<9?u.appendChild(O("span",[m])):u.appendChild(m),e.col+=1);e.map.push(e.pos,e.pos+1,m),e.pos++}}else e.col+=t.length,u=document.createTextNode(c),e.map.push(e.pos,e.pos+t.length,u),l&&s<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==c.charCodeAt(t.length-1),r||n||i||f||a){var w=r||"";n&&(w+=n),i&&(w+=i);var x=O("span",[u],w,a);return o&&(x.title=o),e.content.appendChild(x)}e.content.appendChild(u)}}function vr(e,t){if(e.length>1&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}function mr(e,t){return function(r,n,i,o,l,s,a){i=i?i+" cm-force-border":"cm-force-border";for(var u=r.pos,c=u+n.length;;){for(var h=void 0,f=0;f<t.length;f++)if(h=t[f],h.to>u&&h.from<=u)break;if(h.to>=c)return e(r,n,i,o,l,s,a);e(r,n.slice(0,h.to-u),i,o,null,s,a),o=null,n=n.slice(h.to-u),u=h.to}}}function yr(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function br(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var l,s,a,u,c,h,f,d=i.length,p=0,g=1,v="",m=0;;){if(m==p){a=u=c=h=s="",f=null,m=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],C=x.marker;"bookmark"==C.type&&x.from==p&&C.widgetNode?y.push(C):x.from<=p&&(null==x.to||x.to>p||C.collapsed&&x.to==p&&x.from==p)?(null!=x.to&&x.to!=p&&m>x.to&&(m=x.to,u=""),C.className&&(a+=" "+C.className),C.css&&(s=(s?s+";":"")+C.css),C.startStyle&&x.from==p&&(c+=" "+C.startStyle),C.endStyle&&x.to==m&&(b||(b=[])).push(C.endStyle,x.to),C.title&&!h&&(h=C.title),C.collapsed&&(!f||Ke(f.marker,C)<0)&&(f=x)):x.from>p&&m>x.from&&(m=x.from)}if(b)for(var S=0;S<b.length;S+=2)b[S+1]==m&&(u+=" "+b[S]);if(!f||f.from==p)for(var L=0;L<y.length;++L)yr(t,0,y[L]);if(f&&(f.from||0)==p){if(yr(t,(null==f.to?d+1:f.to)-p,f.marker,null==f.from),null==f.to)return;f.to==p&&(f=!1)}}if(p>=d)break;var k=Math.min(d,m);while(1){if(v){var T=p+v.length;if(!f){var M=T>k?v.slice(0,k-p):v;t.addToken(t,M,l?l+a:a,c,p+M.length==m?u:"",h,s)}if(T>=k){v=v.slice(k-p),p=k;break}p=T,c=""}v=i.slice(o,o=r[g++]),l=fr(r[g++],t.cm.options)}}else for(var N=1;N<r.length;N+=2)t.addToken(t,i.slice(o,o=r[N]),fr(r[N+1],t.cm.options))}function wr(e,t,r){this.line=t,this.rest=Ze(t),this.size=this.rest?de($(this.rest))-r+1:1,this.node=this.text=null,this.hidden=et(e,t)}function xr(e,t,r){for(var n,i=[],o=t;o<r;o=n){var l=new wr(e.doc,ue(e.doc,o),o);n=o+l.size,i.push(l)}return i}var Cr=null;function Sr(e){Cr?Cr.ops.push(e):e.ownsGroup=Cr={ops:[e],delayedCallbacks:[]}}function Lr(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)while(i.cursorActivityCalled<i.cursorActivityHandlers.length)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}function kr(e,t){var r=e.ownsGroup;if(r)try{Lr(r)}finally{Cr=null,t(r)}}var Tr=null;function Mr(e,t){var r=vt(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);Cr?n=Cr.delayedCallbacks:Tr?n=Tr:(n=Tr=[],setTimeout(Nr,0));for(var o=function(e){n.push((function(){return r[e].apply(null,i)}))},l=0;l<r.length;++l)o(l)}}function Nr(){var e=Tr;Tr=null;for(var t=0;t<e.length;++t)e[t]()}function Or(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?Hr(e,t):"gutter"==o?Pr(e,t,r,n):"class"==o?Er(e,t):"widget"==o&&Fr(e,t,n)}t.changes=null}function Ar(e){return e.node==e.text&&(e.node=O("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),l&&s<8&&(e.node.style.zIndex=2)),e.node}function Wr(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var n=Ar(t);t.background=n.insertBefore(O("div",null,r),n.firstChild),e.display.input.setUneditable(t.background)}}function Dr(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):dr(e,t)}function Hr(e,t){var r=t.text.className,n=Dr(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,Er(e,t)):r&&(t.text.className=r)}function Er(e,t){Wr(e,t),t.line.wrapClass?Ar(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function Pr(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=Ar(t);t.gutterBackground=O("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=Ar(t),s=t.gutter=O("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(e.display.input.setUneditable(s),l.insertBefore(s,t.text),t.line.gutterClass&&(s.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=s.appendChild(O("div",ve(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var a=0;a<e.options.gutters.length;++a){var u=e.options.gutters[a],c=o.hasOwnProperty(u)&&o[u];c&&s.appendChild(O("div",[c],"CodeMirror-gutter-elt","left: "+n.gutterLeft[u]+"px; width: "+n.gutterWidth[u]+"px"))}}}function Fr(e,t,r){t.alignable&&(t.alignable=null);for(var n=t.node.firstChild,i=void 0;n;n=i)i=n.nextSibling,"CodeMirror-linewidget"==n.className&&t.node.removeChild(n);zr(e,t,r)}function Ir(e,t,r,n){var i=Dr(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),Er(e,t),Pr(e,t,r,n),zr(e,t,n),t.node}function zr(e,t,r){if(Rr(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)Rr(e,t.rest[n],t,r,!1)}function Rr(e,t,r,n,i){if(t.widgets)for(var o=Ar(r),l=0,s=t.widgets;l<s.length;++l){var a=s[l],u=O("div",[a.node],"CodeMirror-linewidget");a.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),Br(a,u,r,n),e.display.input.setUneditable(u),i&&a.above?o.insertBefore(u,r.gutter||r.text):o.appendChild(u),Mr(a,"redraw")}}function Br(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function Gr(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!W(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),N(t.display.measure,O("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function Ur(e,t){for(var r=Mt(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function Vr(e){return e.lineSpace.offsetTop}function Kr(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function jr(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=N(e.measure,O("pre","x")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function Xr(e){return G-e.display.nativeBarWidth}function Yr(e){return e.display.scroller.clientWidth-Xr(e)-e.display.barWidth}function _r(e){return e.display.scroller.clientHeight-Xr(e)-e.display.barHeight}function $r(e,t,r){var n=e.options.lineWrapping,i=n&&Yr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),s=0;s<l.length-1;s++){var a=l[s],u=l[s+1];Math.abs(a.bottom-u.bottom)>2&&o.push((a.bottom+u.top)/2-r.top)}}o.push(r.bottom-r.top)}}function qr(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(de(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Zr(e,t){t=$e(t);var r=de(t),n=e.display.externalMeasured=new wr(e.doc,t,r);n.lineN=r;var i=n.built=dr(e,n);return n.text=i.pre,N(e.display.lineMeasure,i.pre),n}function Jr(e,t,r,n){return tn(e,en(e,t),r,n)}function Qr(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Wn(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function en(e,t){var r=de(t),n=Qr(e,r);n&&!n.text?n=null:n&&n.changes&&(Or(e,n,r,Tn(e)),e.curOp.forceUpdate=!0),n||(n=Zr(e,t));var i=qr(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function tn(e,t,r,n,i){t.before&&(r=-1);var o,l=r+(n||"");return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||($r(e,t.view,t.rect),t.hasHeights=!0),o=sn(e,t,r,n),o.bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var rn,nn={left:0,right:0,top:0,bottom:0};function on(e,t,r){for(var n,i,o,l,s,a,u=0;u<e.length;u+=3)if(s=e[u],a=e[u+1],t<s?(i=0,o=1,l="left"):t<a?(i=t-s,o=i+1):(u==e.length-3||t==a&&e[u+3]>t)&&(o=a-s,i=o-1,t>=a&&(l="right")),null!=i){if(n=e[u+2],s==a&&r==(n.insertLeft?"left":"right")&&(l=r),"left"==r&&0==i)while(u&&e[u-2]==e[u-3]&&e[u-1].insertLeft)n=e[2+(u-=3)],l="left";if("right"==r&&i==a-s)while(u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft)n=e[(u+=3)+2],l="right";break}return{node:n,start:i,end:o,collapse:l,coverStart:s,coverEnd:a}}function ln(e,t){var r=nn;if("left"==t){for(var n=0;n<e.length;n++)if((r=e[n]).left!=r.right)break}else for(var i=e.length-1;i>=0;i--)if((r=e[i]).left!=r.right)break;return r}function sn(e,t,r,n){var i,o=on(t.map,r,n),a=o.node,u=o.start,c=o.end,h=o.collapse;if(3==a.nodeType){for(var f=0;f<4;f++){while(u&&oe(t.line.text.charAt(o.coverStart+u)))--u;while(o.coverStart+c<o.coverEnd&&oe(t.line.text.charAt(o.coverStart+c)))++c;if(i=l&&s<9&&0==u&&c==o.coverEnd-o.coverStart?a.parentNode.getBoundingClientRect():ln(k(a,u,c).getClientRects(),n),i.left||i.right||0==u)break;c=u,u-=1,h="right"}l&&s<11&&(i=an(e.display.measure,i))}else{var d;u>0&&(h=n="right"),i=e.options.lineWrapping&&(d=a.getClientRects()).length>1?d["right"==n?d.length-1:0]:a.getBoundingClientRect()}if(l&&s<9&&!u&&(!i||!i.left&&!i.right)){var p=a.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+kn(e.display),top:p.top,bottom:p.bottom}:nn}for(var g=i.top-t.rect.top,v=i.bottom-t.rect.top,m=(g+v)/2,y=t.view.measure.heights,b=0;b<y.length-1;b++)if(m<y[b])break;var w=b?y[b-1]:0,x=y[b],C={left:("right"==h?i.right:i.left)-t.rect.left,right:("left"==h?i.left:i.right)-t.rect.left,top:w,bottom:x};return i.left||i.right||(C.bogus=!0),e.options.singleCursorHeightPerLine||(C.rtop=g,C.rbottom=v),C}function an(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!zt(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}function un(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function cn(e){e.display.externalMeasure=null,M(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)un(e.display.view[t])}function hn(e){cn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function fn(){return c&&v?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function dn(){return c&&v?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function pn(e,t,r,n,i){if(!i&&t.widgets)for(var o=0;o<t.widgets.length;++o)if(t.widgets[o].above){var l=Gr(t.widgets[o]);r.top+=l,r.bottom+=l}if("line"==n)return r;n||(n="local");var s=rt(t);if("local"==n?s+=Vr(e.display):s-=e.display.viewOffset,"page"==n||"window"==n){var a=e.display.lineSpace.getBoundingClientRect();s+=a.top+("window"==n?0:dn());var u=a.left+("window"==n?0:fn());r.left+=u,r.right+=u}return r.top+=s,r.bottom+=s,r}function gn(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=fn(),i-=dn();else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:n-l.left,top:i-l.top}}function vn(e,t,r,n,i){return n||(n=ue(e.doc,t.line)),pn(e,n,Jr(e,n,t.ch,i),r)}function mn(e,t,r,n,i,o){function l(t,l){var s=tn(e,i,t,l?"right":"left",o);return l?s.left=s.right:s.right=s.left,pn(e,n,s,r)}n=n||ue(e.doc,t.line),i||(i=en(e,n));var s=ut(n,e.doc.direction),a=t.ch,u=t.sticky;if(a>=n.text.length?(a=n.text.length,u="before"):a<=0&&(a=0,u="after"),!s)return l("before"==u?a-1:a,"before"==u);function c(e,t,r){var n=s[t],i=n.level%2!=0;return l(r?e-1:e,i!=r)}var h=st(s,a,u),f=lt,d=c(a,h,"before"==u);return null!=f&&(d.other=c(a,f,"before"!=u)),d}function yn(e,t){var r=0;t=Le(e.doc,t),e.options.lineWrapping||(r=kn(e.display)*t.ch);var n=ue(e.doc,t.line),i=rt(n)+Vr(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function bn(e,t,r,n,i){var o=me(e,t,r);return o.xRel=i,n&&(o.outside=!0),o}function wn(e,t,r){var n=e.doc;if(r+=e.display.viewOffset,r<0)return bn(n.first,0,null,!0,-1);var i=pe(n,r),o=n.first+n.size-1;if(i>o)return bn(n.first+n.size-1,ue(n,o).text.length,null,!0,1);t<0&&(t=0);for(var l=ue(n,i);;){var s=Sn(e,l,i,t,r),a=Ye(l),u=a&&a.find(0,!0);if(!a||!(s.ch>u.from.ch||s.ch==u.from.ch&&s.xRel>0))return s;i=de(l=u.to.line)}}function xn(e,t,r,n){var i=function(n){return pn(e,t,tn(e,r,n),"line")},o=t.text.length,l=se((function(e){return i(e-1).bottom<=n}),o,0);return o=se((function(e){return i(e).top>n}),l,o),{begin:l,end:o}}function Cn(e,t,r,n){var i=pn(e,t,tn(e,r,n),"line").top;return xn(e,t,r,i)}function Sn(e,t,r,n,i){i-=rt(t);var o,l=0,s=t.text.length,a=en(e,t),u=ut(t,e.doc.direction);if(u){var c;if(e.options.lineWrapping)c=xn(e,t,a,i),l=c.begin,s=c.end;o=new me(r,l);var h,f,d=mn(e,o,"line",t,a).left,p=d<n?1:-1,g=d-n;do{if(h=g,f=o,o=dt(e,t,o,p),null==o||o.ch<l||s<=("before"==o.sticky?o.ch-1:o.ch)){o=f;break}g=mn(e,o,"line",t,a).left-n}while(p<0!=g<0&&Math.abs(g)<=Math.abs(h));if(Math.abs(g)>Math.abs(h)){if(g<0==h<0)throw new Error("Broke out of infinite loop in coordsCharInner");o=f}}else{var v=se((function(r){var o=pn(e,t,tn(e,a,r),"line");return o.top>i?(s=Math.min(r,s),!0):!(o.bottom<=i)&&(o.left>n||!(o.right<n)&&n-o.left<o.right-n)}),l,s);v=le(t.text,v,1),o=new me(r,v,v==s?"before":"after")}var m=mn(e,o,"line",t,a);return(i<m.top||m.bottom<i)&&(o.outside=!0),o.xRel=n<m.left?-1:n>m.right?1:0,o}function Ln(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==rn){rn=O("pre");for(var t=0;t<49;++t)rn.appendChild(document.createTextNode("x")),rn.appendChild(O("br"));rn.appendChild(document.createTextNode("x"))}N(e.measure,rn);var r=rn.offsetHeight/50;return r>3&&(e.cachedTextHeight=r),M(e.measure),r||1}function kn(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=O("span","xxxxxxxxxx"),r=O("pre",[t]);N(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function Tn(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l)r[e.options.gutters[l]]=o.offsetLeft+o.clientLeft+i,n[e.options.gutters[l]]=o.clientWidth;return{fixedPos:Mn(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function Mn(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Nn(e){var t=Ln(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/kn(e.display)-3);return function(i){if(et(e.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function On(e){var t=e.doc,r=Nn(e);t.iter((function(e){var t=r(e);t!=e.height&&fe(e,t)}))}function An(e,t,r,n){var i=e.display;if(!r&&"true"==Mt(t).getAttribute("cm-not-content"))return null;var o,l,s=i.lineSpace.getBoundingClientRect();try{o=t.clientX-s.left,l=t.clientY-s.top}catch(t){return null}var a,u=wn(e,o,l);if(n&&1==u.xRel&&(a=ue(e.doc,u.line).text).length==u.ch){var c=z(a,a.length,e.options.tabSize)-a.length;u=me(u.line,Math.max(0,Math.round((o-jr(e.display).left)/kn(e.display))-c))}return u}function Wn(e,t){if(t>=e.display.viewTo)return null;if(t-=e.display.viewFrom,t<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if(t-=r[n].size,t<0)return n}function Dn(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Hn(e,t){for(var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),l=0;l<r.sel.ranges.length;l++)if(!1!==t||l!=r.sel.primIndex){var s=r.sel.ranges[l];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var a=s.empty();(a||e.options.showCursorWhenSelecting)&&En(e,s.head,i),a||Pn(e,s,o)}}return n}function En(e,t,r){var n=mn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(O("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",n.other){var o=r.appendChild(O("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=n.other.left+"px",o.style.top=n.other.top+"px",o.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function Pn(e,t,r){var n=e.display,i=e.doc,o=document.createDocumentFragment(),l=jr(e.display),s=l.left,a=Math.max(n.sizerWidth,Yr(e)-n.sizer.offsetLeft)-l.right;function u(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),o.appendChild(O("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?a-e:r)+"px;\n                             height: "+(n-t)+"px"))}function c(t,r,n){var o,l,c=ue(i,t),h=c.text.length;function f(r,n){return vn(e,me(t,r),"div",c,n)}return ot(ut(c,i.direction),r||0,null==n?h:n,(function(e,t,i){var c,d,p,g=f(e,"left");if(e==t)c=g,d=p=g.left;else{if(c=f(t-1,"right"),"rtl"==i){var v=g;g=c,c=v}d=g.left,p=c.right}null==r&&0==e&&(d=s),c.top-g.top>3&&(u(d,g.top,null,g.bottom),d=s,g.bottom<c.top&&u(d,g.bottom,null,c.top)),null==n&&t==h&&(p=a),(!o||g.top<o.top||g.top==o.top&&g.left<o.left)&&(o=g),(!l||c.bottom>l.bottom||c.bottom==l.bottom&&c.right>l.right)&&(l=c),d<s+1&&(d=s),u(d,c.top,p-d,c.bottom)})),{start:o,end:l}}var h=t.from(),f=t.to();if(h.line==f.line)c(h.line,h.ch,f.ch);else{var d=ue(i,h.line),p=ue(i,f.line),g=$e(d)==$e(p),v=c(h.line,h.ch,g?d.text.length+1:null).end,m=c(f.line,g?0:null,f.ch).start;g&&(v.top<m.top-2?(u(v.right,v.top,null,v.bottom),u(s,m.top,m.left,m.bottom)):u(v.right,v.top,m.left-v.right,v.bottom)),v.bottom<m.top&&u(s,v.bottom,null,m.top)}r.appendChild(o)}function Fn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){return t.cursorDiv.style.visibility=(r=!r)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function In(e){e.state.focused||(e.display.input.focus(),Rn(e))}function zn(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,Bn(e))}),100)}function Rn(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(yt(e,"focus",e,t),e.state.focused=!0,H(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),a&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),Fn(e))}function Bn(e,t){e.state.delayingBlurEvent||(e.state.focused&&(yt(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function Gn(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=0;n<t.view.length;n++){var i=t.view[n],o=void 0;if(!i.hidden){if(l&&s<8){var a=i.node.offsetTop+i.node.offsetHeight;o=a-r,r=a}else{var u=i.node.getBoundingClientRect();o=u.bottom-u.top}var c=i.line.height-o;if(o<2&&(o=Ln(t)),(c>.001||c<-.001)&&(fe(i.line,o),Un(i.line),i.rest))for(var h=0;h<i.rest.length;h++)Un(i.rest[h])}}}function Un(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t)e.widgets[t].height=e.widgets[t].node.parentNode.offsetHeight}function Vn(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-Vr(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=pe(t,n),l=pe(t,i);if(r&&r.ensure){var s=r.ensure.from.line,a=r.ensure.to.line;s<o?(o=s,l=pe(t,rt(ue(t,s))+e.wrapper.clientHeight)):Math.min(a,t.lastLine())>=l&&(o=pe(t,rt(ue(t,a))-e.wrapper.clientHeight),l=a)}return{from:o,to:Math.max(l,o+1)}}function Kn(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=Mn(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",l=0;l<r.length;l++)if(!r[l].hidden){e.options.fixedGutter&&(r[l].gutter&&(r[l].gutter.style.left=o),r[l].gutterBackground&&(r[l].gutterBackground.style.left=o));var s=r[l].alignable;if(s)for(var a=0;a<s.length;a++)s[a].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function jn(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=ve(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(O("div",[O("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-l)+1,n.lineNumWidth=n.lineNumInnerWidth+l,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",Gi(e),!0}return!1}function Xn(e,t){if(!bt(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null;if(t.top+n.top<0?i=!0:t.bottom+n.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!p){var o=O("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-Vr(e.display))+"px;\n                         height: "+(t.bottom-t.top+Xr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}function Yn(e,t,r,n){var i;null==n&&(n=0);for(var o=0;o<5;o++){var l=!1,s=mn(e,t),a=r&&r!=t?mn(e,r):s;i={left:Math.min(s.left,a.left),top:Math.min(s.top,a.top)-n,right:Math.max(s.left,a.left),bottom:Math.max(s.bottom,a.bottom)+n};var u=$n(e,i),c=e.doc.scrollTop,h=e.doc.scrollLeft;if(null!=u.scrollTop&&(ri(e,u.scrollTop),Math.abs(e.doc.scrollTop-c)>1&&(l=!0)),null!=u.scrollLeft&&(ii(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-h)>1&&(l=!0)),!l)break}return i}function _n(e,t){var r=$n(e,t);null!=r.scrollTop&&ri(e,r.scrollTop),null!=r.scrollLeft&&ii(e,r.scrollLeft)}function $n(e,t){var r=e.display,n=Ln(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=_r(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s=e.doc.height+Kr(r),a=t.top<n,u=t.bottom>s-n;if(t.top<i)l.scrollTop=a?0:t.top;else if(t.bottom>i+o){var c=Math.min(t.top,(u?s:t.bottom)-o);c!=i&&(l.scrollTop=c)}var h=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft,f=Yr(e)-(e.options.fixedGutter?r.gutters.offsetWidth:0),d=t.right-t.left>f;return d&&(t.right=t.left+f),t.left<10?l.scrollLeft=0:t.left<h?l.scrollLeft=Math.max(0,t.left-(d?0:10)):t.right>f+h-3&&(l.scrollLeft=t.right+(d?0:10)-f),l}function qn(e,t){null!=t&&(ei(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Zn(e){ei(e);var t=e.getCursor(),r=t,n=t;e.options.lineWrapping||(r=t.ch?me(t.line,t.ch-1):t,n=me(t.line,t.ch+1)),e.curOp.scrollToPos={from:r,to:n,margin:e.options.cursorScrollMargin}}function Jn(e,t,r){null==t&&null==r||ei(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function Qn(e,t){ei(e),e.curOp.scrollToPos=t}function ei(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var r=yn(e,t.from),n=yn(e,t.to);ti(e,r,n,t.margin)}}function ti(e,t,r,n){var i=$n(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});Jn(e,i.scrollLeft,i.scrollTop)}function ri(e,t){Math.abs(e.doc.scrollTop-t)<2||(r||Ri(e,{top:t}),ni(e,t,!0),r&&Ri(e),Wi(e,100))}function ni(e,t,r){t=Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function ii(e,t,r,n){t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,Kn(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function oi(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+Kr(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+Xr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var li=function(e,t,r){this.cm=r;var n=this.vert=O("div",[O("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=O("div",[O("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");e(n),e(i),gt(n,"scroll",(function(){n.clientHeight&&t(n.scrollTop,"vertical")})),gt(i,"scroll",(function(){i.clientWidth&&t(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,l&&s<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};li.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},li.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},li.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},li.prototype.zeroWidthHack=function(){var e=y&&!d?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new R,this.disableVert=new R},li.prototype.enableZeroWidthBar=function(e,t,r){function n(){var i=e.getBoundingClientRect(),o="vert"==r?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1);o!=e?e.style.pointerEvents="none":t.set(1e3,n)}e.style.pointerEvents="auto",t.set(1e3,n)},li.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var si=function(){};function ai(e,t){t||(t=oi(e));var r=e.display.barWidth,n=e.display.barHeight;ui(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&Gn(e),ui(e,oi(e)),r=e.display.barWidth,n=e.display.barHeight}function ui(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}si.prototype.update=function(){return{bottom:0,right:0}},si.prototype.setScrollLeft=function(){},si.prototype.setScrollTop=function(){},si.prototype.clear=function(){};var ci={native:li,null:si};function hi(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&T(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new ci[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),gt(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,r){"horizontal"==r?ii(e,t):ri(e,t)}),e),e.display.scrollbars.addClass&&H(e.display.wrapper,e.display.scrollbars.addClass)}var fi=0;function di(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:null,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++fi},Sr(e.curOp)}function pi(e){var t=e.curOp;kr(t,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;gi(e)}))}function gi(e){for(var t=e.ops,r=0;r<t.length;r++)vi(t[r]);for(var n=0;n<t.length;n++)mi(t[n]);for(var i=0;i<t.length;i++)yi(t[i]);for(var o=0;o<t.length;o++)bi(t[o]);for(var l=0;l<t.length;l++)wi(t[l])}function vi(e){var t=e.cm,r=t.display;Ei(t),e.updateMaxLine&&it(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<r.viewFrom||e.scrollToPos.to.line>=r.viewTo)||r.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Hi(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function mi(e){e.updatedDisplay=e.mustUpdate&&Ii(e.cm,e.update)}function yi(e){var t=e.cm,r=t.display;e.updatedDisplay&&Gn(t),e.barMeasure=oi(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Jr(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+Xr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Yr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection(e.focus))}function bi(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&ii(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==D()&&(!document.hasFocus||document.hasFocus());e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&ai(t,e.barMeasure),e.updatedDisplay&&Ui(t,e.barMeasure),e.selectionChanged&&Fn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&In(e.cm)}function wi(e){var t=e.cm,r=t.display,n=t.doc;if(e.updatedDisplay&&zi(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&ni(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&ii(t,e.scrollLeft,!0,!0),e.scrollToPos){var i=Yn(t,Le(n,e.scrollToPos.from),Le(n,e.scrollToPos.to),e.scrollToPos.margin);Xn(t,i)}var o=e.maybeHiddenMarkers,l=e.maybeUnhiddenMarkers;if(o)for(var s=0;s<o.length;++s)o[s].lines.length||yt(o[s],"hide");if(l)for(var a=0;a<l.length;++a)l[a].lines.length&&yt(l[a],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&yt(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function xi(e,t){if(e.curOp)return t();di(e);try{return t()}finally{pi(e)}}function Ci(e,t){return function(){if(e.curOp)return t.apply(e,arguments);di(e);try{return t.apply(e,arguments)}finally{pi(e)}}}function Si(e){return function(){if(this.curOp)return e.apply(this,arguments);di(this);try{return e.apply(this,arguments)}finally{pi(this)}}}function Li(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);di(t);try{return e.apply(this,arguments)}finally{pi(t)}}}function ki(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Ne&&Je(e.doc,t)<i.viewTo&&Mi(e);else if(r<=i.viewFrom)Ne&&Qe(e.doc,r+n)>i.viewFrom?Mi(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)Mi(e);else if(t<=i.viewFrom){var o=Ni(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):Mi(e)}else if(r>=i.viewTo){var l=Ni(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):Mi(e)}else{var s=Ni(e,t,t,-1),a=Ni(e,r,r+n,1);s&&a?(i.view=i.view.slice(0,s.index).concat(xr(e,s.lineN,a.lineN)).concat(i.view.slice(a.index)),i.viewTo+=n):Mi(e)}var u=i.externalMeasured;u&&(r<u.lineN?u.lineN+=n:t<u.lineN+u.size&&(i.externalMeasured=null))}function Ti(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[Wn(e,t)];if(null!=o.node){var l=o.changes||(o.changes=[]);-1==B(l,r)&&l.push(r)}}}function Mi(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function Ni(e,t,r,n){var i,o=Wn(e,t),l=e.display.view;if(!Ne||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var s=e.display.viewFrom,a=0;a<o;a++)s+=l[a].size;if(s!=t){if(n>0){if(o==l.length-1)return null;i=s+l[o].size-t,o++}else i=s-t;t+=i,r+=i}while(Je(e.doc,r)!=r){if(o==(n<0?0:l.length-1))return null;r+=n*l[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function Oi(e,t,r){var n=e.display,i=n.view;0==i.length||t>=n.viewTo||r<=n.viewFrom?(n.view=xr(e,t,r),n.viewFrom=t):(n.viewFrom>t?n.view=xr(e,t,n.viewFrom).concat(n.view):n.viewFrom<t&&(n.view=n.view.slice(Wn(e,t))),n.viewFrom=t,n.viewTo<r?n.view=n.view.concat(xr(e,n.viewTo,r)):n.viewTo>r&&(n.view=n.view.slice(0,Wn(e,r)))),n.viewTo=r}function Ai(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function Wi(e,t){e.doc.mode.startState&&e.doc.frontier<e.display.viewTo&&e.state.highlight.set(t,F(Di,e))}function Di(e){var t=e.doc;if(t.frontier<t.first&&(t.frontier=t.first),!(t.frontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=Yt(t.mode,Qt(e,t.frontier)),i=[];t.iter(t.frontier,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(t.frontier>=e.display.viewFrom){var l=o.styles,s=o.text.length>e.options.maxHighlightLength,a=Zt(e,o,s?Yt(t.mode,n):n,!0);o.styles=a.styles;var u=o.styleClasses,c=a.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var h=!l||l.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),f=0;!h&&f<l.length;++f)h=l[f]!=o.styles[f];h&&i.push(t.frontier),o.stateAfter=s?n:Yt(t.mode,n)}else o.text.length<=e.options.maxHighlightLength&&er(e,o.text,n),o.stateAfter=t.frontier%5==0?Yt(t.mode,n):null;if(++t.frontier,+new Date>r)return Wi(e,e.options.workDelay),!0})),i.length&&xi(e,(function(){for(var t=0;t<i.length;t++)Ti(e,i[t],"text")}))}}var Hi=function(e,t,r){var n=e.display;this.viewport=t,this.visible=Vn(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Yr(e),this.force=r,this.dims=Tn(e),this.events=[]};function Ei(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Xr(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Xr(e)+"px",t.scrollbarsClipped=!0)}function Pi(e){if(e.hasFocus())return null;var t=D();if(!t||!W(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=window.getSelection();n.anchorNode&&n.extend&&W(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}function Fi(e){if(e&&e.activeElt&&e.activeElt!=D()&&(e.activeElt.focus(),e.anchorNode&&W(document.body,e.anchorNode)&&W(document.body,e.focusNode))){var t=window.getSelection(),r=document.createRange();r.setEnd(e.anchorNode,e.anchorOffset),r.collapse(!1),t.removeAllRanges(),t.addRange(r),t.extend(e.focusNode,e.focusOffset)}}function Ii(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return Mi(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==Ai(e))return!1;jn(e)&&(Mi(e),t.dims=Tn(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>l&&r.viewTo-l<20&&(l=Math.min(i,r.viewTo)),Ne&&(o=Je(e.doc,o),l=Qe(e.doc,l));var s=o!=r.viewFrom||l!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;Oi(e,o,l),r.viewOffset=rt(ue(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var a=Ai(e);if(!s&&0==a&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var u=Pi(e);return a>4&&(r.lineDiv.style.display="none"),Bi(e,r.updateLineNumbers,t.dims),a>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,Fi(u),M(r.cursorDiv),M(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,s&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,Wi(e,400)),r.updateLineNumbers=null,!0}function zi(e,t){for(var r=t.viewport,n=!0;;n=!1){if((!n||!e.options.lineWrapping||t.oldDisplayWidth==Yr(e))&&(r&&null!=r.top&&(r={top:Math.min(e.doc.height+Kr(e.display)-_r(e),r.top)}),t.visible=Vn(e.display,e.doc,r),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo))break;if(!Ii(e,t))break;Gn(e);var i=oi(e);Dn(e),ai(e,i),Ui(e,i)}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Ri(e,t){var r=new Hi(e,t);if(Ii(e,r)){Gn(e),zi(e,r);var n=oi(e);Dn(e),ai(e,n),Ui(e,n),r.finish()}}function Bi(e,t,r){var n=e.display,i=e.options.lineNumbers,o=n.lineDiv,l=o.firstChild;function s(t){var r=t.nextSibling;return a&&y&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),r}for(var u=n.view,c=n.viewFrom,h=0;h<u.length;h++){var f=u[h];if(f.hidden);else if(f.node&&f.node.parentNode==o){while(l!=f.node)l=s(l);var d=i&&null!=t&&t<=c&&f.lineNumber;f.changes&&(B(f.changes,"gutter")>-1&&(d=!1),Or(e,f,c,r)),d&&(M(f.lineNumber),f.lineNumber.appendChild(document.createTextNode(ve(e.options,c)))),l=f.node.nextSibling}else{var p=Ir(e,f,c,r);o.insertBefore(p,l)}c+=f.size}while(l)l=s(l)}function Gi(e){var t=e.display.gutters.offsetWidth;e.display.sizer.style.marginLeft=t+"px"}function Ui(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Xr(e)+"px"}function Vi(e){var t=e.display.gutters,r=e.options.gutters;M(t);for(var n=0;n<r.length;++n){var i=r[n],o=t.appendChild(O("div",null,"CodeMirror-gutter "+i));"CodeMirror-linenumbers"==i&&(e.display.lineGutter=o,o.style.width=(e.display.lineNumWidth||1)+"px")}t.style.display=n?"":"none",Gi(e)}function Ki(e){var t=B(e.gutters,"CodeMirror-linenumbers");-1==t&&e.lineNumbers?e.gutters=e.gutters.concat(["CodeMirror-linenumbers"]):t>-1&&!e.lineNumbers&&(e.gutters=e.gutters.slice(0),e.gutters.splice(t,1))}Hi.prototype.signal=function(e,t){xt(e,t)&&this.events.push(arguments)},Hi.prototype.finish=function(){for(var e=this,t=0;t<this.events.length;t++)yt.apply(null,e.events[t])};var ji=0,Xi=null;function Yi(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function _i(e){var t=Yi(e);return t.x*=Xi,t.y*=Xi,t}function $i(e,t){var n=Yi(t),i=n.x,o=n.y,l=e.display,s=l.scroller,u=s.scrollWidth>s.clientWidth,c=s.scrollHeight>s.clientHeight;if(i&&u||o&&c){if(o&&y&&a)e:for(var f=t.target,d=l.view;f!=s;f=f.parentNode)for(var p=0;p<d.length;p++)if(d[p].node==f){e.display.currentWheelTarget=f;break e}if(i&&!r&&!h&&null!=Xi)return o&&c&&ri(e,Math.max(0,s.scrollTop+o*Xi)),ii(e,Math.max(0,s.scrollLeft+i*Xi)),(!o||o&&c)&&St(t),void(l.wheelStartX=null);if(o&&null!=Xi){var g=o*Xi,v=e.doc.scrollTop,m=v+l.wrapper.clientHeight;g<0?v=Math.max(0,v+g-50):m=Math.min(e.doc.height,m+g+50),Ri(e,{top:v,bottom:m})}ji<20&&(null==l.wheelStartX?(l.wheelStartX=s.scrollLeft,l.wheelStartY=s.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout((function(){if(null!=l.wheelStartX){var e=s.scrollLeft-l.wheelStartX,t=s.scrollTop-l.wheelStartY,r=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,r&&(Xi=(Xi*ji+r)/(ji+1),++ji)}}),200)):(l.wheelDX+=i,l.wheelDY+=o))}}l?Xi=-.53:r?Xi=15:c?Xi=-.7:f&&(Xi=-1/3);var qi=function(e,t){this.ranges=e,this.primIndex=t};qi.prototype.primary=function(){return this.ranges[this.primIndex]},qi.prototype.equals=function(e){var t=this;if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var r=0;r<this.ranges.length;r++){var n=t.ranges[r],i=e.ranges[r];if(!be(n.anchor,i.anchor)||!be(n.head,i.head))return!1}return!0},qi.prototype.deepCopy=function(){for(var e=this,t=[],r=0;r<this.ranges.length;r++)t[r]=new Zi(we(e.ranges[r].anchor),we(e.ranges[r].head));return new qi(t,this.primIndex)},qi.prototype.somethingSelected=function(){for(var e=this,t=0;t<this.ranges.length;t++)if(!e.ranges[t].empty())return!0;return!1},qi.prototype.contains=function(e,t){var r=this;t||(t=e);for(var n=0;n<this.ranges.length;n++){var i=r.ranges[n];if(ye(t,i.from())>=0&&ye(e,i.to())<=0)return n}return-1};var Zi=function(e,t){this.anchor=e,this.head=t};function Ji(e,t){var r=e[t];e.sort((function(e,t){return ye(e.from(),t.from())})),t=B(e,r);for(var n=1;n<e.length;n++){var i=e[n],o=e[n-1];if(ye(o.to(),i.from())>=0){var l=Ce(o.from(),i.from()),s=xe(o.to(),i.to()),a=o.empty()?i.from()==i.head:o.from()==o.head;n<=t&&--t,e.splice(--n,2,new Zi(a?s:l,a?l:s))}}return new qi(e,t)}function Qi(e,t){return new qi([new Zi(e,t||e)],0)}function eo(e){return e.text?me(e.from.line+e.text.length-1,$(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function to(e,t){if(ye(e,t.from)<0)return e;if(ye(e,t.to)<=0)return eo(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=eo(t).ch-t.to.ch),me(r,n)}function ro(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new Zi(to(i.anchor,t),to(i.head,t)))}return Ji(r,e.sel.primIndex)}function no(e,t,r){return e.line==t.line?me(r.line,e.ch-t.ch+r.ch):me(r.line+(e.line-t.line),e.ch)}function io(e,t,r){for(var n=[],i=me(e.first,0),o=i,l=0;l<t.length;l++){var s=t[l],a=no(s.from,i,o),u=no(eo(s),i,o);if(i=s.to,o=u,"around"==r){var c=e.sel.ranges[l],h=ye(c.head,c.anchor)<0;n[l]=new Zi(h?u:a,h?a:u)}else n[l]=new Zi(a,a)}return new qi(n,e.sel.primIndex)}function oo(e){e.doc.mode=Kt(e.options,e.doc.modeOption),lo(e)}function lo(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.frontier=e.doc.first,Wi(e,100),e.state.modeGen++,e.curOp&&ki(e)}function so(e,t){return 0==t.from.ch&&0==t.to.ch&&""==$(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function ao(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){ar(e,r,i,n),Mr(e,"change",e,t)}function l(e,t){for(var r=[],o=e;o<t;++o)r.push(new sr(u[o],i(o),n));return r}var s=t.from,a=t.to,u=t.text,c=ue(e,s.line),h=ue(e,a.line),f=$(u),d=i(u.length-1),p=a.line-s.line;if(t.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(so(e,t)){var g=l(0,u.length-1);o(h,h.text,d),p&&e.remove(s.line,p),g.length&&e.insert(s.line,g)}else if(c==h)if(1==u.length)o(c,c.text.slice(0,s.ch)+f+c.text.slice(a.ch),d);else{var v=l(1,u.length-1);v.push(new sr(f+c.text.slice(a.ch),d,n)),o(c,c.text.slice(0,s.ch)+u[0],i(0)),e.insert(s.line+1,v)}else if(1==u.length)o(c,c.text.slice(0,s.ch)+u[0]+h.text.slice(a.ch),i(0)),e.remove(s.line+1,p);else{o(c,c.text.slice(0,s.ch)+u[0],i(0)),o(h,f+h.text.slice(a.ch),d);var m=l(1,u.length-1);p>1&&e.remove(s.line+1,p-1),e.insert(s.line+1,m)}Mr(e,"change",e,t)}function uo(e,t,r){function n(e,i,o){if(e.linked)for(var l=0;l<e.linked.length;++l){var s=e.linked[l];if(s.doc!=i){var a=o&&s.sharedHist;r&&!a||(t(s.doc,a),n(s.doc,e,a))}}}n(e,null,!0)}function co(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,On(e),oo(e),ho(e),e.options.lineWrapping||it(e),e.options.mode=t.modeOption,ki(e)}function ho(e){("rtl"==e.doc.direction?H:T)(e.display.lineDiv,"CodeMirror-rtl")}function fo(e){xi(e,(function(){ho(e),ki(e)}))}function po(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function go(e,t){var r={from:we(t.from),to:eo(t),text:ce(e,t.from,t.to)};return Co(e,r,t.from.line,t.to.line+1),uo(e,(function(e){return Co(e,r,t.from.line,t.to.line+1)}),!0),r}function vo(e){while(e.length){var t=$(e);if(!t.ranges)break;e.pop()}}function mo(e,t){return t?(vo(e.done),$(e.done)):e.done.length&&!$(e.done).ranges?$(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),$(e.done)):void 0}function yo(e,t,r,n){var i=e.history;i.undone.length=0;var o,l,s=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&e.cm&&i.lastModTime>s-e.cm.options.historyEventDelay||"*"==t.origin.charAt(0)))&&(o=mo(i,i.lastOp==n)))l=$(o.changes),0==ye(t.from,t.to)&&0==ye(t.from,l.to)?l.to=eo(t):o.changes.push(go(e,t));else{var a=$(i.done);a&&a.ranges||xo(e.sel,i.done),o={changes:[go(e,t)],generation:i.generation},i.done.push(o);while(i.done.length>i.undoDepth)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=s,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,l||yt(e,"historyAdded")}function bo(e,t,r,n){var i=t.charAt(0);return"*"==i||"+"==i&&r.ranges.length==n.ranges.length&&r.somethingSelected()==n.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function wo(e,t,r,n){var i=e.history,o=n&&n.origin;r==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||bo(e,o,$(i.done),t))?i.done[i.done.length-1]=t:xo(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=r,n&&!1!==n.clearRedo&&vo(i.undone)}function xo(e,t){var r=$(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Co(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),(function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o}))}function So(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function Lo(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(So(r[i]));return n}function ko(e,t){var r=Lo(e,t),n=Ie(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],l=n[i];if(o&&l)e:for(var s=0;s<l.length;++s){for(var a=l[s],u=0;u<o.length;++u)if(o[u].marker==a.marker)continue e;o.push(a)}else l&&(r[i]=l)}return r}function To(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?qi.prototype.deepCopy.call(o):o);else{var l=o.changes,s=[];n.push({changes:s});for(var a=0;a<l.length;++a){var u=l[a],c=void 0;if(s.push({from:u.from,to:u.to,text:u.text}),t)for(var h in u)(c=h.match(/^spans_(\d+)$/))&&B(t,Number(c[1]))>-1&&($(s)[h]=u[h],delete u[h])}}}return n}function Mo(e,t,r,n){if(e.cm&&e.cm.display.shift||e.extend){var i=t.anchor;if(n){var o=ye(r,i)<0;o!=ye(n,i)<0?(i=r,r=n):o!=ye(r,n)<0&&(r=n)}return new Zi(i,r)}return new Zi(n||r,r)}function No(e,t,r,n){Eo(e,new qi([Mo(e,e.sel.primary(),t,r)],0),n)}function Oo(e,t,r){for(var n=[],i=0;i<e.sel.ranges.length;i++)n[i]=Mo(e,e.sel.ranges[i],t[i],null);var o=Ji(n,e.sel.primIndex);Eo(e,o,r)}function Ao(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,Eo(e,Ji(i,e.sel.primIndex),n)}function Wo(e,t,r,n){Eo(e,Qi(t,r),n)}function Do(e,t,r){var n={ranges:t.ranges,update:function(t){var r=this;this.ranges=[];for(var n=0;n<t.length;n++)r.ranges[n]=new Zi(Le(e,t[n].anchor),Le(e,t[n].head))},origin:r&&r.origin};return yt(e,"beforeSelectionChange",e,n),e.cm&&yt(e.cm,"beforeSelectionChange",e.cm,n),n.ranges!=t.ranges?Ji(n.ranges,n.ranges.length-1):t}function Ho(e,t,r){var n=e.history.done,i=$(n);i&&i.ranges?(n[n.length-1]=t,Po(e,t,r)):Eo(e,t,r)}function Eo(e,t,r){Po(e,t,r),wo(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function Po(e,t,r){(xt(e,"beforeSelectionChange")||e.cm&&xt(e.cm,"beforeSelectionChange"))&&(t=Do(e,t,r));var n=r&&r.bias||(ye(t.primary().head,e.sel.primary().head)<0?-1:1);Fo(e,zo(e,t,n,!0)),r&&!1===r.scroll||!e.cm||Zn(e.cm)}function Fo(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=e.cm.curOp.selectionChanged=!0,wt(e.cm)),Mr(e,"cursorActivity",e))}function Io(e){Fo(e,zo(e,e.sel,null,!1))}function zo(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],a=Bo(e,l.anchor,s&&s.anchor,r,n),u=Bo(e,l.head,s&&s.head,r,n);(i||a!=l.anchor||u!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new Zi(a,u))}return i?Ji(i,t.primIndex):t}function Ro(e,t,r,n,i){var o=ue(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var s=o.markedSpans[l],a=s.marker;if((null==s.from||(a.inclusiveLeft?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(a.inclusiveRight?s.to>=t.ch:s.to>t.ch))){if(i&&(yt(a,"beforeCursorEnter"),a.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!a.atomic)continue;if(r){var u=a.find(n<0?1:-1),c=void 0;if((n<0?a.inclusiveRight:a.inclusiveLeft)&&(u=Go(e,u,-n,u&&u.line==t.line?o:null)),u&&u.line==t.line&&(c=ye(u,r))&&(n<0?c<0:c>0))return Ro(e,u,t,n,i)}var h=a.find(n<0?-1:1);return(n<0?a.inclusiveLeft:a.inclusiveRight)&&(h=Go(e,h,n,h.line==t.line?o:null)),h?Ro(e,h,t,n,i):null}}return t}function Bo(e,t,r,n,i){var o=n||1,l=Ro(e,t,r,o,i)||!i&&Ro(e,t,r,o,!0)||Ro(e,t,r,-o,i)||!i&&Ro(e,t,r,-o,!0);return l||(e.cantEdit=!0,me(e.first,0))}function Go(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?Le(e,me(t.line-1)):null:r>0&&t.ch==(n||ue(e,t.line)).text.length?t.line<e.first+e.size-1?me(t.line+1,0):null:new me(t.line,t.ch+r)}function Uo(e){e.setSelection(me(e.firstLine(),0),me(e.lastLine()),V)}function Vo(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return r&&(n.update=function(t,r,i,o){t&&(n.from=Le(e,t)),r&&(n.to=Le(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),yt(e,"beforeChange",e,n),e.cm&&yt(e.cm,"beforeChange",e.cm,n),n.canceled?null:{from:n.from,to:n.to,text:n.text,origin:n.origin}}function Ko(e,t,r){if(e.cm){if(!e.cm.curOp)return Ci(e.cm,Ko)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(xt(e,"beforeChange")||e.cm&&xt(e.cm,"beforeChange"))||(t=Vo(e,t,!0),t)){var n=Me&&!r&&Re(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)jo(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text});else jo(e,t)}}function jo(e,t){if(1!=t.text.length||""!=t.text[0]||0!=ye(t.from,t.to)){var r=ro(e,t);yo(e,t,r,e.cm?e.cm.curOp.id:NaN),_o(e,t,r,Ie(e,t));var n=[];uo(e,(function(e,r){r||-1!=B(n,e.history)||(Qo(e.history,t),n.push(e.history)),_o(e,t,null,Ie(e,t))}))}}function Xo(e,t,r){if(!e.cm||!e.cm.state.suppressEdits||r){for(var n,i=e.history,o=e.sel,l="undo"==t?i.done:i.undone,s="undo"==t?i.undone:i.done,a=0;a<l.length;a++)if(n=l[a],r?n.ranges&&!n.equals(e.sel):!n.ranges)break;if(a!=l.length){for(i.lastOrigin=i.lastSelOrigin=null;;){if(n=l.pop(),!n.ranges)break;if(xo(n,s),r&&!n.equals(e.sel))return void Eo(e,n,{clearRedo:!1});o=n}var u=[];xo(o,s),s.push({changes:u,generation:i.generation}),i.generation=n.generation||++i.maxGeneration;for(var c=xt(e,"beforeChange")||e.cm&&xt(e.cm,"beforeChange"),h=function(r){var i=n.changes[r];if(i.origin=t,c&&!Vo(e,i,!1))return l.length=0,{};u.push(go(e,i));var o=r?ro(e,i):$(l);_o(e,i,o,ko(e,i)),!r&&e.cm&&e.cm.scrollIntoView({from:i.from,to:eo(i)});var s=[];uo(e,(function(e,t){t||-1!=B(s,e.history)||(Qo(e.history,i),s.push(e.history)),_o(e,i,null,ko(e,i))}))},f=n.changes.length-1;f>=0;--f){var d=h(f);if(d)return d.v}}}}function Yo(e,t){if(0!=t&&(e.first+=t,e.sel=new qi(q(e.sel.ranges,(function(e){return new Zi(me(e.anchor.line+t,e.anchor.ch),me(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){ki(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)Ti(e.cm,n,"gutter")}}function _o(e,t,r,n){if(e.cm&&!e.cm.curOp)return Ci(e.cm,_o)(e,t,r,n);if(t.to.line<e.first)Yo(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);Yo(e,i),t={from:me(e.first,0),to:me(t.to.line+i,t.to.ch),text:[$(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:me(o,ue(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=ce(e,t.from,t.to),r||(r=ro(e,t)),e.cm?$o(e.cm,t,n):ao(e,t,n),Po(e,r,V)}}function $o(e,t,r){var n=e.doc,i=e.display,o=t.from,l=t.to,s=!1,a=o.line;e.options.lineWrapping||(a=de($e(ue(n,o.line))),n.iter(a,l.line+1,(function(e){if(e==i.maxLine)return s=!0,!0}))),n.sel.contains(t.from,t.to)>-1&&wt(e),ao(n,t,r,Nn(e)),e.options.lineWrapping||(n.iter(a,o.line+t.text.length,(function(e){var t=nt(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,s=!1)})),s&&(e.curOp.updateMaxLine=!0)),n.frontier=Math.min(n.frontier,o.line),Wi(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?ki(e):o.line!=l.line||1!=t.text.length||so(e.doc,t)?ki(e,o.line,l.line+1,u):Ti(e,o.line,"text");var c=xt(e,"changes"),h=xt(e,"change");if(h||c){var f={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};h&&Mr(e,"change",e,f),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}function qo(e,t,r,n,i){if(n||(n=r),ye(n,r)<0){var o=n;n=r,r=o}"string"==typeof t&&(t=e.splitLines(t)),Ko(e,{from:r,to:n,text:t,origin:i})}function Zo(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function Jo(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||(o=e[i]=o.deepCopy(),o.copied=!0);for(var s=0;s<o.ranges.length;s++)Zo(o.ranges[s].anchor,t,r,n),Zo(o.ranges[s].head,t,r,n)}else{for(var a=0;a<o.changes.length;++a){var u=o.changes[a];if(r<u.from.line)u.from=me(u.from.line+n,u.from.ch),u.to=me(u.to.line+n,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function Qo(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;Jo(e.done,r,n,i),Jo(e.undone,r,n,i)}function el(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=ue(e,Se(e,t)):i=de(t),null==i?null:(n(o,i)&&e.cm&&Ti(e.cm,i,r),o)}Zi.prototype.from=function(){return Ce(this.anchor,this.head)},Zi.prototype.to=function(){return xe(this.anchor,this.head)},Zi.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch};var tl=function(e){var t=this;this.lines=e,this.parent=null;for(var r=0,n=0;n<e.length;++n)e[n].parent=t,r+=e[n].height;this.height=r};tl.prototype.chunkSize=function(){return this.lines.length},tl.prototype.removeInner=function(e,t){for(var r=this,n=e,i=e+t;n<i;++n){var o=r.lines[n];r.height-=o.height,ur(o),Mr(o,"delete")}this.lines.splice(e,t)},tl.prototype.collapse=function(e){e.push.apply(e,this.lines)},tl.prototype.insertInner=function(e,t,r){var n=this;this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var i=0;i<t.length;++i)t[i].parent=n},tl.prototype.iterN=function(e,t,r){for(var n=this,i=e+t;e<i;++e)if(r(n.lines[e]))return!0};var rl=function(e){var t=this;this.children=e;for(var r=0,n=0,i=0;i<e.length;++i){var o=e[i];r+=o.chunkSize(),n+=o.height,o.parent=t}this.size=r,this.height=n,this.parent=null};rl.prototype.chunkSize=function(){return this.size},rl.prototype.removeInner=function(e,t){var r=this;this.size-=t;for(var n=0;n<this.children.length;++n){var i=r.children[n],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e),s=i.height;if(i.removeInner(e,l),r.height-=s-i.height,o==l&&(r.children.splice(n--,1),i.parent=null),0==(t-=l))break;e=0}else e-=o}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof tl))){var a=[];this.collapse(a),this.children=[new tl(a)],this.children[0].parent=this}},rl.prototype.collapse=function(e){for(var t=this,r=0;r<this.children.length;++r)t.children[r].collapse(e)},rl.prototype.insertInner=function(e,t,r){var n=this;this.size+=t.length,this.height+=r;for(var i=0;i<this.children.length;++i){var o=n.children[i],l=o.chunkSize();if(e<=l){if(o.insertInner(e,t,r),o.lines&&o.lines.length>50){for(var s=o.lines.length%25+25,a=s;a<o.lines.length;){var u=new tl(o.lines.slice(a,a+=25));o.height-=u.height,n.children.splice(++i,0,u),u.parent=n}o.lines=o.lines.slice(0,s),n.maybeSpill()}break}e-=l}},rl.prototype.maybeSpill=function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),r=new rl(t);if(e.parent){e.size-=r.size,e.height-=r.height;var n=B(e.parent.children,e);e.parent.children.splice(n+1,0,r)}else{var i=new rl(e.children);i.parent=e,e.children=[i,r],e=i}r.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},rl.prototype.iterN=function(e,t,r){for(var n=this,i=0;i<this.children.length;++i){var o=n.children[i],l=o.chunkSize();if(e<l){var s=Math.min(t,l-e);if(o.iterN(e,s,r))return!0;if(0==(t-=s))break;e=0}else e-=l}};var nl=function(e,t,r){var n=this;if(r)for(var i in r)r.hasOwnProperty(i)&&(n[i]=r[i]);this.doc=e,this.node=t};function il(e,t,r){rt(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&qn(e,r)}function ol(e,t,r,n){var i=new nl(e,r,n),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),el(e,t,"widget",(function(t){var r=t.widgets||(t.widgets=[]);if(null==i.insertAt?r.push(i):r.splice(Math.min(r.length-1,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!et(e,t)){var n=rt(t)<e.scrollTop;fe(t,t.height+Gr(i)),n&&qn(o,i.height),o.curOp.forceUpdate=!0}return!0})),Mr(o,"lineWidgetAdded",o,i,"number"==typeof t?t:de(t)),i}nl.prototype.clear=function(){var e=this,t=this.doc.cm,r=this.line.widgets,n=this.line,i=de(n);if(null!=i&&r){for(var o=0;o<r.length;++o)r[o]==e&&r.splice(o--,1);r.length||(n.widgets=null);var l=Gr(this);fe(n,Math.max(0,n.height-l)),t&&(xi(t,(function(){il(t,n,-l),Ti(t,i,"widget")})),Mr(t,"lineWidgetCleared",t,this,i))}},nl.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=Gr(this)-t;i&&(fe(n,n.height+i),r&&xi(r,(function(){r.curOp.forceUpdate=!0,il(r,n,i),Mr(r,"lineWidgetChanged",r,e,de(n))})))},Ct(nl);var ll=0,sl=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++ll};function al(e,t,r,n,i){if(n&&n.shared)return cl(e,t,r,n,i);if(e.cm&&!e.cm.curOp)return Ci(e.cm,al)(e,t,r,n,i);var o=new sl(e,i),l=ye(t,r);if(n&&I(n,o,!1),l>0||0==l&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=A("span",[o.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(_e(e,t.line,t,r,o)||t.line!=r.line&&_e(e,r.line,t,r,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Ae()}o.addToHistory&&yo(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var s,a=t.line,u=e.cm;if(e.iter(a,r.line+1,(function(e){u&&o.collapsed&&!u.options.lineWrapping&&$e(e)==u.display.maxLine&&(s=!0),o.collapsed&&a!=t.line&&fe(e,0),Ee(e,new We(o,a==t.line?t.ch:null,a==r.line?r.ch:null)),++a})),o.collapsed&&e.iter(t.line,r.line+1,(function(t){et(e,t)&&fe(t,0)})),o.clearOnEnter&&gt(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(Oe(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++ll,o.atomic=!0),u){if(s&&(u.curOp.updateMaxLine=!0),o.collapsed)ki(u,t.line,r.line+1);else if(o.className||o.title||o.startStyle||o.endStyle||o.css)for(var c=t.line;c<=r.line;c++)Ti(u,c,"text");o.atomic&&Io(u.doc),Mr(u,"markerAdded",u,o)}return o}sl.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){var t=this.doc.cm,r=t&&!t.curOp;if(r&&di(t),xt(this,"clear")){var n=this.find();n&&Mr(this,"clear",n.from,n.to)}for(var i=null,o=null,l=0;l<this.lines.length;++l){var s=e.lines[l],a=De(s.markedSpans,e);t&&!e.collapsed?Ti(t,de(s),"text"):t&&(null!=a.to&&(o=de(s)),null!=a.from&&(i=de(s))),s.markedSpans=He(s.markedSpans,a),null==a.from&&e.collapsed&&!et(e.doc,s)&&t&&fe(s,Ln(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var u=0;u<this.lines.length;++u){var c=$e(e.lines[u]),h=nt(c);h>t.display.maxLineLength&&(t.display.maxLine=c,t.display.maxLineLength=h,t.display.maxLineChanged=!0)}null!=i&&t&&this.collapsed&&ki(t,i,o+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&Io(t.doc)),t&&Mr(t,"markerCleared",t,this,i,o),r&&pi(t),this.parent&&this.parent.clear()}},sl.prototype.find=function(e,t){var r,n,i=this;null==e&&"bookmark"==this.type&&(e=1);for(var o=0;o<this.lines.length;++o){var l=i.lines[o],s=De(l.markedSpans,i);if(null!=s.from&&(r=me(t?l:de(l),s.from),-1==e))return r;if(null!=s.to&&(n=me(t?l:de(l),s.to),1==e))return n}return r&&{from:r,to:n}},sl.prototype.changed=function(){var e=this,t=this.find(-1,!0),r=this,n=this.doc.cm;t&&n&&xi(n,(function(){var i=t.line,o=de(t.line),l=Qr(n,o);if(l&&(un(l),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!et(r.doc,i)&&null!=r.height){var s=r.height;r.height=null;var a=Gr(r)-s;a&&fe(i,i.height+a)}Mr(n,"markerChanged",n,e)}))},sl.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=B(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},sl.prototype.detachLine=function(e){if(this.lines.splice(B(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},Ct(sl);var ul=function(e,t){var r=this;this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=r};function cl(e,t,r,n,i){n=I(n),n.shared=!1;var o=[al(e,t,r,n,i)],l=o[0],s=n.widgetNode;return uo(e,(function(e){s&&(n.widgetNode=s.cloneNode(!0)),o.push(al(e,Le(e,t),Le(e,r),n,i));for(var a=0;a<e.linked.length;++a)if(e.linked[a].isParent)return;l=$(o)})),new ul(o,l)}function hl(e){return e.findMarks(me(e.first,0),e.clipPos(me(e.lastLine())),(function(e){return e.parent}))}function fl(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),l=e.clipPos(i.to);if(ye(o,l)){var s=al(e,o,l,n.primary,n.primary.type);n.markers.push(s),s.parent=n}}}function dl(e){for(var t=function(t){var r=e[t],n=[r.primary.doc];uo(r.primary.doc,(function(e){return n.push(e)}));for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==B(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}},r=0;r<e.length;r++)t(r)}ul.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var t=0;t<this.markers.length;++t)e.markers[t].clear();Mr(this,"clear")}},ul.prototype.find=function(e,t){return this.primary.find(e,t)},Ct(ul);var pl=0,gl=function(e,t,r,n,i){if(!(this instanceof gl))return new gl(e,t,r,n,i);null==r&&(r=0),rl.call(this,[new tl([new sr("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.frontier=r;var o=me(r,0);this.sel=Qi(o),this.history=new po(null),this.id=++pl,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),ao(this,{from:o,to:o,text:e}),Eo(this,Qi(o),V)};gl.prototype=Q(rl.prototype,{constructor:gl,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=he(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:Li((function(e){var t=me(this.first,0),r=this.first+this.size-1;Ko(this,{from:t,to:me(r,ue(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Jn(this.cm,0,0),Eo(this,Qi(t),V)})),replaceRange:function(e,t,r,n){t=Le(this,t),r=r?Le(this,r):t,qo(this,e,t,r,n)},getRange:function(e,t,r){var n=ce(this,Le(this,e),Le(this,t));return!1===r?n:n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(ge(this,e))return ue(this,e)},getLineNumber:function(e){return de(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=ue(this,e)),$e(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return Le(this,e)},getCursor:function(e){var t,r=this.sel.primary();return t=null==e||"head"==e?r.head:"anchor"==e?r.anchor:"end"==e||"to"==e||!1===e?r.to():r.from(),t},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Li((function(e,t,r){Wo(this,Le(this,"number"==typeof e?me(e,t||0):e),null,r)})),setSelection:Li((function(e,t,r){Wo(this,Le(this,e),Le(this,t||e),r)})),extendSelection:Li((function(e,t,r){No(this,Le(this,e),t&&Le(this,t),r)})),extendSelections:Li((function(e,t){Oo(this,Te(this,e),t)})),extendSelectionsBy:Li((function(e,t){var r=q(this.sel.ranges,e);Oo(this,Te(this,r),t)})),setSelections:Li((function(e,t,r){var n=this;if(e.length){for(var i=[],o=0;o<e.length;o++)i[o]=new Zi(Le(n,e[o].anchor),Le(n,e[o].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Eo(this,Ji(i,t),r)}})),addSelection:Li((function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new Zi(Le(this,e),Le(this,t||e))),Eo(this,Ji(n,n.length-1),r)})),getSelection:function(e){for(var t,r=this,n=this.sel.ranges,i=0;i<n.length;i++){var o=ce(r,n[i].from(),n[i].to());t=t?t.concat(o):o}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=this,r=[],n=this.sel.ranges,i=0;i<n.length;i++){var o=ce(t,n[i].from(),n[i].to());!1!==e&&(o=o.join(e||t.lineSeparator())),r[i]=o}return r},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:Li((function(e,t,r){for(var n=this,i=[],o=this.sel,l=0;l<o.ranges.length;l++){var s=o.ranges[l];i[l]={from:s.from(),to:s.to(),text:n.splitLines(e[l]),origin:r}}for(var a=t&&"end"!=t&&io(this,i,t),u=i.length-1;u>=0;u--)Ko(n,i[u]);a?Ho(this,a):this.cm&&Zn(this.cm)})),undo:Li((function(){Xo(this,"undo")})),redo:Li((function(){Xo(this,"redo")})),undoSelection:Li((function(){Xo(this,"undo",!0)})),redoSelection:Li((function(){Xo(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){this.history=new po(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:To(this.history.done),undone:To(this.history.undone)}},setHistory:function(e){var t=this.history=new po(this.history.maxGeneration);t.done=To(e.done.slice(0),null,!0),t.undone=To(e.undone.slice(0),null,!0)},setGutterMarker:Li((function(e,t,r){return el(this,e,"gutter",(function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&ne(n)&&(e.gutterMarkers=null),!0}))})),clearGutter:Li((function(e){var t=this;this.iter((function(r){r.gutterMarkers&&r.gutterMarkers[e]&&el(t,r,"gutter",(function(){return r.gutterMarkers[e]=null,ne(r.gutterMarkers)&&(r.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!ge(this,e))return null;if(t=e,e=ue(this,e),!e)return null}else if(t=de(e),null==t)return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Li((function(e,t,r){return el(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[n]){if(L(r).test(e[n]))return!1;e[n]+=" "+r}else e[n]=r;return!0}))})),removeLineClass:Li((function(e,t,r){return el(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[n];if(!i)return!1;if(null==r)e[n]=null;else{var o=i.match(L(r));if(!o)return!1;var l=o.index+o[0].length;e[n]=i.slice(0,o.index)+(o.index&&l!=i.length?" ":"")+i.slice(l)||null}return!0}))})),addLineWidget:Li((function(e,t,r){return ol(this,e,t,r)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return al(this,Le(this,e),Le(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return e=Le(this,e),al(this,e,e,r,"bookmark")},findMarksAt:function(e){e=Le(this,e);var t=[],r=ue(this,e.line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=Le(this,e),t=Le(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,(function(o){var l=o.markedSpans;if(l)for(var s=0;s<l.length;s++){var a=l[s];null!=a.to&&i==e.line&&e.ch>=a.to||null==a.from&&i!=e.line||null!=a.from&&i==t.line&&a.from>=t.ch||r&&!r(a.marker)||n.push(a.marker.parent||a.marker)}++i})),n},getAllMarks:function(){var e=[];return this.iter((function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)})),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r})),Le(this,me(r,t))},indexFromPos:function(e){e=Le(this,e);var t=e.ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+r})),t},copy:function(e){var t=new gl(he(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new gl(he(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],fl(n,hl(this)),n},unlinkDoc:function(e){var t=this;if(e instanceof ms&&(e=e.doc),this.linked)for(var r=0;r<this.linked.length;++r){var n=t.linked[r];if(n.doc==e){t.linked.splice(r,1),e.unlinkDoc(t),dl(hl(t));break}}if(e.history==this.history){var i=[e.id];uo(e,(function(e){return i.push(e.id)}),!0),e.history=new po(null),e.history.done=To(this.history.done,i),e.history.undone=To(this.history.undone,i)}},iterLinkedDocs:function(e){uo(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Et(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:Li((function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter((function(e){return e.order=null})),this.cm&&fo(this.cm))}))}),gl.prototype.eachLine=gl.prototype.iter;var vl=0;function ml(e){var t=this;if(wl(t),!bt(t,e)&&!Ur(t.display,e)){St(e),l&&(vl=+new Date);var r=An(t,e,!0),n=e.dataTransfer.files;if(r&&!t.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),s=0,a=function(e,n){if(!t.options.allowDropFileTypes||-1!=B(t.options.allowDropFileTypes,e.type)){var l=new FileReader;l.onload=Ci(t,(function(){var e=l.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),o[n]=e,++s==i){r=Le(t.doc,r);var a={from:r,to:r,text:t.doc.splitLines(o.join(t.doc.lineSeparator())),origin:"paste"};Ko(t.doc,a),Ho(t.doc,Qi(r,eo(a)))}})),l.readAsText(e)}},u=0;u<i;++u)a(n[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var c=e.dataTransfer.getData("Text");if(c){var h;if(t.state.draggingText&&!t.state.draggingText.copy&&(h=t.listSelections()),Po(t.doc,Qi(r,r)),h)for(var f=0;f<h.length;++f)qo(t.doc,"",h[f].anchor,h[f].head,"drag");t.replaceSelection(c,"around","paste"),t.display.input.focus()}}catch(e){}}}}function yl(e,t){if(l&&(!e.state.draggingText||+new Date-vl<100))Tt(t);else if(!bt(e,t)&&!Ur(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!f)){var r=O("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",h&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),h&&r.parentNode.removeChild(r)}}function bl(e,t){var r=An(e,t);if(r){var n=document.createDocumentFragment();En(e,r,n),e.display.dragCursor||(e.display.dragCursor=O("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),N(e.display.dragCursor,n)}}function wl(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function xl(e){if(document.body.getElementsByClassName)for(var t=document.body.getElementsByClassName("CodeMirror"),r=0;r<t.length;r++){var n=t[r].CodeMirror;n&&e(n)}}var Cl=!1;function Sl(){Cl||(Ll(),Cl=!0)}function Ll(){var e;gt(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,xl(kl)}),100))})),gt(window,"blur",(function(){return xl(Bn)}))}function kl(e){var t=e.display;t.lastWrapHeight==t.wrapper.clientHeight&&t.lastWrapWidth==t.wrapper.clientWidth||(t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize())}for(var Tl={3:"Enter",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",127:"Delete",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Ml=0;Ml<10;Ml++)Tl[Ml+48]=Tl[Ml+96]=String(Ml);for(var Nl=65;Nl<=90;Nl++)Tl[Nl]=String.fromCharCode(Nl);for(var Ol=1;Ol<=12;Ol++)Tl[Ol+111]=Tl[Ol+63235]="F"+Ol;var Al={};function Wl(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var s=o[l];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))r=!0;else{if(!/^s(hift)?$/i.test(s))throw new Error("Unrecognized modifier name: "+s);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Dl(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=q(r.split(" "),Wl),o=0;o<i.length;o++){var l=void 0,s=void 0;o==i.length-1?(s=i.join(" "),l=n):(s=i.slice(0,o+1).join(" "),l="...");var a=t[s];if(a){if(a!=l)throw new Error("Inconsistent bindings for "+s)}else t[s]=l}delete e[r]}for(var u in t)e[u]=t[u];return e}function Hl(e,t,r,n){t=Fl(t);var i=t.call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Hl(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var l=Hl(e,t.fallthrough[o],r,n);if(l)return l}}}function El(e){var t="string"==typeof e?e:Tl[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Pl(e,t){if(h&&34==e.keyCode&&e["char"])return!1;var r=Tl[e.keyCode],n=r;return null!=n&&!e.altGraphKey&&(e.altKey&&"Alt"!=r&&(n="Alt-"+n),(C?e.metaKey:e.ctrlKey)&&"Ctrl"!=r&&(n="Ctrl-"+n),(C?e.ctrlKey:e.metaKey)&&"Cmd"!=r&&(n="Cmd-"+n),!t&&e.shiftKey&&"Shift"!=r&&(n="Shift-"+n),n)}function Fl(e){return"string"==typeof e?Al[e]:e}function Il(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){var o=t(r[i]);while(n.length&&ye(o.from,$(n).to)<=0){var l=n.pop();if(ye(l.from,o.from)<0){o.from=l.from;break}}n.push(o)}xi(e,(function(){for(var t=n.length-1;t>=0;t--)qo(e.doc,"",n[t].from,n[t].to,"+delete");Zn(e)}))}Al.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Al.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Al.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Al.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Al["default"]=y?Al.macDefault:Al.pcDefault;var zl={selectAll:Uo,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),V)},killLine:function(e){return Il(e,(function(t){if(t.empty()){var r=ue(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:me(t.head.line+1,0)}:{from:t.head,to:me(t.head.line,r)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return Il(e,(function(t){return{from:me(t.from().line,0),to:Le(e.doc,me(t.to().line+1,0))}}))},delLineLeft:function(e){return Il(e,(function(e){return{from:me(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return Il(e,(function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return{from:n,to:t.from()}}))},delWrappedLineRight:function(e){return Il(e,(function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(me(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(me(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return Rl(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return Gl(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return Bl(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var r=e.charCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")}),j)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var r=e.charCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")}),j)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?Gl(e,t.head):n}),j)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),l=z(e.getLine(o.line),o.ch,n);t.push(_(n-l%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return xi(e,(function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=ue(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new me(i.line,i.ch-1)),i.ch>0)i=new me(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),me(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var l=ue(e.doc,i.line-1).text;l&&(i=new me(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),me(i.line-1,l.length-1),i,"+transpose"))}r.push(new Zi(i,i))}e.setSelections(r)}))},newlineAndIndent:function(e){return xi(e,(function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);Zn(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Rl(e,t){var r=ue(e.doc,t),n=$e(r);return n!=r&&(t=de(n)),ft(!0,e,n,t,1)}function Bl(e,t){var r=ue(e.doc,t),n=qe(r);return n!=r&&(t=de(n)),ft(!0,e,r,t,-1)}function Gl(e,t){var r=Rl(e,t.line),n=ue(e.doc,r.line),i=ut(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(0,n.text.search(/\S/)),l=t.line==r.line&&t.ch<=o&&t.ch;return me(r.line,l?0:o,r.sticky)}return r}function Ul(e,t,r){if("string"==typeof t&&(t=zl[t],!t))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=U}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}function Vl(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=Hl(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&Hl(t,e.options.extraKeys,r,e)||Hl(t,e.options.keyMap,r,e)}var Kl=new R;function jl(e,t,r,n){var i=e.state.keySeq;if(i){if(El(t))return"handled";Kl.set(50,(function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())})),t=i+" "+t}var o=Vl(e,t,n);return"multi"==o&&(e.state.keySeq=t),"handled"==o&&Mr(e,"keyHandled",e,t,r),"handled"!=o&&"multi"!=o||(St(r),Fn(e)),i&&!o&&/\'$/.test(t)?(St(r),!0):!!o}function Xl(e,t){var r=Pl(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?jl(e,"Shift-"+r,t,(function(t){return Ul(e,t,!0)}))||jl(e,r,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return Ul(e,t)})):jl(e,r,t,(function(t){return Ul(e,t)})))}function Yl(e,t,r){return jl(e,"'"+r+"'",t,(function(t){return Ul(e,t,!0)}))}var _l,$l,ql=null;function Zl(e){var t=this;if(t.curOp.focus=D(),!bt(t,e)){l&&s<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var n=Xl(t,e);h&&(ql=n?r:null,n||88!=r||Ft||!(y?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||Jl(t)}}function Jl(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),mt(document,"keyup",r),mt(document,"mouseover",r))}H(t,"CodeMirror-crosshair"),gt(document,"keyup",r),gt(document,"mouseover",r)}function Ql(e){16==e.keyCode&&(this.doc.sel.shift=!1),bt(this,e)}function es(e){var t=this;if(!(Ur(t.display,e)||bt(t,e)||e.ctrlKey&&!e.altKey||y&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(h&&r==ql)return ql=null,void St(e);if(!h||e.which&&!(e.which<10)||!Xl(t,e)){var i=String.fromCharCode(null==n?r:n);"\b"!=i&&(Yl(t,e,i)||t.display.input.onKeyPress(e))}}}function ts(e){var t=this,r=t.display;if(!(bt(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,Ur(r,e))a||(r.scroller.draggable=!1,setTimeout((function(){return r.scroller.draggable=!0}),100));else if(!ls(t,e)){var n=An(t,e);switch(window.focus(),Nt(e)){case 1:t.state.selectingText?t.state.selectingText(e):n?rs(t,e,n):Mt(e)==r.scroller&&St(e);break;case 2:a&&(t.state.lastMiddleDown=+new Date),n&&No(t.doc,n),setTimeout((function(){return r.input.focus()}),20),St(e);break;case 3:S?ss(t,e):zn(t);break}}}function rs(e,t,r){l?setTimeout(F(In,e),0):e.curOp.focus=D();var n,i=+new Date;$l&&$l.time>i-400&&0==ye($l.pos,r)?n="triple":_l&&_l.time>i-400&&0==ye(_l.pos,r)?(n="double",$l={time:i,pos:r}):(n="single",_l={time:i,pos:r});var o,s=e.doc.sel,a=y?t.metaKey:t.ctrlKey;e.options.dragDrop&&Wt&&!e.isReadOnly()&&"single"==n&&(o=s.contains(r))>-1&&(ye((o=s.ranges[o]).from(),r)<0||r.xRel>0)&&(ye(o.to(),r)>0||r.xRel<0)?ns(e,t,r,a):is(e,t,r,n,a)}function ns(e,t,r,n){var i=e.display,o=!1,u=Ci(e,(function(t){a&&(i.scroller.draggable=!1),e.state.draggingText=!1,mt(document,"mouseup",u),mt(document,"mousemove",c),mt(i.scroller,"dragstart",h),mt(i.scroller,"drop",u),o||(St(t),n||No(e.doc,r),a||l&&9==s?setTimeout((function(){document.body.focus(),i.input.focus()}),20):i.input.focus())})),c=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},h=function(){return o=!0};a&&(i.scroller.draggable=!0),e.state.draggingText=u,u.copy=y?t.altKey:t.ctrlKey,i.scroller.dragDrop&&i.scroller.dragDrop(),gt(document,"mouseup",u),gt(document,"mousemove",c),gt(i.scroller,"dragstart",h),gt(i.scroller,"drop",u),zn(e),setTimeout((function(){return i.input.focus()}),20)}function is(e,t,r,n,i){var o=e.display,l=e.doc;St(t);var s,a,u=l.sel,c=u.ranges;if(i&&!t.shiftKey?(a=l.sel.contains(r),s=a>-1?c[a]:new Zi(r,r)):(s=l.sel.primary(),a=l.sel.primIndex),b?t.shiftKey&&t.metaKey:t.altKey)n="rect",i||(s=new Zi(r,r)),r=An(e,t,!0,!0),a=-1;else if("double"==n){var h=e.findWordAt(r);s=e.display.shift||l.extend?Mo(l,s,h.anchor,h.head):h}else if("triple"==n){var f=new Zi(me(r.line,0),Le(l,me(r.line+1,0)));s=e.display.shift||l.extend?Mo(l,s,f.anchor,f.head):f}else s=Mo(l,s,r);i?-1==a?(a=c.length,Eo(l,Ji(c.concat([s]),a),{scroll:!1,origin:"*mouse"})):c.length>1&&c[a].empty()&&"single"==n&&!t.shiftKey?(Eo(l,Ji(c.slice(0,a).concat(c.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),u=l.sel):Ao(l,a,s,K):(a=0,Eo(l,new qi([s],0),K),u=l.sel);var d=r;function p(t){if(0!=ye(d,t))if(d=t,"rect"==n){for(var i=[],o=e.options.tabSize,c=z(ue(l,r.line).text,r.ch,o),h=z(ue(l,t.line).text,t.ch,o),f=Math.min(c,h),p=Math.max(c,h),g=Math.min(r.line,t.line),v=Math.min(e.lastLine(),Math.max(r.line,t.line));g<=v;g++){var m=ue(l,g).text,y=X(m,f,o);f==p?i.push(new Zi(me(g,y),me(g,y))):m.length>y&&i.push(new Zi(me(g,y),me(g,X(m,p,o))))}i.length||i.push(new Zi(r,r)),Eo(l,Ji(u.ranges.slice(0,a).concat(i),a),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,w=s,x=w.anchor,C=t;if("single"!=n)b="double"==n?e.findWordAt(t):new Zi(me(t.line,0),Le(l,me(t.line+1,0))),ye(b.anchor,x)>0?(C=b.head,x=Ce(w.from(),b.anchor)):(C=b.anchor,x=xe(w.to(),b.head));var S=u.ranges.slice(0);S[a]=new Zi(Le(l,x),C),Eo(l,Ji(S,a),K)}}var g=o.wrapper.getBoundingClientRect(),v=0;function m(t){var r=++v,i=An(e,t,!0,"rect"==n);if(i)if(0!=ye(i,d)){e.curOp.focus=D(),p(i);var s=Vn(o,l);(i.line>=s.to||i.line<s.from)&&setTimeout(Ci(e,(function(){v==r&&m(t)})),150)}else{var a=t.clientY<g.top?-20:t.clientY>g.bottom?20:0;a&&setTimeout(Ci(e,(function(){v==r&&(o.scroller.scrollTop+=a,m(t))})),50)}}function y(t){e.state.selectingText=!1,v=1/0,St(t),o.input.focus(),mt(document,"mousemove",w),mt(document,"mouseup",x),l.history.lastSelOrigin=null}var w=Ci(e,(function(e){Nt(e)?m(e):y(e)})),x=Ci(e,y);e.state.selectingText=x,gt(document,"mousemove",w),gt(document,"mouseup",x)}function os(e,t,r,n){var i,o;try{i=t.clientX,o=t.clientY}catch(t){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&St(t);var l=e.display,s=l.lineDiv.getBoundingClientRect();if(o>s.bottom||!xt(e,r))return kt(t);o-=s.top-l.viewOffset;for(var a=0;a<e.options.gutters.length;++a){var u=l.gutters.childNodes[a];if(u&&u.getBoundingClientRect().right>=i){var c=pe(e.doc,o),h=e.options.gutters[a];return yt(e,r,e,c,h,t),kt(t)}}}function ls(e,t){return os(e,t,"gutterClick",!0)}function ss(e,t){Ur(e.display,t)||as(e,t)||bt(e,t,"contextmenu")||e.display.input.onContextMenu(t)}function as(e,t){return!!xt(e,"gutterContextMenu")&&os(e,t,"gutterContextMenu",!1)}function us(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),hn(e)}var cs={toString:function(){return"CodeMirror.Init"}},hs={},fs={};function ds(e){var t=e.optionHandlers;function r(r,n,i,o){e.defaults[r]=n,i&&(t[r]=o?function(e,t,r){r!=cs&&i(e,t,r)}:i)}e.defineOption=r,e.Init=cs,r("value","",(function(e,t){return e.setValue(t)}),!0),r("mode",null,(function(e,t){e.doc.modeOption=t,oo(e)}),!0),r("indentUnit",2,oo,!0),r("indentWithTabs",!1),r("smartIndent",!0),r("tabSize",4,(function(e){lo(e),hn(e),ki(e)}),!0),r("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter((function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(me(n,o))}n++}));for(var i=r.length-1;i>=0;i--)qo(e.doc,t,r[i],me(r[i].line,r[i].ch+t.length))}})),r("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff]/g,(function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=cs&&e.refresh()})),r("specialCharPlaceholder",pr,(function(e){return e.refresh()}),!0),r("electricChars",!0),r("inputStyle",m?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),r("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),r("rtlMoveVisually",!w),r("wholeLineUpdateBefore",!0),r("theme","default",(function(e){us(e),ps(e)}),!0),r("keyMap","default",(function(e,t,r){var n=Fl(t),i=r!=cs&&Fl(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)})),r("extraKeys",null),r("lineWrapping",!1,vs,!0),r("gutters",[],(function(e){Ki(e.options),ps(e)}),!0),r("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?Mn(e.display)+"px":"0",e.refresh()}),!0),r("coverGutterNextToScrollbar",!1,(function(e){return ai(e)}),!0),r("scrollbarStyle","native",(function(e){hi(e),ai(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),r("lineNumbers",!1,(function(e){Ki(e.options),ps(e)}),!0),r("firstLineNumber",1,ps,!0),r("lineNumberFormatter",(function(e){return e}),ps,!0),r("showCursorWhenSelecting",!1,Dn,!0),r("resetSelectionOnContextMenu",!0),r("lineWiseCopyCut",!0),r("readOnly",!1,(function(e,t){"nocursor"==t?(Bn(e),e.display.input.blur(),e.display.disabled=!0):e.display.disabled=!1,e.display.input.readOnlyChanged(t)})),r("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),r("dragDrop",!0,gs),r("allowDropFileTypes",null),r("cursorBlinkRate",530),r("cursorScrollMargin",0),r("cursorHeight",1,Dn,!0),r("singleCursorHeightPerLine",!0,Dn,!0),r("workTime",100),r("workDelay",100),r("flattenSpans",!0,lo,!0),r("addModeClass",!1,lo,!0),r("pollInterval",100),r("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),r("historyEventDelay",1250),r("viewportMargin",10,(function(e){return e.refresh()}),!0),r("maxHighlightLength",1e4,lo,!0),r("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),r("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),r("autofocus",null),r("direction","ltr",(function(e,t){return e.doc.setDirection(t)}),!0)}function ps(e){Vi(e),ki(e),Kn(e)}function gs(e,t,r){var n=r&&r!=cs;if(!t!=!n){var i=e.display.dragFunctions,o=t?gt:mt;o(e.display.scroller,"dragstart",i.start),o(e.display.scroller,"dragenter",i.enter),o(e.display.scroller,"dragover",i.over),o(e.display.scroller,"dragleave",i.leave),o(e.display.scroller,"drop",i.drop)}}function vs(e){e.options.lineWrapping?(H(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),it(e)),On(e),ki(e),hn(e),setTimeout((function(){return ai(e)}),100)}function ms(e,t){var r=this;if(!(this instanceof ms))return new ms(e,t);this.options=t=t?I(t):{},I(hs,t,!1),Ki(t);var n=t.value;"string"==typeof n&&(n=new gl(n,t.mode,null,t.lineSeparator,t.direction)),this.doc=n;var i=new ms.inputStyles[t.inputStyle](this),o=this.display=new ae(e,n,i);for(var u in o.wrapper.CodeMirror=this,Vi(this),us(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),hi(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:!1,cutIncoming:!1,selectingText:!1,draggingText:!1,highlight:new R,keySeq:null,specialChars:null},t.autofocus&&!m&&o.input.focus(),l&&s<11&&setTimeout((function(){return r.display.input.reset(!0)}),20),ys(this),Sl(),di(this),this.curOp.forceUpdate=!0,co(this,n),t.autofocus&&!m||this.hasFocus()?setTimeout(F(Rn,this),20):Bn(this),fs)fs.hasOwnProperty(u)&&fs[u](r,t[u],cs);jn(this),t.finishInit&&t.finishInit(this);for(var c=0;c<bs.length;++c)bs[c](r);pi(this),a&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function ys(e){var t=e.display;gt(t.scroller,"mousedown",Ci(e,ts)),gt(t.scroller,"dblclick",l&&s<11?Ci(e,(function(t){if(!bt(e,t)){var r=An(e,t);if(r&&!ls(e,t)&&!Ur(e.display,t)){St(t);var n=e.findWordAt(r);No(e.doc,n.anchor,n.head)}}})):function(t){return bt(e,t)||St(t)}),S||gt(t.scroller,"contextmenu",(function(t){return ss(e,t)}));var r,n={end:0};function i(){t.activeTouch&&(r=setTimeout((function(){return t.activeTouch=null}),1e3),n=t.activeTouch,n.end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function a(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}gt(t.scroller,"touchstart",(function(i){if(!bt(e,i)&&!o(i)){t.input.ensurePolled(),clearTimeout(r);var l=+new Date;t.activeTouch={start:l,moved:!1,prev:l-n.end<=300?n:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}})),gt(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),gt(t.scroller,"touchend",(function(r){var n=t.activeTouch;if(n&&!Ur(t,r)&&null!=n.left&&!n.moved&&new Date-n.start<300){var o,l=e.coordsChar(t.activeTouch,"page");o=!n.prev||a(n,n.prev)?new Zi(l,l):!n.prev.prev||a(n,n.prev.prev)?e.findWordAt(l):new Zi(me(l.line,0),Le(e.doc,me(l.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),St(r)}i()})),gt(t.scroller,"touchcancel",i),gt(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(ri(e,t.scroller.scrollTop),ii(e,t.scroller.scrollLeft,!0),yt(e,"scroll",e))})),gt(t.scroller,"mousewheel",(function(t){return $i(e,t)})),gt(t.scroller,"DOMMouseScroll",(function(t){return $i(e,t)})),gt(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){bt(e,t)||Tt(t)},over:function(t){bt(e,t)||(bl(e,t),Tt(t))},start:function(t){return yl(e,t)},drop:Ci(e,ml),leave:function(t){bt(e,t)||wl(e)}};var u=t.input.getField();gt(u,"keyup",(function(t){return Ql.call(e,t)})),gt(u,"keydown",Ci(e,Zl)),gt(u,"keypress",Ci(e,es)),gt(u,"focus",(function(t){return Rn(e,t)})),gt(u,"blur",(function(t){return Bn(e,t)}))}ms.defaults=hs,ms.optionHandlers=fs;var bs=[];function ws(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=Qt(e,t):r="prev");var l=e.options.tabSize,s=ue(o,t),a=z(s.text,null,l);s.stateAfter&&(s.stateAfter=null);var u,c=s.text.match(/^\s*/)[0];if(n||/\S/.test(s.text)){if("smart"==r&&(u=o.mode.indent(i,s.text.slice(c.length),s.text),u==U||u>150)){if(!n)return;r="prev"}}else u=0,r="not";"prev"==r?u=t>o.first?z(ue(o,t-1).text,null,l):0:"add"==r?u=a+e.options.indentUnit:"subtract"==r?u=a-e.options.indentUnit:"number"==typeof r&&(u=a+r),u=Math.max(0,u);var h="",f=0;if(e.options.indentWithTabs)for(var d=Math.floor(u/l);d;--d)f+=l,h+="\t";if(f<u&&(h+=_(u-f)),h!=c)return qo(o,h,me(t,0),me(t,c.length),"+input"),s.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<c.length){var v=me(t,c.length);Ao(o,p,new Zi(v,v));break}}}ms.defineInitHook=function(e){return bs.push(e)};var xs=null;function Cs(e){xs=e}function Ss(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var l,s=e.state.pasteIncoming||"paste"==i,a=Et(t),u=null;if(s&&n.ranges.length>1)if(xs&&xs.text.join("\n")==t){if(n.ranges.length%xs.text.length==0){u=[];for(var c=0;c<xs.text.length;c++)u.push(o.splitLines(xs.text[c]))}}else a.length==n.ranges.length&&(u=q(a,(function(e){return[e]})));for(var h=n.ranges.length-1;h>=0;h--){var f=n.ranges[h],d=f.from(),p=f.to();f.empty()&&(r&&r>0?d=me(d.line,d.ch-r):e.state.overwrite&&!s?p=me(p.line,Math.min(ue(o,p.line).text.length,p.ch+$(a).length)):xs&&xs.lineWise&&xs.text.join("\n")==t&&(d=p=me(d.line,0))),l=e.curOp.updateInput;var g={from:d,to:p,text:u?u[h%u.length]:a,origin:i||(s?"paste":e.state.cutIncoming?"cut":"+input")};Ko(e.doc,g),Mr(e,"inputRead",e,g)}t&&!s&&ks(e,t),Zn(e),e.curOp.updateInput=l,e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=!1}function Ls(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||xi(t,(function(){return Ss(t,r,0,null,"paste")})),!0}function ks(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(t.indexOf(o.electricChars.charAt(s))>-1){l=ws(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(ue(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=ws(e,i.head.line,"smart"));l&&Mr(e,"electricInput",e,i.head.line)}}}function Ts(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:me(i,0),head:me(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function Ms(e,t){e.setAttribute("autocorrect","off"),e.setAttribute("autocapitalize","off"),e.setAttribute("spellcheck",!!t)}function Ns(){var e=O("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=O("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return a?e.style.width="1000px":e.setAttribute("wrap","off"),g&&(e.style.border="1px solid black"),Ms(e),t}var Os=function(e){var t=e.optionHandlers,r=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];n[e]==r&&"mode"!=e||(n[e]=r,t.hasOwnProperty(e)&&Ci(this,t[e])(this,r,i),yt(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Fl(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:Si((function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw new Error("Overlays may not be stateful.");Z(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},(function(e){return e.priority})),this.state.modeGen++,ki(this)})),removeOverlay:Si((function(e){for(var t=this,r=this.state.overlays,n=0;n<r.length;++n){var i=r[n].modeSpec;if(i==e||"string"==typeof e&&i.name==e)return r.splice(n,1),t.state.modeGen++,void ki(t)}})),indentLine:Si((function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),ge(this.doc,e)&&ws(this,e,t,r)})),indentSelection:Si((function(e){for(var t=this,r=this.doc.sel.ranges,n=-1,i=0;i<r.length;i++){var o=r[i];if(o.empty())o.head.line>n&&(ws(t,o.head.line,e,!0),n=o.head.line,i==t.doc.sel.primIndex&&Zn(t));else{var l=o.from(),s=o.to(),a=Math.max(n,l.line);n=Math.min(t.lastLine(),s.line-(s.ch?0:1))+1;for(var u=a;u<n;++u)ws(t,u,e);var c=t.doc.sel.ranges;0==l.ch&&r.length==c.length&&c[i].from().ch>0&&Ao(t.doc,i,new Zi(l,c[i].to()),V)}}})),getTokenAt:function(e,t){return nr(this,e,t)},getLineTokens:function(e,t){return nr(this,me(e),t,!0)},getTokenTypeAt:function(e){e=Le(this.doc,e);var t,r=Jt(this,ue(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var l=n+i>>1;if((l?r[2*l-1]:0)>=o)i=l;else{if(!(r[2*l+1]<o)){t=r[2*l+2];break}n=l+1}}var s=t?t.indexOf("overlay "):-1;return s<0?t:0==s?null:t.slice(0,s-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=this,i=[];if(!r.hasOwnProperty(t))return i;var o=r[t],l=this.getModeAt(e);if("string"==typeof l[t])o[l[t]]&&i.push(o[l[t]]);else if(l[t])for(var s=0;s<l[t].length;s++){var a=o[l[t][s]];a&&i.push(a)}else l.helperType&&o[l.helperType]?i.push(o[l.helperType]):o[l.name]&&i.push(o[l.name]);for(var u=0;u<o._global.length;u++){var c=o._global[u];c.pred(l,n)&&-1==B(i,c.val)&&i.push(c.val)}return i},getStateAfter:function(e,t){var r=this.doc;return e=Se(r,null==e?r.first+r.size-1:e),Qt(this,e+1,t)},cursorCoords:function(e,t){var r,n=this.doc.sel.primary();return r=null==e?n.head:"object"==typeof e?Le(this.doc,e):e?n.from():n.to(),mn(this,r,t||"page")},charCoords:function(e,t){return vn(this,Le(this.doc,e),t||"page")},coordsChar:function(e,t){return e=gn(this,e,t||"page"),wn(this,e.left,e.top)},lineAtHeight:function(e,t){return e=gn(this,{top:e,left:0},t||"page").top,pe(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=ue(this.doc,e)}else n=e;return pn(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-rt(n):0)},defaultTextHeight:function(){return Ln(this.display)},defaultCharWidth:function(){return kn(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o=this.display;e=mn(this,Le(this.doc,e));var l=e.bottom,s=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==n)l=e.top;else if("above"==n||"near"==n){var a=Math.max(o.wrapper.clientHeight,this.doc.height),u=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>a)&&e.top>t.offsetHeight?l=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=a&&(l=e.bottom),s+t.offsetWidth>u&&(s=u-t.offsetWidth)}t.style.top=l+"px",t.style.left=t.style.right="","right"==i?(s=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?s=0:"middle"==i&&(s=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=s+"px"),r&&_n(this,{left:s,top:l,right:s+t.offsetWidth,bottom:l+t.offsetHeight})},triggerOnKeyDown:Si(Zl),triggerOnKeyPress:Si(es),triggerOnKeyUp:Ql,execCommand:function(e){if(zl.hasOwnProperty(e))return zl[e].call(null,this)},triggerElectric:Si((function(e){ks(this,e)})),findPosH:function(e,t,r,n){var i=this,o=1;t<0&&(o=-1,t=-t);for(var l=Le(this.doc,e),s=0;s<t;++s)if(l=As(i.doc,l,o,r,n),l.hitSide)break;return l},moveH:Si((function(e,t){var r=this;this.extendSelectionsBy((function(n){return r.display.shift||r.doc.extend||n.empty()?As(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()}),j)})),deleteH:Si((function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):Il(this,(function(r){var i=As(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}}))})),findPosV:function(e,t,r,n){var i=this,o=1,l=n;t<0&&(o=-1,t=-t);for(var s=Le(this.doc,e),a=0;a<t;++a){var u=mn(i,s,"div");if(null==l?l=u.left:u.left=l,s=Ws(i,u,o,r),s.hitSide)break}return s},moveV:Si((function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy((function(l){if(o)return e<0?l.from():l.to();var s=mn(r,l.head,"div");null!=l.goalColumn&&(s.left=l.goalColumn),i.push(s.left);var a=Ws(r,s,e,t);return"page"==t&&l==n.sel.primary()&&qn(r,vn(r,a,"div").top-s.top),a}),j),i.length)for(var l=0;l<n.sel.ranges.length;l++)n.sel.ranges[l].goalColumn=i[l]})),findWordAt:function(e){var t=this.doc,r=ue(t,e.line).text,n=e.ch,i=e.ch;if(r){var o=this.getHelper(e,"wordChars");"before"!=e.sticky&&i!=r.length||!n?++i:--n;var l=r.charAt(n),s=re(l,o)?function(e){return re(e,o)}:/\s/.test(l)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!re(e)};while(n>0&&s(r.charAt(n-1)))--n;while(i<r.length&&s(r.charAt(i)))++i}return new Zi(me(e.line,n),me(e.line,i))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?H(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),yt(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==D()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Si((function(e,t){Jn(this,e,t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Xr(this)-this.display.barHeight,width:e.scrollWidth-Xr(this)-this.display.barWidth,clientHeight:_r(this),clientWidth:Yr(this)}},scrollIntoView:Si((function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:me(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?Qn(this,e):ti(this,e.from,e.to,e.margin)})),setSize:Si((function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&cn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Ti(r,i,"widget");break}++i})),this.curOp.forceUpdate=!0,yt(this,"refresh",this)})),operation:function(e){return xi(this,e)},refresh:Si((function(){var e=this.display.cachedTextHeight;ki(this),this.curOp.forceUpdate=!0,hn(this),Jn(this,this.doc.scrollLeft,this.doc.scrollTop),Gi(this),(null==e||Math.abs(e-Ln(this.display))>.5)&&On(this),yt(this,"refresh",this)})),swapDoc:Si((function(e){var t=this.doc;return t.cm=null,co(this,e),hn(this),this.display.input.reset(),Jn(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,Mr(this,"swapDoc",this,t),t})),getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Ct(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})}};function As(e,t,r,n,i){var o=t,l=r,s=ue(e,t.line);function a(){var n=t.line+r;return!(n<e.first||n>=e.first+e.size)&&(t=new me(n,t.ch,t.sticky),s=ue(e,n))}function u(n){var o;if(o=i?dt(e.cm,s,t,r):ht(s,t,r),null==o){if(n||!a())return!1;t=ft(i,e.cm,s,t.line,r)}else t=o;return!0}if("char"==n)u();else if("column"==n)u(!0);else if("word"==n||"group"==n)for(var c=null,h="group"==n,f=e.cm&&e.cm.getHelper(t,"wordChars"),d=!0;;d=!1){if(r<0&&!u(!d))break;var p=s.text.charAt(t.ch)||"\n",g=re(p,f)?"w":h&&"\n"==p?"n":!h||/\s/.test(p)?null:"p";if(!h||d||g||(g="s"),c&&c!=g){r<0&&(r=1,u(),t.sticky="after");break}if(g&&(c=g),r>0&&!u(!d))break}var v=Bo(e,t,o,l,!0);return be(o,v)&&(v.hitSide=!0),v}function Ws(e,t,r,n){var i,o,l=e.doc,s=t.left;if("page"==n){var a=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),u=Math.max(a-.5*Ln(e.display),3);i=(r>0?t.bottom:t.top)+r*u}else"line"==n&&(i=r>0?t.bottom+3:t.top-3);for(;;){if(o=wn(e,s,i),!o.outside)break;if(r<0?i<=0:i>=l.height){o.hitSide=!0;break}i+=5*r}return o}var Ds=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new R,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Hs(e,t){var r=Qr(e,t.line);if(!r||r.hidden)return null;var n=ue(e.doc,t.line),i=qr(r,n,t.line),o=ut(n,e.doc.direction),l="left";if(o){var s=st(o,t.ch);l=s%2?"right":"left"}var a=on(i.map,t.ch,l);return a.offset="right"==a.collapse?a.end:a.start,a}function Es(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function Ps(e,t){return t&&(e.bad=!0),e}function Fs(e,t,r,n,i){var o="",l=!1,s=e.doc.lineSeparator();function a(e){return function(t){return t.id==e}}function u(){l&&(o+=s,l=!1)}function c(e){e&&(u(),o+=e)}function h(t){if(1==t.nodeType){var r=t.getAttribute("cm-text");if(null!=r)return void c(r||t.textContent.replace(/\u200b/g,""));var o,f=t.getAttribute("cm-marker");if(f){var d=e.findMarks(me(n,0),me(i+1,0),a(+f));return void(d.length&&(o=d[0].find())&&c(ce(e.doc,o.from,o.to).join(s)))}if("false"==t.getAttribute("contenteditable"))return;var p=/^(pre|div|p)$/i.test(t.nodeName);p&&u();for(var g=0;g<t.childNodes.length;g++)h(t.childNodes[g]);p&&(l=!0)}else 3==t.nodeType&&c(t.nodeValue)}for(;;){if(h(t),t==r)break;t=t.nextSibling}return o}function Is(e,t,r){var n;if(t==e.display.lineDiv){if(n=e.display.lineDiv.childNodes[r],!n)return Ps(e.clipPos(me(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return zs(o,t,r)}}function zs(e,t,r){var n=e.text.firstChild,i=!1;if(!t||!W(n,t))return Ps(me(de(e.line),0),!0);if(t==n&&(i=!0,t=n.childNodes[r],r=0,!t)){var o=e.rest?$(e.rest):e.line;return Ps(me(de(o),o.text.length),i)}var l=3==t.nodeType?t:null,s=t;l||1!=t.childNodes.length||3!=t.firstChild.nodeType||(l=t.firstChild,r&&(r=l.nodeValue.length));while(s.parentNode!=n)s=s.parentNode;var a=e.measure,u=a.maps;function c(t,r,n){for(var i=-1;i<(u?u.length:0);i++)for(var o=i<0?a.map:u[i],l=0;l<o.length;l+=3){var s=o[l+2];if(s==t||s==r){var c=de(i<0?e.line:e.rest[i]),h=o[l]+n;return(n<0||s!=t)&&(h=o[l+(n?1:0)]),me(c,h)}}}var h=c(l,s,r);if(h)return Ps(h,i);for(var f=s.nextSibling,d=l?l.nodeValue.length-r:0;f;f=f.nextSibling){if(h=c(f,f.firstChild,0),h)return Ps(me(h.line,h.ch-d),i);d+=f.textContent.length}for(var p=s.previousSibling,g=r;p;p=p.previousSibling){if(h=c(p,p.firstChild,-1),h)return Ps(me(h.line,h.ch+g),i);g+=p.textContent.length}}Ds.prototype.init=function(e){var t=this,r=this,n=r.cm,i=r.div=e.lineDiv;function o(e){if(!bt(n,e)){if(n.somethingSelected())Cs({lineWise:!1,text:n.getSelections()}),"cut"==e.type&&n.replaceSelection("",null,"cut");else{if(!n.options.lineWiseCopyCut)return;var t=Ts(n);Cs({lineWise:!0,text:t.text}),"cut"==e.type&&n.operation((function(){n.setSelections(t.ranges,0,V),n.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var o=xs.text.join("\n");if(e.clipboardData.setData("Text",o),e.clipboardData.getData("Text")==o)return void e.preventDefault()}var l=Ns(),s=l.firstChild;n.display.lineSpace.insertBefore(l,n.display.lineSpace.firstChild),s.value=xs.text.join("\n");var a=document.activeElement;P(s),setTimeout((function(){n.display.lineSpace.removeChild(l),a.focus(),a==i&&r.showPrimarySelection()}),50)}}Ms(i,n.options.spellcheck),gt(i,"paste",(function(e){bt(n,e)||Ls(e,n)||s<=11&&setTimeout(Ci(n,(function(){return t.updateFromDOM()})),20)})),gt(i,"compositionstart",(function(e){t.composing={data:e.data,done:!1}})),gt(i,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data,done:!1})})),gt(i,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)})),gt(i,"touchstart",(function(){return r.forceCompositionEnd()})),gt(i,"input",(function(){t.composing||t.readFromDOMSoon()})),gt(i,"copy",o),gt(i,"cut",o)},Ds.prototype.prepareSelection=function(){var e=Hn(this.cm,!1);return e.focus=this.cm.state.focused,e},Ds.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Ds.prototype.showPrimarySelection=function(){var e=window.getSelection(),t=this.cm,n=t.doc.sel.primary(),i=n.from(),o=n.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var l=Is(t,e.anchorNode,e.anchorOffset),s=Is(t,e.focusNode,e.focusOffset);if(!l||l.bad||!s||s.bad||0!=ye(Ce(l,s),i)||0!=ye(xe(l,s),o)){var a=t.display.view,u=i.line>=t.display.viewFrom&&Hs(t,i)||{node:a[0].measure.map[2],offset:0},c=o.line<t.display.viewTo&&Hs(t,o);if(!c){var h=a[a.length-1].measure,f=h.maps?h.maps[h.maps.length-1]:h.map;c={node:f[f.length-1],offset:f[f.length-2]-f[f.length-3]}}if(u&&c){var d,p=e.rangeCount&&e.getRangeAt(0);try{d=k(u.node,u.offset,c.offset,c.node)}catch(g){}d&&(!r&&t.state.focused?(e.collapse(u.node,u.offset),d.collapsed||(e.removeAllRanges(),e.addRange(d))):(e.removeAllRanges(),e.addRange(d)),p&&null==e.anchorNode?e.addRange(p):r&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Ds.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},Ds.prototype.showMultipleSelections=function(e){N(this.cm.display.cursorDiv,e.cursors),N(this.cm.display.selectionDiv,e.selection)},Ds.prototype.rememberSelection=function(){var e=window.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Ds.prototype.selectionInEditor=function(){var e=window.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return W(this.div,t)},Ds.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Ds.prototype.blur=function(){this.div.blur()},Ds.prototype.getField=function(){return this.div},Ds.prototype.supportsTouch=function(){return!0},Ds.prototype.receivedFocus=function(){var e=this;function t(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,t))}this.selectionInEditor()?this.pollSelection():xi(this.cm,(function(){return e.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,t)},Ds.prototype.selectionChanged=function(){var e=window.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Ds.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=window.getSelection(),t=this.cm;if(v&&c&&this.cm.options.gutters.length&&Es(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=Is(t,e.anchorNode,e.anchorOffset),n=Is(t,e.focusNode,e.focusOffset);r&&n&&xi(t,(function(){Eo(t.doc,Qi(r,n),V),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)}))}}},Ds.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),l=o.from(),s=o.to();if(0==l.ch&&l.line>n.firstLine()&&(l=me(l.line-1,ue(n.doc,l.line-1).length)),s.ch==ue(n.doc,s.line).text.length&&s.line<n.lastLine()&&(s=me(s.line+1,0)),l.line<i.viewFrom||s.line>i.viewTo-1)return!1;l.line==i.viewFrom||0==(e=Wn(n,l.line))?(t=de(i.view[0].line),r=i.view[0].node):(t=de(i.view[e].line),r=i.view[e-1].node.nextSibling);var a,u,c=Wn(n,s.line);if(c==i.view.length-1?(a=i.viewTo-1,u=i.lineDiv.lastChild):(a=de(i.view[c+1].line)-1,u=i.view[c+1].node.previousSibling),!r)return!1;var h=n.doc.splitLines(Fs(n,r,u,t,a)),f=ce(n.doc,me(t,0),me(a,ue(n.doc,a).text.length));while(h.length>1&&f.length>1)if($(h)==$(f))h.pop(),f.pop(),a--;else{if(h[0]!=f[0])break;h.shift(),f.shift(),t++}var d=0,p=0,g=h[0],v=f[0],m=Math.min(g.length,v.length);while(d<m&&g.charCodeAt(d)==v.charCodeAt(d))++d;var y=$(h),b=$(f),w=Math.min(y.length-(1==h.length?d:0),b.length-(1==f.length?d:0));while(p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1))++p;if(1==h.length&&1==f.length&&t==l.line)while(d&&d>l.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1))d--,p++;h[h.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),h[0]=h[0].slice(d).replace(/\u200b+$/,"");var x=me(t,d),C=me(a,f.length?$(f).length-p:0);return h.length>1||h[0]||ye(x,C)?(qo(n.doc,h,x,C,"+input"),!0):void 0},Ds.prototype.ensurePolled=function(){this.forceCompositionEnd()},Ds.prototype.reset=function(){this.forceCompositionEnd()},Ds.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Ds.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()}),80))},Ds.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||xi(this.cm,(function(){return ki(e.cm)}))},Ds.prototype.setUneditable=function(e){e.contentEditable="false"},Ds.prototype.onKeyPress=function(e){0!=e.charCode&&(e.preventDefault(),this.cm.isReadOnly()||Ci(this.cm,Ss)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Ds.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Ds.prototype.onContextMenu=function(){},Ds.prototype.resetPosition=function(){},Ds.prototype.needsContentAttribute=!0;var Rs=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new R,this.inaccurateSelection=!1,this.hasSelection=!1,this.composing=null};function Bs(e,t){if(t=t?I(t):{},t.value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var r=D();t.autofocus=r==e||null!=e.getAttribute("autofocus")&&r==document.body}function n(){e.value=s.getValue()}var i;if(e.form&&(gt(e.form,"submit",n),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var l=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=l}}catch(a){}}t.finishInit=function(t){t.save=n,t.getTextArea=function(){return e},t.toTextArea=function(){t.toTextArea=isNaN,n(),e.parentNode.removeChild(t.getWrapperElement()),e.style.display="",e.form&&(mt(e.form,"submit",n),"function"==typeof e.form.submit&&(e.form.submit=i))}},e.style.display="none";var s=ms((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return s}function Gs(e){e.off=mt,e.on=gt,e.wheelEventPixels=_i,e.Doc=gl,e.splitLines=Et,e.countColumn=z,e.findColumn=X,e.isWordChar=te,e.Pass=U,e.signal=yt,e.Line=sr,e.changeEnd=eo,e.scrollbarModel=ci,e.Pos=me,e.cmpPos=ye,e.modes=Rt,e.mimeModes=Bt,e.resolveMode=Vt,e.getMode=Kt,e.modeExtensions=jt,e.extendMode=Xt,e.copyState=Yt,e.startState=$t,e.innerMode=_t,e.commands=zl,e.keyMap=Al,e.keyName=Pl,e.isModifierKey=El,e.lookupKey=Hl,e.normalizeKeyMap=Dl,e.StringStream=qt,e.SharedTextMarker=ul,e.TextMarker=sl,e.LineWidget=nl,e.e_preventDefault=St,e.e_stopPropagation=Lt,e.e_stop=Tt,e.addClass=H,e.contains=W,e.rmClass=T,e.keyNames=Tl}Rs.prototype.init=function(e){var t=this,r=this,n=this.cm,i=this.wrapper=Ns(),o=this.textarea=i.firstChild;function a(e){if(!bt(n,e)){if(n.somethingSelected())Cs({lineWise:!1,text:n.getSelections()}),r.inaccurateSelection&&(r.prevInput="",r.inaccurateSelection=!1,o.value=xs.text.join("\n"),P(o));else{if(!n.options.lineWiseCopyCut)return;var t=Ts(n);Cs({lineWise:!0,text:t.text}),"cut"==e.type?n.setSelections(t.ranges,null,V):(r.prevInput="",o.value=t.text.join("\n"),P(o))}"cut"==e.type&&(n.state.cutIncoming=!0)}}e.wrapper.insertBefore(i,e.wrapper.firstChild),g&&(o.style.width="0px"),gt(o,"input",(function(){l&&s>=9&&t.hasSelection&&(t.hasSelection=null),r.poll()})),gt(o,"paste",(function(e){bt(n,e)||Ls(e,n)||(n.state.pasteIncoming=!0,r.fastPoll())})),gt(o,"cut",a),gt(o,"copy",a),gt(e.scroller,"paste",(function(t){Ur(e,t)||bt(n,t)||(n.state.pasteIncoming=!0,r.focus())})),gt(e.lineSpace,"selectstart",(function(t){Ur(e,t)||St(t)})),gt(o,"compositionstart",(function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}})),gt(o,"compositionend",(function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)}))},Rs.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=Hn(e);if(e.options.moveInputWithCursor){var i=mn(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return n},Rs.prototype.showSelection=function(e){var t=this.cm,r=t.display;N(r.cursorDiv,e.cursors),N(r.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Rs.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t,r,n=this.cm,i=n.doc;if(n.somethingSelected()){this.prevInput="";var o=i.sel.primary();t=Ft&&(o.to().line-o.from().line>100||(r=n.getSelection()).length>1e3);var a=t?"-":r||n.getSelection();this.textarea.value=a,n.state.focused&&P(this.textarea),l&&s>=9&&(this.hasSelection=a)}else e||(this.prevInput=this.textarea.value="",l&&s>=9&&(this.hasSelection=null));this.inaccurateSelection=t}},Rs.prototype.getField=function(){return this.textarea},Rs.prototype.supportsTouch=function(){return!1},Rs.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!m||D()!=this.textarea))try{this.textarea.focus()}catch(e){}},Rs.prototype.blur=function(){this.textarea.blur()},Rs.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Rs.prototype.receivedFocus=function(){this.slowPoll()},Rs.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},Rs.prototype.fastPoll=function(){var e=!1,t=this;function r(){var n=t.poll();n||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,r))}t.pollingFast=!0,t.polling.set(20,r)},Rs.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||!t.state.focused||Pt(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(l&&s>=9&&this.hasSelection===i||y&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}var a=0,u=Math.min(n.length,i.length);while(a<u&&n.charCodeAt(a)==i.charCodeAt(a))++a;return xi(t,(function(){Ss(t,i.slice(a),n.length-a,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},Rs.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Rs.prototype.onKeyPress=function(){l&&s>=9&&(this.hasSelection=null),this.fastPoll()},Rs.prototype.onContextMenu=function(e){var t=this,r=t.cm,n=r.display,i=t.textarea,o=An(r,e),u=n.scroller.scrollTop;if(o&&!h){var c=r.options.resetSelectionOnContextMenu;c&&-1==r.doc.sel.contains(o)&&Ci(r,Eo)(r.doc,Qi(o),V);var f=i.style.cssText,d=t.wrapper.style.cssText;t.wrapper.style.cssText="position: absolute";var p,g=t.wrapper.getBoundingClientRect();if(i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-g.top-5)+"px; left: "+(e.clientX-g.left-5)+"px;\n      z-index: 1000; background: "+(l?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",a&&(p=window.scrollY),n.input.focus(),a&&window.scrollTo(null,p),n.input.reset(),r.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=!0,n.selForContextMenu=r.doc.sel,clearTimeout(n.detectingSelectAll),l&&s>=9&&m(),S){Tt(e);var v=function(){mt(window,"mouseup",v),setTimeout(y,20)};gt(window,"mouseup",v)}else setTimeout(y,50)}function m(){if(null!=i.selectionStart){var e=r.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,n.selForContextMenu=r.doc.sel}}function y(){if(t.contextMenuPending=!1,t.wrapper.style.cssText=d,i.style.cssText=f,l&&s<9&&n.scrollbars.setScrollTop(n.scroller.scrollTop=u),null!=i.selectionStart){(!l||l&&s<9)&&m();var e=0,o=function(){n.selForContextMenu==r.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Ci(r,Uo)(r):e++<10?n.detectingSelectAll=setTimeout(o,500):(n.selForContextMenu=null,n.input.reset())};n.detectingSelectAll=setTimeout(o,200)}}},Rs.prototype.readOnlyChanged=function(e){e||this.reset()},Rs.prototype.setUneditable=function(){},Rs.prototype.needsContentAttribute=!1,ds(ms),Os(ms);var Us="iter insert remove copy getEditor constructor".split(" ");for(var Vs in gl.prototype)gl.prototype.hasOwnProperty(Vs)&&B(Us,Vs)<0&&(ms.prototype[Vs]=function(e){return function(){return e.apply(this.doc,arguments)}}(gl.prototype[Vs]));return Ct(gl),ms.inputStyles={textarea:Rs,contenteditable:Ds},ms.defineMode=function(e){ms.defaults.mode||"null"==e||(ms.defaults.mode=e),Gt.apply(this,arguments)},ms.defineMIME=Ut,ms.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),ms.defineMIME("text/plain","null"),ms.defineExtension=function(e,t){ms.prototype[e]=t},ms.defineDocExtension=function(e,t){gl.prototype[e]=t},ms.fromTextArea=Bs,Gs(ms),ms.version="5.26.0",ms}))}}]);