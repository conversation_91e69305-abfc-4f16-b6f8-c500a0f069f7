<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">
    <bean id="jedisConnFactory"
          class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="usePool" value="true"/>
        <property name="hostName" value="${redis.host}"/>
        <property name="password" value="${redis.password}"/>
        <property name="port" value="${redis.port}"/>
    </bean>
    <!--<bean id="jedisConnFactory" class="cn.dahe.cas.auth.factory.CacheCloudRedisFactoryBean"/>-->

    <alias name="jedisConnFactory" alias="tokenConnectionFactory"/>
    <alias name="jedisConnFactory" alias="cmsConnFactory"/>

    <bean id="stringRedisSerializer" class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
    <bean id="jsonRedisSerializer"
          class="org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer"/>

    <!--<bean id="cmsConnFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">-->
    <!--<property name="hostName" value="${cms.redis.host}"/>-->
    <!--<property name="password" value="${cms.redis.password}"/>-->
    <!--<property name="port" value="${cms.redis.port}"/>-->
    <!--</bean>-->

    <!-- 系统内进行使用，hash key使用generic，value使用jdk进行序列化 -->
    <!-- redis template definition -->
    <bean id="redisTemplate"
          class="org.springframework.data.redis.core.RedisTemplate" primary="true">
        <property name="connectionFactory" ref="jedisConnFactory"/>
        <property name="keySerializer" ref="stringRedisSerializer"/>
        <property name="hashKeySerializer" ref="jsonRedisSerializer"/>
    </bean>

    <bean id="statisticsRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="jedisConnFactory"/>
        <property name="keySerializer" ref="stringRedisSerializer"/>
        <property name="valueSerializer" ref="stringRedisSerializer"/>
    </bean>

    <bean id="treeRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="cmsConnFactory"/>
        <property name="keySerializer" ref="stringRedisSerializer"/>
        <property name="defaultSerializer">
            <bean class="com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer"/>
        </property>
        <property name="hashKeySerializer" ref="stringRedisSerializer"/>
    </bean>
    <bean id="departmentTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="cmsConnFactory"/>
        <property name="defaultSerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="hashValueSerializer">
            <bean class="org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer">
                <constructor-arg value="cn.dahe.cas.auth.dto.SiteTreeDto"/>
            </bean>
        </property>
    </bean>
    <bean id="hashTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="jedisConnFactory"/>
        <property name="defaultSerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="hashValueSerializer">
            <bean class="org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer">
                <constructor-arg value="cn.dahe.cas.auth.dto.RoleDto"/>
                <property name="objectMapper">
                    <bean class="cn.dahe.cas.auth.serializer.SsoObjectMapper"/>
                </property>
            </bean>
        </property>
    </bean>

    <bean id="siteTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="cmsConnFactory"/>
        <property name="defaultSerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="hashValueSerializer">
            <bean class="org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer">
                <constructor-arg value="cn.dahe.cas.auth.dto.SiteDto"/>
                <property name="objectMapper">
                    <bean class="cn.dahe.cas.auth.serializer.SsoObjectMapper"/>
                </property>
            </bean>
        </property>
    </bean>
    <!--仅供token的生成使用-->
    <bean id="tokenRedisTemplate"
          class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="jedisConnFactory"/>
        <property name="keySerializer" ref="stringRedisSerializer"/>
        <property name="valueSerializer" ref="stringRedisSerializer"/>
    </bean>

    <!--对外提供redis信息共享,key使用string，value直接进行fastjson序列化-->
    <bean id="externalRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="jedisConnFactory"/>
        <property name="defaultSerializer" ref="jsonRedisSerializer"/>
        <property name="keySerializer" ref="stringRedisSerializer"/>
        <property name="hashKeySerializer" ref="jsonRedisSerializer"/>
    </bean>

    <bean id="stringRedisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">
        <property name="connectionFactory" ref="jedisConnFactory"/>
        <property name="valueSerializer">
            <bean class="org.springframework.data.redis.serializer.GenericToStringSerializer">
                <constructor-arg value="java.lang.Long"/>
            </bean>
        </property>
    </bean>

    <cache:annotation-driven cache-manager="springCacheManager" proxy-target-class="true"/>
    <bean id="springCacheManager" class="org.springframework.data.redis.cache.RedisCacheManager">
        <constructor-arg ref="redisTemplate"/>
        <property name="usePrefix" value="true"/>
        <property name="cachePrefix">
            <bean class="org.springframework.data.redis.cache.DefaultRedisCachePrefix">
                <constructor-arg value="${cache.prefix}"/>
            </bean>
        </property>
        <property name="defaultExpiration" value="#{60*60*24*30}"/>
        <property name="expires">
            <map key-type="java.lang.String" value-type="java.lang.Long">
                <entry key="#{T(cn.dahe.cas.auth.config.CacheKey).IP_INFO_KEY}" value="#{60*60*24*30}"/>
                <entry key="#{T(cn.dahe.cas.auth.config.CacheKey).TGT_AUTH_INFO_KEY}" value="#{60*60*24*30}"/>
                <entry key="#{T(cn.dahe.cas.auth.config.CacheKey).PHONE_INFO_KEY}" value="#{60*60*24*30}"/>
                <entry key="#{T(cn.dahe.cas.auth.config.CacheKey).WEATHER_INFO_KEY}" value="#{60*60*3}"/>
            </map>
        </property>
    </bean>
</beans>