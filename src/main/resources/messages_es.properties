#Author: <PERSON>, <PERSON>, <PERSON>, and <PERSON>

#Welcome Screen Messages

#
# Licensed to <PERSON>per<PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Â¡Felicidades por iniciar CAS correctamente! Para aprender cÃ³mo autenticar, por favor repase la configuraciÃ³n del gestor de configuraciÃ³n por defecto.
screen.welcome.security=Por razones de seguridad, Â¡por favor cierre su sesiÃ³n y su navegador web cuando haya terminado de acceder a los servicios que requieren autenticaciÃ³n!
screen.welcome.instructions=Introduzca su nombre de usuario y contraseÃ±a.
screen.welcome.label.netid=Nombre de <span class="accesskey">u</span>suario:
screen.welcome.label.netid.accesskey=u
screen.welcome.label.password=<span class="accesskey">C</span>ontraseÃ±a:
screen.welcome.label.password.accesskey=c
screen.welcome.label.warn=<span class="accesskey">A</span>visarme antes de abrir sesiÃ³n en otros sitios.
screen.welcome.label.warn.accesskey=a
screen.welcome.button.login=INICIAR SESIÃN
screen.welcome.button.clear=LIMPIAR

logo.title=ir a la pÃ¡gina principal de Apereo
copyright=Copyright &copy; 2005&ndash;2015 Apereo, Inc. Se reservan todos los derechos.
screen.capslock.on = Â¡La tecla BLOQ MAYÃS estÃ¡ activada!

# Blocked Errors Page
screen.blocked.header=Acceso denegado
screen.blocked.message=Ha introducido una contraseÃ±a equivocada para el usuario demasiadas veces. Se le ha restringido.

#Confirmation Screen Messages
screen.confirmation.message=Haga clic <a href="{0}">aquÃ­</a> para ir a la aplicaciÃ³n.

#Generic Success Screen Messages
screen.success.header=Inicio de sesiÃ³n exitoso
screen.success.success=Usted, {0}, ha iniciado con Ã©xito su sesiÃ³n en el Servicio de AutenticaciÃ³n Central.
screen.success.security=Por razones de seguridad, por favor cierre su sesiÃ³n y su navegador web cuando haya terminado de acceder a los servicios que requieren autenticaciÃ³n.

#Logout Screen Messages
screen.logout.header=Cierre de sesiÃ³n exitoso
screen.logout.success=Ha cerrado con Ã©xito su sesiÃ³n del Servicio de AutenticaciÃ³n Central.
screen.logout.security=Por razones de seguridad, cierre su navegador web.
screen.logout.redirect=El servicio desde el cual ha llegado ha proporcionado un <a href="{0}">enlace que puede seguir por hacer clic aquÃ­</a>.

screen.service.sso.error.header=ReautenticaciÃ³n requerida para acceder a este servicio
screen.service.sso.error.message=IntentÃ³ acceder a un servicio que requiere autenticaciÃ³n sin reautenticar. Por favor intente <a href="{0}">autenticar de nuevo</a>.

error.invalid.loginticket=No puede intentar reenviar un formulario que ya se ha enviado.
username.required=El nombre de usuario es un campo requerido.
password.required=La contraseÃ±a es un campo requerido.

# Authentication failure messages
authenticationFailure.AccountDisabledException=Se ha deshabilitado esta cuenta.
authenticationFailure.AccountLockedException=Se ha bloqueado esta cuenta.
authenticationFailure.CredentialExpiredException=Su contraseÃ±a ha caducado.
authenticationFailure.InvalidLoginLocationException=No puede iniciar sesiÃ³n desde esta estaciÃ³n de trabajo.
authenticationFailure.InvalidLoginTimeException=EstÃ¡ prohibido iniciar sesiÃ³n con su cuenta en este momento.
authenticationFailure.AccountNotFoundException=Credenciales invÃ¡lidas.
authenticationFailure.FailedLoginException=Credenciales invÃ¡lidas.
authenticationFailure.UNKNOWN=Credenciales invÃ¡lidas.

INVALID_REQUEST_PROXY=ambos de los parÃ¡metros 'pgt' y 'targetService' se requieren
INVALID_TICKET_SPEC=El tique fallÃ³ la especificaciÃ³n de validaciÃ³n. Los errores posibles pueden incluir intentar validar un tique de proxy mediante un validador de tiques de servicio, o no cumplir con la peticiÃ³n de renovaciÃ³n (renew true).
INVALID_REQUEST=ambos de los parÃ¡metros 'service' y 'ticket' se requieren
INVALID_TICKET=No se ha reconocido el tique ''{0}''
INVALID_SERVICE=El tique ''{0}'' no coincide con el servicio proporcionado. El servicio original era ''{1}'' y el servicio proporcionado era ''{2}''.
INVALID_PROXY_CALLBACK=La direcciÃ³n web de retrollamada de proxy ''{0}'' no se pudo autenticar.
UNAUTHORIZED_SERVICE_PROXY=El servicio proporcionado ''{0}'' no estÃ¡ autorizado a usar la autenticaciÃ³n de proxy CAS.

screen.service.error.header=AplicaciÃ³n no autorizada a usar CAS
service.not.authorized.missing.attr=No estÃ¡ autorizado a accedir a la aplicaciÃ³n porque a su cuenta \
le faltan privilegios que el servidor CAS requiere para autenticar a este servicio. Por favor, notifique a su soporte tÃ©cnico.
screen.service.error.message=La aplicaciÃ³n que usted ha intentado autenticar no estÃ¡ autorizada a usar CAS.
screen.service.empty.error.message=El registro de servicios del CAS estÃ¡ vacÃ­o y no tiene definiciones de servicio. \
Las aplicaciones que quieren autenticar con CAS deben ser explÃ­citamente definidas en el registro de servicios.

# Password policy
password.expiration.warning=Su contraseÃ±a caduca en {0} dÃ­as. Por favor <a href="{1}">cambie su contraseÃ±a</a> ahora.
password.expiration.loginsRemaining=Tiene {0} inicios de sesiÃ³n restantes antes que <strong>DEBE</strong> cambiar su contraseÃ±a.
screen.accountdisabled.heading=Se ha deshabilitado esta cuenta.
screen.accountdisabled.message=Por favor contacte al administrador de sistema para recobrar acceso.
screen.accountlocked.heading=Se ha bloqueado esta cuenta.
screen.accountlocked.message=Por favor contacte al administrador de sistema para recobrar acceso.
screen.expiredpass.heading=Su contraseÃ±a ha caducado.
screen.expiredpass.message=Por favor <a href="{0}">cambie su contraseÃ±a</a>.
screen.mustchangepass.heading=Debe cambiar su contraseÃ±a.
screen.mustchangepass.message=Por favor <a href="{0}">cambie su contraseÃ±a</a>.
screen.badhours.heading=EstÃ¡ prohibido iniciar sesiÃ³n con su cuenta en este momento.
screen.badhours.message=Por favor intente mÃ¡s tarde.
screen.badworkstation.heading=No puede iniciar sesiÃ³n desde esta estaciÃ³n de trabajo.
screen.badworkstation.message=Por favor contacte al administrador de sistema para recobrar acceso.

# OAuth
screen.oauth.confirm.header=AutorizaciÃ³n
screen.oauth.confirm.message=Â¿Quiere conceder acceso a su perfil completo a "{0}"?
screen.oauth.confirm.allow=Permitir

# Unavailable
screen.unavailable.heading=CAS no estÃ¡ disponible
screen.unavailable.message=Hubo un error al intentar cumplir con su peticiÃ³n. Por favor notifique a su soporte tÃ©cnico o intente otra vez.
