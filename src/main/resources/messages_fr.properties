#Welcome Screen Messages

#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=FÃ©licitations, votre serveur est en ligne ! Pour savoir comment vous authentifier, merci de regarder la mÃ©thode d'authentification dÃ©finie par dÃ©faut.
screen.welcome.security=Pour des raisons de sÃ©curitÃ©, veuillez vous dÃ©connecter et fermer votre navigateur lorsque vous avez fini d'accÃ©der aux services authentifiÃ©s.
screen.welcome.instructions=Entrez votre identifiant et votre mot de passe.
screen.welcome.label.netid=<span class="accesskey">I</span>dentifiant:
screen.welcome.label.netid.accesskey=i
screen.welcome.label.password=<span class="accesskey">M</span>ot de passe:
screen.welcome.label.password.accesskey=m
screen.welcome.label.publicstation=Je suis sur un ordinateur public.
screen.welcome.label.warn=<span class="accesskey">P</span>rÃ©venez-moi avant d'accÃ©der Ã  d'autres services.
screen.welcome.label.warn.accesskey=p
screen.welcome.button.login=SE CONNECTER
screen.welcome.button.clear=EFFACER
screen.welcome.label.loginwith=Ou authentifiez-vous avec :

screen.cookies.disabled.title=Cookies navigateur dÃ©sactivÃ©s
screen.cookies.disabled.message=Votre navigateur ne supporte pas les cookies. L'authentification centralisÃ©e NE FONCTIONNERA PAS.

screen.aup.button.accept=ACCEPTER
screen.aup.button.cancel=ANNULER

screen.nonsecure.title=Connexion non sÃ©curisÃ©e
screen.nonsecure.message=Vous accÃ©dez actuellement au serveur CAS via une connexion non sÃ©curisÃ©e. L'authentification centralisÃ©e NE FONCTIONNERA PAS. Pour faire fonctionner l'authentification centralisÃ©e, vous devez vous authentifier en HTTPS.

logo.title=allez Ã  la page d'accueil Apereo
copyright=Copyright &copy; 2005&ndash;2015 Apereo, Inc. Tous droits rÃ©servÃ©s.
screen.capslock.on = la touche Verr Maj est activÃ©e !

# Remember-Me Authentication
screen.rememberme.checkbox.title=Se souvenir de moi

# Blocked Errors Page
screen.blocked.header=AccÃ¨s non autorisÃ©
screen.blocked.message=Vous avez saisi un mauvais mot de passe trop de fois de suite. Vous avez Ã©tÃ© rejetÃ©.
AbstractAccessDecisionManager.accessDenied=Vous n'Ãªtes pas autorisÃ© Ã  accÃ©der Ã  cette ressource. Contactez votre administrateur CAS pour plus d'informations.

#Confirmation Screen Messages
screen.confirmation.message=Cliquez <a href="{0}">ici</a> pour accÃ©der au service.

#Generic Success Screen Messages
screen.success.header=Connexion rÃ©ussie
screen.success.success=Vous vous Ãªtes authentifiÃ©(e) auprÃ¨s du Service Central d'Authentification.
screen.success.security=Pour des raisons de sÃ©curitÃ©, veuillez vous dÃ©connecter et fermer votre navigateur lorsque vous avez fini d'accÃ©der aux services authentifiÃ©s.

#Logout Screen Messages
screen.logout.header=DÃ©connexion rÃ©ussie
screen.logout.success=Vous vous Ãªtes dÃ©connectÃ©(e) du Service Central d'Authentification.
screen.logout.security=Pour des raisons de sÃ©curitÃ©, veuillez fermer votre navigateur.
screen.logout.redirect=Le service duquel vous arrivez a fourni un <a href="{0}">lien que vous pouvez suivre en cliquant ici</a>.

screen.service.sso.error.header=Une nouvelle authentification est requise pour accÃ©der Ã  ce service.
screen.service.sso.error.message=Vous avez tentÃ© d'accÃ©der Ã  un service qui requiert une nouvelle authentification sans vous authentifier Ã  nouveau.  Veuillez <a href="{0}">vous authentifier de nouveau</a>.
screen.service.required.message=Vous avez tentÃ© de vous authentifier sans prÃ©ciser d'application cible. Merci de vÃ©rifier votre requÃªte et de recommencer.

error.invalid.loginticket=Vous ne pouvez pas re-soumettre un formulaire d'autentification qui a dÃ©jÃ  Ã©tÃ© soumis.
username.required=Vous devez entrer votre identifiant.
password.required=Vous devez entrer votre mot de passe.

# Authentication failure messages
authenticationFailure.AccountDisabledException=Votre compte a Ã©tÃ© dÃ©sactivÃ©.
authenticationFailure.AccountLockedException=Votre compte est bloquÃ©.
authenticationFailure.CredentialExpiredException=Votre mot de passe a expirÃ©.
authenticationFailure.InvalidLoginLocationException=Vous ne pouvez pas vous authentifier depuis cet ordinateur.
authenticationFailure.InvalidLoginTimeException=Vous ne pouvez pas vous authentifier pendant cette pÃ©riode.
authenticationFailure.AccountNotFoundException=Mauvais identifiant / mot de passe.
authenticationFailure.FailedLoginException=Mauvais identifiant / mot de passe.
authenticationFailure.UNKNOWN=Mauvais identifiant / mot de passe.

INVALID_REQUEST_PROXY=Les paramÃ¨tres 'pgt' et 'targetService' sont tous deux nÃ©cessaires
INVALID_TICKET_SPEC=La validation du ticket est impossible. Les raisons possibles peuvent Ãªtre la validation d'un Proxy Ticket sur une URL de validation de Service Ticket, ou une mauvaise requÃªte de type renew.
INVALID_REQUEST=Les paramÃ¨tres 'service' et 'ticket' sont tous deux nÃ©cessaires
INVALID_TICKET=Le ticket ''{0}'' est inconnu
INVALID_SERVICE=Le ticket ''{0}'' ne correspond pas au service demandÃ©. Le service original Ã©tait ''{1}'' et le service demandÃ© Ã©tait ''{2}''.
INVALID_PROXY_CALLBACK=L''url de rappel du proxy ''{0}'' n''a pu Ãªtre authentifiÃ©e.
UNAUTHORIZED_SERVICE_PROXY=Le service utilisÃ© ''{0}'' n''est pas autorisÃ© Ã  utiliser l''authentification proxy CAS.

screen.service.error.header=Application non autorisÃ©e Ã  utiliser CAS
service.not.authorized.missing.attr=Vous n'Ãªtes pas autorisÃ© Ã  accÃ©der Ã  cette application car votre compte \
n'a pas les privilÃ¨ges requis par le seveur CAS pour accÃ©der Ã  ce service. Merci de contacter votre support.
screen.service.error.message=L'application pour laquelle vous avez tentÃ© de vous authentifier n'est pas autorisÃ©e Ã  utiliser CAS.
screen.service.empty.error.message=Aucun service CAS n'a Ã©tÃ© dÃ©fini. \
Les applications qui veulent s'authentifier sur CAS doivent explicitement s'enregistrer dans la base des services.

# Password policy
password.expiration.warning=Votre mot de passe expire dans {0} jour(s). Merci de <a href="{1}">changer votre mot de passe</a> maintenant.
password.expiration.loginsRemaining=Il vous reste {0} authentification(s) avant de <strong>DEVOIR</strong> changer votre mot de passe.
screen.accountdisabled.heading=Ce compte a Ã©tÃ© dÃ©sactivÃ©.
screen.accountdisabled.message=Merci de contacter votre administrateur systÃ¨me pour rÃ©cupÃ©rer votre accÃ¨s.
screen.accountlocked.heading=Votre compte a Ã©tÃ© bloquÃ©.
screen.accountlocked.message=Merci de contacter votre administrateur pour le dÃ©bloquer.
screen.expiredpass.heading=Votre mot de passe a expirÃ©.
screen.expiredpass.message=Merci de <a href="{0}">changer votre mot de passe</a>.
screen.mustchangepass.heading=Vous devez changer votre mot de passe.
screen.mustchangepass.message=Merci de <a href="{0}">changer votre mot de passe</a>.
screen.badhours.heading=Vous ne pouvez pas vous authentifier durant cette plage horaire.
screen.badhours.message=Merci de rÃ©essayer plus tard.
screen.badworkstation.heading=Vous ne pouvez pas vous authentifier depuis cet ordinateur.
screen.badworkstation.message=Merci de contacter votre administrateur systÃ¨me pour rÃ©cupÃ©rer votre accÃ¨s.

# OAuth
screen.oauth.confirm.header=Autorisation
screen.oauth.confirm.message=Voulez-vous donner l'accÃ¨s de votre profil complet Ã  "{0}" ?
screen.oauth.confirm.allow=Autoriser

# Unavailable
screen.unavailable.heading=CAS est indisponible
screen.unavailable.message=Une erreur s'est produite lors du traitement de votre requÃªte. Merci de contacter votre support ou de rÃ©essayer.
