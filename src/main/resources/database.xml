<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <context:property-placeholder location="classpath:jdbc.properties,classpath:cas.properties"/>
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" primary="true">
        <property name="url" value="${jdbc_url}"/>
        <property name="username" value="${jdbc_username}"/>
        <property name="password" value="${jdbc_password}"/>
        <property name="filters" value="stat"/>
    </bean>
    <!--为审计提供的datasource-->
    <alias name="dataSource" alias="inspektrAuditTrailDataSource"/>
</beans>
