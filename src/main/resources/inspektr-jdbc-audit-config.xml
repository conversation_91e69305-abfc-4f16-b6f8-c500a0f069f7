<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <!--audit相关配置-->
    <alias name="dataSource" alias="inspektrAuditTrailDataSource"/>
    <bean id="inspektrAuditTransactionTemplate"
          class="org.springframework.transaction.support.TransactionTemplate"
          p:transactionManager-ref="transactionManager"
          p:isolationLevelName="ISOLATION_READ_COMMITTED"
          p:propagationBehaviorName="PROPAGATION_REQUIRED" />
    <bean id="auditCleanupCriteria"
          class="org.jasig.inspektr.audit.support.MaxAgeWhereClauseMatchCriteria"
          c:maxAgeDays="${cas.audit.max.agedays:180}" />

    <bean id="auditTrailManager"
          class="cn.dahe.cas.auth.cas.JpaAuditTrailManager"
          p:cleanupCriteria-ref="auditCleanupCriteria"
          c:transactionTemplate-ref="inspektrAuditTransactionTemplate"
          p:dataSource-ref="inspektrAuditTrailDataSource"/>
    <bean id="auditTrailManagementAspect" class="org.jasig.inspektr.audit.AuditTrailManagementAspect">
        <!-- String applicationCode -->
        <constructor-arg index="0" value="${cas.throttle.appcode}"/>

        <!-- PrincipalResolver auditablePrincipalResolver -->
        <constructor-arg index="1" ref="auditablePrincipalResolver"/>

        <!-- List<AuditTrailManager> auditTrailManagers -->
        <constructor-arg index="2">
            <list>
                <ref bean="auditTrailManager"/>
            </list>
        </constructor-arg>

        <!-- Map<String,AuditActionResolver> auditActionResolverMap -->
        <constructor-arg index="3">
            <map>
                <entry key="AUTHENTICATION_RESOLVER">
                    <ref bean="authenticationActionResolver"/>
                </entry>
                <entry key="CREATE_TICKET_GRANTING_TICKET_RESOLVER">
                    <ref bean="ticketCreationActionResolver"/>
                </entry>
                <entry key="DESTROY_TICKET_GRANTING_TICKET_RESOLVER">
                    <bean class="org.jasig.inspektr.audit.spi.support.DefaultAuditActionResolver"/>
                </entry>
                <entry key="GRANT_SERVICE_TICKET_RESOLVER">
                    <ref bean="ticketCreationActionResolver"/>
                </entry>
                <entry key="GRANT_PROXY_GRANTING_TICKET_RESOLVER">
                    <ref bean="ticketCreationActionResolver"/>
                </entry>
                <entry key="VALIDATE_SERVICE_TICKET_RESOLVER">
                    <ref bean="ticketValidationActionResolver"/>
                </entry>
                <entry key="DELETE_SERVICE_ACTION_RESOLVER">
                    <ref bean="deleteServiceActionResolver"/>
                </entry>
                <entry key="SAVE_SERVICE_ACTION_RESOLVER">
                    <ref bean="saveServiceActionResolver"/>
                </entry>
            </map>
        </constructor-arg>

        <!-- Map<String,AuditResourceResolver> auditResourceResolverMap -->
        <constructor-arg index="4">
            <map>
                <entry key="AUTHENTICATION_RESOURCE_RESOLVER">
                    <bean class="org.jasig.cas.audit.spi.CredentialsAsFirstParameterResourceResolver"/>
                </entry>
                <entry key="CREATE_TICKET_GRANTING_TICKET_RESOURCE_RESOLVER">
                    <ref bean="returnValueResourceResolver"/>
                </entry>
                <entry key="DESTROY_TICKET_GRANTING_TICKET_RESOURCE_RESOLVER">
                    <ref bean="ticketResourceResolver"/>
                </entry>
                <entry key="GRANT_SERVICE_TICKET_RESOURCE_RESOLVER">
                    <bean class="org.jasig.cas.audit.spi.ServiceResourceResolver"/>
                </entry>
                <entry key="GRANT_PROXY_GRANTING_TICKET_RESOURCE_RESOLVER">
                    <ref bean="returnValueResourceResolver"/>
                </entry>
                <entry key="VALIDATE_SERVICE_TICKET_RESOURCE_RESOLVER">
                    <ref bean="ticketResourceResolver"/>
                </entry>
                <entry key="DELETE_SERVICE_RESOURCE_RESOLVER">
                    <ref bean="deleteServiceResourceResolver"/>
                </entry>
                <entry key="SAVE_SERVICE_RESOURCE_RESOLVER">
                    <ref bean="saveServiceResourceResolver"/>
                </entry>
            </map>
        </constructor-arg>
    </bean>

    <bean id="saveServiceResourceResolver" class="org.jasig.inspektr.audit.spi.support.ParametersAsStringResourceResolver"/>

    <bean id="deleteServiceResourceResolver" class="org.jasig.inspektr.audit.spi.support.ParametersAsStringResourceResolver"/>

    <bean id="saveServiceActionResolver" class="org.jasig.inspektr.audit.spi.support.DefaultAuditActionResolver">
        <constructor-arg index="0" value="_SUCCEEDED"/>
        <constructor-arg index="1" value="_FAILED"/>
    </bean>

    <bean id="deleteServiceActionResolver" class="org.jasig.inspektr.audit.spi.support.ObjectCreationAuditActionResolver">
        <constructor-arg index="0" value="_SUCCEEDED"/>
        <constructor-arg index="1" value="_FAILED"/>
    </bean>

    <bean id="auditablePrincipalResolver" class="cn.dahe.cas.auth.cas.SsoTicketOrCredentialPrincipalResolver"
          c:centralAuthenticationService-ref="centralAuthenticationService"/>

    <bean id="authenticationActionResolver"
          class="org.jasig.inspektr.audit.spi.support.DefaultAuditActionResolver">
        <!-- String successSuffix -->
        <constructor-arg index="0" value="_SUCCESS"/>

        <!-- String failureSuffix -->
        <constructor-arg index="1" value="_FAILED"/>
    </bean>

    <bean id="ticketCreationActionResolver"
          class="org.jasig.inspektr.audit.spi.support.DefaultAuditActionResolver">
        <!-- String successSuffix -->
        <constructor-arg index="0" value="_CREATED"/>

        <!-- String failureSuffix -->
        <constructor-arg index="1" value="_NOT_CREATED"/>
    </bean>

    <bean id="ticketValidationActionResolver"
          class="org.jasig.inspektr.audit.spi.support.DefaultAuditActionResolver">
        <!-- String successSuffix -->
        <constructor-arg index="0" value="D"/>

        <!-- String failureSuffix -->
        <constructor-arg index="1" value="_FAILED"/>
    </bean>

    <bean id="returnValueResourceResolver"
          class="org.jasig.inspektr.audit.spi.support.ReturnValueAsStringResourceResolver"/>

    <bean id="ticketResourceResolver"
          class="org.jasig.cas.audit.spi.TicketAsFirstParameterResourceResolver"/>

</beans>
