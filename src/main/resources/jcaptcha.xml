<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <bean id="captchaService" class="com.octo.captcha.service.multitype.GenericManageableCaptchaService">
        <constructor-arg index="0" type="com.octo.captcha.engine.CaptchaEngine" ref="imageEngine"/>
        <constructor-arg type="int" index="1" value="180"/>
        <constructor-arg type="int" index="2" value="100000"/>
        <constructor-arg type="int" index="3" value="100000"/>
    </bean>
    <bean id="imageEngine" class="com.octo.captcha.engine.GenericCaptchaEngine">
        <constructor-arg index="0">
            <list>
                <ref bean="captchaFactory"/>
            </list>
        </constructor-arg>
    </bean>
    <bean id="captchaFactory" class="com.octo.captcha.image.gimpy.GimpyFactory">
        <constructor-arg>
            <ref bean="wordgen" />
        </constructor-arg>
        <constructor-arg>
            <ref bean="wordtoimage" />
        </constructor-arg>
    </bean>
    <bean id="wordgen" class="com.octo.captcha.component.word.wordgenerator.RandomWordGenerator">
        <constructor-arg>
            <value>23456789abcdefghijkmnpqrstuvwxyz</value>
        </constructor-arg>
    </bean>
    <bean id="wordtoimage" class="com.octo.captcha.component.image.wordtoimage.ComposedWordToImage">
        <constructor-arg index="0">
            <ref bean="fontGenRandom" />
        </constructor-arg>
        <constructor-arg index="1">
            <ref bean="backGenUni" />
        </constructor-arg>
        <constructor-arg index="2">
            <ref bean="decoratedPaster" />
        </constructor-arg>
    </bean>
    <bean id="fontGenRandom" class="com.octo.captcha.component.image.fontgenerator.RandomFontGenerator">
        <!--最小字体-->
        <constructor-arg index="0">
            <value>16</value>
        </constructor-arg>
        <!--最大字体-->
        <constructor-arg index="1">
            <value>22</value>
        </constructor-arg>
        <constructor-arg index="2">
            <list>
                <bean class="java.awt.Font">
                    <constructor-arg index="0">
                        <value>Arial</value>
                    </constructor-arg>
                    <constructor-arg index="1">
                        <value>0</value>
                    </constructor-arg>
                    <constructor-arg index="2">
                        <value>16</value>
                    </constructor-arg>
                </bean>
            </list>
        </constructor-arg>
    </bean>
    <bean id="backGenUni" class="com.octo.captcha.component.image.backgroundgenerator.UniColorBackgroundGenerator">
        <!--背景宽度-->
        <constructor-arg index="0">
            <value>80</value>
        </constructor-arg>
        <!--背景高度-->
        <constructor-arg index="1">
            <value>32</value>
        </constructor-arg>
        <!--背景颜色-->
        <constructor-arg index="2">
            <!--<value>#{T(java.awt.Color).CYAN}</value>-->
            <ref bean="backColor" />
        </constructor-arg>
    </bean>
    <bean id="decoratedPaster" class="com.octo.captcha.component.image.textpaster.DecoratedRandomTextPaster">
        <!--最大字符长度-->
        <constructor-arg type="java.lang.Integer" index="0">
            <value>4</value>
        </constructor-arg>
        <!--最小字符长度-->
        <constructor-arg type="java.lang.Integer" index="1">
            <value>4</value>
        </constructor-arg>
        <!--文本颜色-->
        <constructor-arg index="2">
            <ref bean="colorGen" />
        </constructor-arg>
        <!--文本混淆-->
        <constructor-arg index="3">
            <list>
                <!--<ref bean="baffleDecorator"/>-->
            </list>
        </constructor-arg>
    </bean>
    <bean id="baffleDecorator" class="com.octo.captcha.component.image.textpaster.textdecorator.BaffleTextDecorator">
        <constructor-arg type="java.lang.Integer" index="0">
            <value>1</value>
        </constructor-arg>
        <constructor-arg type="java.awt.Color" index="1">
            <ref bean="colorWrite" />
        </constructor-arg>
    </bean>

    <bean id="colorGen" class="com.octo.captcha.component.image.color.SingleColorGenerator">
        <constructor-arg type="java.awt.Color" index="0">
            <ref bean="colorDimGrey" />
        </constructor-arg>
    </bean>

    <bean id="backColor" class="java.awt.Color">
        <constructor-arg type="int" index="0">
            <value>227</value>
        </constructor-arg>
        <constructor-arg type="int" index="1">
            <value>237</value>
        </constructor-arg>
        <constructor-arg type="int" index="2">
            <value>241</value>
        </constructor-arg>
    </bean>

    <bean id="colorWrite" class="java.awt.Color">
        <constructor-arg type="int" index="0">
            <value>0</value>
        </constructor-arg>
        <constructor-arg type="int" index="1">
            <value>0</value>
        </constructor-arg>
        <constructor-arg type="int" index="2">
            <value>255</value>
        </constructor-arg>
    </bean>

    <bean id="colorDimGrey" class="java.awt.Color">
        <constructor-arg type="int" index="0">
            <value>16</value>
        </constructor-arg>
        <constructor-arg type="int" index="1">
            <value>16</value>
        </constructor-arg>
        <constructor-arg type="int" index="2">
            <value>16</value>
        </constructor-arg>
    </bean>
</beans>