#Author: <PERSON> "<PERSON><PERSON><PERSON>" <PERSON>
#Version $Revision$ $Date$
#Since 3.0.3

#Welcome Screen Messages

#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Proficiat met de succesvolle installatie van CAS!  Met de standaard "authentication handler" kan je ingeloggen als de gebruikersnaam gelijk is aan het wachtwoord. Je kan het nu proberen.
screen.welcome.security=Voor de veiligheid moet je uitloggen en je browser sluiten wanneer je geen toegang meer nodig hebt tot afgeschermde applicaties!
screen.welcome.instructions=Om verder te gaan dien je jezelf te authenticeren.
screen.welcome.label.netid.accesskey=g
screen.welcome.label.netid=<span class="accesskey">G</span>ebruikersnaam:
screen.welcome.label.password=<span class="accesskey">W</span>achtwoord:
screen.welcome.label.password.accesskey=w
screen.welcome.label.warn=<span class="accesskey">V</span>raag toestemming vooraleer me ingelogd door te sturen naar andere sites.
screen.welcome.label.warn.accesskey=v
screen.welcome.button.login=LOGIN
screen.welcome.button.clear=CLEAR

#Confirmation Screen Messages
screen.confirmation.message=<a href="{0}">Doorgaan naar de applicatie.</a>

#Generic Success Screen Messages
screen.success.header=Succesvol ingelogd.
screen.success.success=Je bent ingelogd bij de Central Authentication Service.
screen.success.security=Voor de veiligheid moet je uitloggen en je browser sluiten wanneer je geen toegang meer nodig hebt tot afgeschermde applicaties!

#Logout Screen Messages
screen.logout.header=Succesvol uitgelogd.
screen.logout.success=Je bent nu uitgelogd bij de Central Authentication Service.
screen.logout.security=Voor de veiligheid dien je je browser nu af te sluiten.
screen.logout.redirect=De applicatie waar je vandaan komt heeft <a href="{0}">deze link opgegeven die je kan volgen door hier te klikken</a>.

error.invalid.loginticket=Je mag geen formulier verzenden dat je al eens hebt verzonden.
username.required=Gelieve een gebruikersnaam in te vullen.
password.required=Gelieve een wachtwoord in te vullen.
error.authentication.credentials.bad=De combinatie van gebruikersnaam en wachtwoord was niet juist.
error.authentication.credentials.unsupported=De verstuurde identificatiegegevens worden niet ondersteund door CAS.

INVALID_REQUEST_PROXY='pgt' en 'targetService' zijn verplichte parameters.
INVALID_TICKET_SPEC=Het ticket kwam niet overeen met de specificatie voor validatie. Misschien probeer je een Proxy Ticket te valideren op de Service Ticket validator, of komt "renew true" niet overeen.
INVALID_REQUEST='service' en 'ticket' zijn verplichte parameters.
INVALID_TICKET=ticket ''{0}'' is niet gekend.
INVALID_SERVICE=ticket ''{0}'' komt niet overeen met de opgegeven service.

screen.service.error.header=Geen toegang.
screen.service.error.message=De applicatie waarvoor je toegang vroeg heeft geen toestemming om deze CAS te gebruiken.
