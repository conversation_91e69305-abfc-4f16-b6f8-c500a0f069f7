<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans.xsd">
    <!--pac4j相关配置-->
    <bean id="casConfig" class="org.pac4j.cas.config.CasConfiguration">
        <property name="loginUrl" value="${cas.server.login}"/>
        <property name="logoutHandler" ref="shiroSingleLogoutHandler"/>
    </bean>
    <bean id="logout" class="org.apache.shiro.web.filter.authc.LogoutFilter">
        <property name="redirectUrl" value="${cas.server}/logout?service=${cas.server}"/>
    </bean>

    <bean id="casClient" class="cn.dahe.cas.auth.cas.CasClient">
        <property name="callbackUrl" value="${cas.server}/callback"/>
        <property name="configuration" ref="casConfig"/>
    </bean>

    <bean id="callbackFilter" class="io.buji.pac4j.filter.CallbackFilter">
        <property name="defaultUrl" value="/home"/>
        <!--<property name="callbackLogic" ref="ssoCallbackLogin"/>-->
        <property name="config">
            <bean class="org.pac4j.core.config.Config">
                <constructor-arg ref="casClient"/>
            </bean>
        </property>
    </bean>

    <bean id="securityFilter" class="io.buji.pac4j.filter.SecurityFilter">
        <property name="clients" value="CasClient"/>
        <property name="config">
            <bean class="org.pac4j.core.config.Config">
                <constructor-arg ref="casClient"/>
            </bean>
        </property>
    </bean>
    <bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
        <property name="securityManager" ref="securityManager"/>
        <property name="loginUrl" value="/admin/user/logintip"/>
        <property name="filterChainDefinitions">
            <value>
                # some example chain definitions:
                /v2/api-docs/** = anon
                /swagger-resources/** = anon
                /swagger-resources/configuration/ui/** = anon
                /swagger-resources/configuration/security/** = anon
                /v2/api-docs-ext/** = anon
                /webjars/** = anon
                /swagger-resources/** = anon
                /swagger-ui.html = anon
                /doc.html = anon
                /admin/user/login = anon
                /admin/user/logintip = anon
                /front/user/info = anon
                /front/** = securityFilter
                /admin/** = securityFilter
                /druid/** = securityFilter,roles[admin]
                /monitoring/** = securityFilter,roles[admin]
                /home/<USER>
                /callback=callbackFilter
                /** = anon
                # more URL-to-FilterChain definitions here
            </value>
        </property>
    </bean>

    <!-- Define any javax.servlet.Filter beans you want anywhere in this application context.   -->
    <!-- They will automatically be acquired by the 'shiroFilter' bean above and made available -->
    <!-- to the 'filterChainDefinitions' property.  Or you can manually/explicitly add them     -->
    <!-- to the shiroFilter's 'filters' Map if desired. See its JavaDoc for more details.       -->
    <bean id="credentialsMatcher" class="org.apache.shiro.authc.credential.HashedCredentialsMatcher">
        <property name="storedCredentialsHexEncoded" value="false"/>
        <property name="hashAlgorithmName" value="md5"/>
        <property name="hashIterations" value="3"/>
    </bean>
    <bean id="jpaRealm" class="cn.dahe.cas.auth.realm.JpaRealm">
        <property name="credentialsMatcher" ref="credentialsMatcher"/>
    </bean>
    <bean id="smsRealm" class="cn.dahe.cas.auth.realm.SmsRealm">
    </bean>

    <bean id="sessionManager" class="org.apache.shiro.web.session.mgt.DefaultWebSessionManager">
        <property name="globalSessionTimeout" value="#{60*60*1000}"/>
        <property name="sessionIdCookie.name" value="sid"/>
        <property name="sessionDAO" ref="redisSessionDao"/>
        <property name="sessionValidationSchedulerEnabled" value="true"/>
        <property name="sessionValidationInterval" value="#{1000*60*30}"/>
    </bean>
    <bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
        <property name="realms">
            <list>
                <ref bean="smsRealm"/>
                <ref bean="ssoRealm"/>
            </list>
        </property>
        <property name="sessionManager" ref="sessionManager"/>
        <property name="cacheManager" ref="redisCacheManager"/>
        <!-- Single realm app.  If you have multiple realms, use the 'realms' property instead. -->
        <!-- By default the servlet container sessions will be used.  Uncomment this line
             to use shiro's native sessions (see the JavaDoc for more): -->
        <!-- <property name="sessionMode" value="native"/> -->
        <!-- 加入rememberMe的配置管理 -->
        <property name="rememberMeManager" ref="rememberMeManager" />
    </bean>

    <!-- rememberMe管理器   -->
    <bean id="rememberMeManager" class="org.apache.shiro.web.mgt.CookieRememberMeManager">
        <property name="cipherKey" value="#{T(cn.dahe.cas.auth.realm.MySymmetricCipherService).getCipherKey()}" />
        <property name="cookie" ref="rememberMeCookie" />
    </bean>

    <!-- rememberMe配置 -->
    <bean id="rememberMeCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
        <constructor-arg value="rememberMe" />
        <property name="httpOnly" value="true" />
        <!-- 默认记住7天（单位：秒） -->
        <property name="maxAge" value="604800" />
    </bean>
    <bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>

    <!-- Define the Shiro Realm implementation you want to use to connect to your back-end -->
    <!-- security datasource: -->
    <bean class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator"
          depends-on="lifecycleBeanPostProcessor"/>
    <bean class="org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor">
        <property name="securityManager" ref="securityManager"/>
    </bean>
</beans>
