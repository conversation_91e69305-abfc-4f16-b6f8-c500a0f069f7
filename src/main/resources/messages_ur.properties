#Author: <PERSON><PERSON><PERSON> (Rutgers University)
#Since 3.0.5

#Welcome Screen Messages

#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=CAS ko online lany par Mubark baad! Default Tasdeek karney wala aap ki tasdeek iss soorat main karay ga agar password wo hi hoo jo user name hay. Aiye, aur try ki jiyay.
screen.welcome.security=Security ki wajoohat ki bina par aap mehrbani farma kar apnay web browser say Log Out aur Exit zaroor ki jiyay jub aap aisi services isstamal kar chookay hoon jo tasdeek chahti hoon.
screen.welcome.instructions=Apni Apereo ki NetID aur Password enter ki jiyay.
screen.welcome.label.netid=<span class="accesskey">N</span>etID:
screen.welcome.label.netid.accesskey=n
screen.welcome.label.password=<span class="accesskey">P</span>assword:
screen.welcome.label.password.accesskey=p
screen.welcome.label.warn=Mujay doosri sites main login karnay say pahlay <span class="accesskey">K</span>habardar karain.
screen.welcome.label.warn.accesskey=k
screen.welcome.button.login=LOGIN
screen.welcome.button.clear=CLEAR

#Confirmation Screen Messages
screen.confirmation.message=<a href="{0}">Yahan Click</a> karain agar app application main dakhil hona chahtay hain.

#Generic Success Screen Messages
screen.success.header=Log In Kamyab
screen.success.success=Aap kamyabi say Centeral Authentication Service main login hoo chokay hain.
screen.success.security=Security ki wajoohat ki bina par jub aap aisi services isstamal kar chookay hoon jo tasdeek chahti hoon tou baraye mehrbani apnay web browser say Log Out aur Exit zaroor ki jiyay

#Logout Screen Messages
screen.logout.header=Logout Kamyab
screen.logout.success=Aap kamyabi say Centeral Authentication Service say logout hoo chokay hain.
screen.logout.security=Security ki wajoohat ki bina par apnay web browser say exit karain.
screen.logout.redirect=Aap jis service say aye hain oos nay aik <a href="{0}">link supply kia hay jissay aap agar chahain tou follow kar saktay hain</a>.


#Service Error Messages
screen.service.error.header=Bay Sanud Service
screen.service.error.message=Aap jiss service kay liay tasdeek ki kooshush kar rahay thay woo service CAS istamal karnay ki mijaz nahi.

error.invalid.loginticket=Aap oos form ko dobara arsaal karnay ki kooshsish nahi kar saktay joo aap pahly arsal kar chookay hoon.
username.required=Username ka khana por karna lazmi hay.
password.required=Password ka khana por karna lazmi hay.
error.authentication.credentials.bad=Aap ka mohya kia howa waseeka (parteet puter) ki tasdeek karna momkin nahi.
error.authentication.credentials.unsupported=Aap kay mohya kiay howay waseeka (parteet puter) ko CAS support nahi karta.

INVALID_REQUEST_PROXY='pgt' aur 'targetService' parameters doonon lazmi hain.
INVALID_TICKET_SPEC=Ticket toseek ki tasreeh par poora nahi utri. Momkin gultiyoon main shamil, hoo sakta hay kay proxy ticket ki toseek ki kooshish Service ticket kay toseek kaninda say ki gai hoo, yaa 'renew true request' say iss ki mitabkat na hooti hoo.
INVALID_REQUEST='service' aur 'ticket' parameters doonon lazmi hain.
INVALID_TICKET=ticket ''{0}'' ki shnakhat nahi hoo saki.
INVALID_SERVICE=ticket ''{0}'' ki mitabkat mohya karda service say nahi hoo saki.
