#Welcome Screen Messages

#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=\u0412\u0456\u0442\u0430\u0454\u043c\u043e \u0437 \u0443\u0441\u043f\u0456\u0448\u043d\u0438\u043c \u0437\u0430\u043f\u0443\u0441\u043a\u043e\u043c \u0441\u0438\u0441\u0442\u0435\u043c\u0438 CAS! "Authentication handler", \u0432\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0439 \u0437\u0430 \u0443\u043c\u043e\u0432\u0447\u0430\u043d\u043d\u044f\u043c, \u0432\u0438\u0440\u043e\u0431\u043b\u044f\u0454 \u0432\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u043d\u044f \u0430\u0432\u0442\u0435\u043d\u0442\u0438\u0447\u043d\u043e\u0441\u0442\u0456 \u0432 \u0442\u043e\u043c\u0443 \u0432\u0438\u043f\u0430\u0434\u043a\u0443 \u044f\u043a\u0449\u043e \u0456\u043c'\u044f \u043a\u043e\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430 \u0456 \u043f\u0430\u0440\u043e\u043b\u044c \u0441\u043f\u0456\u0432\u043f\u0430\u0434\u0430\u044e\u0442\u044c: \u0441\u043f\u0440\u043e\u0431\u0443\u0439\u0442\u0435 \u0441\u0438\u0441\u0442\u0435\u043c\u0443 CAS \u0432 \u0434\u0456\u0457.
screen.welcome.security=\u0417 \u043c\u0435\u0442\u043e\u044e \u043d\u0430\u0434\u0456\u0439\u043d\u043e\u0433\u043e \u0440\u0456\u0432\u043d\u044f \u0431\u0435\u0437\u043f\u0435\u043a\u0438, \u0431\u0443\u0434\u044c \u043b\u0430\u0441\u043a\u0430, \u0432\u0438\u0439\u0434\u0456\u0442\u044c \u0456\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u0438, \u0430 \u0442\u0430\u043a\u043e\u0436 \u0437\u0430\u043a\u0440\u0438\u0439\u0442\u0435 \u0431\u0440\u0430\u0443\u0437\u0435\u0440, \u0437\u0430\u043a\u0456\u043d\u0447\u0438\u0432\u0448\u0438 \u0434\u043e\u0441\u0442\u0443\u043f \u0434\u043e \u0441\u0435\u0440\u0432\u0456\u0441\u0443 \u044f\u043a\u0438\u0439 \u043f\u043e\u0442\u0440\u0435\u0431\u0443\u0454 \u0432\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u043d\u044f \u0441\u043f\u0440\u0430\u0432\u0436\u043d\u043e\u0441\u0442\u0456!
screen.welcome.instructions=\u0412\u0432\u0435\u0434\u0456\u0442\u044c \u043b\u043e\u0433\u0456\u043d \u0456 \u043f\u0430\u0440\u043e\u043b\u044c.
screen.welcome.label.netid=\u041b\u043e\u0433\u0456\u043d:
screen.welcome.label.netid.accesskey=n
screen.welcome.label.password=<span class="accesskey">\u041f</span>\u0430\u0440\u043e\u043b\u044c:
screen.welcome.label.password.accesskey=p
screen.welcome.label.warn=<span class="accesskey">\u041f</span>\u043e\u043f\u0435\u0440\u0435\u0434\u0438\u0442\u0438 \u043f\u0435\u0440\u0435\u0434 \u0432\u0445\u043e\u0434\u043e\u043c \u043d\u0430 \u0456\u043d\u0448\u0456 \u0441\u0430\u0439\u0442\u0438.
screen.welcome.label.warn.accesskey=\u043f
screen.welcome.button.login=\u0423\u0412\u0406\u0419\u0422\u0418
screen.welcome.button.clear=\u041e\u0427\u0418\u0421\u0422\u0418\u0422\u0418

#Confirmation Screen Messages
screen.confirmation.message=\u041d\u0430\u0442\u0438\u0441\u043d\u0456\u0442\u044c <a href="{0}?ticket={1}">\u043d\u0430 \u043f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f</a> \u0434\u043b\u044f \u0432\u0445\u043e\u0434\u0443 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443.

#Generic Success Screen Messages
screen.success.header=\u0412\u0445\u0456\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443 \u0443\u0441\u043f\u0456\u0448\u043d\u0438\u0439.
screen.success.success=\u0412\u0438 \u0443\u0441\u043f\u0456\u0448\u043d\u043e \u0443\u0432\u0456\u0439\u0448\u043b\u0438 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443 "Central Authentication Service".
screen.success.security=\u0417 \u043c\u0435\u0442\u043e\u044e \u043d\u0430\u0434\u0456\u0439\u043d\u043e\u0433\u043e \u0440\u0456\u0432\u043d\u044f \u0431\u0435\u0437\u043f\u0435\u043a\u0438, \u0431\u0443\u0434\u044c \u043b\u0430\u0441\u043a\u0430, \u0432\u0438\u0439\u0434\u0456\u0442\u044c \u0456\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u0438, \u0430 \u0442\u0430\u043a\u043e\u0436 \u0437\u0430\u043a\u0440\u0438\u0439\u0442\u0435 \u0431\u0440\u0430\u0443\u0437\u0435\u0440, \u0437\u0430\u043a\u0456\u043d\u0447\u0438\u0432\u0448\u0438 \u0434\u043e\u0441\u0442\u0443\u043f \u0434\u043e \u0441\u0435\u0440\u0432\u0456\u0441\u0443 \u044f\u043a\u0438\u0439 \u043f\u043e\u0442\u0440\u0435\u0431\u0443\u0454 \u0432\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u043d\u044f \u0441\u043f\u0440\u0430\u0432\u0436\u043d\u043e\u0441\u0442\u0456!

#Logout Screen Messages
screen.logout.header=\u0412\u0438\u0445\u0456\u0434 \u0456\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u0438 \u0443\u0441\u043f\u0456\u0448\u043d\u0438\u0439.
screen.logout.success=\u0412\u0438 \u0443\u0441\u043f\u0456\u0448\u043d\u043e \u0432\u0438\u0439\u0448\u043b\u0438 \u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u0438 "Central Authentication Service".
screen.logout.security=\u0417 \u043c\u0435\u0442\u043e\u044e \u043d\u0430\u0434\u0456\u0439\u043d\u043e\u0433\u043e \u0440\u0456\u0432\u043d\u044f \u0431\u0435\u0437\u043f\u0435\u043a\u0438, \u0437\u0430\u043a\u0440\u0438\u0439\u0442\u0435 \u0431\u0440\u0430\u0443\u0437\u0435\u0440.
screen.logout.redirect=\u0421\u0435\u0440\u0432\u0456\u0441, \u0434\u043e \u044f\u043a\u043e\u0433\u043e \u043d\u0435\u043e\u0431\u0445\u0456\u0434\u043d\u0438\u0439 \u0434\u043e\u0441\u0442\u0443\u043f, \u043d\u0430\u0434\u0430\u0432 <a href="{0}">\u043f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f \u0434\u043b\u044f \u0432\u0445\u043e\u0434\u0443</a>.

#Service Error Messages
screen.service.error.header=\u0421\u0435\u0440\u0432\u0456\u0441 \u0431\u0435\u0437 \u043f\u0440\u0430\u0432 \u0434\u043e\u0441\u0442\u0443\u043f\u0443.
screen.service.error.message=\u0421\u0435\u0440\u0432\u0456\u0441, \u0434\u043b\u044f \u044f\u043a\u043e\u0433\u043e \u0437\u0440\u043e\u0431\u043b\u0435\u043d\u0430 \u0441\u043f\u0440\u043e\u0431\u0430 \u043f\u0435\u0440\u0435\u0432\u0456\u0440\u043a\u0438 \u0430\u0432\u0442\u0435\u043d\u0442\u0438\u0447\u043d\u043e\u0441\u0442\u0456, \u043d\u0435 \u043c\u0430\u0454 \u0434\u043e\u0441\u0442\u0443\u043f\u0443 \u0434\u043e \u0441\u0438\u0441\u0442\u0435\u043c\u0438 CAS.


error.invalid.loginticket=\u0412\u0438 \u043d\u0435 \u043c\u043e\u0436\u0435\u0442\u0435 \u0432\u0456\u0434\u0456\u0441\u043b\u0430\u0442\u0438 \u0432\u0435\u0431-\u0444\u043e\u0440\u043c\u0443, \u044f\u043a\u0430 \u0432\u0436\u0435 \u0432\u0456\u0434\u0456\u0441\u043b\u0430\u043d\u0430.
username.required=\u0406\u043c'\u044f \u043a\u043e\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0447\u0430 - \u043e\u0431\u043e\u0432'\u044f\u0437\u043a\u043e\u0432\u0435 \u043f\u043e\u043b\u0435 \u0432\u0432\u0435\u0434\u0435\u043d\u043d\u044f.
password.required=\u041f\u0430\u0440\u043e\u043b\u044c - \u043e\u0431\u043e\u0432'\u044f\u0437\u043a\u043e\u0432\u0435 \u043f\u043e\u043b\u0435 \u0432\u0432\u0435\u0434\u0435\u043d\u043d\u044f.
error.authentication.credentials.bad=\u0421\u043f\u0440\u0430\u0432\u0436\u043d\u0456\u0441\u0442\u044c \u043d\u0430\u0434\u0430\u043d\u0438\u0445 \u0432\u0456\u0440\u0447\u0438\u0445 \u0434\u0430\u043d\u0438\u0445 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0431\u0443\u0442\u0438 \u043f\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0436\u0435\u043d\u0430.
error.authentication.credentials.unsupported=\u041d\u0430\u0434\u0430\u043d\u0456 \u0432\u0456\u0440\u0447\u0456 \u0434\u0430\u043d\u0456 \u043d\u0435 \u043f\u0456\u0434\u0442\u0440\u0438\u043c\u0443\u044e\u0442\u044c\u0441\u044f \u0441\u0438\u0441\u0442\u0435\u043c\u043e\u044e CAS.

INVALID_REQUEST_PROXY=\u041e\u0431\u0438\u0434\u0432\u0430 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u0430 'pgt' \u0456 'targetService' \u043e\u0431\u043e\u0432'\u044f\u0437\u043a\u043e\u0432\u0456.
INVALID_TICKET_SPEC="Ticket" \u043d\u0435 \u043f\u0440\u043e\u0439\u0448\u043e\u0432 \u0443\u0441\u043f\u0456\u0448\u043d\u0443 \u043f\u0435\u0440\u0435\u0432\u0456\u0440\u043a\u0443 \u0434\u0456\u0439\u0441\u043d\u043e\u0441\u0442\u0456. \u041c\u043e\u0436\u043b\u0438\u0432\u0456 \u0434\u0436\u0435\u0440\u0435\u043b\u0430 \u043f\u043e\u043c\u0438\u043b\u043e\u043a \u043c\u043e\u0436\u0443\u0442\u044c \u0432\u043a\u043b\u044e\u0447\u0430\u0442\u0438 \u0441\u043f\u0440\u043e\u0431\u0443 \u043f\u0435\u0440\u0435\u0432\u0456\u0440\u043a\u0438 \u0434\u0435\u0456\u0441\u0442\u0432\u0456\u0442\u0435\u043b\u044c\u043d\u043e\u0441\u0442\u0456 "Proxy Ticket" \u0437\u0430 \u0434\u043e\u043f\u043e\u043c\u043e\u0433\u043e\u044e "Service Ticket validator" \u0430\u0431\u043e \u043d\u0435 \u0432\u0456\u0434\u043f\u043e\u0432\u0456\u0434\u043d\u0456\u0441\u0442\u044c \u0432\u0438\u043a\u043e\u043d\u0430\u043d\u043d\u044f \u0437 \u0432\u0438\u043c\u043e\u0433\u043e\u044e "renew request: true".
INVALID_REQUEST=\u041e\u0431\u0438\u0434\u0432\u0430 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u0430 'service' \u0456 'ticket' \u043e\u0431\u044f\u0437\u0430\u0442\u0435\u043b\u044c\u043d\u044b.
INVALID_TICKET="ticket" ''{0}'' \u043d\u0435 \u0440\u043e\u0437\u043f\u0456\u0437\u043d\u0430\u043d\u043e.
INVALID_SERVICE="ticket" ''{0}'' \u043d\u0435 \u0432\u0456\u0434\u043f\u043e\u0432\u0456\u0434\u0430\u0454 \u043d\u0430\u0434\u0430\u043d\u043e\u043c\u0443 \u0441\u0435\u0440\u0432\u0456\u0441\u0443.
management.services.link.logout=\u0412\u0438\u0439\u0442\u0438
management.services.title=\u0423\u043f\u0440\u0430\u0432\u043b\u0456\u043d\u043d\u044f \u0441\u0435\u0440\u0432\u0456\u0441\u0430\u043c\u0438
management.services.status.deleted={0} \u0443\u0441\u043f\u0456\u0448\u043d\u043e \u0432\u0438\u0434\u0430\u043b\u0435\u043d\u0438\u0439.
management.services.service.warn=CAS \u043f\u0440\u0430\u0446\u044e\u0454 \u0432 "\u0432\u0456\u0434\u043a\u0440\u0438\u0442\u043e\u043c\u0443" \u0440\u0435\u0436\u0438\u043c\u0456, \u0442\u043e\u043c\u0443 \u0449\u043e \u0436\u043e\u0434\u0435\u043d \u0441\u0435\u0440\u0432\u0456\u0441 \u043d\u0435 \u043d\u0430\u043b\u0430\u0448\u0442\u043e\u0432\u0430\u043d\u0438\u0439 \u0437\u0430 \u0434\u043e\u043f\u043e\u043c\u043e\u0433\u043e\u044e \u0446\u0456\u0454\u0457 \u0443\u0442\u0438\u043b\u0456\u0442\u0438. \u041f\u0456\u0441\u043b\u044f \u043d\u0430\u043b\u0430\u0448\u0442\u0443\u0432\u0430\u043d\u043d\u044f, CAS \u0431\u0456\u043b\u044c\u0448 \u043d\u0435 \u0431\u0443\u0434\u0435 \u0444\u0443\u043d\u043a\u0446\u0456\u043e\u043d\u0443\u0432\u0430\u0442\u0438 \u0443 \u0432\u0456\u0434\u043a\u0440\u0438\u0442\u043e\u043c\u0443 \u0440\u0435\u0436\u0438\u043c\u0456, \u0442\u043e\u043c\u0443 \u0432\u0430\u043c \u043f\u043e\u0442\u0440\u0456\u0431\u043d\u043e \u0431\u0443\u0434\u0435 \u0437\u0430\u0440\u0435\u0454\u0441\u0442\u0440\u0443\u0432\u0430\u0442\u0438 \u0442\u0443\u0442 \u0443\u0441\u0456 \u0434\u043e\u0434\u0430\u0442\u043a\u0438, \u044f\u043a\u0456 \u0431\u0443\u0434\u0443\u0442\u044c \u0432\u0438\u043a\u043e\u0440\u0438\u0441\u0442\u043e\u0432\u0443\u0432\u0430\u0442\u0438 CAS \u0434\u043b\u044f \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u0430\u0446\u0456\u0457. \u042f\u043a\u0449\u043e \u0432\u0438 \u043f\u043b\u0430\u043d\u0443\u0454\u0442\u0435 \u0446\u0435 \u0432\u0438\u043a\u043e\u0440\u0438\u0441\u0442\u043e\u0432\u0443\u0432\u0430\u0442\u0438, \u0441\u043f\u043e\u0447\u0430\u0442\u043a\u0443 \u043f\u043e\u0442\u0440\u0456\u0431\u043d\u043e \u0414\u041e\u0414\u0410\u0422\u0418 \u0421\u0410\u041c \u0421\u0415\u0420\u0412\u0406\u0421. URL \u0441\u0435\u0440\u0432\u0456\u0441\u0443 \u0434\u043b\u044f \u0443\u043f\u0440\u0430\u0432\u043b\u0456\u043d\u043d\u044f <strong>"{0}"</strong>.
management.services.add.button.cancel=\u0421\u043a\u0430\u0441\u0443\u0432\u0430\u043d\u043d\u044f
management.services.add.button.save=\u0417\u0431\u0435\u0440\u0435\u0433\u0442\u0438 \u0437\u043c\u0456\u043d\u0438
management.services.add.instructions=\u0411\u0443\u0434\u044c \u043b\u0430\u0441\u043a\u0430 \u043f\u0435\u0440\u0435\u043a\u043e\u043d\u0430\u0439\u0442\u0435\u0441\u044f, \u0437\u0431\u0435\u0440\u0435\u0436\u0435\u043d\u0456 \u0447\u0438 \u0437\u043c\u0456\u043d\u0438, \u043d\u0430\u0442\u0438\u0441\u043d\u0443\u0432\u0448\u0438 \u043d\u0430 \u043a\u043d\u043e\u043f\u043a\u0443 "\u0417\u0431\u0435\u0440\u0435\u0433\u0442\u0438 \u0437\u043c\u0456\u043d\u0438" \u0432\u043d\u0438\u0437\u0443 \u0441\u0442\u043e\u0440\u0456\u043d\u043a\u0438
management.services.add.property.attributes=\u0410\u0442\u0442\u0440\u0456\u0431\u0443\u0442\u0438
management.services.add.property.description=\u041e\u043f\u0438\u0441
management.services.add.property.evaluationOrder=\u041f\u043e\u0440\u044f\u0434\u043e\u043a
management.services.add.property.ignoreAttributes=\u041d\u0435 \u0432\u0438\u043a\u043e\u0440\u0438\u0441\u0442\u043e\u0432\u0443\u0432\u0430\u0442\u0438 \u043a\u0435\u0440\u0443\u0432\u0430\u043d\u043d\u044f \u0430\u0442\u0442\u0440\u0456\u0431\u0443\u0442\u043e\u0432
management.services.add.property.name=\u0406\u043c'\u044f
management.services.add.property.serviceUrl=URL \u0441\u0435\u0440\u0432\u0456\u0441\u0443
management.services.add.property.serviceUrl.instructions=\u0412\u0438\u043a\u043e\u0440\u0438\u0441\u0442\u043e\u0432\u0443\u0439\u0442\u0435 \u0440\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u0456 \u0432\u0438\u0440\u0430\u0437\u0438 \u0432 \u0441\u0442\u0438\u043b\u0456 Ant
management.services.add.property.status=\u0421\u0442\u0430\u0442\u0443\u0441
management.services.add.property.status.allowedToProxy=\u0414\u043e\u0437\u0432\u043e\u043b\u0438\u0442\u0438 \u043f\u0440\u043e\u043a\u0441\u0456\u0440\u043e\u0432\u0430\u043d\u0456\u0435
management.services.add.property.status.anonymousAccess=\u0410\u043d\u043e\u043d\u0456\u043c\u043d\u0438\u0439 \u0434\u043e\u0441\u0442\u0443\u043f
management.services.add.property.status.enabled=\u0410\u043a\u0442\u0438\u0432\u043d\u043e
management.services.add.property.status.ssoParticipant=\u0423\u0447\u0430\u0441\u043d\u0438\u043a SSO
management.services.add.property.themeName=\u0406\u043c'\u044f \u0442\u0435\u043c\u0438
management.services.manage.action.delete=\u0432\u0438\u0434\u0430\u043b\u0438\u0442\u0438
management.services.manage.action.edit=\u0440\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438
management.services.manage.label.allowedToProxy=\u0414\u043e\u0437\u0432\u043e\u043b\u0438\u0442\u0438 \u043f\u0440\u043e\u043a\u0441\u0456\u0440\u043e\u0432\u0430\u043d\u0456\u0435
management.services.manage.label.enabled=\u0412\u043a\u043b\u044e\u0447\u0435\u043d\u043e
management.services.manage.label.name=\u041d\u0430\u0437\u0432\u0430 \u0441\u0435\u0440\u0432\u0456\u0441\u0443
management.services.manage.label.serviceUrl=URL \u0441\u0435\u0440\u0432\u0456\u0441\u0443
management.services.manage.label.ssoParticipant=\u0423\u0447\u0430\u0441\u043d\u0438\u043a SSO
management.services.status.notdeleted=\u0421\u0435\u0440\u0432\u0456\u0441 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0431\u0443\u0442\u0438 \u0432\u0438\u0434\u0430\u043b\u0435\u043d\u0438\u0439.
manageServiceView=\u0423\u043f\u0440\u0430\u0432\u043b\u0456\u043d\u043d\u044f \u0441\u0435\u0440\u0432\u0456\u0441\u0430\u043c\u0438
registeredService.serviceId.exists=\u0421\u0435\u0440\u0432\u0456\u0441 \u0437 \u0442\u0430\u043a\u0438\u043c URL \u0432\u0436\u0435 \u0456\u0441\u043d\u0443\u0454
screen.blocked.header=\u0414\u043e\u0441\u0442\u0443\u043f \u0437\u0430\u0431\u043e\u0440\u043e\u043d\u0435\u043d\u043e
screen.blocked.message=\u0412\u0438 \u0437\u0430\u043d\u0430\u0434\u0442\u043e \u0431\u0430\u0433\u0430\u0442\u043e \u0440\u0430\u0437\u0456\u0432 \u0432\u0432\u043e\u0434\u0438\u043b\u0438 \u043d\u0435\u0432\u0456\u0440\u043d\u0438\u0439 \u043f\u0430\u0440\u043e\u043b\u044c. \u0412\u0430\u0448 \u0430\u043a\u0430\u0443\u043d\u0442 \u0437\u0430\u0431\u043b\u043e\u043a\u043e\u0432\u0430\u043d\u0438\u0439.
screen.service.sso.error.header=\u0414\u043b\u044f \u0434\u043e\u0441\u0442\u0443\u043f\u0443 \u0434\u043e \u0441\u0435\u0440\u0432\u0456\u0441\u0443 \u043f\u043e\u0442\u0440\u0456\u0431\u043d\u0430 \u043f\u043e\u0432\u0442\u043e\u0440\u043d\u0430 \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u0430\u0446\u0456\u044f
screen.service.sso.error.message=\u0412\u0438 \u043d\u0430\u043c\u0430\u0433\u0430\u0454\u0442\u0435\u0441\u044f \u043e\u0442\u0440\u0438\u043c\u0430\u0442\u0438 \u0434\u043e\u0441\u0442\u0443\u043f \u0434\u043e \u0441\u0435\u0440\u0432\u0456\u0441\u0443, \u044f\u043a\u0438\u0439 \u0432\u0438\u043c\u0430\u0433\u0430\u0454 \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u0430\u0446\u0456\u044e \u0431\u0435\u0437 \u043f\u043e\u0432\u0442\u043e\u0440\u0456\u0432 \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u0430\u0446\u0456\u0457. \u0421\u043f\u0440\u043e\u0431\u0443\u0439\u0442\u0435 <a href="{0}">\u0430\u0432\u0442\u043e\u0440\u0438\u0437\u0443\u0432\u0430\u0442\u0438\u0441\u044f \u0437\u043d\u043e\u0432\u0443</a>.
addServiceView=\u0414\u043e\u0434\u0430\u0442\u0438 \u043d\u043e\u0432\u0438\u0439 \u0441\u0435\u0440\u0432\u0456\u0441
application.errors.global=\u0411\u0443\u0434\u044c \u043b\u0430\u0441\u043a\u0430, \u0432\u0438\u043f\u0440\u0430\u0432\u0442\u0435 \u043d\u0430\u0432\u0435\u0434\u0435\u043d\u0456 \u043d\u0438\u0436\u0447\u0435 \u043f\u043e\u043c\u0438\u043b\u043a\u0438
application.title=Jasig Central Authentication Service
editServiceView=\u0420\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u0441\u0435\u0440\u0432\u0456\u0441
viewStatisticsView=\u041f\u043e\u0434\u0438\u0432\u0438\u0442\u0438\u0441\u044f \u0441\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043a\u0443
