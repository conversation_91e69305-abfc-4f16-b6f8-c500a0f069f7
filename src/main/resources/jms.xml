<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="logMqConsumer" class="org.apache.rocketmq.client.consumer.DefaultMQPushConsumer">
        <property name="namesrvAddr" value="${rocketmq.uri}"/>
        <property name="messageListener" ref="logListener"/>
        <property name="consumerGroup" value="t1"/>
    </bean>

    <bean id="logMqProducer" class="org.apache.rocketmq.client.producer.DefaultMQProducer">
        <property name="namesrvAddr" value="${rocketmq.uri}"/>
        <property name="producerGroup" value="t1"/>
    </bean>

    <bean id="roleMqProducer" class="org.apache.rocketmq.client.producer.DefaultMQProducer">
        <property name="namesrvAddr" value="${rocketmq.uri}"/>
        <property name="producerGroup" value="t1_role"/>
    </bean>

    <bean id="roleMqConsumer" class="org.apache.rocketmq.client.consumer.DefaultMQPushConsumer">
        <property name="namesrvAddr" value="${rocketmq.uri}"/>
        <property name="messageListener" ref="roleListener"/>
        <property name="consumerGroup" value="t1_role"/>
    </bean>
</beans>