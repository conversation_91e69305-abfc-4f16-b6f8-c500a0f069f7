#Author: <PERSON><PERSON><PERSON> <<EMAIL>>
#Version: $Revision$ $Date$
#Since: 3.1

#Welcome Screen Messages

#
# Licensed to <PERSON>per<PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=\u304a\u3081\u3067\u3068\u3046\u3054\u3056\u3044\u307e\u3059! CAS \u3092\u30aa\u30f3\u30e9\u30a4\u30f3\u306b\u3059\u308b\u3053\u3068\u304c\u3067\u304d\u307e\u3057\u305f\uff0e\u30c7\u30d5\u30a9\u30eb\u30c8\u306e\u8a8d\u8a3c\u30cf\u30f3\u30c9\u30e9\u3067\u306f\uff0c\u30e6\u30fc\u30b6\u540d\u3068\u30d1\u30b9\u30ef\u30fc\u30c9\u304c\u540c\u3058\u3068\u304d\u306b\u8a8d\u8a3c\u3055\u308c\u307e\u3059\uff0e\u305c\u3072\uff0c\u304a\u8a66\u3057\u304f\u3060\u3055\u3044\uff0e
screen.welcome.security=\u30bb\u30ad\u30e5\u30ea\u30c6\u30a3\u4e0a\u306e\u7406\u7531\u304b\u3089\uff0c\u8a8d\u8a3c\u304c\u5fc5\u8981\u306a\u30b5\u30fc\u30d3\u30b9\u306e\u30a2\u30af\u30bb\u30b9\u7d42\u4e86\u6642\u306b\u306f\uff0c\u30a6\u30a7\u30d6\u30d6\u30e9\u30a6\u30b6\u3092\u30ed\u30b0\u30a2\u30a6\u30c8\u3057\uff0c\u7d42\u4e86\u3057\u3066\u304f\u3060\u3055\u3044\uff0e
screen.welcome.instructions=\u30cd\u30c3\u30c8ID \u304a\u3088\u3073\u30d1\u30b9\u30ef\u30fc\u30c9\u3092\u5165\u529b\u3057\u3066\u304f\u3060\u3055\u3044
screen.welcome.label.netid=\u30cd\u30c3\u30c8ID:
screen.welcome.label.netid.accesskey=n
screen.welcome.label.password=\u30d1\u30b9\u30ef\u30fc\u30c9:
screen.welcome.label.password.accesskey=p
screen.welcome.label.warn=\u4ed6\u306e\u30b5\u30a4\u30c8\u306b\u30ed\u30b0\u30a4\u30f3\u3059\u308b\u524d\u306b\u8b66\u544a\u3092\u51fa\u3059\uff0e
screen.welcome.label.warn.accesskey=w
screen.welcome.button.login=\u30ed\u30b0\u30a4\u30f3
screen.welcome.button.clear=\u30af\u30ea\u30a2

#Confirmation Screen Messages
screen.confirmation.message=<a href="{0}">\u3053\u3053</a>\u3092\u30af\u30ea\u30c3\u30af\u3057\u3066\u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u306b\u79fb\u52d5\u3057\u307e\u3059\uff0e

#Generic Success Screen Messages
screen.success.header=\u30ed\u30b0\u30a4\u30f3\u3057\u307e\u3057\u305f
screen.success.success=Central Authentication Service \u306b\u30ed\u30b0\u30a4\u30f3\u3067\u304d\u307e\u3057\u305f\uff0e
screen.success.security=\u30bb\u30ad\u30e5\u30ea\u30c6\u30a3\u4e0a\u306e\u7406\u7531\u304b\u3089\uff0c\u8a8d\u8a3c\u304c\u5fc5\u8981\u306a\u30b5\u30fc\u30d3\u30b9\u306e\u30a2\u30af\u30bb\u30b9\u7d42\u4e86\u6642\u306b\u306f\uff0c\u30a6\u30a7\u30d6\u30d6\u30e9\u30a6\u30b6\u3092\u30ed\u30b0\u30a2\u30a6\u30c8\u3057\uff0c\u7d42\u4e86\u3057\u3066\u304f\u3060\u3055\u3044\uff0e

#Logout Screen Messages
screen.logout.header=\u30ed\u30b0\u30a2\u30a6\u30c8\u3057\u307e\u3057\u305f
screen.logout.success=Central Authentication Service \u3092\u30ed\u30b0\u30a2\u30a6\u30c8\u3067\u304d\u307e\u3057\u305f\uff0e
screen.logout.security=\u30bb\u30ad\u30e5\u30ea\u30c6\u30a3\u4e0a\u306e\u7406\u7531\u304b\u3089\uff0c\u30a6\u30a7\u30d6\u30d6\u30e9\u30a6\u30b6\u3092\u7d42\u4e86\u3057\u3066\u304f\u3060\u3055\u3044\uff0e
screen.logout.redirect=\u3042\u306a\u305f\u304c\u30a2\u30af\u30bb\u30b9\u3057\u305f\u30b5\u30fc\u30d3\u30b9\u306b\u3088\u308a\u63d0\u4f9b\u3055\u308c\u305f\u30ea\u30f3\u30af\u306b\u30a2\u30af\u30bb\u30b9\u3059\u308b\u3068\u304d\u306f<a href="{0}">\u3053\u3053</a>\u3092\u30af\u30ea\u30c3\u30af\u3057\u307e\u3059\uff0e

#Service Error Messages
screen.service.error.header=\u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u306f CAS \u3092\u4f7f\u3046\u6a29\u9650\u304c\u3042\u308a\u307e\u305b\u3093
screen.service.error.message=\u8a8d\u8a3c\u3057\u3088\u3046\u3068\u3057\u305f\u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u306f CAS \u3092\u4f7f\u3046\u6a29\u9650\u304c\u3042\u308a\u307e\u305b\u3093\uff0e

screen.service.sso.error.header=\u3053\u306e\u30b5\u30fc\u30d3\u30b9\u306b\u30a2\u30af\u30bb\u30b9\u3059\u308b\u305f\u3081\u306b\u306f\u518d\u8a8d\u8a3c\u304c\u5fc5\u8981
screen.service.sso.error.message=\u518d\u8a8d\u8a3c\u3092\u8981\u6c42\u3059\u308b\u30b5\u30fc\u30d3\u30b9\u306b\u30a2\u30af\u30bb\u30b9\u3057\u3088\u3046\u3068\u3057\u307e\u3057\u305f\uff0e<a href="{0}">\u518d\u8a8d\u8a3c</a>\u3092\u8a66\u307f\u3066\u304f\u3060\u3055\u3044\uff0e

error.invalid.loginticket=\u3059\u3067\u306b\u9001\u4fe1\u6e08\u307f\u306e\u30d5\u30a9\u30fc\u30e0\u306f\u518d\u9001\u4fe1\u3067\u304d\u307e\u305b\u3093\uff0e
username.required=\u30e6\u30fc\u30b6\u540d\u306f\u5fc5\u9808\u30d5\u30a3\u30fc\u30eb\u30c9\u3067\u3059\uff0e
password.required=\u30d1\u30b9\u30ef\u30fc\u30c9\u306f\u5fc5\u9808\u30d5\u30a3\u30fc\u30eb\u30c9\u3067\u3059\uff0e
error.authentication.credentials.bad=\u3042\u306a\u305f\u304c\u5165\u529b\u3057\u305f\u8a8d\u8a3c\u60c5\u5831\u306f\uff0c\u8a8d\u8a3c\u53ef\u80fd\u306a\u3082\u306e\u3067\u3042\u308b\u3053\u3068\u304c\u78ba\u8a8d\u3067\u304d\u307e\u305b\u3093\u3067\u3057\u305f\uff0e
error.authentication.credentials.unsupported=\u5165\u529b\u3057\u305f\u8a8d\u8a3c\u60c5\u5831\u306f CAS \u3067\u306f\u30b5\u30dd\u30fc\u30c8\u3055\u308c\u3066\u3044\u307e\u305b\u3093\uff0e

INVALID_REQUEST_PROXY=\u300cpgt\u300d\u304a\u3088\u3073\u300ctargetService\u300d\u30d1\u30e9\u30e1\u30fc\u30bf\u306e\u4e21\u65b9\u304c\u5fc5\u8981\u3067\u3059
INVALID_TICKET_SPEC=\u30c1\u30b1\u30c3\u30c8\u306e\u6b63\u5f53\u6027\u57fa\u6e96\u30c1\u30a7\u30c3\u30af\u306b\u5931\u6557\u3057\u307e\u3057\u305f\uff0e\u300c\u30b5\u30fc\u30d3\u30b9\u30c1\u30b1\u30c3\u30c8\u300d\u30d0\u30ea\u30c7\u30fc\u30bf\u306b\u3088\u308b\u300c\u30d7\u30ed\u30af\u30b7\u30c1\u30b1\u30c3\u30c8\u300d\u306e\u6b63\u5f53\u6027\u30c1\u30a7\u30c3\u30af\u3092\u884c\u3063\u305f\u304b\uff0c\u66f4\u65b0\u8981\u6c42\u306e\u898f\u683c\u306b\u3042\u3063\u3066\u3044\u306a\u3044\u30b1\u30fc\u30b9\u304c\u8003\u3048\u3089\u308c\u307e\u3059\uff0e
INVALID_REQUEST=\u300cservice\u300d\u304a\u3088\u3073\u300cticket\u300d\u30d1\u30e9\u30e1\u30fc\u30bf\u306e\u4e21\u65b9\u304c\u5fc5\u8981\u3067\u3059
INVALID_TICKET=ticket\u300c{0}\u300d\u306f\u8a8d\u8b58\u3067\u304d\u307e\u305b\u3093\u3067\u3057\u305f
INVALID_SERVICE=ticket\u300c{0}\u300d\u306f\u63d0\u4f9b\u3055\u308c\u3066\u3044\u308b\u30b5\u30fc\u30d3\u30b9\u306b\u4e00\u81f4\u3057\u307e\u305b\u3093

