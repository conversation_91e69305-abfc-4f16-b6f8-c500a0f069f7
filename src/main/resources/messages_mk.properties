#
# Licensed to <PERSON>per<PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

# <AUTHOR> \u0410\u0458\u0430\u043d\u043e\u0432\u0441\u043a\u0438 <ajan at ii edu mk>
# \u0418\u043d\u0441\u0442\u0438\u0442\u0443\u0442 \u0437\u0430 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0442\u0438\u043a\u0430
# @since 3.1.1

#Welcome Screen Messages
screen.welcome.welcome=\u0414\u043e\u0431\u0440\u043e\u0434\u043e\u0458\u0434\u043e\u0432\u0442\u0435
screen.welcome.security=\u041f\u043e\u0440\u0430\u0434\u0438 \u0441\u0438\u0433\u0443\u0440\u043d\u043e\u0441\u043d\u0438 \u043f\u0440\u0438\u0447\u0438\u043d\u0438 \u0432\u0435 \u043c\u043e\u043b\u0438\u043c\u0435 \u0434\u0430 \u043d\u0435 \u0437\u0430\u0431\u043e\u0440\u0430\u0432\u0438\u0442\u0435 \u0434\u0430 \u0441\u0435 \u043e\u0434\u0458\u0430\u0432\u0438\u0442\u0435 \u0438 \u0434\u0430 \u0433\u043e \u0437\u0430\u0442\u0432\u043e\u0440\u0438\u0442\u0435 \u0432\u0430\u0448\u0438\u043e\u0442 \u043f\u0440\u0435\u0431\u0430\u0440\u0443\u0432\u0430\u0447 \u043f\u043e \u0437\u0430\u0432\u0440\u0448\u0443\u0432\u0430\u045a\u0435\u0442\u043e \u043d\u0430 \u0440\u0430\u0431\u043e\u0442\u0430\u0442\u0430 \u0441\u043e \u0430\u043f\u043b\u0438\u043a\u0430\u0446\u0438\u0438\u0442\u0435.
screen.welcome.instructions=\u0412\u043d\u0435\u0441\u0435\u0442\u0435 \u043a\u043e\u0440\u0438\u0441\u043d\u0438\u0447\u043a\u043e \u0438\u043c\u0435 \u0438 \u043b\u043e\u0437\u0438\u043d\u043a\u0430.
screen.welcome.label.netid=<span class="accesskey">\u041a</span>\u043e\u0440\u0438\u0441\u043d\u0438\u0447\u043a\u043e \u0438\u043c\u0435:
screen.welcome.label.netid.accesskey=\u043a
screen.welcome.label.password=<span class="accesskey">\u041b</span>\u043e\u0437\u0438\u043d\u043a\u0430:
screen.welcome.label.password.accesskey=\u043b
screen.welcome.label.warn=<span class="accesskey">\u041f</span>\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0434\u0438 \u043c\u0435 \u043f\u0440\u0438 \u043d\u0430\u0458\u0430\u0432\u0443\u0432\u0430\u045a\u0435 \u0432\u043e \u0434\u0440\u0443\u0433\u0438 \u0430\u043f\u043b\u0438\u043a\u0430\u0446\u0438\u0438.
screen.welcome.label.warn.accesskey=\u043f
screen.welcome.button.login=\u041d\u0410\u0408\u0410\u0412\u0410
screen.welcome.button.clear=\u041f\u041e\u041d\u0418\u0428\u0422\u0418

#Confirmation Screen Messages
screen.confirmation.message=\u041f\u0440\u0438\u0442\u0438\u0441\u043d\u0435\u0442\u0435 <a href="{0}">\u0442\u0443\u043a\u0430</a> \u0437\u0430 \u0432\u043b\u0435\u0437 \u0432\u043e \u0430\u043f\u043b\u0438\u043a\u0430\u0446\u0438\u0458\u0430\u0442\u0430.

#Generic Success Screen Messages
screen.success.header=\u0423\u0441\u043f\u0435\u0448\u043d\u0430 \u043d\u0430\u0458\u0430\u0432\u0430
screen.success.success=\u0423\u0441\u043f\u0435\u0448\u043d\u043e \u0441\u0435 \u043d\u0430\u0458\u0430\u0432\u0438\u0432\u0442\u0435 \u043d\u0430 \u0426\u0435\u043d\u0442\u0440\u0430\u043b\u043d\u0438\u043e\u0442 \u0410\u0432\u0442\u0435\u043d\u0442\u0438\u043a\u0430\u0446\u0438\u0441\u043a\u0438 \u0421\u0435\u0440\u0432\u0438\u0441.
screen.success.security=\u041f\u043e\u0440\u0430\u0434\u0438 \u0441\u0438\u0433\u0443\u0440\u043d\u043e\u0441\u043d\u0438 \u043f\u0440\u0438\u0447\u0438\u043d\u0438 \u0432\u0435 \u043c\u043e\u043b\u0438\u043c\u0435 \u0434\u0430 \u0441\u0435 \u043e\u0434\u0458\u0430\u0432\u0438\u0442\u0435 \u0438 \u0434\u0430 \u0433\u043e \u0437\u0430\u0442\u0432\u043e\u0440\u0438\u0442\u0435 \u0432\u0430\u0448\u0438\u043e\u0442 browser \u043f\u043e \u0437\u0430\u0432\u0440\u0448\u0443\u0432\u0430\u045a\u0435\u0442\u043e \u043d\u0430 \u0440\u0430\u0431\u043e\u0442\u0430\u0442\u0430 \u0441\u043e \u0430\u043f\u043b\u0438\u043a\u0430\u0446\u0438\u0438\u0442\u0435.

#Logout Screen Messages
screen.logout.header=\u0423\u0441\u043f\u0435\u0448\u043d\u0430 \u043e\u0434\u0458\u0430\u0432\u0430
screen.logout.success=\u0423\u0441\u043f\u0435\u0448\u043d\u043e \u0441\u0435 \u043e\u0434\u0458\u0430\u0432\u0438\u0432\u0442\u0435 \u043e\u0434 \u0426\u0435\u043d\u0442\u0440\u0430\u043b\u043d\u0438\u043e\u0442 \u0410\u0432\u0442\u0435\u0442\u0438\u043a\u0430\u0446\u0438\u0441\u043a\u0438 \u0421\u0435\u0440\u0432\u0438\u0441.
screen.logout.security=\u041f\u043e\u0440\u0430\u0434\u0438 \u0441\u0438\u0433\u0443\u0440\u043d\u043e\u0441\u043d\u0438 \u043f\u0440\u0438\u0447\u0438\u043d\u0438 \u0432\u0435 \u043c\u043e\u043b\u0438\u043c\u0435 \u0434\u0430 \u0433\u043e \u0437\u0430\u0442\u0432\u043e\u0440\u0438\u0442\u0435 \u0432\u0430\u0448\u0438\u043e\u0442 browser.
screen.logout.redirect=\u0421\u0435\u0440\u0432\u0438\u0441\u043e\u0442 \u043a\u043e\u0458 \u0432\u0435 \u0434\u043e\u043d\u0435\u0441\u0435 \u043d\u0430 \u0426\u0410\u0421 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0438\u0442\u0435, \u043d\u0443\u0434\u0438 \u043b\u0438\u043d\u043a \u043d\u0430 \u043a\u043e\u0458 \u043c\u043e\u0436\u0435\u0442\u0435 \u0434\u0430 \u043f\u0440\u043e\u0434\u043e\u043b\u0436\u0438\u0442\u0435 \u043d\u0430\u0442\u0430\u043c\u0443 \u0441\u043e \u043f\u0440\u0438\u0442\u0438\u0441\u043a\u0430\u045a\u0435 <a href="{0}">\u0422\u0423\u041a\u0410</a>.

screen.service.sso.error.header=\u0417\u0430 \u043f\u0440\u0438\u0441\u0442\u0430\u043f \u043d\u0430 \u043e\u0432\u043e\u0458 \u0441\u0435\u0440\u0432\u0438\u0441 \u0435 \u043f\u043e\u0442\u0440\u0435\u0431\u043d\u0430 \u043f\u043e\u0432\u0442\u043e\u0440\u043d\u043e \u0430\u0432\u0442\u0435\u043d\u0442\u0438\u043a\u0430\u0446\u0438\u0458\u0430
screen.service.sso.error.message=\u0421\u0435 \u043e\u0431\u0438\u0434\u043e\u0432\u0442\u0435 \u0434\u0430 \u043f\u0440\u0438\u0441\u0442\u0430\u043f\u0438\u0442\u0435 \u043d\u0430 \u0441\u0435\u0440\u0432\u0438\u0441 \u043a\u043e\u0458 \u043f\u043e\u0431\u0430\u0440\u0443\u0432\u0430 \u0430\u0432\u0442\u0435\u043d\u0442\u0438\u043a\u0430\u0446\u0438\u0458\u0430, \u043f\u0440\u0438\u0442\u043e\u0430 \u043d\u0435 \u0441\u0442\u0435 \u043f\u043e\u0432\u0442\u043e\u0440\u043d\u043e \u0430\u0432\u0442\u0435\u043d\u0442\u0438\u0446\u0438\u0440\u0430\u043d\u0438. \u0412\u0435 \u043c\u043e\u043b\u0438\u043c\u0435 \u0434\u0430 \u0441\u0435 \u043e\u0431\u0438\u0434\u0435\u0442\u0435 \u0434\u0430 \u0441\u0435 \u0430\u0432\u0442\u0435\u0442\u0438\u043d\u0446\u0438\u0440\u0430\u0442\u0435 \u043f\u043e\u0432\u0442\u043e\u0440\u043d\u043e \u0441\u043e \u043f\u0440\u0438\u0442\u0438\u0441\u043a\u0430\u045a\u0435 <a href="{0}&renew=true">\u0422\u0423\u041a\u0410</a>.

error.invalid.loginticket=\u0421\u043e\u0434\u0440\u0436\u0438\u043d\u0430\u0442\u0430 \u043d\u0430 \u0444\u043e\u0440\u043c\u0443\u043b\u0430\u0440\u043e\u0442 \u0435 \u0432\u0435\u045c\u0435 \u0438\u0441\u043f\u0440\u0430\u0442\u0435\u043d\u0430. \u041f\u043e\u0432\u0442\u043e\u0440\u043d\u043e \u0438\u0441\u043f\u0440\u0430\u045c\u0430\u045a\u0435 \u043d\u0435 \u0435 \u0434\u043e\u0437\u0432\u043e\u043b\u0435\u043d\u043e.
username.required=\u041a\u043e\u0440\u0438\u0441\u043d\u0438\u0447\u043a\u043e\u0442\u043e \u0438\u043c\u0435 \u0437\u0430\u0434\u043e\u043b\u0436\u0438\u0442\u0435\u043b\u043d\u043e \u0442\u0440\u0435\u0431\u0430 \u0434\u0430 \u0441\u0435 \u043f\u043e\u043f\u043e\u043b\u043d\u0438.
password.required=\u041b\u043e\u0437\u0438\u043d\u043a\u0430\u0442\u0430 \u0437\u0430\u0434\u043e\u043b\u0436\u0438\u0442\u0435\u043b\u043d\u043e \u0442\u0440\u0435\u0431\u0430 \u0434\u0430 \u0441\u0435 \u043f\u043e\u043f\u043e\u043b\u043d\u0438
error.authentication.credentials.bad=\u041a\u043e\u0440\u0438\u0441\u043d\u0438\u0447\u043a\u043e\u0442\u043e \u0438\u043c\u0435 \u0438/\u0438\u043b\u0438 \u043b\u043e\u0437\u0438\u043d\u043a\u0430\u0442\u0430 \u043d\u0435 \u0441\u0435 \u0438\u0441\u043f\u0440\u0430\u0432\u043d\u0438.
error.authentication.credentials.unsupported=\u0426\u0410\u0421 \u043d\u0435 \u0433\u043e \u043f\u043e\u0434\u0434\u0440\u0436\u0443\u0432\u0430 \u043e\u0432\u043e\u0458 \u043d\u0430\u0447\u0438\u043d \u043d\u0430 \u0430\u0432\u0442\u0435\u043d\u0442\u0438\u043a\u0430\u0446\u0438\u0458\u0430.

INVALID_REQUEST_PROXY=\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u0438\u0442\u0435 'pgt' \u0438 'targetService' \u0441\u0435 \u0437\u0430\u0434\u043e\u043b\u0436\u0438\u0442\u0435\u043b\u043d\u0438.
INVALID_TICKET_SPEC=\u0411\u0438\u043b\u0435\u0442\u043e\u0442 \u043d\u0435 \u0458\u0430 \u043f\u043e\u043c\u0438\u043d\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430\u0442\u0430 \u043d\u0430 \u0438\u0441\u043f\u0440\u0430\u0432\u043d\u043e\u0441\u0442. \u041e\u0432\u0430\u0430 \u0433\u0440\u0435\u0448\u043a\u0430 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0443\u043f\u0430\u0442\u0443\u0432\u0430 \u043d\u0430 \u043e\u0431\u0438\u0434 \u043d\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043d\u0430 \u0438\u0441\u043f\u0440\u0430\u0432\u043d\u043e\u0441\u0442 \u043d\u0430 \u041f\u043e\u0441\u0440\u0435\u0434\u043d\u0438\u043a \u0411\u0438\u043b\u0435\u0442 \u0441\u043e \u043f\u043e\u043c\u043e\u0448 \u043d\u0430 \u0432\u0430\u043b\u0438\u0434\u0430\u0442\u043e\u0440 \u043d\u0430 \u0421\u0435\u0440\u0432\u0438\u0441\u0435\u043d \u0411\u0438\u043b\u0435\u0442 \u0438\u043b\u0438 \u043d\u0430 \u043d\u0435\u0437\u0430\u0434\u043e\u0432\u043e\u043b\u0443\u0432\u0430\u045a\u0435 \u043d\u0430 \u0431\u0430\u0440\u0430\u045a\u0430\u0442\u0430 \u0441\u043e \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0430\u0440\u043e\u0442 renew=true.

INVALID_REQUEST=\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u0438\u0442\u0435 'service' \u0438 'ticket' \u0441\u0435 \u0437\u0430\u0434\u043e\u043b\u0436\u0438\u0442\u0435\u043b\u043d\u0438.
INVALID_TICKET=\u0411\u0438\u043b\u0435\u0442\u043e\u0442 ''{0}'' \u043d\u0435 \u0435 \u043f\u0440\u0435\u043f\u043e\u0437\u043d\u0430\u0442.
INVALID_SERVICE=\u0411\u0438\u043b\u0435\u0442\u043e\u0442 ''{0}'' \u043d\u0435 \u043e\u0434\u0433\u043e\u0432\u0430\u0440\u0430 \u043d\u0430 \u043e\u0432\u043e\u0458 \u0441\u0435\u0440\u0432\u0438\u0441. \u041e\u0440\u0438\u0433\u0438\u043d\u0430\u043b\u043d\u0438\u043e\u0442 \u0441\u0435\u0440\u0432\u0438\u0441 \u0431\u0435\u0448\u0435 ''{1}'', \u0430 \u0438\u0441\u043f\u043e\u0440\u0430\u0447\u0430\u043d\u0438\u043e\u0442 \u0441\u0435\u0440\u0432\u0438\u0441 \u0435 ''{2}''.

screen.service.error.header=\u0410\u043f\u043b\u0438\u043a\u0430\u0446\u0438\u0458\u0430\u0442\u0430 \u043d\u0435 \u0435 \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u0438\u0440\u0430\u043d\u0430 \u0437\u0430 \u043a\u043e\u0440\u0438\u0441\u0442\u0435\u045a\u0435 \u043d\u0430 \u0426\u0410\u0421
screen.service.error.message=\u0410\u043f\u043b\u0438\u043a\u0430\u0446\u0438\u0458\u0430\u0442\u0430 \u0432\u043e \u043a\u043e\u0458\u0430 \u0441\u0435 \u043e\u0431\u0438\u0434\u0443\u0432\u0430\u0442\u0435 \u0434\u0430 \u0441\u0435 \u043d\u0430\u0458\u0430\u0432\u0438\u0442\u0435 \u043d\u0435 \u0435 \u0430\u0432\u0442\u043e\u0440\u0438\u0437\u0438\u0440\u0430\u043d\u0430 \u0437\u0430 \u043a\u043e\u0440\u0438\u0441\u0442\u0435\u045a\u0435 \u043d\u0430 \u0426\u0410\u0421.
