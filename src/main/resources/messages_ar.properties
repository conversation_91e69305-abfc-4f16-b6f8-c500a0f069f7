#Welcome Screen Messages

#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=<span dir='rtl'>\u062A\u0647\u0627\u0646\u064A\u0646\u0627 \u0639\u0644\u0649 \u062C\u0644\u0628 CAS \u0639\u0644\u0649 \u0627\u0644\u0627\u0646\u062A\u0631\u0646\u062A!</span>
screen.welcome.security=<span dir='rtl'>\u0644\u0623\u0633\u0628\u0627\u0628 \u0623\u0645\u0646\u064A\u0629\u060C \u0627\u0644\u0631\u062C\u0627\u0621 \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062E\u0631\u0648\u062C \u0648\u062E\u0631\u0648\u062C \u0645\u062A\u0635\u0641\u062D \u0627\u0644\u0648\u064A\u0628 \u0627\u0644\u062E\u0627\u0635 \u0628\u0643 \u0628\u0639\u062F \u0627\u0644\u062D\u0635\u0648\u0644 \u0639\u0644\u0649 \u0627\u0644\u062E\u062F\u0645\u0627\u062A \u0627\u0644\u062A\u064A \u062A\u062A\u0637\u0644\u0628 \u0627\u0644\u0645\u0635\u0627\u062F\u0642\u0629!</span>
screen.welcome.instructions=<span dir='rtl'>\u0623\u062F\u062E\u0644 \u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0648\u0643\u0644\u0645\u0629 \u0627\u0644\u0633\u0631</span>
screen.welcome.label.netid=<span dir='rtl'>\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 </span>
screen.welcome.label.netid.accesskey=
screen.welcome.label.password=<span dir='rtl'>\u0643\u0644\u0645\u0629 \u0627\u0644\u0633\u0631</span>
screen.welcome.label.password.accesskey=
screen.welcome.label.warn=<span dir='rtl'>\u062A\u062D\u0630\u0631\u0646\u064A \u0642\u0628\u0644 \u062A\u0633\u062C\u064A\u0644\u064A \u0641\u064A \u0627\u0644\u0645\u0648\u0627\u0642\u0639 \u0627\u0644\u0623\u062E\u0631\u0649.</span>
screen.welcome.label.warn.accesskey=
screen.welcome.button.login=\u062F\u062E\u0648\u0644
screen.welcome.button.clear=\u0627\u0644\u063A\u0627

logo.title=\u0627\u0644\u0630\u0647\u0627\u0628 \u0625\u0644\u0649 \u0627\u0644\u0635\u0641\u062D\u0629 Apereo
copyright=<span dir='rtl'>\u062D\u0642 \u0627\u0644\u0646\u0634\u0631 &copy; 2005 - 2015 Apereo, Inc. \u062C\u0645\u064A\u0639 \u0627\u0644\u062D\u0642\u0648\u0642 \u0645\u062D\u0641\u0648\u0638\u0629.</span>

# Blocked Errors Page
screen.blocked.header=<span dir='rtl'>\u0627\u0644\u0648\u0635\u0648\u0644 \u0645\u0631\u0641\u0648\u0636</span>
screen.blocked.message=<span dir='rtl'>\u0644\u0642\u062F \u0642\u0645\u062A \u0628\u0625\u062F\u062E\u0627\u0644 \u0643\u0644\u0645\u0629 \u0645\u0631\u0648\u0631 \u062E\u0627\u0637\u0626\u0629 \u0644\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0645\u0631\u0627\u062A \u0643\u062B\u064A\u0631\u0629 \u062C\u062F\u0627. \u0644\u0642\u062F \u0643\u0646\u062A \u0645\u062E\u0646\u0648\u0642.</span>

#Confirmation Screen Messages
screen.confirmation.message=<span dir='rtl'>\u0627\u0636\u063A\u0637 \u0647\u0646\u0627 {0} \u0644\u0644\u0630\u0647\u0627\u0628 \u0625\u0644\u0649 \u0627\u0644\u062A\u0637\u0628\u064A\u0642</span>

#Generic Success Screen Messages
screen.success.header=<span dir='rtl'>\u062A\u0633\u062C\u064A\u0644 \u0646\u0627\u062C\u062D</span>
screen.success.success=<span dir='rtl'>\u0644\u0642\u062F \u0642\u0645\u062A \u0628\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644 \u0628\u0646\u062C\u0627\u062D \u0644\u0644\u062F\u062E\u0648\u0644 \u0625\u0644\u0649 \u0645\u0631\u0643\u0632 \u0627\u0644\u0645\u0635\u0627\u062F\u0642\u0629</span>
screen.success.security=<span dir='rtl'>\u0644\u0623\u0633\u0628\u0627\u0628 \u0623\u0645\u0646\u064A\u0629\u060C \u0627\u0644\u0631\u062C\u0627\u0621 \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062E\u0631\u0648\u062C \u0648 \u0627\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0645\u062A\u0635\u0641\u062D \u0627\u0644\u0648\u064A\u0628 \u0627\u0644\u062E\u0627\u0635 \u0628\u0643 \u0628\u0639\u062F \u0627\u0644\u062D\u0635\u0648\u0644 \u0639\u0644\u0649 \u0627\u0644\u062E\u062F\u0645\u0627\u062A \u0627\u0644\u062A\u064A \u062A\u062A\u0637\u0644\u0628 \u0627\u0644\u0645\u0635\u0627\u062F\u0642\u0629!</span>

#Logout Screen Messages
screen.logout.header=<span dir='rtl'>\u062E\u0631\u0648\u062C \u0646\u0627\u062C\u062D</span>
screen.logout.success=<span dir='rtl'>\u0644\u0642\u062F \u0642\u0645\u062A \u0628\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062E\u0631\u0648\u062C \u0628\u0646\u062C\u0627\u062D \u0644\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0645\u0631\u0643\u0632 \u0627\u0644\u0645\u0635\u0627\u062F\u0642\u0629</span>
screen.logout.security=<span dir='rtl'>\u0644\u0623\u0633\u0628\u0627\u0628 \u0623\u0645\u0646\u064A\u0629\u060C \u064A\u062C\u0628 \u0627\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0645\u062A\u0635\u0641\u062D \u0627\u0644\u0648\u064A\u0628 \u0627\u0644\u062E\u0627\u0635 \u0628\u0643 </span>
screen.logout.redirect=<span dir='rtl'>\u0627\u0644\u0645\u0631\u0643\u0632 \u0627\u0644\u0630\u064A \u0648\u0635\u0644\u062A \u0645\u0646\u0647 \u0642\u062F \u0632\u0648\u062F  \u0648\u0635\u0644 \u0627\u0631\u062A\u0628\u0627\u0637  \u0627\u062A\u0628\u0639 \u0628\u0627\u0644\u0636\u063A\u0637 \u0647\u0646\u0627 </span>

screen.service.sso.error.header=<span dir='rtl'> \u0644\u0644\u0648\u0635\u0648\u0644 \u0625\u0644\u0649 \u0647\u0630\u0627 \u0627\u0644\u0645\u0631\u0643\u0632 \u064A\u062C\u0628 \u0625\u0639\u0627\u062F\u0629 \u0627\u0644\u0645\u0635\u0627\u062F\u0642\u0629 </span>
screen.service.sso.error.message=<span dir='rtl'>\u0644\u0642\u062F \u062D\u0627\u0648\u0644\u062A \u0627\u0644\u0648\u0635\u0648\u0644 \u0625\u0644\u0649 \u062E\u062F\u0645\u0629 \u064A\u062A\u0637\u0644\u0628 \u0645\u0635\u0627\u062F\u0642\u0629 \u0645\u0646 \u062F\u0648\u0646 \u0645\u0635\u0627\u062F\u0642\u0629 \u0645\u0646 \u062C\u062F\u064A\u062F.\u0627\u0644\u0631\u062C\u0627\u0621 \u062D\u0627\u0648\u0644 \u0644\u0645\u0635\u0627\u062F\u0642\u0629 \u0645\u0631\u0629 \u0623\u062E\u0631\u0649 {0} </span>

error.invalid.loginticket=\u0644\u0627 \u064A\u0645\u0643\u0646\u0643 \u0645\u062D\u0627\u0648\u0644\u0629 \u0625\u0639\u0627\u062F\u0629 \u0627\u0644\u062A\u0642\u062F\u064A\u0645 \u0644\u0644\u0646\u0645\u0648\u0630\u062C \u0627\u0644\u0630\u064A \u062A\u0645 \u062A\u0642\u062F\u064A\u0645\u0647 \u0628\u0627\u0644\u0641\u0639\u0644
username.required=\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0647\u0648 \u0627\u0644\u062D\u0642\u0644 \u0627\u0644\u0645\u0637\u0644\u0648\u0628
password.required=\u0643\u0644\u0645\u0629 \u0627\u0644\u0633\u0631 \u0647\u064A \u0627\u0644\u062D\u0642\u0644 \u0627\u0644\u0645\u0637\u0644\u0648\u0628
error.authentication.credentials.bad= \u0623\u0648\u0631\u0627\u0642 \u0627\u0644\u0627\u0639\u062A\u0645\u0627\u062F \u0627\u0644\u062A\u064A \u0642\u062F\u0645\u062A\u0647\u0627 \u0644\u0627 \u064A\u0645\u0643\u0646 \u062A\u062D\u062F\u064A\u0647 \u0644\u0644\u0645\u0635\u0627\u062F\u0642\u0629 \u0639\u0644\u064A\u0647
error.authentication.credentials.unsupported= \u0623\u0648\u0631\u0627\u0642 \u0627\u0644\u0627\u0639\u062A\u0645\u0627\u062F \u0627\u0644\u062A\u064A \u0642\u062F\u0645\u062A\u0647\u0627 \u063A\u064A\u0631 \u0645\u0639\u062A\u0645\u062F\u0629 \u0628\u0648\u0627\u0633\u0637\u0629

INVALID_REQUEST_PROXY=pgt  \u0648 targetService \u0645\u0639\u0644\u0645\u0627\u062A \u06A9\u0644\u0627\u0647\u0645\u0627 \u0645\u0637\u0644\u0648\u0628
INVALID_TICKET_SPEC=\u0641\u0634\u0644 \u0627\u0644\u062A\u062D\u0642\u0642 \u0645\u0646 \u0635\u062D\u0629 \u0645\u0648\u0627\u0635\u0641\u0627\u062A \u0627\u0644\u062A\u0630\u0627\u0643\u0631. \u064A\u0645\u0643\u0646 \u0623\u0646 \u062A\u062A\u0636\u0645\u0646 \u0623\u062E\u0637\u0627\u0621 \u0645\u062D\u062A\u0645\u0644\u0629 \u062A\u062D\u0627\u0648\u0644 \u0627\u0644\u062A\u062D\u0642\u0642 \u0645\u0646 \u0635\u062D\u0629 \u0627\u0644\u062A\u0630\u0627\u0643\u0631 \u0639\u0646 \u0637\u0631\u064A\u0642 \u0648\u0643\u064A\u0644 \u0645\u062F\u0642\u0642 \u062A\u0630\u0643\u0631\u0629 \u0627\u0644\u062E\u062F\u0645\u0629\u060C \u0623\u0648 \u0644\u0645 \u064A\u0645\u062A\u062B\u0644 \u0644\u0637\u0644\u0628 \u062A\u062C\u062F\u064A\u062F \u062D\u0642\u064A\u0642\u064A
INVALID_REQUEST=service  \u0648 ticket \u0645\u0639\u0644\u0645\u0627\u062A \u06A9\u0644\u0627\u0647\u0645\u0627 \u0645\u0637\u0644\u0648\u0628
INVALID_TICKET=\u062A\u0630\u0643\u0631\u0629 {0} \u0644\u0627 \u064A\u0639\u062A\u0631\u0641
INVALID_SERVICE=\u062A\u0630\u0643\u0631\u0629 {0} \u0644\u0627 \u064A\u062A\u0637\u0627\u0628\u0642 \u0645\u0639 \u0627\u0644\u062E\u062F\u0645\u0629 \u0627\u0644\u0645\u0642\u062F\u0645\u0629. \u0643\u0627\u0646\u062A \u0627\u0644\u062E\u062F\u0645\u0629 \u0627\u0644\u0623\u0635\u0644\u064A\u0629 {1} \u0648\u06A9\u0627\u0646\u062A \u0627\u0644\u062E\u062F\u0645\u0629 \u0627\u0644\u0645\u0642\u062F\u0645\u0629  {2{

screen.service.error.header=<span dir='rtl'>\u0627\u0644\u062A\u0637\u0628\u064A\u0642 \u0644\u0627 \u064A\u0633\u0645\u062D \u0627\u0633\u062A\u062E\u062F\u0627\u0645 CAS</span>
screen.service.error.message=<span dir='rtl'>\u0627\u0644\u062E\u062F\u0645\u0629 \u0627\u0644\u062A\u064A \u062A\u0637\u0644\u0628\u064A\u0646\u0647\u0627 \u063A\u0631 \u0645\u0633\u0645\u0648\u062D\u0629 \u0628\u0647\u0627 \u0644\u062F\u0649 \u0627\u0633\u062A\u0639\u0645\u0627\u0644  CAS</span>
