#Welcome Screen Messages

#
# Licensed to <PERSON>per<PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Dobrodo\u0161li v ARNES CAS online\!  Uporabite uporabni\u0161ko ime in geslo, ki vam ga je dodeli administrator ARNES organizacije
screen.welcome.security=Zaradi varnostnih razlogov, prosimo, da naredite odjavo in zaprete brskalnik, ko zapustite spletni vir, ki je zahteval va\u0161o avtentikacijo.
screen.welcome.instructions=Vpi\u0161ite va\u0161o uporabni\u0161ko ime(eduprincipalName\: <EMAIL>) in geslo.
screen.welcome.label.netid=<span class\="accesskey">edu</span>PersonPrincipalName\:
screen.welcome.label.netid.accesskey=n
screen.welcome.label.password=<span class\="accesskey">G</span>eslo\:
screen.welcome.label.password.accesskey=p
screen.welcome.label.warn=<span class\="accesskey">O</span>pozori me, ko naredim novo prijavo v drugi spletni vir.
screen.welcome.label.warn.accesskey=w
screen.welcome.button.login=Prijava
screen.welcome.button.clear=ZBRI\u0160I

#Confirmation Screen Messages
screen.confirmation.message=Klikni <a href\="{0}">tukaj</a> za vstop v aplikacijo.

#Generic Success Screen Messages
screen.success.header=Prijava uspela
screen.success.success=Uspe\u0161no ste se prijavili v Centralno Avtenikacijsko Storitev.
screen.success.security=Zaradi varnostnih razlogov, prosimo, da naredite odjavo in zaprete brskalnik, ko zapustite spletni vir, ki je zahteval va\u0161o avtentikacijo.

#Logout Screen Messages
screen.logout.header=Odjava uspela
screen.logout.success=Uspe\u0161no ste se prijavili v Centralno Avtenikacijsko Storitev.
screen.logout.security=Zaradi varnostnih razlogov zaprite brskalnik
screen.logout.redirect=Spletna storitev iz katere ste se odjavili, je priskrbela <a href\="{0}"> povezavo za nazaj, \u010De se \u017Eelite vrniti, kliknite na povezavo.</a>.

#Service Error Messages
screen.service.error.header=Ne avtorizerana Storitev
screen.service.error.message=Vstopiti ste hoteli do o spletne storitve nima dovoljenja do uporabe CAS storitve.


error.invalid.loginticket=Ne morete narediti re-submit forme, ki je \u017Ee bila poslana.
username.required=Uporabni\u0161ko ime je nujno vpisati\!
password.required=Geslo je nujno vpisati\!
error.authentication.credentials.bad=Veredostojnost, ki ste jo vpisali ne moremo dolo\u010Diti, da je pristno\!
error.authentication.credentials.unsupported=Veredostojnost, ki ste jo vpisali ni podprto v CAS-u\!

INVALID_REQUEST_PROXY='pgt' in 'targetService' parametra sta oba nujna\!
INVALID_TICKET_SPEC=Ne uspe\u0161na validacija zahtevka. Mo\u017Ene napake so nastale pri vklju\u010Ditvi validacije v Proxy Ticket preko Service Ticket validacije.
INVALID_REQUEST='service' in 'ticket' parametra sta oba nujna\!
INVALID_TICKET=zahtevek ''{0}'' ni prepoznana
INVALID_SERVICE=zahtevek ''{0}''  se ne ujema priskrbljeno storitvijo
