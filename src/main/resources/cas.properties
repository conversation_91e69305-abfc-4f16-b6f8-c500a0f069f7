#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# <PERSON><PERSON>eo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

server.name=${cas.server}
server.prefix=${server.name}

# security configuration based on IP address to access the /status and /statistics pages
 cas.securityContext.adminpages.ip=127\.0\.0\.1


##
# Unique CAS node name
# host.name is used to generate unique Service Ticket IDs and SAMLArtifacts.  This is usually set to the specific
# hostname of the machine running the CAS node, but it could be any label so long as it is unique in the cluster.
host.name=${cas.server}

##
# JPA Ticket Registry Database Configuration
#
# ticketreg.database.ddl.auto=create-drop
# ticketreg.database.dialect=org.hibernate.dialect.OracleDialect|MySQLInnoDBDialect|HSQLDialect
# ticketreg.database.batchSize=10
# ticketreg.database.driverClass=org.hsqldb.jdbcDriver
# ticketreg.database.url=***********************************
# ticketreg.database.user=sa
# ticketreg.database.password=
# ticketreg.database.pool.minSize=6
# ticketreg.database.pool.maxSize=18
# ticketreg.database.pool.maxWait=10000
# ticketreg.database.pool.maxIdleTime=120
# ticketreg.database.pool.acquireIncrement=6
# ticketreg.database.pool.idleConnectionTestPeriod=30
# ticketreg.database.pool.connectionHealthQuery=select 1
# ticketreg.database.pool.acquireRetryAttempts=5
# ticketreg.database.pool.acquireRetryDelay=2000
# ticketreg.database.pool.connectionHealthQuery=select 1

##
# JPA Service Registry Database Configuration
#
svcreg.database.ddl.auto=update
svcreg.database.dialect=org.hibernate.dialect.MySQL5Dialect
# svcreg.database.hibernate.batchSize=10
# svcreg.database.driverClass=org.hsqldb.jdbcDriver
# svcreg.database.url=***********************************
# svcreg.database.user=sa
# svcreg.database.password=
# svcreg.database.pool.minSize=6
# svcreg.database.pool.maxSize=18
# svcreg.database.pool.maxWait=10000
# svcreg.database.pool.maxIdleTime=120
# svcreg.database.pool.acquireIncrement=6
# svcreg.database.pool.idleConnectionTestPeriod=30
# svcreg.database.pool.connectionHealthQuery=select 1
# svcreg.database.pool.acquireRetryAttempts=5
# svcreg.database.pool.acquireRetryDelay=2000
# svcreg.database.pool.connectionHealthQuery=select 1

##
# CAS SSO Cookie Generation & Security
# See https://github.com/mitreid-connect/json-web-key-generator
#
# Do note that the following settings MUST be generated per deployment.
#
# The encryption secret key. By default, must be a octet string of size 256.
tgc.encryption.key=V8vrToKPzrRNCIBrIAVpKvIu2kJj0jMuaKZLEGTHztE

# The signing secret key. By default, must be a octet string of size 512.
tgc.signing.key=U9mxjvMPE03qvp8Hnrp2UZMVJbk_hb15sqqbCfDIXzR8F6E5NoWJdEq1wBXkIIiTH0LMcJ1pPcUOFATa1PUNYQ

# Decides whether SSO cookie should be created only under secure connections.
tgc.secure=false

# The expiration value of the SSO cookie
tgc.maxAge=-1

# The name of the SSO cookie
tgc.name=TGC
tgc.domain=example.org

# The path to which the SSO cookie will be scoped
tgc.path=/

# The expiration value of the SSO cookie for long-term authentications
# tgc.remember.me.maxAge=1209600

# Decides whether SSO Warning cookie should be created only under secure connections.
# warn.cookie.secure=true

# The expiration value of the SSO Warning cookie
# warn.cookie.maxAge=-1

# The name of the SSO Warning cookie
# warn.cookie.name=CASPRIVACY

# The path to which the SSO Warning cookie will be scoped
# warn.cookie.path=/cas

# Whether we should track the most recent session by keeping the latest service ticket
# tgt.onlyTrackMostRecentSession = true

##
# CAS UI Theme Resolution
#
# cas.themeResolver.defaultThemeName=cas-theme-default
# cas.themeResolver.pathprefix=/WEB-INF/view/jsp/
# cas.themeResolver.param.name=theme
# Location of the Spring xml config file where views may be collected
# cas.viewResolver.xmlFile=/META-INF/spring/views.xml

##
# CAS Logout Behavior
# WEB-INF/cas-servlet.xml
#
# Specify whether CAS should redirect to the specified service parameter on /logout requests
cas.logout.followServiceRedirects=true

##
# CAS Cached Attributes Timeouts
# Controls the cached attribute expiration policy
#
# Notes the duration in which attributes will be kept alive
# cas.attrs.timeToExpireInHours=2

##
# Single Sign-On Session
#
# Indicates whether an SSO session should be created for renewed authentication requests.
# create.sso.renewed.authn=true
#
# Indicates whether an SSO session can be created if no service is present.
# create.sso.missing.service=true

##
# CAS Authentication Policy
#
 cas.authn.policy.any.tryall=false
# cas.authn.policy.req.tryall=false
# cas.authn.policy.req.handlername=handlerName

##
# CAS PersonDirectory Principal Resolution
#
# cas.principal.resolver.persondir.principal.attribute=cn
# cas.principal.resolver.persondir.return.null=false

##
# CAS Internationalization
#
 locale.default=zh_CN
# locale.param.name=locale
# message.bundle.encoding=UTF-8
# message.bundle.cacheseconds=180
# message.bundle.fallback.systemlocale=false
# message.bundle.usecode.message=true
# message.bundle.basenames=classpath:custom_messages,classpath:messages

##
# CAS Authentication Throttling
#
cas.throttle.failure.threshold=3
cas.throttle.failure.range.seconds=120
#cas.throttle.username.parameter=
cas.throttle.appcode=SSO
#cas.throttle.authn.failurecode=
cas.throttle.audit.query=SELECT date FROM audit_trail WHERE client_ip = ? AND user = ? AND action = ? AND application_code = ? AND date >= ? ORDER BY date DESC

##
# CAS Health Monitoring
#
# cas.monitor.st.warn.threshold=5000
# cas.monitor.tgt.warn.threshold=10000
# cas.monitor.free.mem.threshold=10

##
# CAS MongoDB Service Registry
#
# mongodb.host=mongodb database url
# mongodb.port=mongodb database port
# mongodb.userId=mongodb userid to bind
# mongodb.userPassword=mongodb password to bind
# cas.service.registry.mongo.db=Collection name to store service definitions
# mongodb.timeout=5000

##
# Spring Webflow Web Application Session
# Define the settings that are required to encrypt and persist the CAS web application session.
# See the cas-servlet.xml file to understand how these properties are used.
#
# The encryption secret key. By default, must be a octet string of size 256.
webflow.encryption.key=GKHdjizCQRXrIBAA

# The signing secret key. By default, must be a octet string of size 512.
webflow.signing.key=Eqo6RUbUfnkL2EjBM--lDmGHjS30JNhSR-eOldFnsBZxTkJR-ZaloglncwhD6rQo16_o0gRfm7-yXmFjKo6P9w

##
# Remote User Authentication
#
# ip.address.range=

##
# Apache Shiro Authentication
#
# shiro.authn.requiredRoles=
# shiro.authn.requiredPermissions=
# shiro.authn.config.file=classpath:shiro.ini

##
# YubiKey Authentication
#
# yubikey.client.id=
# yubikey.secret.key=

##
# JDBC Authentication
#
# cas.jdbc.authn.query.encode.sql=
# cas.jdbc.authn.query.encode.alg=
# cas.jdbc.authn.query.encode.salt.static=
# cas.jdbc.authn.query.encode.password=
# cas.jdbc.authn.query.encode.salt=
# cas.jdbc.authn.query.encode.iterations.field=
# cas.jdbc.authn.query.encode.iterations=

cas.jdbc.authn.query.sql=select password from user where username=?

# cas.jdbc.authn.search.password=
# cas.jdbc.authn.search.user=
# cas.jdbc.authn.search.table=

##
# Duo security 2fa authentication provider
# https://www.duosecurity.com/docs/duoweb#1.-generate-an-akey
#
# cas.duo.api.host=
# cas.duo.integration.key=
# cas.duo.secret.key=
# cas.duo.application.key=

##
# File Authentication
#
# file.authn.filename=classpath:people.txt
# file.authn.separator=::

##
# General Authentication
#
# cas.principal.transform.upperCase=false
# cas.authn.password.encoding.char=UTF-8
# cas.authn.password.encoding.alg=SHA-256
# cas.principal.transform.prefix=
# cas.principal.transform.suffix=

##
# X509 Authentication
#
# cas.x509.authn.crl.checkAll=false
# cas.x509.authn.crl.throw.failure=true
# cas.x509.authn.crl.refresh.interval=
# cas.x509.authn.revocation.policy.threshold=
# cas.x509.authn.trusted.issuer.dnpattern=
# cas.x509.authn.max.path.length=
# cas.x509.authn.max.path.length.unspecified=
# cas.x509.authn.check.key.usage=
# cas.x509.authn.require.key.usage=
# cas.x509.authn.subject.dnpattern=
# cas.x509.authn.principal.descriptor=
# cas.x509.authn.principal.serial.no.prefix=
# cas.x509.authn.principal.value.delim=

##
# Accepted Users Authentication
#
accept.authn.users=casuser::Mellon

##
# Rejected Users Authentication
#
# reject.authn.users=

##
# JAAS Authentication
#
# cas.authn.jaas.realm=CAS
# cas.authn.jaas.kerb.realm=
# cas.authn.jaas.kerb.kdc=

##
# Single Sign-On Session TGT Timeouts
#
# Inactivity Timeout Policy
tgt.timeout.maxTimeToLiveInSeconds=18000

# Hard Timeout Policy
# tgt.timeout.hard.maxTimeToLiveInSeconds
#
# Throttled Timeout Policy
# tgt.throttled.maxTimeToLiveInSeconds=28800
# tgt.throttled.timeInBetweenUsesInSeconds=5

# Default Expiration Policy
tgt.maxTimeToLiveInSeconds=108000
tgt.timeToKillInSeconds=18000

##
# Service Ticket Timeout
st.timeToKillInSeconds=300
st.numberOfUses=5

##
# Http Client Settings
#
# The http client read timeout in milliseconds
# http.client.read.timeout=5000

# The http client connection timeout in milliseconds
# http.client.connection.timeout=5000
#
# The http client truststore file, in addition to the default's
# http.client.truststore.file=classpath:truststore.jks
#
# The http client truststore's password
# http.client.truststore.psw=changeit

##
# Single Logout Out Callbacks
#
# To turn off all back channel SLO requests set this to true
# slo.callbacks.disabled=false
#
# To send callbacks to endpoints synchronously, set this to false
# slo.callbacks.asynchronous=true

##
# CAS Protocol Security Filter
#
# Are multi-valued parameters accepted?
# cas.http.allow.multivalue.params=false

# Define the list of request parameters to examine for sanity
# cas.http.check.params=ticket,service,renew,gateway,warn,target,SAMLart,pgtUrl,pgt,pgtId,pgtIou,targetService

# Define the list of request parameters only allowed via POST
# cas.http.allow.post.params=username,password

##
# JSON Service Registry
#
# Directory location where JSON service files may be found.
service.registry.config.location=classpath:services

##
# Service Registry Periodic Reloading Scheduler
# Default sourced from WEB-INF/spring-configuration/applicationContext.xml
#
# Force a startup delay of 2 minutes.
# service.registry.quartz.reloader.startDelay=120000
#
# Reload services every 2 minutes
# service.registry.quartz.reloader.repeatInterval=120000

##
# Background Scheduler
#
# Wait for scheduler to finish running before shutting down CAS.
# scheduler.shutdown.wait=true
#
# Attempt to interrupt background jobs when shutting down CAS
# scheduler.shutdown.interruptJobs=true

##
# Audits
#
# Use single line format for audit blocks
# cas.audit.singleline=true
# Separator to use between each fields in a single audit event
# cas.audit.singleline.separator=|
# Application code for audits
# cas.audit.appcode=CAS
#
## JDBC Audits
#
cas.audit.max.agedays=180
cas.audit.database.dialect=org.hibernate.dialect.MySQL57InnoDBDialect
#cas.audit.database.batchSize=
cas.audit.database.ddl.auto=update
cas.audit.database.gen.ddl=true
cas.audit.database.show.sql=true
#cas.audit.database.driverClass=
#cas.audit.database.url=
#cas.audit.database.user=root
#cas.audit.database.password=
#cas.audit.database.pool.minSize=
#cas.audit.database.pool.minSize=
#cas.audit.database.pool.maxSize=
#cas.audit.database.pool.maxIdleTime=
#cas.audit.database.pool.maxWait=
#cas.audit.database.pool.acquireIncrement=
#cas.audit.database.pool.acquireRetryAttempts=
#cas.audit.database.pool.acquireRetryDelay=
#cas.audit.database.pool.idleConnectionTestPeriod=
#cas.audit.database.pool.connectionHealthQuery=

##
# Metrics
# Default sourced from WEB-INF/spring-configuration/metricsConfiguration.xml:
#
# Define how often should metric data be reported. Default is 30 seconds.
# metrics.refresh.internal=30s

##
# Encoding
#
# Set the encoding to use for requests. Default is UTF-8
# httprequest.web.encoding=UTF-8

# Default is true. Switch this to "false" to not enforce the specified encoding in any case,
# applying it as default response encoding as well.
# httprequest.web.encoding.force=true

##
# Response Headers
#
# httpresponse.header.cache=false
# httpresponse.header.hsts=false
# httpresponse.header.xframe=false
# httpresponse.header.xcontent=false
# httpresponse.header.xss=false

##
# SAML
#
# Indicates the SAML response issuer
# cas.saml.response.issuer=localhost
#
# Indicates the skew allowance which controls the issue instant of the SAML response
# cas.saml.response.skewAllowance=0
#
# Indicates whether SAML ticket id generation should be saml2-compliant.
# cas.saml.ticketid.saml2=false

##
# Default Ticket Registry
#
# default.ticket.registry.initialcapacity=1000
# default.ticket.registry.loadfactor=1
# default.ticket.registry.concurrency=20

##
# Ticket Registry Cleaner
#
# Indicates how frequently the Ticket Registry cleaner should run. Configured in seconds.
# ticket.registry.cleaner.startdelay=20
# ticket.registry.cleaner.repeatinterval=5000

##
# Ticket ID Generation
#
# lt.ticket.maxlength=20
# st.ticket.maxlength=20
# tgt.ticket.maxlength=50
# pgt.ticket.maxlength=50

##
# Google Apps public/private key
#
# cas.saml.googleapps.publickey.file=file:/etc/cas/public.key
# cas.saml.googleapps.privatekey.file=file:/etc/cas/private.p8
# cas.saml.googleapps.key.alg=RSA

##
# WS-FED
#
# The claim from ADFS that should be used as the user's identifier.
# cas.wsfed.idp.idattribute=upn
#
# Federation Service identifier
# cas.wsfed.idp.id=https://adfs.example.org/adfs/services/trust
#
# The ADFS login url.
# cas.wsfed.idp.url=https://adfs.example.org/adfs/ls/
#
# Identifies resource(s) that point to ADFS's signing certificates.
# These are used verify the WS Federation token that is returned by ADFS.
# Multiple certificates may be separated by comma.
# cas.wsfed.idp.signingcerts=classpath:adfs-signing.crt
#
# Unique identifier that will be set in the ADFS configuration.
# cas.wsfed.rp.id=urn:cas:localhost
#
# Slack dealing with time-drift between the ADFS Server and the CAS Server.
# cas.wsfed.idp.tolerance=10000
#
# Decides which bundle of attributes should be resolved during WS-FED authentication.
# cas.wsfed.idp.attribute.resolver.enabled=true
# cas.wsfed.idp.attribute.resolver.type=WSFED

##
# LDAP User Details
#
# ldap.userdetails.service.user.attr=
# ldap.userdetails.service.role.attr=

##
# Password Policy
#
# Warn all users of expiration date regardless of warningDays value.
# password.policy.warnAll=false

# Threshold number of days to begin displaying password expiration warnings.
# password.policy.warningDays=30

# URL to which the user will be redirected to change the password.
# password.policy.url=https://password.example.edu/change

# password.policy.warn.attribute.name=attributeName
# password.policy.warn.attribute.value=attributeValue
# password.policy.warn.display.matched=true

##
# CAS REST API Services
#
 cas.rest.services.attributename=username
 cas.rest.services.attributevalue=yangzhenyu

##
# Ticket Registry
#
# Secret key to use when encrypting tickets in a distributed ticket registry.
# ticket.encryption.secretkey=C@$W3bSecretKey!

# Seed to use when encrypting tickets in a distributed ticket registry.
# ticket.encryption.seed=S!ngl3$ign0n4W3b

# Secret key to use when signing tickets in a distributed ticket registry.
# By default, must be a octet string of size 512.
# ticket.signing.secretkey=szxK-5_eJjs-aUj-64MpUZ-GPPzGLhYPLGl0wrYjYNVAGva2P0lLe6UGKGM7k8dWxsOVGutZWgvmY3l5oVPO3w
# Secret key algorithm used
# ticket.secretkey.alg=AES

##
# Hazelcast Ticket Registry
#
# hz.config.location=file:/etc/cas/hazelcast.xml
# hz.mapname=tickets
# hz.cluster.logging.type=slf4j
# hz.cluster.portAutoIncrement=true
# hz.cluster.port=5701
# hz.cluster.multicast.enabled=false
# hz.cluster.members=cas1.example.com,cas2.example.com
# hz.cluster.tcpip.enabled=true
# hz.cluster.multicast.enabled=false
# hz.cluster.max.heapsize.percentage=85
# hz.cluster.max.heartbeat.seconds=5
# hz.cluster.eviction.percentage=10
# hz.cluster.eviction.policy=LRU
# hz.cluster.instance.name=${host.name}

##
# Ehcache Ticket Registry
#
 ehcache.config.file=classpath:ehcache-replicated.xml
 ehcache.cachemanager.shared=true
 ehcache.cachemanager.name=ticketRegistryCacheManager
 ehcache.disk.expiry.interval.seconds=0
 ehcache.disk.persistent=false
# ehcache.eternal=false
# ehcache.max.elements.memory=10000
# ehcache.max.elements.disk=0
# ehcache.eviction.policy=LRU
# ehcache.overflow.disk=false
# ehcache.cache.st.name=org.jasig.cas.ticket.ServiceTicket
# ehcache.cache.st.timeIdle=0
# ehcache.cache.st.timeAlive=300
# ehcache.cache.tgt.name=org.jasig.cas.ticket.TicketGrantingTicket
# ehcache.cache.tgt.timeIdle=7201
# ehcache.cache.tgt.timeAlive=0
# ehcache.cache.loader.async=true
# ehcache.cache.loader.chunksize=5000000
# ehcache.repl.async.interval=10000
# ehcache.repl.async.batch.size=100
# ehcache.repl.sync.puts=true
# ehcache.repl.sync.putscopy=true
# ehcache.repl.sync.updates=true
# ehcache.repl.sync.updatesCopy=true
# ehcache.repl.sync.removals=true

##
# Ehcache Monitoring
#
 cache.monitor.warn.free.threshold=10
 cache.monitor.eviction.threshold=0

##
# Memcached Ticket Registry
#
# memcached.servers=localhost:11211
# memcached.hashAlgorithm=FNV1_64_HASH
# memcached.protocol=BINARY
# memcached.locatorType=ARRAY_MOD
# memcached.failureMode=Redistribute

##
# Memcached Monitoring
#
# cache.monitor.warn.free.threshold=10
# cache.monitor.eviction.threshold=0

##
# RADIUS Authentication Server
#
# cas.radius.client.inetaddr=localhost
# cas.radius.client.port.acct=
# cas.radius.client.socket.timeout=60
# cas.radius.client.port.authn=
# cas.radius.client.sharedsecret=N0Sh@ar3d$ecReT
# cas.radius.server.protocol=EAP_MSCHAPv2
# cas.radius.server.retries=3
# cas.radius.server.nasIdentifier=-1
# cas.radius.server.nasPort=-1
# cas.radius.server.nasPortId=-1
# cas.radius.server.nasRealPort=-1
# cas.radius.server.nasPortType=-1
# cas.radius.server.nasIpAddress=
# cas.radius.server.nasIpv6Address=
# cas.radius.failover.authn=false
# cas.radius.failover.exception=false

##
# SPNEGO Authentication
#
# cas.spnego.ldap.attribute=spnegoattribute
# cas.spnego.ldap.filter=host={0}
# cas.spnego.ldap.basedn=
# cas.spnego.hostname.pattern=.+
# cas.spnego.ip.pattern=
# cas.spnego.alt.remote.host.attribute
# cas.spengo.use.principal.domain=false
# cas.spnego.ntlm.allowed=true
# cas.spnego.kerb.debug=false
# cas.spnego.kerb.realm=EXAMPLE.COM
# cas.spnego.kerb.kdc=***********
# cas.spnego.login.conf.file=/path/to/login
# cas.spnego.jcifs.domain=
# cas.spnego.jcifs.domaincontroller=
# cas.spnego.jcifs.netbios.cache.policy:600
# cas.spnego.jcifs.netbios.wins=
# cas.spnego.jcifs.password=
# cas.spnego.jcifs.service.password=
# cas.spnego.jcifs.socket.timeout:300000
# cas.spnego.jcifs.username=
# cas.spnego.kerb.conf=
# cas.spnego.ntlm=false
# cas.spnego.supportedBrowsers=MSIE,Trident,Firefox,AppleWebKit
# cas.spnego.mixed.mode.authn=false
# cas.spnego.send.401.authn.failure=false
# cas.spnego.principal.resolver.transform=NONE
# cas.spnego.service.principal=HTTP/<EMAIL>

##
# NTLM Authentication
#
# ntlm.authn.domain.controller=
# ntlm.authn.include.pattern=
# ntlm.authn.load.balance=true

##
# Authentication delegation using pac4j
#
# cas.pac4j.client.authn.typedidused=true
# cas.pac4j.facebook.id=
# cas.pac4j.facebook.secret=
# cas.pac4j.facebook.scope=
# cas.pac4j.facebook.fields=
# cas.pac4j.twitter.id=
# cas.pac4j.twitter.secret=
# cas.pac4j.saml.keystorePassword=
# cas.pac4j.saml.privateKeyPassword=
# cas.pac4j.saml.keystorePath=
# cas.pac4j.saml.identityProviderMetadataPath=
# cas.pac4j.saml.maximumAuthenticationLifetime=
# cas.pac4j.saml.serviceProviderEntityId=
# cas.pac4j.saml.serviceProviderMetadataPath=
# cas.pac4j.cas.loginUrl=
# cas.pac4j.cas.protocol=
# cas.pac4j.oidc.id=
# cas.pac4j.oidc.secret=
# cas.pac4j.oidc.discoveryUri=
# cas.pac4j.oidc.useNonce=
# cas.pac4j.oidc.preferredJwsAlgorithm=
# cas.pac4j.oidc.maxClockSkew=
# cas.pac4j.oidc.customParamKey1=
# cas.pac4j.oidc.customParamValue1=
# cas.pac4j.oidc.customParamKey2=
# cas.pac4j.oidc.customParamValue2=

#CSDN OAuth Login
csdn.id=1100560
csdn.secret=49b54ca408404466b4eee2821f4dfcee
csdn.authUrl=http://api.csdn.net/oauth2