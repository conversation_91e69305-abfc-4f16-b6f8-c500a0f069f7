#Welcome Screen Messages

#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Parab\u00e9ns por colocar o CAS no ar! O autenticador padr\u00e3o usa Nome de Usu\u00e1rio igual a Senha: v\u00e1 em frente e tente!
screen.welcome.security=Por raz\u00f5es de seguran\u00e7a, por favor deslogue e feche o seu navegador quando terminar de acessar os servi\u00e7os que precisam de autentica\u00e7\u00e3o!
screen.welcome.instructions=Entre com seu usu\u00e1rio e Senha
screen.welcome.label.netid=<span class="accesskey">U</span>su\u00e1rio:
screen.welcome.label.netid.accesskey=u
screen.welcome.label.password=<span class="accesskey">S</span>enha:
screen.welcome.label.password.accesskey=s
screen.welcome.label.warn=<span class="accesskey">A</span>visar anter de logar em outros sites.
screen.welcome.label.warn.accesskey=a
screen.welcome.button.login=ENTRAR
screen.welcome.button.clear=LIMPAR

#Confirmation Screen Messages
screen.confirmation.message=Clique <a href="{0}">aqui</a> para ir para a aplica\u00e7\u00e3o.

#Generic Success Screen Messages
screen.success.header=Sucesso ao se logar
screen.success.success=Voc\u00ea se logou com sucesso no Servi\u00e7o de Autentica\u00e7\u00e3o Central.
screen.success.security=Por raz\u00f5es de seguran\u00e7a, por favor efetue um Logout e feche seu navegador quando voc\u00ea terminar de acessar os servi\u00e7os que precisam de autentica\u00e7\u00e3o!

#Logout Screen Messages
screen.logout.header=Sucesso ao se deslogar
screen.logout.success=Voc\u00ea se deslogou com sucesso no Servi\u00e7o de Autentica\u00e7\u00e3o Central.
screen.logout.security=Por raz\u00f5es de seguran\u00e7a, feche o seu navegador.
screen.logout.redirect=O servi\u00e7o de onde voc\u00ea veio fornecer um <a href="{0}">link que voc\u00ea pode seguir clicando aqui</a>.

screen.service.sso.error.header=Re-Autenti\u00e7\u00e3o Obrigat\u00f3ria para Acessar esse Servi\u00e7o
screen.service.sso.error.message=Voc\u00ea tentou acessar um servi\u00e7o que necessita de autentica\u00e7\u00e3o sem re-autentica\u00e7\u00e3o. Por favor, tente <a href="{0}">autenticar novamente</a>.

error.invalid.loginticket=Voc\u00ea n\u00e3o pode tentar re-enviar um formul\u00e1rio que j\u00e1 vou enviado anteriormente.
username.required=Usu\u00e1rio \u00e9 um campo obrigat\u00f3rio.
password.required=Senha \u00e9 um campo obrigat\u00f3rio.
error.authentication.credentials.bad=Usu\u00e1rio ou senha inv\u00e1lidos.
error.authentication.credentials.unsupported=As credenciais fornecidas n\u00e3o n\u00e3o suportadas pelo CAS.

INVALID_REQUEST_PROXY='pgt' e 'targetService' s\u00e3o par\u00e2metros obrigat\u00f3rios
INVALID_TICKET_SPEC=O Ticket falhou a valida\u00e7\u00e3o da especifica\u00e7\u00e3o. Possiveis erros incluem tentativa de validar um Proxy Ticket por meio de um validador Service Ticket, ou n\u00e3o estar de acordo com o pedido de renova\u00e7\u00e3o.
INVALID_REQUEST='service' e 'ticket' s\u00e3o par\u00e2metros obrigat\u00f3rios
INVALID_TICKET=ticket ''{0}'' n\u00e3o reconhecido
INVALID_SERVICE=ticket ''{0}'' n\u00e3o casa com o servi\u00e7o fornecido. O servi\u00e7o original era ''{1}'' e o servi\u00e7o fornecido era ''{2}''.

screen.service.error.header=Aplica\u00e7\u00e3o n\u00e3o Autorizada a usar o CAS
screen.service.error.message=A aplica\u00e7\u00e3o que voc\u00ea tentou autenticar n\u00e3o \u00e9 autorizada a usar o CAS.
