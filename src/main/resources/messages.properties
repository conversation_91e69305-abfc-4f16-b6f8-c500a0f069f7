screen.welcome.welcome=Congratulations on bringing CAS online!  To learn how to authenticate, please review the default authentication handler configuration.
screen.welcome.security=For security reasons, please <a href="logout">log out</a> and exit your web browser when you are done accessing services that require \
  authentication!
screen.welcome.instructions=Enter your Username and Password
screen.welcome.label.netid=<span class="accesskey">U</span>sername:
screen.welcome.label.netid.accesskey=u
screen.welcome.label.password=<span class="accesskey">P</span>assword:
screen.welcome.label.password.accesskey=p
screen.welcome.label.publicstation=I am at a public workstation.
screen.welcome.label.warn=<span class="accesskey">W</span>arn me before logging me into other sites.
screen.welcome.label.warn.accesskey=w
screen.welcome.button.login=LOGIN
screen.welcome.button.clear=CLEAR
screen.welcome.label.loginwith=Or login with:

screen.cookies.disabled.title=Browser cookies disabled
screen.cookies.disabled.message=Your browser does not accept cookies. Single Sign On WILL NOT WORK.

screen.aup.button.accept=ACCEPT
screen.aup.button.cancel=CANCEL

screen.nonsecure.title=Non-secure Connection
screen.nonsecure.message=You are currently accessing CAS over a non-secure connection. Single Sign On WILL NOT WORK. In order to have single sign on work, you MUST log in over HTTPS.

logo.title=go to Apereo home page
copyright=Copyright &copy; 2005&ndash;2015 Apereo, Inc. 
screen.capslock.on = CAPSLOCK key is turned on!

# Remember-Me Authentication
screen.rememberme.checkbox.title=Remember Me

# Blocked Errors Page
screen.blocked.header=Access Denied
screen.blocked.message=You've entered the wrong password for the user too many times. You've been throttled.
AbstractAccessDecisionManager.accessDenied=You are not authorized to access this resource. Contact your CAS administrator for more info.

#Confirmation Screen Messages
screen.confirmation.message=Click <a href="{0}">here</a> to go to the application.

#Generic Success Screen Messages
screen.success.header=Log In Successful
screen.success.success=You, {0}, have successfully logged into the Central Authentication Service.
screen.success.security=When you are finished, for security reasons, please <a href="logout">log out</a> and exit your web browser.

#Logout Screen Messages
screen.logout.header=Logout successful
screen.logout.success=You have successfully logged out of the Central Authentication Service.
screen.logout.security=For security reasons, exit your web browser.
screen.logout.redirect=The service from which you arrived has supplied a <a href="{0}">link you may follow by clicking here</a>.

screen.service.sso.error.header=Re-Authentication Required to Access this Service
screen.service.sso.error.message=You attempted to access a service that requires authentication without re-authenticating.  Please try <a href="{0}">authenticating again</a>.
screen.service.required.message=You attempted authentication without specifying the target application. Please re-examine the request and try again.
        
username.required=Username is a required field.
password.required=Password is a required field.

# Authentication failure messages
authenticationFailure.AccountDisabledException=This account has been disabled.
authenticationFailure.AccountLockedException=This account has been locked.
authenticationFailure.CredentialExpiredException=Your password has expired.
authenticationFailure.InvalidLoginLocationException=You cannot login from this workstation.
authenticationFailure.InvalidLoginTimeException=Your account is forbidden to login at this time.
authenticationFailure.AccountNotFoundException=Invalid credentials.
authenticationFailure.FailedLoginException=Invalid credentials.
authenticationFailure.UNKNOWN=Invalid credentials.

INVALID_REQUEST_PROXY=The request is incorrectly formatted. Ensure all required parameters are properly encoded and included.
INVALID_TICKET_SPEC=Ticket failed validation specification. Possible errors could include attempting to validate a Proxy Ticket via a Service Ticket validator, or not complying with the renew true request.
INVALID_REQUEST='service' and 'ticket' parameters are both required
INVALID_TICKET=Ticket ''{0}'' not recognized
INVALID_SERVICE=Ticket ''{0}'' does not match supplied service. The original service was ''{1}'' and the supplied service was ''{2}''.
INVALID_PROXY_CALLBACK=The supplied proxy callback url ''{0}'' could not be authenticated.
UNAUTHORIZED_SERVICE_PROXY=The supplied service ''{0}'' is not authorized to use CAS proxy authentication.

screen.service.error.header=Application Not Authorized to Use CAS
service.not.authorized.missing.attr=You are not authorized to access the application as your account \
is missing privileges required by the CAS server to authenticate into this service. Please notify your support desk.
screen.service.error.message=The application you attempted to authenticate to is not authorized to use CAS.
screen.service.empty.error.message=The services registry of CAS is empty and has no service definitions. \
Applications that wish to authenticate with CAS must explicitly be defined in the services registry.

# Password policy
password.expiration.warning=Your password expires in {0} day(s). Please <a href="{1}">change your password</a> now.
password.expiration.loginsRemaining=You have {0} login(s) remaining before you <strong>MUST</strong> change your password.
screen.accountdisabled.heading=This account has been disabled.
screen.accountdisabled.message=Please contact the system administrator to regain access.
screen.accountlocked.heading=This account has been locked.
screen.accountlocked.message=Please contact the system administrator to regain access.
screen.expiredpass.heading=Your password has expired.
screen.expiredpass.message=Please <a href="{0}">change your password</a>.
screen.mustchangepass.heading=You must change your password.
screen.mustchangepass.message=Please <a href="{0}">change your password</a>.
screen.badhours.heading=Your account is forbidden to login at this time.
screen.badhours.message=Please try again later.
screen.badworkstation.heading=You cannot login from this workstation.
screen.badworkstation.message=Please contact the system administrator to regain access.

# OAuth
screen.oauth.confirm.header=Authorization
screen.oauth.confirm.message=Do you want to grant access to your complete profile to "{0}" ?
screen.oauth.confirm.allow=Allow

# Unavailable
screen.unavailable.heading=CAS is Unavailable
screen.unavailable.message=There was an error trying to complete your request. \
<strong>Please notify your support desk or try again.</strong> \
<div>Apereo is a non-profit open source software governance foundation. The CAS software is an Apereo sponsored project \
and is freely downloadable and usable by anyone. However, Apereo does not operate the systems of anyone using the \
software and in most cases doesn't even know who is using it or how to contact them unless they are an active part \
of the Apereo community.<br/></br>If you are having problems logging in using CAS, \
<strong>you will need to contact the IT staff or Help Desk of your organization for assistance</strong>. \
<br/><br/>We wish we could be more directly helpful to you.</div>





#####################################################################
# SSO Sessions View
#####################################################################
# No sessions found screen
cas.ssosessions.loading=Loading SSO Sessions...
cas.ssosessions.nosessionsfound=No Sessions Found
cas.ssosessions.button.refresh=Refresh


# Report View
cas.ssosessions.report.pagetitle=SSO Sessions Report
cas.ssosessions.report.panel.totalactiveprincipals=Total Active Principals
cas.ssosessions.report.panel.usagecountsessions=Usage Count Sessions
cas.ssosessions.report.panel.totalssosessions=Total SSO Sessions

cas.ssosessions.buttons.removeall=Remove All Sessions
cas.ssosessions.buttons.removesingle=Remove

cas.ssosessions.buttons.filter.all=All
cas.ssosessions.buttons.filter.proxied=Proxied
cas.ssosessions.buttons.filter.nonproxied=Non-Proxied

cas.ssosessions.table.header.principal=Principal
cas.ssosessions.table.header.ticketgrantingticket=Ticket Granting Ticket
cas.ssosessions.table.header.authenticationdate=Authentication Date
cas.ssosessions.table.header.usagecount=Usage Count


# Remove Session AJAX messages
cas.sessions.ajax.error=There appears to be an error. Please try your request again.

cas.sessions.alert.removal.success.multi.partone=Successfully removed
cas.sessions.alert.removal.success.multi.parttwo=sessions.

cas.sessions.alert.removal.error.multi.partone=Error removing
cas.sessions.alert.removal.error.multi.parttwo=sessions.  Please try your request again.

cas.sessions.alert.removal.success.single.partone=Successfully removed
cas.sessions.alert.removal.success.single.parttwo=session.
cas.sessions.alert.removal.error.single.partone=Error removing
cas.sessions.alert.removal.error.single.parttwo=Please try your request again.

# Ex of passing attributes
#footer.poweredBy=Powered by <a href="http://www.apereo.org/cas">Apereo Central Authentication Service {0}</a>
#<spring:message code="footer.poweredBy" arguments="<%=org.jasig.cas.CasVersion.getVersion()%>" />

#####################################################################
# Statistics View
#####################################################################
# Ticket Registry Section
cas.statistics.pagetitle=Statistics View
cas.statistics.section.ticket.title=Ticket Registry Statistics
cas.statistics.section.ticket.panel.unexpiredtgts.title=Unexpired TGTs
cas.statistics.section.ticket.panel.unexpiredsts.title=Unexpired STs
cas.statistics.section.ticket.panel.expiredtgts.title=Expired TGTs
cas.statistics.section.ticket.panel.expiredsts.title=Expired STs
cas.statistics.section.ticket.button=View SSO Sessions

# JVM Section
cas.statistics.section.serverstatistics.title=JVM Server Statistics

## memory gauges
cas.statistics.section.serverstatistics.freememorygauge.label=Total JVM Memory
cas.statistics.section.serverstatistics.maxmemorygauge.label=Max Memory

## Uptime
cas.statistics.section.serverstatistics.panel.uptime.title=Uptime
cas.statistics.section.serverstatistics.panel.uptime.day=day
cas.statistics.section.serverstatistics.panel.uptime.days=days
cas.statistics.section.serverstatistics.panel.uptime.hour=hour
cas.statistics.section.serverstatistics.panel.uptime.hours=hours
cas.statistics.section.serverstatistics.panel.uptime.minute=minute
cas.statistics.section.serverstatistics.panel.uptime.minutes=minutes
cas.statistics.section.serverstatistics.panel.uptime.second=second
cas.statistics.section.serverstatistics.panel.uptime.seconds=seconds

## Server Info
cas.statistics.section.serverstatistics.panel.serverinfo.title=Server Info
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.label=Property
cas.statistics.section.serverstatistics.panel.serverinfo.table.value.label=Value

## Property Names
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.server.label=Server
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.casticketsuffix.label=CAS Ticket Suffix
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.starttime.label=Server Start Time
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.uptime.label=Uptime
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.memory.label=Memory
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.free=free
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.total=total
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.maxmemory.label=Maximum Memory
cas.statistics.section.serverstatistics.panel.serverinfo.table.property.availprocessors.label=Available Processors

## Thread Dump Section
cas.statistics.section.threaddump.title=Thread Dump
cas.statistics.section.threaddump.button=View more
cas.statistics.section.threaddump.modal.title=Thread Dump

## Metrics Section
cas.statistics.section.metrics.title=Metrics
cas.statistics.section.metrics.button=View Metrics

## Modal
cas.statistics.modal.close.button=Close

##
## End Statistics View
#####################################################################


#####################################################################
# Configuration View
#####################################################################
# Error loading screen
cas.viewconfig.loading=Loading Configuration...
cas.viewconfig.errormessage=Error reading configuration
cas.viewconfig.button.refresh=Refresh

# Report View
cas.viewconfig.pagetitle=View Configuration
cas.viewconfig.table.column.key=Key
cas.viewconfig.table.column.value=Value

##
## End Configuration View
#####################################################################
