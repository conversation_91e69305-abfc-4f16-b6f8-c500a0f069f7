<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mongo="http://www.springframework.org/schema/data/mongo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/data/mongo
        http://www.springframework.org/schema/data/mongo/spring-mongo.xsd">
    <mongo:mongo-client host="${mongod.host}" port="${mongod.port}" credentials="${mongod.credential}" id="mongoClient">
        <mongo:client-options connections-per-host="8"
                              threads-allowed-to-block-for-connection-multiplier="25"
                              connect-timeout="1000"
                              max-wait-time="1500"
                              socket-keep-alive="true"
                              socket-timeout="20000"
        />
    </mongo:mongo-client>
    <mongo:db-factory dbname="${mongod.name}" mongo-ref="mongoClient" id="mongoDbFactory"/>
    <bean id="mongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
        <constructor-arg name="mongoDbFactory" ref="mongoDbFactory"/>
    </bean>
    <mongo:mapping-converter id="converter" />
    <mongo:repositories base-package="cn.dahe.cas.auth.repositories"/>
    <mongo:mapping-converter base-package="cn.dahe.cas.auth.domain" db-factory-ref="mongoDbFactory"/>
</beans>