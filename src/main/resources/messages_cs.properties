#Generated by ResourceBundle Editor (http://eclipse-rbe.sourceforge.net)
#Welcome Screen Messages

INVALID_PROXY_CALLBACK = Poskytnut\u00E9 URL proxy callbacku ''{0}'' nelze autentifikovat.

INVALID_REQUEST = Parametry 'service' a 'ticket' jsou povinn\u00E9

INVALID_REQUEST_PROXY = Parametry 'pgt' a 'targetService' jsou povinn\u00E9

INVALID_SERVICE = Ticket ''{0}'' nesouhlas\u00ED s poskytovanou slu\u017Ebou. P\u016Fvodn\u00ED slu\u017Eba byla ''{1}'', poskytnut\u00E1 je ''{2}''.

INVALID_TICKET = Ticket ''{0}'' nebyl rozpozn\u00E1n

INVALID_TICKET_SPEC = Ticket neporo\u0161el kontrolou validity. Mo\u017En\u00E9 chyby zahrnuj\u00ED pokus o ov\u011B\u0159en\u00ED Proxy Ticketu pomoc\u00ED ov\u011B\u0159en\u00ED Service Ticketu nebo nedodr\u017Een\u00ED po\u017Eadavku na renew.

UNAUTHORIZED_SERVICE_PROXY = Poskytnut\u00E1 slu\u017Eba ''{0}'' nen\u00ED opr\u00E1vn\u011Bn\u00ED k pou\u017Eit\u00ED CAS proxy autentizace.

authenticationFailure.AccountDisabledException      = Tento \u00FA\u010Det byl zak\u00E1z\u00E1n.
authenticationFailure.AccountLockedException        = Tento \u00FA\u010Det byl uzam\u010Den.
authenticationFailure.AccountNotFoundException      = Nezn\u00E1m\u00E9 p\u0159ihla\u0161ovac\u00ED \u00FAdaje.
authenticationFailure.CredentialExpiredException    = Va\u0161e heslo ji\u017E nen\u00ED platn\u00E9.
authenticationFailure.FailedLoginException          = Neplatn\u00E9 p\u0159ihla\u0161ovac\u00ED \u00FAdaje.
authenticationFailure.InvalidLoginLocationException = Z tohoto po\u010D\u00EDta\u010De se nem\u016F\u017Eete p\u0159ihl\u00E1sit.
authenticationFailure.InvalidLoginTimeException     = P\u0159ihl\u00E1\u0161en\u00ED v tento \u010Das pro V\u00E1\u0161 \u00FA\u010Det povoleno.
authenticationFailure.UNKNOWN                       = Neplatn\u00E9 p\u0159ihla\u0161ovac\u00ED \u00FAdaje.

copyright = Copyright &copy; 2005&ndash;2012 Apereo, Inc. V\u0161echna pr\u00E1va vyhrazena.

error.invalid.loginticket                    = Nen\u00ED mo\u017En\u00E9 znovu odeslat formul\u00E1\u0159, kter\u00FD ji\u017E byl odesl\u00E1n.

logo.title = j\u00EDt na str\u00E1nky Apereo

password.expiration.loginsRemaining = Zb\u00FDv\u00E1 V\u00E1m {0} p\u0159ihl\u00E1\u0161en\u00ED, ne\u017E budete <strong>MUSET</strong> zm\u011Bnit sv\u00E9 heslo.
password.expiration.warning         = Va\u0161e heslo vypr\u0161\u00ED za {0} dn\u00ED. <a href="{1}">Zm\u011B\u0148te pros\u00EDm ihned sv\u00E9 heslo</a>.

required.password = Heslo je povinn\u00FD \u00FAdaj.
required.username = U\u017Eivatelsk\u00E9 jm\u00E9no je povinn\u00FD \u00FAdaj.

screen.accountdisabled.heading          = Tento \u00FA\u010Det byl zak\u00E1z\u00E1n.
screen.accountdisabled.message          = Pro obnoven\u00ED p\u0159\u00EDstupu kontaktujte pros\u00EDm sv\u00E9ho syst\u00E9mov\u00E9ho administr\u00E1tora.
screen.accountlocked.heading            = Tento \u00FA\u010Det byl uzam\u010Den.
screen.accountlocked.message            = Pro obnoven\u00ED p\u0159\u00EDstupu kontaktujte pros\u00EDm sv\u00E9ho syst\u00E9mov\u00E9ho administr\u00E1tora.
screen.badhours.heading                 = V\u00E1\u0161 \u00FA\u010Det nem\u00E1 povolen\u00ED k p\u0159ihl\u00E1\u0161en\u00ED v tomto \u010Dase.
screen.badhours.message                 = Zkuste to pros\u00EDm pozd\u011Bji.
screen.badworkstation.heading           = Z tohoto po\u010D\u00EDta\u010De se nem\u016F\u017Eete p\u0159ihl\u00E1sit.
screen.badworkstation.message           = Pro obnoven\u00ED p\u0159\u00EDstupu kontaktujte pros\u00EDm sv\u00E9ho syst\u00E9mov\u00E9ho administr\u00E1tora.
screen.blocked.header                   = P\u0159\u00EDstup odep\u0159en
screen.blocked.message                  = Zadal(a) jste \u0161patn\u00E9 heslo p\u0159\u00EDli\u0161 \u010Dasto. P\u0159\u00EDtsup byl do\u010Dasn\u011B zablokov\u00E1n.
#Confirmation Screen Messages
screen.confirmation.message             = Pro p\u0159echod na web <a href="{0}">klikn\u011Bte zde</a>.
screen.expiredpass.heading              = Va\u0161e heslo ji\u017E n\u011Bn\u00ED platn\u00E9.
screen.expiredpass.message              = <a href="{0}">Zm\u011B\u0148te pros\u00EDm sv\u00E9 heslo</a>.
#Logout Screen Messages
screen.logout.header                    = \u00DAsp\u011B\u0161n\u00E9 odhl\u00E1\u0161en\u00ED
screen.logout.redirect                  = Web ze kter\u00E9ho jste sem p\u0159i\u0161li doporu\u010Dil <a href="{0}">odkaz, kam pokra\u010Dovat</a>.
screen.logout.security                  = Z bezpe\u010Dnostn\u00EDch d\u016Fvod\u016F uzav\u0159ete v\u0161echna okna prohl\u00ED\u017Ee\u010De.
screen.logout.success                   = \u00DAsp\u011B\u0161n\u011B jste se odhl\u00E1sili od Centr\u00E1ln\u00ED Autentiza\u010Dn\u00ED Slu\u017Eby.
screen.mustchangepass.heading           = Mus\u00EDte zm\u011Bnit sv\u00E9 heslo.
screen.mustchangepass.message           = <a href="{0}">Zm\u011B\u0148te pros\u00EDm sv\u00E9 heslo</a>.
screen.oauth.confirm.allow              = Povolit
screen.oauth.confirm.header             = Autorizace
screen.oauth.confirm.message            = Chcete povolit p\u0159\u00EDstup ke sv\u00E9mu profilu pro "{0}"?
screen.service.empty.error.message      = Registr slu\u017Eeb CASu je pr\u00E1zdn\u00FD a nem\u00E1 definovan\u00E9 \u017E\u00E1dn\u00E9 slu\u017Eby. Aplikace, kter\u00E9 chcete autentizovat pomoc\u00ED CASu mus\u00EDte explicitn\u011B uv\u00E9st v registru slu\u017Eeb.
#Service Error Messages
screen.service.error.header             = Aplikace nen\u00ED autorizovan\u00E1 k pou\u017Eit\u00ED p\u0159ihl\u0161ov\u00E1n\u00ED pomoc\u00ED CASu.
screen.service.error.message            = Aplikace ke kter\u00E9 se sna\u017E\u00EDte p\u0159ihl\u00E1sit nen\u00ED opr\u00E1vn\u011Bna k vyu\u017Eit\u00ED CASu.
screen.service.sso.error.header         = Pro tuto slu\u017Ebu je po\u017Eadov\u00E1no op\u011Btovn\u00E9 p\u0159ihl\u00E1\u0161en\u00ED
screen.service.sso.error.message        = Pokou\u0161\u00EDte se p\u0159istoupit ke slu\u017Eb\u011B, kter\u00E1 vy\u017Eaduje op\u011Btovn\u00E9 p\u0159ihl\u00E1\u0161en\u00ED. Zkuste se pros\u00EDm <a href="{0}">p\u0159ihl\u00E1sit znovu</a>.
#Generic Success Screen Messages
screen.success.header                   = \u00DAsp\u011B\u0161n\u00E9 p\u0159ihl\u00E1\u0161en\u00ED
screen.success.security                 = Z bezpe\u010Dnostn\u00EDch d\u016Fvod\u016F se po ukon\u010Den\u00ED pr\u00E1ce odhla\u0161te a zav\u0159ete v\u0161echna okna prohl\u00ED\u017Ee\u010De!
screen.success.success                  = \u00DAsp\u011B\u0161n\u011B jste se p\u0159ihl\u00E1sili k Centr\u00E1ln\u00ED Autentika\u010Dn\u00ED Slu\u017Eb\u011B.
screen.unavailable.heading              = CAS nen\u00ED dostupn\u00FD
screen.unavailable.message              = P\u0159i zpracov\u00E1n\u00ED Va\u0161eho po\u017Eadavku do\u0161lo k chyb\u011B. Uv\u011Bdomte pros\u00EDm syst\u00E9movou podporu nebo to zkuste znovu.
screen.welcome.button.clear             = VY\u010CISTIT
screen.welcome.button.login             = P\u0158IHL\u00C1SIT
screen.welcome.instructions             = Zadejte sv\u00E9 u\u017Eivatelsk\u00E9 jm\u00E9no a heslo
screen.welcome.label.netid              = <span class="accesskey">U</span>\u017Eivatelsk\u00E9 jm\u00E9no
screen.welcome.label.netid.accesskey    = u
screen.welcome.label.password           = <span class="accesskey">H</span>eslo:
screen.welcome.label.password.accesskey = h
screen.welcome.label.warn               = Upo<span class="accesskey">z</span>ornit p\u0159ed p\u0159ihl\u00E1\u0161en\u00ED k jin\u00E9 aplikaci.
screen.welcome.label.warn.accesskey     = z
screen.welcome.security                 = Z bezpe\u010Dnostn\u00EDch d\u016Fvod\u016F se po ukon\u010Den\u00ED pr\u00E1ce odhla\u0161te a zav\u0159ete v\u0161echna okna prohl\u00ED\u017Ee\u010De!
#
# Licensed to Apereo under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
screen.welcome.welcome                  = Gratulujeme, \u00FAsp\u011B\u0161n\u011B jste zprovoznili CAS! Pro zji\u0161t\u011Bn\u00ED, jak se p\u0159ihl\u00E1sit, prohl\u00E1dn\u011Bte si v\u00FDchoz\u00ED konfiguraci autentifika\u010Dn\u00EDho handleru.
