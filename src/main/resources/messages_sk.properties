screen.welcome.welcome=Vitajte v CAS - Central Authentication Service
screen.welcome.security=Z bezpeÄnostnÃ½ch dÃ´vodov sa, prosÃ­m, odhlÃ¡ste a ukonÄite prÃ¡cu s webovÃ½m prehliadaÄom, ak ste ukonÄili prÃ¡cu so sluÅ¾bami vyÅ¾adujÃºcimi autentifikÃ¡ciu!
screen.welcome.instructions=<PERSON><PERSON>j<PERSON>Å¡e pouÅ¾Ã­vateÄ¾skÃ© meno a heslo
screen.welcome.label.netid=<span class="accesskey">M</span>eno:
screen.welcome.label.netid.accesskey=m
screen.welcome.label.password=<span class="accesskey">H</span>eslo:
screen.welcome.label.password.accesskey=h
screen.welcome.label.publicstation=Som na verejnej pracovnej stanici.
screen.welcome.label.warn=<span class\="accesskey">V</span>arovaÅ¥ pred prihlÃ¡senÃ­m do inÃ½ch sluÅ¾ieb.
screen.welcome.label.warn.accesskey=v
screen.welcome.button.login=PRIHLÃSIÅ¤
screen.welcome.button.clear=VYMAZAÅ¤

screen.cookies.disabled.title=Cookies v prehliadaÄi sÃº zakÃ¡zanÃ©
screen.cookies.disabled.message=VÃ¡Å¡ browser nepodporuje cookies. Single Sign On NEBUDE FUNGOVAÅ¤.

screen.aup.button.accept=PRIJAÅ¤
screen.aup.button.cancel=ZRUÅ IÅ¤

screen.nonsecure.title=NezabezpeÄenÃ© spojenie
screen.nonsecure.message=Pristupujete k serveru CAS cez nezabezpeÄenÃ© spojenie. Single Sign On NEBUDE FUNGOVAÅ¤. Ak chcete aby single sign on fungovalo, musÃ­te sa prihlÃ¡siÅ¥ cez HTTPS.

logo.title=Prechod na domovskÃº strÃ¡nku
copyright=Copyright &copy; 2005&ndash;2015 Apereo, Inc.
screen.capslock.on = KlÃ¡vesa CAPSLOCK je zapnutÃ¡\!

# Remember-Me Authentication
screen.rememberme.checkbox.title=ZapamÃ¤taÅ¥ si ma

# Blocked Errors Page
screen.blocked.header=PrÃ­stup zamietnutÃ½
screen.blocked.message=VloÅ¾ili ste viackrÃ¡t nesprÃ¡vne heslo. VaÅ¡e prihlÃ¡senie je doÄasne zablokovanÃ©.
AbstractAccessDecisionManager.accessDenied=Nie ste oprÃ¡vnenÃ½ k prÃ­stupu k tomuto zdroju. Pre viac informÃ¡ciÃ­ kontaktujte administrÃ¡tora.

#Confirmation Screen Messages
screen.confirmation.message=Kliknite <a href\="{0}">sem</a> pre prÃ­stup k aplikÃ¡cii.

#Generic Success Screen Messages
screen.success.header=PrihlÃ¡senie bolo ÃºspeÅ¡nÃ©
screen.success.success= {0}, prÃ¡ve ste sa ÃºspeÅ¡ne prihlÃ¡sili do CAS.
screen.success.security=Ak ste ukonÄili prÃ¡cu, z bezpeÄnostnÃ½ch dÃ´vodov prosÃ­m odhlÃ¡ste sa a ukonÄite prÃ¡cu s prehliadaÄom.

#Logout Screen Messages
screen.logout.header=OdhlÃ¡senie bolo ÃºspeÅ¡nÃ©
screen.logout.success=ÃspeÅ¡ne ste sa odhlÃ¡sili zo sluÅ¾by centrÃ¡lnej autentifikÃ¡cie.
screen.logout.security=Z bezpeÄnostnÃ½ch dÃ´vodov ukonÄite prÃ¡cu s prehliadaÄom.
screen.logout.redirect=SluÅ¾ba, z ktorej ste priÅ¡li poskytla <a href\="{0}">linku, ktorÃº mÃ´Å¾ete nasledovaÅ¥ kliknutÃ­m sem</a>.

screen.service.sso.error.header=Na prÃ­stup k sluÅ¾be je nutnÃ¡ opakovanÃ¡ autentifikÃ¡cia
screen.service.sso.error.message=PokÃºsili ste sa o prÃ­stup k sluÅ¾be, ktorÃ¡ vyÅ¾aduje autentifkÃ¡ciu bez opÃ¤tovnej autentifikÃ¡cie.  ProsÃ­m skÃºste sa <a href\="{0}">autentifikovaÅ¥ znovu</a>.
screen.service.required.message=PokÃºsili ste sa autentifikovaÅ¥ bez zadania cieÄ¾ovej aplikÃ¡cie. ProsÃ­m preverte poÅ¾iadavku a skÃºste znovu.

error.invalid.loginticket=Nie je moÅ¾nÃ© odoslaÅ¥ opakovanÃº poÅ¾iadavku na formulÃ¡r, ktorÃ½ uÅ¾ bol odoslanÃ½.
username.required=Meno pouÅ¾Ã­vateÄ¾a je povinnÃ© pole.
password.required=Heslo je povinnÃ© pole.

# Authentication failure messages
authenticationFailure.AccountDisabledException=Tento pouÅ¾Ã­vateÄ¾skÃ½ ÃºÄet bol deaktivovanÃ½.
authenticationFailure.AccountLockedException=Tento pouÅ¾Ã­vateÄ¾skÃ½ ÃºÄet bol uzamknutÃ½.
authenticationFailure.CredentialExpiredException=VaÅ¡e heslo je expirovanÃ©.
authenticationFailure.InvalidLoginLocationException=Z tejto pracovnej stanice nie je moÅ¾nÃ© sa prihlÃ¡siÅ¥.
authenticationFailure.InvalidLoginTimeException=VÃ¡Å¡ ÃºÄet mÃ¡ v tomto Äase zakÃ¡zanÃ½ prÃ­stup.
authenticationFailure.AccountNotFoundException=NesprÃ¡vne pouÅ¾Ã­vateÄ¾skÃ© meno alebo heslo.
authenticationFailure.FailedLoginException=NesprÃ¡vne pouÅ¾Ã­vateÄ¾skÃ© meno alebo heslo.
authenticationFailure.UNKNOWN=NesprÃ¡vne pouÅ¾Ã­vateÄ¾skÃ© meno alebo heslo.

INVALID_REQUEST_PROXY=VaÅ¡a poÅ¾iadavka je nekorektne formulovanÃ¡. ProsÃ­m uistite sa Å¾e vÅ¡etky poÅ¾adovanÃ© parametre sÃº v poÅ¾iadavke a sÃº sprÃ¡vne formÃ¡tovanÃ©.
INVALID_TICKET_SPEC=Ticket nepreÅ¡iel validÃ¡ciou. MoÅ¾nÃ¡ prÃ­Äina je validÃ¡cia Proxy Ticketu cez Service Ticket validator.
INVALID_REQUEST='service' a 'ticket' parametre sÃº povinnÃ©
INVALID_TICKET=Ticket ''{0}'' nebol rozpoznanÃ½
INVALID_SERVICE=Ticket ''{0}'' nezodpovedÃ¡ dodanej sluÅ¾be. PÃ´vodnÃ¡ sluÅ¾ba bola ''{1}'' a dodanÃ¡ bola ''{2}''.
INVALID_PROXY_CALLBACK=DodanÃ½ proxy callback url ''{0}'' nemÃ´Å¾e byÅ¥ authentifikovanÃ½.
UNAUTHORIZED_SERVICE_PROXY=DodanÃ¡ sluÅ¾ba ''{0}'' nie je autorizovanÃ¡ na pouÅ¾itie CAS autentifikÃ¡cie.

screen.service.error.header=AplikÃ¡cia nie je autorizovanÃ¡ na pouÅ¾itie CAS
service.not.authorized.missing.attr=Nie ste autorizovanÃ½ na prÃ­stup k aplikÃ¡cii pretoÅ¾e VaÅ¡e pouÅ¾Ã­vateÄ¾skÃ½ ÃºÄet \
neobsahuje privilÃ©giÃ¡ poÅ¾adovanÃ© serverom CAS na autentifikÃ¡ciu do tejto sluÅ¾by. ProsÃ­m oboznÃ¡mte VÃ¡Å¡ho sprÃ¡vcu systÃ©mu.
screen.service.error.message=AplikÃ¡cia do ktorej sa pokÃºÅ¡ate prihlÃ¡siÅ¥ nie je autorizovanÃ¡ na pouÅ¾itie tÃ½mto CAS serverom.
screen.service.empty.error.message=Register sluÅ¾ieb serveru CAS je prÃ¡zdny a neobsahuje definÃ­cie sluÅ¾ieb. \
AplikÃ¡cia ktorÃ¡ sa chce autentifikovaÅ¥ serverom CAS musÃ­ byÅ¥ explicitne definovanÃ¡ v registri sluÅ¾ieb.

# Password policy
password.expiration.warning=VaÅ¡e heslo expiruje za {0} deÅ/dnÃ­. ProsÃ­m <a href="{1}">zmente VaÅ¡e heslo</a> teraz.
password.expiration.loginsRemaining=MÃ¡te {0} prÃ­stup(ov) k dispozÃ­cii, kÃ½m <strong>MUSÃTE</strong> zmeniÅ¥ VaÅ¡e heslo.
screen.accountdisabled.heading=Tento pouÅ¾Ã­vateÄ¾skÃ½ ÃºÄet bol deaktivovanÃ½.
screen.accountdisabled.message=ProsÃ­m kontaktujte systÃ©movÃ©ho administrÃ¡tora na opÃ¤tovnÃ© zÃ­skanie prÃ­stupu.
screen.accountlocked.heading=Tento pouÅ¾Ã­vateÄ¾skÃ½ ÃºÄet bol uzamknutÃ½.
screen.accountlocked.message=ProsÃ­m kontaktujte systÃ©movÃ©ho administrÃ¡tora na opÃ¤tovnÃ© zÃ­skanie prÃ­stupu.
screen.expiredpass.heading=VaÅ¡e heslo expirovalo.
screen.expiredpass.message=ProsÃ­m <a href\="{0}">zmeÅte svoje heslo</a>.
screen.mustchangepass.heading=Je nutnÃ© zmeniÅ¥ VaÅ¡e heslo.
screen.mustchangepass.message=ProsÃ­m <a href\="{0}">zmeÅte svoje heslo</a>.
screen.badhours.heading=VÃ¡Å¡ pouÅ¾Ã­vateÄ¾skÃ½ ÃºÄet je v tomto momente zakÃ¡zanÃ½.
screen.badhours.message=ProsÃ­m skÃºste znovu neskÃ´r.
screen.badworkstation.heading=Nie je moÅ¾nÃ© sa prihlÃ¡siÅ¥ z tejto pracovnej stanice.
screen.badworkstation.message=Na opÃ¤tovnÃ© zÃ­skanie prÃ­stupu prosÃ­m kontaktujte administrÃ¡tora.

# OAuth
screen.oauth.confirm.header=AuthorizÃ¡cia
screen.oauth.confirm.message=Chete povoliÅ¥ prÃ­stup k VÃ¡Å¡mu kompletnÃ©mu profilu pre "{0}" ?
screen.oauth.confirm.allow=PovoliÅ¥

# Unavailable
screen.unavailable.heading=CAS je nedostupnÃ½
screen.unavailable.message=Pri spracovanÃ­ poÅ¾iadavky nastala chyba. ProsÃ­m informujte podporu alebo skÃºste znovu.
