<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="debug" name="XMLConfigTest" packages="cn.dahe.cas.auth">

    <ThresholdFilter level="trace"/>

    <Appenders>
        <Console name="STDOUT" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{DEFAULT} %C{5} %highlight{%-5level: %msg%n%throwable} %n"/>
        </Console>
        <Console name="FLOW">
            <!-- this pattern outputs class name and line number -->
            <PatternLayout pattern="%d{DEFAULT} %C{5} %highlight{%-5level: %msg%n%throwable} %n"/>
            <filters>
                <MarkerFilter marker="FLOW" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
                <MarkerFilter marker="EXCEPTION" onMatch="ACCEPT" onMismatch="DENY"/>
            </filters>
        </Console>
    </Appenders>

    <Loggers>
        <Logger name="cn.dahe.cas.auth" level="debug" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>

        <Logger name="org.jasig" level="debug" additivity="false">
            <Property name="user">${sys:user.name}</Property>
            <AppenderRef ref="STDOUT" level="debug"/>
        </Logger>

        <logger name="org.springframework.data.mongodb.core" level="debug">
            <AppenderRef ref="STDOUT" level="debug"/>
        </logger>

        <Root level="error">
            <AppenderRef ref="STDOUT"/>
        </Root>
    </Loggers>

</Configuration>