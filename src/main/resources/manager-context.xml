<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">
    <context:property-placeholder location="classpath:*.properties" order="1" ignore-unresolvable="true" file-encoding="UTF-8"/>
    <import resource="shiro.xml"/>
    <import resource="redis.xml"/>
    <import resource="jcaptcha.xml"/>
    <import resource="jpa.xml"/>
    <import resource="jms.xml"/>
    <import resource="database.xml"/>
    <import resource="spring-mongo.xml"/>
    <context:component-scan base-package="cn.dahe.cas">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
    <bean id="globalConvert" class="com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter">
        <property name="fastJsonConfig" ref="fastJsonConfig"/>
    </bean>
    <bean class="cn.dahe.cas.auth.config.JsonConfig"/>
    <bean id="mvcConversionService" class="org.springframework.format.support.FormattingConversionServiceFactoryBean" primary="true">
        <property name="converters">
            <set>
                <bean class="cn.dahe.cas.auth.converter.CommonEnumConverter"/>
                <bean class="cn.dahe.cas.auth.converter.SiteStatusConverter"/>
                <bean class="cn.dahe.cas.auth.converter.StatusConverter"/>
                <bean class="cn.dahe.cas.auth.converter.LogTypeConverter"/>
                <bean class="cn.dahe.cas.auth.converter.LogMessageConverter"/>
                <bean class="cn.dahe.cas.auth.converter.DateConverter"/>
                <bean class="cn.dahe.cas.auth.converter.SsoUserConverter"/>
                <bean class="cn.dahe.cas.auth.converter.StringToEnumConverterFactory"/>
                <bean class="cn.dahe.cas.auth.converter.UserProfileDtoConverter"/>
                <ref bean="ssoUserDtoConverter"/>
            </set>
        </property>
    </bean>
    <task:executor id="ssoThreadPoolTaskExecutor" pool-size="20-100" queue-capacity="100"/>
    <task:scheduler id="ssoScheduler" pool-size="30"/>
    <task:annotation-driven executor="ssoThreadPoolTaskExecutor" scheduler="ssoScheduler" proxy-target-class="true" mode="aspectj"/>
    <bean id="tokenCreator" class="cn.dahe.cas.auth.security.RedisTokenSecurity"/>
    <bean id="restTemplate" class="org.springframework.web.client.RestTemplate"/>
    <bean id="authenticationFilter" class="org.jasig.cas.client.authentication.AuthenticationFilter">
        <property name="casServerLoginUrl" value="${cas.server}/login"/>
        <property name="serverName" value="${cas.server}/home"/>
    </bean>
    <bean id="ticketValidationFilter" class="org.jasig.cas.client.validation.Cas30ProxyReceivingTicketValidationFilter">
        <property name="serverName" value="${cas.server}/home/"/>
        <property name="ticketValidator">
            <bean class="org.jasig.cas.client.validation.Cas30ServiceTicketValidator">
                <constructor-arg index="0" value="${cas.server}"/>
            </bean>
        </property>
    </bean>
    <bean id="casSingleLogoutFilter" class="cn.dahe.cas.auth.filter.CasLogoutFilter">
        <property name="ignoreInitConfiguration" value="true"/>
        <property name="casServerUrlPrefix" value="${cas.server}"/>
    </bean>
    <!--单点登录过期时间设置-->
    <bean id="defaultTcgCookie" class="org.jasig.cas.web.support.TGCCookieRetrievingCookieGenerator">
        <property name="cookieMaxAge" value="#{24*60*60}"/>
    </bean>

    <!--sso用户属性解析dao-->
    <bean id="singleRowJdbcPersonAttributeDao"
          class="cn.dahe.cas.auth.cas.CachePersonAttributeDao">
        <constructor-arg index="0" ref="dataSource" />
        <constructor-arg index="1" value="${attribute_sql}" />
        <property name="queryAttributeMapping">
            <map>
                <entry key="username" value="uid" />
            </map>
        </property>
        <property name="resultAttributeMapping">
            <map>
                <entry key="uid" value="uid"/>
                <entry key="username" value="username" />
                <entry key="true_name">
                    <set>
                        <value>truename</value>
                    </set>
                </entry>
                <entry key="icon" value="avatar" />
                <entry key="email" value="email" />
                <entry key="sex" value="sex"/>
                <entry key="department" value="sid"/>
                <entry key="organization" value="organization"/>
                <entry key="area_code" value="areaCode"/>
            </map>
        </property>
    </bean>
    <bean id="mvcValidator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean"/>
    <bean class="org.springframework.validation.beanvalidation.MethodValidationPostProcessor">
        <property name="validator" ref="mvcValidator"/>
    </bean>
    <bean class="cn.dahe.cas.auth.service.impl.AreaServiceImpl">
        <constructor-arg name="areasJson" value="classpath:areas.json"/>
    </bean>
</beans>