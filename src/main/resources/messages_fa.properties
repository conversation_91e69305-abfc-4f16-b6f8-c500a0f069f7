#Welcome Screen Messages

#
# Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=<span dir='rtl'>\u0645\u0648\u0641\u0642 \u0634\u062F\u06CC\u062F CAS \u0631\u0627 \u0628\u0647 \u0635\u0648\u0631\u062A \u0622\u0646\u0644\u0627\u06CC\u0646 \u0628\u0627\u0631\u06AF\u0630\u0627\u0631\u06CC \u06A9\u0646\u06CC\u062F! \u0645\u062F\u06CC\u0631 \u062A\u0627\u06CC\u06CC\u062F \u067E\u06CC\u0634 \u0641\u0631\u0636\u060C \u062A\u0627\u06CC\u06CC\u062F \u0645\u06CC\u06A9\u0646\u062F \u0686\u0647 \u0632\u0645\u0627\u0646\u06CC \u0646\u0627\u0645 \u06A9\u0627\u0631\u0628\u0631\u06CC \u0648 \u0631\u0645\u0632 \u0648\u0631\u0648\u062F \u0647\u0645\u062E\u0648\u0627\u0646\u06CC \u062F\u0627\u0631\u0646\u062F: \u0645\u0631\u0627\u062D\u0644 \u0631\u0627 \u0627\u062F\u0627\u0645\u0647 \u062F\u0647\u06CC\u062F \u0648 \u0627\u0645\u062A\u062D\u0627\u0646 \u06A9\u0646\u06CC\u062F.</span>
screen.welcome.security=<span dir='rtl'>\u0628\u0647 \u062F\u0644\u0627\u06CC\u0644 \u0627\u0645\u0646\u06CC\u062A\u06CC \u0632\u0645\u0627\u0646\u06CC \u06A9\u0647 \u062F\u06CC\u06AF\u0631 \u0646\u06CC\u0627\u0632\u06CC \u0628\u0647 \u062F\u0633\u062A\u06CC\u0627\u0628\u06CC \u0628\u0647 \u0633\u0631\u0648\u06CC\u0633\u0647\u0627\u06CC\u06CC \u06A9\u0647 \u0627\u062D\u062A\u06CC\u0627\u062C \u0628\u0647 \u062A\u0627\u06CC\u06CC\u062F \u062F\u0627\u0631\u0646\u062F \u0646\u062F\u0627\u0634\u062A\u06CC\u062F\u060C \u0627\u0632 \u067E\u0627\u06CC\u06AF\u0627\u0647 \u062E\u0627\u0631\u062C \u0634\u062F\u0647 \u0645\u0631\u0648\u0631\u06AF\u0631 \u062E\u0648\u062F \u0631\u0627 \u0628\u0628\u0646\u062F\u06CC\u062F!</span>
screen.welcome.instructions=<span dir='rtl'>\u0646\u0627\u0645 \u06A9\u0627\u0631\u0628\u0631\u06CC \u0648 \u0631\u0645\u0632 \u0648\u0631\u0648\u062F \u0631\u0627 \u0648\u0627\u0631\u062F \u06A9\u0646\u06CC\u062F</span>
screen.welcome.label.netid=<span dir='rtl'>\u0646\u0627\u0645 \u06A9\u0627\u0631\u0628\u0631\u06CC</span>
screen.welcome.label.netid.accesskey=
screen.welcome.label.password=<span dir='rtl'>\u0631\u0645\u0632 \u0648\u0631\u0648\u062F</span>
screen.welcome.label.password.accesskey=
screen.welcome.label.warn=<span dir='rtl'>\u0642\u0628\u0644 \u0627\u0632 \u0648\u0631\u0648\u062F \u0628\u0647 \u0633\u0627\u06CC\u062A\u0647\u0627\u06CC \u062F\u06CC\u06AF\u0631 \u0628\u0647 \u0645\u0646 \u0647\u0634\u062F\u0627\u0631 \u0628\u062F\u0647</span>
screen.welcome.label.warn.accesskey=
screen.welcome.button.login=\u0648\u0631\u0648\u062F
screen.welcome.button.clear=\u0627\u0646\u0635\u0631\u0627\u0641

logo.title=\u0628\u0647 \u0635\u0641\u062D\u0647 \u0627\u0635\u0644\u06CC Apereo \u0628\u0631\u0648
copyright=<span dir='rtl'>\u062D\u0642 \u0646\u0634\u0631 &copy; 2005 - 2015 Apereo, Inc. \u06A9\u0644\u06CC\u0647 \u062D\u0642\u0648\u0642 \u0645\u062D\u0641\u0648\u0638 \u0627\u0633\u062A</span>

# Blocked Errors Page
screen.blocked.header=<span dir='rtl'>\u062F\u0633\u062A\u0631\u0633\u06CC \u0645\u0645\u06A9\u0646 \u0646\u06CC\u0633\u062A</span>
screen.blocked.message=<span dir='rtl'>\u0628\u0647 \u062F\u0641\u0639\u0627\u062A \u0628\u0631\u0627\u06CC \u0646\u0627\u0645 \u06A9\u0627\u0631\u0628\u0631\u06CC\u060C \u0631\u0645\u0632 \u0648\u0631\u0648\u062F \u0631\u0627 \u0627\u0634\u062A\u0628\u0627\u0647 \u0648\u0627\u0631\u062F \u06A9\u0631\u062F\u0647\u0627\u06CC\u062F. \u0627\u0632 \u0648\u0631\u0648\u062F \u0634\u0645\u0627 \u062C\u0644\u0648\u06AF\u06CC\u0631\u06CC \u0634\u062F\u0647 \u0627\u0633\u062A.</span>

#Confirmation Screen Messages
screen.confirmation.message=<span dir='rtl'>\u0628\u0631\u0627\u06CC \u0648\u0631\u0648\u062F \u0628\u0647 \u0628\u0631\u0646\u0627\u0645\u0647 </a>\u0627\u06CC\u0646\u062C\u0627<a href=\u201D{0}\u201D> \u0631\u0627 \u06A9\u0644\u06CC\u06A9 \u06A9\u0646\u06CC\u062F</span>

#Generic Success Screen Messages
screen.success.header=<span dir='rtl'>\u0648\u0631\u0648\u062F \u0645\u0648\u0641\u0642\u06CC\u062A \u0622\u0645\u06CC\u0632 \u0628\u0648\u062F</span>
screen.success.success=<span dir='rtl'>\u0628\u0627 \u0645\u0648\u0641\u0642\u06CC\u062A \u0648\u0627\u0631\u062F \u067E\u0627\u06CC\u06AF\u0627\u0647 \u062A\u0627\u06CC\u06CC\u062F \u0645\u0631\u06A9\u0632\u06CC CAS  \u0634\u062F\u06CC\u062F</span>
screen.success.security=<span dir='rtl'>\u0628\u0647 \u062F\u0644\u0627\u06CC\u0644 \u0627\u0645\u0646\u06CC\u062A\u06CC \u0632\u0645\u0627\u0646\u06CC \u06A9\u0647 \u062F\u06CC\u06AF\u0631 \u0646\u06CC\u0627\u0632\u06CC \u0628\u0647 \u062F\u0633\u062A\u06CC\u0627\u0628\u06CC \u0628\u0647 \u0633\u0631\u0648\u06CC\u0633 \u0647\u0627\u06CC\u06CC \u06A9\u0647 \u0627\u062D\u062A\u06CC\u0627\u062C \u0628\u0647 \u062A\u0627\u06CC\u06CC\u062F \u062F\u0627\u0631\u0646\u062F \u0646\u062F\u0627\u0634\u062A\u06CC\u062F\u060C \u0627\u0632 \u067E\u0627\u06CC\u06AF\u0627\u0647 \u062E\u0627\u0631\u062C \u0634\u062F\u0647 \u0645\u0631\u0648\u0631\u06AF\u0631 \u062E\u0648\u062F \u0631\u0627 \u0628\u0628\u0646\u062F\u06CC\u062F!</span>

#Logout Screen Messages
screen.logout.header=<span dir='rtl'>\u062E\u0631\u0648\u062C \u0627\u0632 \u067E\u0627\u06CC\u06AF\u0627\u0647 \u0645\u0648\u0641\u0642\u06CC\u062A \u0622\u0645\u06CC\u0632 \u0628\u0648\u062F</span>
screen.logout.success=<span dir='rtl'>\u0628\u0627 \u0645\u0648\u0641\u0642\u06CC\u062A \u0627\u0632 \u067E\u0627\u06CC\u06AF\u0627\u0647 \u062A\u0627\u06CC\u06CC\u062F \u0645\u0631\u06A9\u0632\u06CC CAS \u062E\u0627\u0631\u062C \u0634\u062F\u06CC\u062F</span>
screen.logout.security=<span dir='rtl'>\u0628\u0631\u0627\u06CC \u062D\u0641\u0638 \u0627\u0645\u0646\u06CC\u062A \u0627\u0632 \u0645\u0631\u0648\u0631\u06AF\u0631 \u062E\u0648\u062F \u062E\u0627\u0631\u062C \u0634\u0648\u06CC\u062F</span>
screen.logout.redirect=<span dir='rtl'>\u0633\u0631\u0648\u06CC\u0633\u06CC \u0627\u0631\u062C\u0627\u0639 \u062F\u0647\u0646\u062F\u0647 \u0634\u0645\u0627 </a>\u0627\u06CC\u0646 \u0644\u06CC\u0646\u06A9<a href="{0}"> \u0631\u0627 \u062A\u0648\u0644\u06CC\u062F \u06A9\u0631\u062F\u0647 \u0627\u0633\u062A. \u0628\u0631\u0627\u06CC \u0627\u062F\u0627\u0645\u0647 \u0644\u06CC\u0646\u06A9 \u0631\u0627 \u06A9\u0644\u06CC\u06A9 \u06A9\u0646\u06CC\u062F.</span>

screen.service.sso.error.header=<span dir='rtl'>\u0628\u0631\u0627\u06CC \u062F\u0633\u062A\u0631\u0633\u06CC \u0628\u0647 \u0627\u06CC\u0646 \u0633\u0631\u0648\u06CC\u0633 \u0646\u06CC\u0627\u0632 \u0628\u0647 \u062A\u0627\u06CC\u06CC\u062F \u062F\u0648\u0628\u0627\u0631\u0647 \u062F\u0627\u0631\u06CC\u062F</span>
screen.service.sso.error.message=<span dir='rtl'>\u0633\u0639\u06CC \u062F\u0627\u0634\u062A\u06CC\u062F \u0628\u062F\u0648\u0646 \u062A\u0627\u06CC\u06CC\u062F \u062F\u0648\u0628\u0627\u0631\u0647\u060C \u0628\u0647 \u0633\u0631\u0648\u06CC\u0633\u06CC \u062F\u0633\u062A\u0631\u0633\u06CC \u067E\u06CC\u062F\u0627 \u06A9\u0646\u06CC\u062F \u06A9\u0647 \u0646\u06CC\u0627\u0632 \u0628\u0647 \u062A\u0627\u06CC\u06CC\u062F \u062F\u0627\u0631\u062F. \u0644\u0637\u0641\u0627\u064B \u0628\u0639\u062F \u0627\u0632 </a>\u062A\u0627\u06CC\u06CC\u062F <a href=\u201D{0}\u201D> \u062F\u0648\u0628\u0627\u0631\u0647 \u0627\u0645\u062A\u062D\u0627\u0646 \u06A9\u0646\u06CC\u062F</span>

error.invalid.loginticket=\u0646\u0645\u06CC\u062A\u0648\u0627\u0646\u06CC\u062F \u0641\u0631\u0645\u06CC \u06A9\u0647 \u0627\u0631\u0633\u0627\u0644 \u0634\u062F\u0647 \u0631\u0627 \u062F\u0648\u0628\u0627\u0631\u0647 \u0627\u0631\u0633\u0627\u0644 \u06A9\u0646\u06CC\u062F
username.required=\u0648\u0627\u0631\u062F \u06A9\u0631\u062F\u0646 \u0646\u0627\u0645 \u06A9\u0627\u0631\u0628\u0631\u06CC \u0627\u0644\u0632\u0627\u0645\u06CC \u0627\u0633\u062A
password.required=\u0648\u0627\u0631\u062F \u06A9\u0631\u062F\u0646 \u0631\u0645\u0632 \u0648\u0631\u0648\u062F \u0627\u0644\u0632\u0627\u0645\u06CC \u0627\u0633\u062A
error.authentication.credentials.bad=\u0646\u0627\u0645 \u06A9\u0627\u0631\u0628\u0631\u06CC \u0648 \u0631\u0645\u0632 \u0648\u0631\u0648\u062F \u0635\u062D\u06CC\u062D \u0646\u0645\u06CC\u0628\u0627\u0634\u062F
error.authentication.credentials.unsupported=>CAS \u0627\u06CC\u0646 \u0646\u0627\u0645 \u06A9\u0627\u0631\u0628\u0631\u06CC \u0648 \u0631\u0645\u0632 \u0648\u0631\u0648\u062F \u0631\u0627 \u067E\u0634\u062A\u06CC\u0627\u0646\u06CC \u0646\u0645\u06CC\u06A9\u0646\u062F

INVALID_REQUEST_PROXY=\u067E\u0627\u0631\u0627\u0645\u062A\u0631\u0647\u0627\u06CC pgt  \u0648 targetService \u0647\u0631 \u062F\u0648 \u0627\u0644\u0632\u0627\u0645\u06CC \u0647\u0633\u062A\u0646\u062F
INVALID_TICKET_SPEC=\u0634\u0646\u0627\u0633\u0647 \u0645\u0648\u0631\u062F \u062A\u0627\u06CC\u06CC\u062F \u0642\u0631\u0627\u0631 \u0646\u06AF\u0631\u0641\u062A. \u062E\u0637\u0627\u0647\u0627\u06CC \u0645\u0645\u06A9\u0646 \u0645\u06CC\u062A\u0648\u0627\u0646\u062F \u0634\u0627\u0645\u0644 \u0633\u0639\u06CC \u062F\u0631 \u0645\u0648\u0631\u062F \u062A\u0627\u06CC\u06CC\u062F \u0642\u0631\u0627\u0631 \u062F\u0627\u062F\u0646 \u0634\u0646\u0627\u0633\u0647-\u06CC \u067E\u0631\u0627\u06A9\u0633\u06CC \u0627\u0632 \u0637\u0631\u06CC\u0642 \u0633\u06CC\u0633\u062A\u0645 \u062A\u0627\u06CC\u06CC\u062F \u06A9\u0646\u0646\u062F\u0647\u06CC \u0634\u0646\u0627\u0633\u0647\u06CC \u0633\u0631\u0648\u06CC\u0633 \u06CC\u0627 \u0647\u0645\u062E\u0648\u0627\u0646\u06CC \u0646\u062F\u0627\u0634\u062A\u0646 \u0628\u0627 \u062F\u0631\u062E\u0648\u0627\u0633\u062A \u062A\u062C\u062F\u06CC\u062F \u0634\u062F\u0647 \u0628\u0627\u0634\u062F.
INVALID_REQUEST=\u067E\u0627\u0631\u0627\u0645\u062A\u0631\u0647\u0627\u06CC service\u0648 ticket \u0647\u0631 \u062F\u0648 \u0627\u0644\u0632\u0627\u0645\u06CC \u0647\u0633\u062A\u0646\u062F
INVALID_TICKET=\u0634\u0646\u0627\u0633\u0647 {0} \u0634\u0646\u0627\u0633\u0627\u06CC\u06CC \u0646\u0634\u062F
INVALID_SERVICE=\u0634\u0646\u0627\u0633\u0647 {0} \u0628\u0627 \u0633\u0631\u0648\u06CC\u0633 \u0639\u0631\u0636\u0647 \u0634\u062F\u0647 \u0647\u0645\u062E\u0648\u0627\u0646\u06CC \u0646\u062F\u0627\u0631\u062F. \u0633\u0631\u0648\u06CC\u0633 \u0627\u0635\u0644\u06CC{1}  \u0648 \u0633\u0631\u0648\u06CC\u0633 \u0639\u0631\u0636\u0647 \u0634\u062F\u0647{2} \u0628\u0648\u062F\u0647 \u0627\u0633\u062A.

screen.service.error.header=<span dir='rtl'>\u0628\u0631\u0646\u0627\u0645\u0647 \u0628\u0631\u0627\u06CC \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 CAS \u062A\u0627\u06CC\u06CC\u062F \u0646\u0634\u062F\u0647 \u0627\u0633\u062A</span>
screen.service.error.message=<span dir='rtl'>\u0628\u0631\u0646\u0627\u0645\u0647\u0627\u06CC \u06A9\u0647 \u0633\u0639\u06CC \u062F\u0631 \u062A\u0627\u06CC\u06CC\u062F \u0622\u0646 \u062F\u0627\u0634\u062A\u06CC\u062F\u060C \u0628\u0631\u0627\u06CC \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0627\u0632 CAS \u0645\u0639\u062A\u0628\u0631 \u0646\u06CC\u0633\u062A.</span>
