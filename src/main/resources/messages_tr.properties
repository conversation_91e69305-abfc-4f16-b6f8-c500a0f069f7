# Author : Me<PERSON> <<EMAIL>>
# http://www.jroller.com/mert

#Welcome Screen Messages

#
# Licensed to <PERSON>per<PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Tebrikler!, CAS'\u0131 \u00e7al\u0131\u015f\u0131r hale getirdiniz. Haz\u0131rdaki kimliklendirme mekanizmas\u0131 kullan\u0131c\u0131 ad\u0131 ve parola ayn\u0131 oldu\u011fu durumlarda giri\u015fe izin vermektedir. Hemen deneyebilirsiniz.
screen.welcome.security=G\u00fcvenli\u011finiz i\u00e7in, i\u015finiz bittikten sonra kulland\u0131\u011f\u0131n\u0131z uygulamalardan \u00e7\u0131k\u0131\u015f yap\u0131n\u0131z ve taray\u0131c\u0131n\u0131z\u0131 kapat\u0131n\u0131z.
screen.welcome.instructions=Kullan\u0131c\u0131 ad\u0131 ve parolan\u0131z\u0131 giriniz
screen.welcome.label.netid=<span class="accesskey">K</span>ullan\u0131c\u0131 Ad\u0131:
screen.welcome.label.netid.accesskey=k
screen.welcome.label.password=<span class="accesskey">P</span>arola:
screen.welcome.label.password.accesskey=p
screen.welcome.label.warn=Di\u011fer sitelere girmeden \u00f6nce beni <span class="accesskey">u</span>yar.
screen.welcome.label.warn.accesskey=u
screen.welcome.button.login=G\u0130R\u0130\u015e
screen.welcome.button.clear=TEM\u0130ZLE

#Confirmation Screen Messages
screen.confirmation.message=Uygulamaya eri\u015fmek i\u00e7in <a href="{0}">buraya</a> t\u0131klay\u0131n\u0131z.

#Generic Success Screen Messages
screen.success.header=Oturum ba\u015far\u0131yla a\u00e7\u0131ld\u0131.
screen.success.success=Merkezi Kimliklendirme Servisi'ne ba\u015far\u0131l\u0131 bir \u015fekilde giri\u015f yapt\u0131n\u0131z.
screen.success.security=G\u00fcvenlik nedenlerinden dolay\u0131, uygulamalar\u0131n kullan\u0131m\u0131 bittikten sonra sistemden \u00e7\u0131k\u0131\u015f yap\u0131p, taray\u0131c\u0131n\u0131z\u0131 kapat\u0131n\u0131z.

#Logout Screen Messages
screen.logout.header=Oturum ba\u015far\u0131yla kapat\u0131ld\u0131.
screen.logout.success=Merkezi Kimliklendirme Servisi'nden ba\u015far\u0131l\u0131 bir \u015fekilde \u00e7\u0131k\u0131\u015f yapt\u0131n\u0131z.
screen.logout.security=G\u00fcvenlik nedenlerinden dolay\u0131, taray\u0131c\u0131n\u0131z\u0131 kapan\u0131t\u0131z.
screen.logout.redirect=Kimliklendirme servisi'ne y\u00f6nlendirme i\u00e7in verilen <a href="{0}">ba\u011flant\u0131ya t\u0131klayarak devam edebilirsiniz</a>.

screen.service.sso.error.header=Bu servise eri\u015fim i\u00e7in tekrar kimliklendirme gerekmektedir.
screen.service.sso.error.message=Bir servise ard\u0131\u015f\u0131k kimlik onay\u0131 yaparak eri\u015fmeye \u00e7al\u0131\u015ft\u0131n\u0131z. Onay i\u00e7in l\u00fctfen tekrar <a href="{0}">t\u0131klay\u0131n\u0131z</a>.

error.invalid.loginticket=\u00d6nceden g\u00f6nderilmi\u015f bir giri\u015f formunu tekrar g\u00f6nderemezsiniz.
username.required=Kullan\u0131c\u0131 Ad\u0131 girilmesi gerekli bir aland\u0131r.
password.required=Parola girilmesi gerekli bir aland\u0131r.
error.authentication.credentials.bad=Kullan\u0131c\u0131 Kodu veya Parola bilginizde yanl\u0131\u015fl\u0131k var. L\u00fctfen kontrol edip tekrar deneyiniz.
error.authentication.credentials.unsupported=Sa\u011flad\u0131\u011f\u0131n\u0131z kimliklendirme bilgileri Merkezi Kimliklendirme Sistemi taraf\u0131ndan tan\u0131nmamaktad\u0131r.

INVALID_REQUEST_PROXY='pgt' ve 'targetService' parametrelerinin her ikisi birden gereklidir.
INVALID_TICKET_SPEC=Bilet do\u011frulama ba\u015far\u0131s\u0131z oldu. Olas\u0131 hatalar, servis bilet do\u011frulay\u0131c\u0131 ile Vekil (Proxy) bilet do\u011frulamak veya do\u011fru yenileme iste\u011fi kural\u0131na uyulmamas\u0131 olabilir.
INVALID_REQUEST='service' ve 'ticket' parametrelerinin her ikisi birden gereklidir.
INVALID_TICKET=Tan\u0131ms\u0131z bilet: ''{0}''
INVALID_SERVICE=Bilet ''{0}'' belirtilen servis ile e\u015fle\u015fmiyor.  As\u0131l servis: ''{1}'', belirtilen servis: ''{2}''.

screen.service.error.header=Uygulama, Merkezi Kimliklendirme Servisi'ni kullanmak i\u00e7in yetkilendirilmemi\u015f.
screen.service.error.message=Kimliklendirme onay\u0131 yap\u0131lmaya \u00e7al\u0131\u015f\u0131lan uygulama, Merkezi Kimliklendirme Servisi'ni kullanmak i\u00e7in yetkilendirilmemi\u015f.
