screen.welcome.welcome=GlÃ¼ckwunsch, Sie haben Ihr CAS System zum Laufen gebracht! Der voreingestellte Authentication Handler gewÃ¤hrt Einlass, wenn der Benutzername dem Passwort entspricht: Nur zu, probieren Sie es aus.
screen.welcome.security=Aus SicherheitsgrÃ¼nden sollten Sie bei Verlassen der passwortgeschÃ¼tzten Bereiche sich explizit ausloggen und Ihren Webbrowser schlieÃen!
screen.welcome.instructions=Bitte geben Sie Ihre Apereo NetID und Ihr Passwort ein.
screen.welcome.label.netid=<span class="accesskey">N</span>etID:
screen.welcome.label.netid.accesskey=n
screen.welcome.label.password=<span class="accesskey">P</span>asswort:
screen.welcome.label.password.accesskey=p
screen.welcome.label.warn=Ich mÃ¶chte ge<span class="accesskey">w</span>arnt werden, bevor ich mich in einen anderen Bereich einlogge.
screen.welcome.label.warn.accesskey=w
screen.welcome.button.login=ANMELDEN
screen.welcome.button.clear=LÃSCHEN

logo.title=zur Apereo Seite wechseln
copyright=Copyright &copy; 2005&ndash;2015 Apereo, Inc. Alle Rechte vorbehalten.

# Blocked Errors Page
screen.blocked.header=Zugriff verweigert
screen.blocked.message=Das Kennwort f\u00FCr den Benutzer wurde zu oft falsch eingegeben. Der Zugriff wird gedrosselt.

#Confirmation Screen Messages
screen.confirmation.message=Klicken Sie <a href="{0}">hier</a> um zu der zuvor angeforderten Seite zurÃ¼ckzukehren.

#Generic Success Screen Messages
screen.success.header=Anmeldung erfolgreich
screen.success.success=Sie haben sich erfolgreich am Central Authentication Service angemeldet.
screen.success.security=Aus SicherheitsgrÃ¼nden sollten Sie bei Verlassen der passwortgeschÃ¼tzten Bereiche sich explizit ausloggen und Ihren Webbrowser schliessen!

#Logout Screen Messages
screen.logout.header=Abmeldung erfolgreich
screen.logout.success=Sie haben sich erfolgreich vom Central Authentication Service abgemeldet.
screen.logout.security=Aus SicherheitsgrÃ¼nden sollten Sie den Browser schliessen.
screen.logout.redirect=Der Service, von dem Sie herkommen, hat einen <a href\="{0}">Link angegeben, den Sie verfolgen k\u00F6nnen, indem Sie hier klicken</a>.

screen.service.sso.error.header=Eine Neuanmeldung ist erforderlich, um auf den Service zuzugreifen.
screen.service.sso.error.message=Der Service, fÃ¼r den Sie versucht haben, sich zu authentifizieren, hat nicht das Recht, CAS zu benutzen.

error.invalid.loginticket=Sie kÃ¶nnen kein Formular erneut abschicken, das bereits Ã¼bertragen wurde.
username.required=Benutzername ist ein Pflichtfeld.
password.required=Passwort ist ein Pflichtfeld.

# Authentication failure messages
authenticationFailure.AccountDisabledException=Dieses Konto wurde deaktiviert.
authenticationFailure.AccountLockedException=Dieses Konto wurde gesperrt.
authenticationFailure.CredentialExpiredException=Ihr Kennwort ist abgelaufen.
authenticationFailure.InvalidLoginLocationException=Sie k\u00F6nnen sich von dieser Workstation nicht anmelden.
authenticationFailure.InvalidLoginTimeException=Ihrem Konto ist es nicht gestattet sich zu diesem Zeitpunkt anzumelden.
authenticationFailure.AccountNotFoundException=Ung\u00FCltige Anmeldedaten.
authenticationFailure.FailedLoginException=Ung\u00FCltige Anmeldedaten.
authenticationFailure.UNKNOWN=Ung\u00FCltige Anmeldedaten.

INVALID_REQUEST_PROXY='pgt' und 'targetService' Parameter werden beide benÃ¶tigt
INVALID_TICKET_SPEC=Das Ticket entspricht nicht den ÃberprÃ¼fungsregeln. Ein mÃ¶glicher Fehler kÃ¶nnte sein, dass versucht wurde, ein Proxy Ticket mit einem Service Ticket Validierer zu Ã¼berprÃ¼fen, oder man sich nicht an den renew true Request gehalten hat.
INVALID_REQUEST='service' und 'ticket' Parameter werden beide benÃ¶tigt
INVALID_TICKET=Ticket ''{0}'' wurde nicht anerkannt
INVALID_SERVICE=Ticket ''{0}'' passt nicht zum angegebenen Service. Der ursprÃ¼ngliche Service war ''{1}'' und der Ã¼bermittelte Service war ''{2}''.
INVALID_PROXY_CALLBACK=Die angegebene Proxy-Callback-Url ''{0}'' kann nicht authentifiziert werden.
UNAUTHORIZED_SERVICE_PROXY=Dem angegebenen Service ''{0}'' ist es nicht gestattet eine CAS Proxy Authentifizierung zu verwenden.

screen.service.error.header=Applikation nicht berechtigt CAS zu verwenden
screen.service.error.message=Die Applikation, mit der eine Authentifkation versucht wurde, ist nicht berechtigt CAS zu verwenden.
screen.service.empty.error.message=Die Service-Registrierung des CAS Server ist leer und hat keine Service-Definitionen.\
Applikationen, welche \u00FCber CAS authentifizieren m\u00F6chten, m\u00FCssen in der Services-Registrierung definiert werden.

# Password policy
password.expiration.warning=Ihr Kennwort l\u00E4uft in {0} Tagen ab. Bitte <a href\="{1}">\u00E4ndern Sie Ihr Kennwort</a>.
password.expiration.loginsRemaining=Sie haben {0} anmeldungen \u00FCbrig, bevor Sie Ihr Kennwort \u00E4ndern <strong>m\u00FCssen</strong>.
screen.accountdisabled.heading=Dieses Konto wurde deaktiviert.
screen.accountdisabled.message=Bitte kontaktieren Sie Ihren System Administrator um wieder Zugriff zu erhalten.
screen.accountlocked.heading=Dieses Konto wurde gesperrt.
screen.accountlocked.message=Bitte kontaktieren Sie den Systemadministrator um wieder Zugang zu erlangen.
screen.expiredpass.heading=Ihr Kennwort ist abgelaufen.
screen.expiredpass.message=Bitte <a href="{0}">Ã¤ndern Sie Ihr Kennwort</a>.
screen.mustchangepass.heading=Sie mÃ¼ssen Ihr Kennwort Ã¤ndern.
screen.mustchangepass.message=Bitte <a href="{0}">Ã¤ndern Sie Ihr Kennwort</a>.
screen.badhours.heading=Ihrem Konto ist es nicht gestattet sich zu diesem Zeitpunkt anzumelden.
screen.badhours.message=Bitte versuchen Sie es spÃ¤ter noch einmal.
screen.badworkstation.heading=Sie kÃ¶nnen sich von dieser Workstation aus nicht anmelden.
screen.badworkstation.message=Bitte kontaktieren Sie Ihren System Administrator um Zugriff zu erhalten.

# OAuth
screen.oauth.confirm.header=Authorisierung
screen.oauth.confirm.message=Wollen Sie "{0}" vollen Zugriff auf Ihr Profil gestatten?
screen.oauth.confirm.allow=Erlauben

# Unavailable
screen.unavailable.heading=CAS ist nicht verf\u00FCgbar
screen.unavailable.message=Beim Verarbeiten Ihrer Anfrage ist ein Fehler aufgetreten. Bitte informieren Sie Ihren Support oder versuchen Sie es noch einmal.
