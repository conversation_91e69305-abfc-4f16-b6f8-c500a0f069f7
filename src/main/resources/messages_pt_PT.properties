#Welcome Screen Messages

#
# Licensed to <PERSON>pereo under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Parab\u00e9ns! O CAS est\u00e1 agora online! O autenticador padr\u00e3o usa o nome de utilizador igual \u221a\u2020 palavra-passe: v\u00e1 em frente e experimente!
screen.welcome.security=Por quest\u00f5es de seguran\u00e7a, por favor feche o seu browser quando terminar de aceder aos servi\u00e7os que necessitam de autentica\u00e7\u00e3o!
screen.welcome.instructions=Insira o seu utilizador e respectiva palavra-passe
screen.welcome.label.netid=<span class="accesskey">U</span>tilizador:
screen.welcome.label.netid.accesskey=u
screen.welcome.label.password=<span class="accesskey">P</span>alavra-passe:
screen.welcome.label.password.accesskey=p
screen.welcome.label.warn=<span class="accesskey">A</span>vise-me antes de entrar noutros sites.
screen.welcome.label.warn.accesskey=A
screen.welcome.button.login=ENTRAR
screen.welcome.button.clear=LIMPAR

# Blocked Errors Page
screen.blocked.header=Accesso Bloqueado
screen.blocked.message=Inseriu a palavra-chave incorrectamente demasiadas vezes. A sua conta foi bloqueada.

#Confirmation Screen Messages
screen.confirmation.message=Clique <a href="{0}">aqui</a> para ir para a aplica\u00e7\u00e3o.

#Generic Success Screen Messages
screen.success.header=Sess\u00e3o iniciada com sucesso.
screen.success.success=A sua sess\u00e3o no Servi\u00e7o de Autentica\u00e7\u00e3o Central foi iniciada com sucesso.
screen.success.security=Por raz\u00f5es de seguran\u00e7a, por favor fa\u00e7a  Logout e feche o seu browser quando terminar de aceder aos servi\u00e7os que necessitam de autentica\u00e7\u00e3o!


#Logout Screen Messages
screen.logout.header=Sess\u00e3o terminada com sucesso.
screen.logout.success=A sua sess\u00e3o no Servi\u00e7o de Autentica\u00e7\u00e3o Central foi terminada com sucesso.
screen.logout.security=Por raz\u00f5es de seguran\u00e7a, por favor feche o seu browser.
screen.logout.redirect=O servi\u00e7o de origem providenciou um <a href="{0}">link que pode ser seguido ao clicar aqui</a>.

screen.service.sso.error.header=\u221a\u00e2 necess\u221a\u00b0ria reautentica\u221a\u00df\u221a\u00a3o para aceder a este servi\u221a\u00dfo
screen.service.sso.error.message=Voc\u221a\u2122 tentou o acesso a um servi\u221a\u00dfo que requer reautentica\u221a\u00df\u221a\u00a3o sem a efectuar. Por favor tente <a href="{0}">autenticar-se novamente</a>.

error.invalid.loginticket=N\u221a\u00a3o pode tentar reenviar um formul\u221a\u00b0rio que foi enviado anteriormente.
username.required=Utilizador \u221a\u00a9 um campo obrigat\u221a\u2265rio.
password.required=Palavra-passe \u221a\u00a9 um campo obrigat\u221a\u2265rio.
error.authentication.credentials.bad=Utilizador ou palavra-passe inv\u221a\u00b0lidos.
error.authentication.credentials.unsupported=As credenciais fornecidas n\u221a\u00a3o s\u221a\u00a3o suportadas pelo Servi\u221a\u00dfo de Autentica\u221a\u00df\u221a\u00a3o Central.

INVALID_REQUEST_PROXY=Os par\u221a\u00a2metros 'pgt' e 'targetService' s\u221a\u00a3o obrigat\u221a\u2265rios
INVALID_TICKET_SPEC=O Ticket falhou a valida\u221a\u00df\u221a\u00a3o de especifica\u221a\u00df\u221a\u00a3o. Poder\u221a\u00a3o ser causas a tentativa de validar um Proxy Ticket atr\u221a\u00b0v\u221a\u00a9s de um validador Service Ticket ou n\u221a\u00a3o estar de acordo com o pedido de renova\u221a\u00df\u221a\u00a3o.
INVALID_REQUEST=Os par\u221a\u00a2metros 'service' e 'ticket' s\u221a\u00a3o obrigat\u221a\u2265rios
INVALID_TICKET=ticket ''{0}'' n\u221a\u00a3o reconhecido
INVALID_SERVICE=ticket ''{0}'' n\u221a\u00a3o coincide com o servi\u221a\u00dfo fornecido. O servi\u221a\u00dfo original foi ''{1}'' e o servi\u221a\u00dfo fornecido foi ''{2}''.

screen.service.error.header=Aplica\u221a\u00df\u221a\u00a3o n\u221a\u00a3o autorizada a usar o Servi\u221a\u00dfo de Autentica\u221a\u00df\u221a\u00a3o Central
screen.service.error.message=A aplica\u221a\u00df\u221a\u00a3o onde se tentou autenticar n\u221a\u00a3o est\u221a\u00b0 autorizada a usar o Servi\u221a\u00dfo de Autentica\u221a\u00df\u221a\u00a3o Central
