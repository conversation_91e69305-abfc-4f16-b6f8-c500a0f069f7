#初始化信息
init.phone = 17630643626
init.trueName = 超级管理员
init.icon=https://uploads.dahe.cn/nxy/dahe2017/favicon.ico

#系统配置信息
shiro.activesession.name = shiro-activeSessionCache
cache.prefix = :
password.md5.hashIterations = 3
sm4.security.key=341210E10E7E409F2DCCAE01EBB4843E
cas.server = https://yuqingtong.dahe.cn
cas.logout = ${cas.server}/home
cas.home = ${cas.server}/home
cas.server.login=${cas.server}/login
cas.static.login=${cas.server}/check?service={0}&redirect={1}
cas.server.logout=${cas.server}/logout
sms.allow.host = example.org
default.sites = {local:'${cas.server}'}
redis.prefix = sso:
tgt.key=${redis.prefix}tgt
st.key=${redis.prefix}st
tgt.ids.key=${redis.prefix}tgt:zids
st.ids.key=${redis.prefix}st:zids
forbid.ips=${redis.prefix}forbid:ips
forbid.phones=${redis.prefix}forbid:phones
log.count=${redis.prefix}log:logcount
log.date=${redis.prefix}log:logdate
log.switch=${redis.prefix}log:logswitch
init.logcount=50000
init.logdate=90
init.logswitch=1
verfiy.code = ${redis.prefix}verfiy
code.char = 1234567890qwertyuiopasdfghjklzxcvbnm
password.length = 6

#redis
redis.host=*************
redis.password=bv85t6js8fj04IFG9VJ
redis.port=6401
sso.redis.host=*************
sso.redis.password=bv85t6js8fj04IFG9VJ
sso.redis.port=6401
sso.token.key = sso:COM_TOKEN
#cache_cloud.appid = 10004
#cache_cloud.url = http://************:9999/cache/client/redis/
#cache_cloud.api=${cache_cloud.url}${cache_cloud.type:cluster}/${cache_cloud.appid}.json?clientVersion=${cache_cloud.version:1.0-SNAPSHOT}

#mongodb配置
mongod.host=*************
mongod.port=27017
mongod.name=yqtsso
mongod.username=yqtsso
mongod.pwd=9f904IU89FJK5904ic
mongod.credential=${mongod.username}:${mongod.pwd}@${mongod.name}

#rocketmq
rocketmq.uri = ************:19876
role.producer.topic=t1role
log.tag = local

#sms
sms.expires = 900
sms.sso.url = https://sms.dahe.cn/dahe/sms/sso
sms.sso.token = Sso9876DH
verify.code = sso:verify
sms.interval = 172800
sms.username = Z1024
sms.password = emcsll
sms.spnumber = 0079
sms.code.template = 你的手机号{0},注册验证码：{1},5分钟内提交有效,如果不是本人操作请忽略！【大河网】
sms.find.template = 您的用户名：{0}，密码已修改，新密码：{1}。请牢记新密码！【大河网】
sms.code.length=4

######第三方接口######
ipip.addr = http://ipapi.ipip.net/find?addr=
ipip.token = c5724cb28be17035b30312a503dacb3fdb7b04e0
z1024.api = http://*************:9205/API/SendMessages.jsp
interceptor.suffixs = js,ico,html,htm,png,jpe,jpeg,js

#所属环境
test_env=false
############################待确定配置##############################
#zimg相关参数
image.type=jpg,png,jpeg,gif
image.size=21000000
image.host=https://t1img.dahe.cn
ZIMG_URL=https://t1img.dahe.cn

##########cms
cms.prefix=cms
tree_key=${cms.prefix}:site_tree
department_key=${cms.prefix}:site_department
#站点和部门的列表
cms_site=${cms.prefix}:site_list
cms_organization=${cms.prefix}:department_list
#最近新闻url
cms_recentnews=http://t1.dahe.cn/dahe/open/last-news

#短信接口
workorder.prefix=gd
sms.api=https://t1work.dahe.cn/api/sendsms
sms.code=${workorder.prefix}:ssoqdsms_code
sms.yz=${workorder.prefix}:ssoyzsms_code
sms.bind.wx = ${workorder.prefix}:wechatsms_code

yq.user=https://yq.dahe.cn/api/user/update