# <AUTHOR> <mgw at umk.pl>
# @since 3.1.1

#Welcome Screen Messages

#
# Licensed to Apereo under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Gratulujemy, us\u0142uga CAS jest gotowa do dzia\u0142ania! Domy\u015blny tryb uwierzytelniania akceptuje dane, \
w kt\u00f3rych has\u0142o jest takie samo jak nazwa u\u017cytkownika - spr\u00f3buj!
screen.welcome.security=<PERSON>la zachowania bezpiecze\u0144stwa, gdy zako\u0144czysz korzystanie z us\u0142ug wymagaj\u0105cych uwierzytelnienia, \
wyloguj si\u0119 i zamknij przegl\u0105dark\u0119!
screen.welcome.instructions=Wprowad\u017a sw\u00f3j identyfikator sieciowy i has\u0142o
screen.welcome.label.netid=<span class="accesskey">I</span>dentyfikator:
screen.welcome.label.netid.accesskey=i
screen.welcome.label.password=<span class="accesskey">H</span>as\u0142o:
screen.welcome.label.password.accesskey=h
screen.welcome.label.publicstation=Pracuj\u0119 na publicznej stacji roboczej.
screen.welcome.label.warn=<span class="accesskey">O</span>strzegaj mnie przed zalogowaniem na innych serwerach.
screen.welcome.label.warn.accesskey=o
screen.welcome.button.login=ZALOGUJ
screen.welcome.button.clear=WYCZY\u015a\u0106

screen.cookies.disabled.title=Przegl\u0105darka ma wy\u0142\u0105czone ciasteczka
screen.cookies.disabled.message=Twoja przegl\u0105darka ma wy\u0142\u0105czone pliki cookie. Pojedyncze logowanie NIE B\u0118DZIE DZIA\u0141A\u0141O.

screen.aup.button.accept=ZAAKCEPTUJ
screen.aup.button.cancel=ANULUJ

screen.nonsecure.title=Po\u0142\u0105czenie niezabezpieczone
screen.nonsecure.message=\u0141\u0105czysz si\u0119 do CAS niezabezpieczonym po\u0142\u0105czeniem. Pojedyncze logowanie NIE B\u0118DZIE DIZA\u0141A\u0141O. \
Aby pojedyncze logowanie funkcjonowa\u0142o, MUSISZ uwierzytelni\u0107 si\u0119 po HTTPS.

logo.title=Id\u017a na stron\u0119 domow\u0105 Jasig
copyright=Copyright &copy; 2005&ndash;2012 Jasig, Inc. Wszelkie prawa zastrze\u017cone.
screen.capslock.on = CAPSLOCK jest w\u0142\u0105czony!

# Remember-Me Authentication
screen.rememberme.checkbox.title=Zapami\u0119taj mnie

# Blocked Errors Page
screen.blocked.header=Dost\u0119p zabroniony
screen.blocked.message=Wprowadzono nieprawid\u0142owe has\u0142o zbyt wiele razy. Dost\u0119p zosta\u0142 ograniczony.

#Confirmation Screen Messages
screen.confirmation.message=Naci\u015bnij <a href="{0}">tutaj</a>, by przej\u015bc do aplikacji.

#Generic Success Screen Messages
screen.success.header=Udane logowanie
screen.success.success=Zalogowa\u0142e\u015b si\u0119 w CAS - Centralnej Us\u0142udze Uwierzytelniania.
screen.success.security=Dla zachowania bezpiecze\u0144stwa, gdy zako\u0144czysz korzystanie z us\u0142ug wymagaj\u0105cych uwierzytelnienia, \
wyloguj si\u0119 i zamknij przegl\u0105dark\u0119!

#Logout Screen Messages
screen.logout.header=Udane wylogowanie
screen.logout.success=Wylogowa\u0142e\u015b si\u0119 z CAS - Centralnej Us\u0142ugi Uwierzytelniania.
screen.logout.security=Dla zachowania bezpiecze\u0144stwa zamknij przegl\u0105dark\u0119.
screen.logout.redirect=Us\u0142uga przekaza\u0142a <a href="{0}">adres, do kt\u00f3rego przejdziesz naciskaj\u0105c tutaj</a>.

screen.service.sso.error.header=W celu dost\u0119pu do us\u0142ugi wymagane jest ponowne uwierzytelnienie
screen.service.sso.error.message=Pr\u00f3ba dost\u0119pu do us\u0142ugi, kt\u00f3ra wymaga ponownego uwierzytelnienia. Pon\u00f3w <a href="{0}">uwierzytelnienie</a>.
screen.service.required.message=Pr\u00f3bujesz uwierzytelni\u0107 si\u0119 nie podaj\u0105c aplikacji docelowej. Sprawd\u017a prosz\u0119 zapytanie i spr\u00f3buj ponownie.

error.invalid.loginticket=Nie mo\u017cesz ponownie wys\u0142a\u0107 formularza wcze\u015bniej wys\u0142anego.
username.required=Nazwa u\u017cytkownika jest polem wymaganym.
password.required=Has\u0142o jest polem wymaganym.

# Authentication failure messages
authenticationFailure.AccountDisabledException=Konto zosta\u0142o wy\u0142\u0105czone.
authenticationFailure.AccountLockedException=Konto zosta\u0142o zablokowane.
authenticationFailure.CredentialExpiredException=Termin wa\u017cno\u015bci has\u0142a up\u0142yn\u0105\u0142.
authenticationFailure.InvalidLoginLocationException=Nie mo\u017cesz zalogowa\u0107 si\u0119 z tego komputera.
authenticationFailure.InvalidLoginTimeException=Nie mo\u017cesz zalogowa\u0107 si\u0119 w tym czasie.
authenticationFailure.AccountNotFoundException=Dostarczone dane uwierzytelniania nie mog\u0105 zosta\u0107 uznane za poprawne.
authenticationFailure.FailedLoginException=Dostarczone dane uwierzytelniania nie mog\u0105 zosta\u0107 uznane za poprawne.
authenticationFailure.UNKNOWN=Dostarczone dane uwierzytelniania nie nie s\u0105 akceptowane przez us\u0142ug\u0119 CAS.

INVALID_REQUEST_PROXY=parametry 'pgt' i 'targetService' s\u0105 wymagane
INVALID_TICKET_SPEC=Bilet niezgodny ze specyfikacj\u0105. Mo\u017cliwe przyczyny b\u0142\u0119du to pr\u00f3ba sprawdzenia biletu proxy \
za pomoc\u0105 walidatora biletu us\u0142ugi, lub brak zgodno\u015bci ze zleceniem odnowienia uwierzytelnienia.
INVALID_REQUEST=parametry 'service' i 'ticket' s\u0105 wymagane
INVALID_TICKET=nieznana posta\u0107 biletu ''{0}''
INVALID_SERVICE=Bilet ''{0}'' nie nale\u017cy do tej us\u0142ugi.  Oryginalna us\u0142uga to ''{1}'', aktualna us\u0142uga to ''{2}''.
INVALID_PROXY_CALLBACK=Podany URL po\u015brednika ''{0}'' nie zosta\u0142 uwierzytelniony.
UNAUTHORIZED_SERVICE_PROXY=Podana us\u0142uga ''{0}'' nie ma uprawnie\u0144 do korzystania z CAS.

screen.service.error.header=Brak uprawnie\u0144 do korzystania z CAS
service.not.authorized.missing.attr=Nie mo\u017cesz korzysta\u0107 z aplikacji gdy\u017c Twoje konto nie ma uprawnie\u0144 do wymaganych przez CAS \
do uwierzytelnienia w tej aplikacji. Prosz\u0119 skontaktowa\u0107 si\u0119 z pomoc\u0105 techniczn\u0105 w celu uzyskania dost\u0119pu.
screen.service.error.message=Aplikacja, w kt\u00f3rej chcia\u0142e\u015b zosta\u0107 uwierzytelniony nie ma uprawnie\u0144 do korzystania z CAS.
screen.service.empty.error.message=Rejestr us\u0142ug CAS jest pusty i nie zawiera \u017cadnej definicji. \
Aplikacja, kt\u00f3re chcia\u0142eyby zosta\u0107 uwierzytelnione w CAS musz\u0105 by\u0107 zarejestrowane w rejestrze us\u0142ug.

# Password policy
password.expiration.warning=Termin wa\u017cno\u015bci has\u0142a up\u0142ywa za {0} dni. Prosz\u0119 <a href="{0}">zmieni\u0107 has\u0142o</a>.
password.expiration.loginsRemaining=Pozosta\u0142o {0} autoryzacji zanim b\u0119dziesz <strong>MUSIA\u0141/A</strong> zmieni\u0107 has\u0142o
screen.accountdisabled.heading=Konto zosta\u0142o wy\u0142\u0105czone.
screen.accountdisabled.message=Prosz\u0119 skontaktowa\u0107 si\u0119 z pomoc\u0105 techniczn\u0105 w celu uzyskania dost\u0119pu.
screen.accountlocked.heading=Konto zosta\u0142o zablokowane.
screen.accountlocked.message=Prosz\u0119 skontaktowa\u0107 si\u0119 z pomoc\u0105 techniczn\u0105 w celu uzyskania dost\u0119pu.

# LPPE Password Expired
screen.expiredpass.heading=Termin wa\u017cno\u015bci has\u0142a up\u0142yn\u0105\u0142.
screen.expiredpass.message=Prosz\u0119 <a href="{0}">zmieni\u0107 has\u0142o</a>.

# LPPE Password Must be changed
screen.mustchangepass.heading=Musisz zmieni\u0107 has\u0142o.
screen.mustchangepass.message=Prosz\u0119 <a href="{0}">zmieni\u0107 has\u0142o</a>.

# LPPE Login out of authorized hours
screen.badhours.heading=Nie mo\u017cesz zalogowa\u0107 si\u0119 w tym czasie.
screen.badhours.message=Prosz\u0119 spr\u00f3bowa\u0107 p\u00f3\u017aniej.

# LPPE Login out of authorized workstations
screen.badworkstation.heading=Nie mo\u017cesz zalogowa\u0107 si\u0119 z tego komputera.
screen.badworkstation.message=Prosz\u0119 skontaktowa\u0107 si\u0119 z pomoc\u0105 techniczn\u0105 w celu uzyskania dost\u0119pu.

# OAuth
screen.oauth.confirm.header=Autoryzacja
screen.oauth.confirm.message=Czy chcesz udost\u0119pni\u0107 ca\u0142y sw\u00f3j profil "{0}"?
screen.oauth.confirm.allow=Udost\u0119pnij

# Unavailable
screen.unavailable.heading=CAS jest niedost\u0119pny
screen.unavailable.message=Wyst\u0105pi\u0142 b\u0142\u0105d podczas obs\u0142ugi zlecenia. \
Prosz\u0119Â spr\u00f3bowa\u0107 jeszcze raz lub skontaktowa\u0107 si\u0119 z pomoc\u0105 techniczn\u0105.
