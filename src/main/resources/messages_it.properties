#Author: <PERSON> http://robcos.com
#Version: $Revision$ $Date$
#Since: 3.0.5

#Welcome Screen Messages

#
# Licensed to <PERSON>per<PERSON> under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Benvenuti al Central Authentication Service (CAS).
screen.welcome.security=Per motivi di sicurezza dovresti effettuare il logout e chiudere tutte le finestre del browser quando hai finito di utilizzare servizi che necessitano autenticazione.
screen.welcome.instructions=Inserisci login e password
screen.welcome.label.netid=<span class="accesskey">L</span>ogin:
screen.welcome.label.netid.accesskey=L
screen.welcome.label.password=<span class="accesskey">P</span>assword:
screen.welcome.label.password.accesskey=P
screen.welcome.label.warn=<span class="accesskey">A</span>vvisami prima di autenticarmi su un altro sito
screen.welcome.label.warn.accesskey=A
screen.welcome.button.login=LOGIN
screen.welcome.button.clear=ANNULLA

# Blocked Errors Page
screen.blocked.header=Accesso Negato
screen.blocked.message=È stata inserita la password sbagliata troppe volte. L'account è stato bloccato.

#Confirmation Screen Messages
screen.confirmation.message=Clicca <a href="{0}">quí</a> per accedere al servizio.

#Generic Success Screen Messages
screen.success.header=Login eseguito correttamente
screen.success.success=Hai effettuato il login al Central Authentication Service.
screen.success.security=Per motivi di sicurezza dovresti effettuare il logout e chiudere tutte le finestre del browser quando hai finito di utilizzare servizi che necessitano autenticazione.

#Logout Screen Messages
screen.logout.header=Logout effettuato con successo
screen.logout.success=Hai correttamente effettuato il logout dal Central Authentication Service.
screen.logout.security=Per motivi di sicurezza, si consiglia di chiudere tutte le finestre del browser.
screen.logout.redirect=Puoi rifare il login cliccando <a href="{0}">quì</a>

screen.service.sso.error.header=È necessario effettuare nuovamente l'autenticazione per avere l'accesso a questo servizio
screen.service.sso.error.message=Si è tentato di accedere a un servizio che richiede di effettuare nuovamente l'autenticazione. Si prega di <a href="{0}">autenticarsi nuovamente</a>.

error.invalid.loginticket=Ricompila il form dall'inizio senza utilizzare il tasto 'indietro'
username.required=Il campo login é obbligatorio
password.required=Il campo password é obbligatorio
error.authentication.credentials.bad=Login o password errate
error.authentication.credentials.unsupported=Le credenziali utilizzate non sono supportate da CAS

INVALID_REQUEST_PROXY=I parametri 'pgt' e 'targetService' sono entrambi obbligatori
INVALID_TICKET_SPEC=La convalida del Ticket non ha avuto successo. Una possibile causa di errore potrebbe essere il tentativo di convalidare un Proxy Ticket via un Service Ticket validator.
INVALID_REQUEST=I parametri 'service' e 'ticket' sono entrambi obbligatori
INVALID_TICKET=Il ticket ''{0}'' non é stato riconosciuto
INVALID_SERVICE=Il ticket ''{0}'' non corrisponde a nessun servizio disponibile

#Service Error Messages
screen.service.error.header=Servizio non autorizzato.
screen.service.error.message=Il servizio a cui stai cercando di accedere non é configurato per CAS

# LPPE Account Error
screen.accounterror.password.message=La data di rinnovo della password non è specificata, è scaduta o non valida. Si prega di contattare l'amministratore di sistema per recuperare le credenziali di accesso.

# LPPE Account Disabled
screen.accountdisabled.heading=Questo account è disabilitato.
screen.accountdisabled.message=Si prega di contattare l'amministratore di sistema per recuperare le credenziali di accesso.

# LPPE Password Expired
screen.expiredpass.heading=La vostra password è scaduta.
screen.expiredpass.message=Si prega di <a href="{0}">cambiare la password</a>.

# LPPE Password Must be changed
screen.mustchangepass.heading=La password deve essere cambiata.
screen.mustchangepass.message=Si prega di <a href="{0}">cambiare la password</a>.

# LPPE Login out of authorized hours
screen.badhours.heading=Non si è autorizzati a effettuare il login a quest'ora.
screen.badhours.message=Si prega di riprovare più tardi.

# LPPE Login out of authorized workstations
screen.badworkstation.heading=Non si è autorizzati a effettuare il login da questa postazione.
screen.badworkstation.message=Si prega di conttattare l'amministratore di sistema per recuperare le credenziali d'accesso.

# LPPE Password Warning
screen.warnpass.heading.today=La vostra password scade oggi!
screen.warnpass.heading.tomorrow=La vostra password scade domani!
screen.warnpass.heading.other=La vostra password scade tra {0} giorni.
screen.warnpass.message.line1=Si prega di <a href="{0}">cambiare la password</a> ora.
screen.warnpass.message.line2=E in corso la redirezione automatica verso la vostra <a href="{0}">applicazione</a> tra 10 secondi.
