#\u521D\u59CB\u5316\u4FE1\u606F
init.phone = 17630643626
init.trueName = \u8D85\u7EA7\u7BA1\u7406\u5458
init.icon=https://uploads.dahe.cn/nxy/dahe2017/favicon.ico

#\u7CFB\u7EDF\u914D\u7F6E\u4FE1\u606F
shiro.activesession.name = shiro-activeSessionCache
cache.prefix = :
password.md5.hashIterations = 3
sm4.security.key=E1D13C76008096D01B616E5ACF839F1C
cas.server = http://localhost:8080
cas.logout = ${cas.server}/home
cas.home = ${cas.server}/home
cas.server.login=${cas.server}/login
cas.static.login=${cas.server}/check?service={0}&redirect={1}
cas.server.logout=${cas.server}/logout
sms.allow.host = example.org
default.sites = {local:'${cas.server}'}
redis.prefix = sso:
tgt.key=${redis.prefix}tgt
st.key=${redis.prefix}st
tgt.ids.key=${redis.prefix}tgt:zids
st.ids.key=${redis.prefix}st:zids
forbid.ips=${redis.prefix}forbid:ips
forbid.phones=${redis.prefix}forbid:phones
log.count=${redis.prefix}log:logcount
log.date=${redis.prefix}log:logdate
log.switch=${redis.prefix}log:logswitch
init.logcount=50000
init.logdate=90
init.logswitch=1
verfiy.code = ${redis.prefix}verfiy
code.char = 1234567890qwertyuiopasdfghjklzxcvbnm
password.length = 6

#redis
redis.host=**********
redis.password=123zgf
redis.port=6379
sso.redis.host = **********
sso.redis.password=123zgf
sso.redis.port=6379
sso.token.key = sso:COM_TOKEN
#cache_cloud.appid = 10004
#cache_cloud.url = http://************:9999/cache/client/redis/
#cache_cloud.api=${cache_cloud.url}${cache_cloud.type:cluster}/${cache_cloud.appid}.json?clientVersion=${cache_cloud.version:1.0-SNAPSHOT}

#mongodb\u914D\u7F6E
mongod.host=localhost
mongod.port=27017
mongod.name=dhsso
mongod.username=dhsso
mongod.pwd=123zgf
mongod.credential=${mongod.username}:${mongod.pwd}@${mongod.name}

#rocketmq
rocketmq.uri = localhost:9876
role.producer.topic=t1role
log.tag = local

#sms
sms.expires = 900
sms.sso.url = https://sms.dahe.cn/dahe/sms/sso
sms.sso.token = Sso9876DH
verify.code = sso:verify
sms.interval = 172800
sms.username = Z1024
sms.password = emcsll
sms.spnumber = 0079
sms.code.template = \u4F60\u7684\u624B\u673A\u53F7{0},\u6CE8\u518C\u9A8C\u8BC1\u7801\uFF1A{1},5\u5206\u949F\u5185\u63D0\u4EA4\u6709\u6548,\u5982\u679C\u4E0D\u662F\u672C\u4EBA\u64CD\u4F5C\u8BF7\u5FFD\u7565\uFF01\u3010\u5927\u6CB3\u7F51\u3011
sms.find.template = \u60A8\u7684\u7528\u6237\u540D\uFF1A{0}\uFF0C\u5BC6\u7801\u5DF2\u4FEE\u6539\uFF0C\u65B0\u5BC6\u7801\uFF1A{1}\u3002\u8BF7\u7262\u8BB0\u65B0\u5BC6\u7801\uFF01\u3010\u5927\u6CB3\u7F51\u3011
sms.code.length=4

######\u7B2C\u4E09\u65B9\u63A5\u53E3######
ipip.addr = http://ipapi.ipip.net/find?addr=
ipip.token = c5724cb28be17035b30312a503dacb3fdb7b04e0
z1024.api = http://*************:9205/API/SendMessages.jsp
interceptor.suffixs = js,ico,html,htm,png,jpe,jpeg,js

#\u6240\u5C5E\u73AF\u5883
test_env=true
############################\u5F85\u786E\u5B9A\u914D\u7F6E##############################
#zimg\u76F8\u5173\u53C2\u6570
image.type=jpg,png,jpeg,gif
image.size=21000000
image.host=https://t1img.dahe.cn
ZIMG_URL=https://t1img.dahe.cn

##########cms
cms.prefix=cms
tree_key=${cms.prefix}:site_tree
department_key=${cms.prefix}:site_department
#\u7AD9\u70B9\u548C\u90E8\u95E8\u7684\u5217\u8868
cms_site=${cms.prefix}:site_list
cms_organization=${cms.prefix}:department_list
#\u6700\u8FD1\u65B0\u95FBurl
cms_recentnews=http://t1.dahe.cn/dahe/open/last-news

#\u77ED\u4FE1\u63A5\u53E3
workorder.prefix=gd
sms.api=https://t1work.dahe.cn/api/sendsms
sms.code=${workorder.prefix}:ssoqdsms_code
sms.yz=${workorder.prefix}:ssoyzsms_code
sms.bind.wx = ${workorder.prefix}:wechatsms_code

yq.user=https://yq.dahe.cn/api/user/update
