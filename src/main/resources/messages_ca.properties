#Author: <PERSON><PERSON> i Silvan and <PERSON>

#Welcome Screen Messages

#
# Licensed to Apereo under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=Felicitats per engegar el CAS correctament! Per a aprendre com autenticar, si us plau, repasseu la configuraciÃ³ del gestor d'autenticaciÃ³ per defecte.
screen.welcome.security=Per raons de seguretat, si us plau, tanqueu la sessiÃ³ i el vostre navegador web quan hÃ giu acabat d'accedir als serveis que requereixen autenticaciÃ³.
screen.welcome.instructions=IntroduÃ¯u el vostre nom d'usuari i contrasenya.
screen.welcome.label.netid=Nom d'<span class="accesskey">u</span>suari:
screen.welcome.label.netid.accesskey=u
screen.welcome.label.password=<span class="accesskey">C</span>ontrasenya:
screen.welcome.label.password.accesskey=c
screen.welcome.label.warn=<span class="accesskey">A</span>viseu-me abans d'obrir sessiÃ³ en altres llocs.
screen.welcome.label.warn.accesskey=a
screen.welcome.button.login=INICIA SESSIÃ
screen.welcome.button.clear=NETEJA

logo.title=vÃ©s a la pÃ gina principal de l'Apereo
copyright=Copyright &copy; 2005&ndash;2015 Apereo, Inc. Es reserven tots els drets.
screen.capslock.on = La tecla BLOQ MAJ estÃ  activada!

# Blocked Errors Page
screen.blocked.header=AccÃ©s denegat
screen.blocked.message=Heu introduÃ¯da una contrasenya equivocada per al usuari massa vegades. Se us ha restringit.

#Confirmation Screen Messages
screen.confirmation.message=Feu clic <a href="{0}">aquÃ­</a> per a anar a l'aplicaciÃ³.

#Generic Success Screen Messages
screen.success.header=Inici de sessiÃ³ reeixit
screen.success.success=VÃ³s, {0}, heu iniciat amb Ã¨xit la sessiÃ³ al Servei Central d''AutenticaciÃ³ (CAS).
screen.success.security=Per raons de seguretat, si us plau, tanqueu la sessiÃ³ i el vostre navegador web quan hÃ giu terminat.

#Logout Screen Messages
screen.logout.header=Tancament de sessiÃ³ reeixit
screen.logout.success=Heu tancat amb Ã¨xit la sessiÃ³ al Servei Central d'AutenticaciÃ³ (CAS).
screen.logout.security=Per raons de seguretat, tanqueu el vostre navegador web.
screen.logout.redirect=El servei des del qual heu arribat ha proporcionat un <a href="{0}">enllaÃ§ que podeu seguir per fer clic aquÃ­</a>.

screen.service.sso.error.header=Cal reautenticar per a accedir a aquest servei
screen.service.sso.error.message=Heu intentat accedir a un servei que requereix autenticaciÃ³ sense reautenticar. Si us plau, intenteu <a href="{0}">autenticar de nou</a>.

error.invalid.loginticket=No podeu intentar reenviar un formulari que ja s'ha enviat.
username.required=El nom d'usuari Ã©s un camp obligatori.
password.required=La contrasenya Ã©s un camp obligatori.

# Authentication failure messages
authenticationFailure.AccountDisabledException=S'ha deshabilitat aquest compte.
authenticationFailure.AccountLockedException=S'ha bloquejat aquest compte.
authenticationFailure.CredentialExpiredException=La vostra contrasenya ha caducada.
authenticationFailure.InvalidLoginLocationException=No podeu iniciar sessiÃ³ des d'aquesta estaciÃ³ de treball.
authenticationFailure.InvalidLoginTimeException=EstÃ  prohibit iniciar sessiÃ³ amb el vostre compte en aquest moment.
authenticationFailure.AccountNotFoundException=Les credencials sÃ³n invÃ lides.
authenticationFailure.FailedLoginException=Les credencials sÃ³n invÃ lides.
authenticationFailure.UNKNOWN=Les credencials sÃ³n invÃ lides.

INVALID_REQUEST_PROXY=calen ambdÃ³s dels parÃ metres 'pgt' i 'targetService'
INVALID_TICKET_SPEC=El tiquet ha fallat l'especificaciÃ³ de validaciÃ³. Els errors possibles poden incloure intentar validar un tiquet d'intermediari mitjanÃ§ant un validador de tiquets de servei, o no complir amb la peticiÃ³ de renovaciÃ³ (renew true).
INVALID_REQUEST=calen ambdÃ³s dels parÃ metres 'service' i 'ticket'
INVALID_TICKET=No s''ha reconegut el tiquet ''{0}''
INVALID_SERVICE=El tiquet ''{0}'' no coincideix amb el servei proporcionat. El servei original era ''{1}'' i el servei proporcionat era ''{2}''.
INVALID_PROXY_CALLBACK=L''adreÃ§a de retrotrucada d''intermediari proveÃ¯da ''{0}'' no s''ha pogut autenticar.
UNAUTHORIZED_SERVICE_PROXY=El servei proporcionat ''{0}'' no estÃ  autoritzat a utilitzar l''autenticaciÃ³ intermediÃ ria del CAS.

screen.service.error.header=AplicaciÃ³ no autoritzada a utilitzar el CAS
service.not.authorized.missing.attr=No esteu autoritzat a accedir a l'aplicaciÃ³ perquÃ¨ al vostre compte \
li manquen els privilegis que el servidor CAS requereix per a autenticar a aquest servei. Si us plau, notifiqueu al vostre suport tÃ¨cnic.
screen.service.error.message=L'aplicaciÃ³ a que heu intentat autenticar no estÃ  autoritzada a utilitzar el CAS.
screen.service.empty.error.message=El registre de serveis del CAS estÃ  buit i no tÃ© definicions de servei. \
Les aplicacions que volen autenticar amb el CAS han de ser explÃ­citament definides en el registre de serveis.

# Password policy
password.expiration.warning=La vostra contrasenya caduca en {0} dies. Si us plau, <a href="{1}">canvieu la vostra contrasenya</a> ara.
password.expiration.loginsRemaining=Teniu {0} inicis de sessiÃ³ restant abans que <strong>HEU</strong> de canviar la vostra contrasenya.
screen.accountdisabled.heading=S'ha deshabilitat aquest compte.
screen.accountdisabled.message=Si us plau, contacteu a l'administrador de sistema per a recobrar accÃ©s.
screen.accountlocked.heading=S'ha bloquejat aquest compte.
screen.accountlocked.message=Si us plau, contacteu a l'administrador de sistema per a recobrar accÃ©s.
screen.expiredpass.heading=La vostra contrasenya ha caducada.
screen.expiredpass.message=Si us plau, <a href="{0}">canvieu la vostra contrasenya</a>.
screen.mustchangepass.heading=Heu de canviar la vostra contrasenya.
screen.mustchangepass.message=Si us plau, <a href="{0}">canvieu la vostra contrasenya</a>.
screen.badhours.heading=EstÃ  prohibit iniciar sessiÃ³ amb el vostre compte en aquest moment.
screen.badhours.message=Si us plau, intenteu mÃ©s tard.
screen.badworkstation.heading=No podeu iniciar sessiÃ³ des d'aquesta estaciÃ³ de treball.
screen.badworkstation.message=Si us plau, contacteu a l'administrador de sistema per a recobrar accÃ©s.

# OAuth
screen.oauth.confirm.header=AutoritzaciÃ³
screen.oauth.confirm.message=Voleu concedir accÃ©s al vostre perfil complet a "{0}"?
screen.oauth.confirm.allow=Permet

# Unavailable
screen.unavailable.heading=El CAS no estÃ  disponible
screen.unavailable.message=Ha hagut un error al intentar complir amb la vostra peticiÃ³. Si us plau, notifiqueu al vostre suport tÃ¨cnic o intenteu de nou.
