#Author: <PERSON><PERSON> http://www.infoflexconnect.se
#Updated 2006-08-29: PÃ¥l <PERSON><PERSON> & <PERSON><PERSON><PERSON> IT Support Department at Uppsala University http://www.uu.se
#Updated 2007-06-21: PÃ¥l <PERSON><PERSON> IT Support Department at Uppsala University http://www.uu.se

#Welcome Screen Messages

#
# Licensed to Apereo under one or more contributor license
# agreements. See the NOTICE file distributed with this work
# for additional information regarding copyright ownership.
# Apereo licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file
# except in compliance with the License.  You may obtain a
# copy of the License at the following location:
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

screen.welcome.welcome=VÃ¤lkommen till den centrala autentiseringstjÃ¤nsten CAS. NÃ¤r du installerat men Ã¤nnu inte konfigurerat CAS kan du autentisera genom att ange samma text som bÃ¥de anvÃ¤ndaridentitet och lÃ¶senord fÃ¶r att prova CAS.
screen.welcome.security=Av sÃ¤kerhetsskÃ¤l bÃ¶r du logga ut och stÃ¤nga webblÃ¤saren nÃ¤r du Ã¤r fÃ¤rdig med webbtjÃ¤nsterna som krÃ¤ver inloggning.
screen.welcome.instructions=Ange din anvÃ¤ndaridentitet och ditt lÃ¶senord.
screen.welcome.label.netid=<span class="accesskey">A</span>nvÃ¤ndarid:
screen.welcome.label.netid.accesskey=a
screen.welcome.label.password=<span class="accesskey">L</span>Ã¶senord:
screen.welcome.label.password.accesskey=l
screen.welcome.label.warn=<span class="accesskey">V</span>arna mig innan jag loggar pÃ¥ en annan webbtjÃ¤nst.
screen.welcome.label.warn.accesskey=v
screen.welcome.button.login=LOGGA IN
screen.welcome.button.clear=RENSA

#Confirmation Screen Messages
screen.confirmation.message=Klicka <a href="{0}">hÃ¤r</a> fÃ¶r att komma till webbtjÃ¤nsten.

#Generic Success Screen Messages
screen.success.header=Inloggningen lyckades
screen.success.success=Du har loggat in i den centrala autentiseringstjÃ¤nsten CAS.
screen.success.security=Av sÃ¤kerhetsskÃ¤l bÃ¶r du logga ut och stÃ¤nga webblÃ¤saren nÃ¤r du Ã¤r fÃ¤rdig med webbtjÃ¤nsterna som krÃ¤ver inloggning.

#Logout Screen Messages
screen.logout.header=Du har loggat ut!
screen.logout.success=Du har loggat ut frÃ¥n den centrala autentiseringstjÃ¤nsten CAS.
screen.logout.security=Av sÃ¤kerhetsskÃ¤l bÃ¶r du stÃ¤nga din webblÃ¤sare.
screen.logout.redirect=Du kan logga in igen genom att klicka <a href="{0}">hÃ¤r</a>.

screen.service.sso.error.header=Du mÃ¥ste logga in igen fÃ¶r att anvÃ¤nda denna webbtjÃ¤nst
screen.service.sso.error.message=Du fÃ¶rsÃ¶kte anvÃ¤nda en webbtjÃ¤nst som krÃ¤ver att du loggar in igen fÃ¶r att anvÃ¤nda den. <a href="{0}">Logga in igen</a>!

error.invalid.loginticket=Du kan inte Ã¥teranvÃ¤nda ett webbformulÃ¤r som redan har skickats in.
username.required=AnvÃ¤ndaridentitet Ã¤r en obligatoriskt uppgift.
password.required=LÃ¶senord Ã¤r en obligatoriskt uppgift.
error.authentication.credentials.bad=Inloggningsuppgifterna du angav kunde inte valideras!
error.authentication.credentials.unsupported=Inloggningsuppgifterna du angav kan inte hanteras av CAS.

INVALID_REQUEST_PROXY=BÃ¥de 'pgt' och 'targetService' Ã¤r obligatoriska parametrar.
INVALID_TICKET_SPEC=Ticket-valideringen misslyckades. MÃ¶jliga fel skulle kunna vara att fÃ¶rsÃ¶ka validera en Proxy Ticket via en validator fÃ¶r Service Ticket, eller att en ny inloggning inte genomfÃ¶rdes trots begÃ¤ran.
INVALID_REQUEST=BÃ¥de 'service' och 'ticket' Ã¤r obligatoriska parametrar.
INVALID_TICKET=ticket ''{0}'' kÃ¤nns inte igen.
INVALID_SERVICE=ticket ''{0}'' Ã¶verenstÃ¤mmer inte med angiven webbtjÃ¤nst.

screen.service.error.header=Ej auktoriserad webbtjÃ¤nst
screen.service.error.message=WebbtjÃ¤nsten du fÃ¶rsÃ¶kter ansluta till Ã¤r ej auktoriserad att anvÃ¤nda den centrala autentiseringstjÃ¤nsten CAS.
