<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">
    <context:property-placeholder ignore-unresolvable="true" location="classpath:/*.properties" file-encoding="UTF-8"/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <context:component-scan base-package="cn.dahe.cas.auth.controller"/>
    <bean name="pathMatcher" class="org.springframework.util.AntPathMatcher">
        <property name="trimTokens" value="false"/>
    </bean>
    <mvc:view-resolvers order="1">
        <mvc:jsp view-class="org.springframework.web.servlet.view.JstlView" prefix="/WEB-INF/view/jsp/" suffix=".jsp"/>
    </mvc:view-resolvers>
    <mvc:annotation-driven validator="mvcValidator" conversion-service="mvcConversionService">
        <mvc:return-value-handlers>
            <ref bean="restReturnValue"/>
        </mvc:return-value-handlers>
        <mvc:message-converters>
            <ref bean="globalConvert"/>
        </mvc:message-converters>
        <mvc:argument-resolvers>
            <ref bean="userParamResolver"/>
            <ref bean="tokenParameterResolver"/>
            <bean class="cn.dahe.cas.auth.config.StartOnePageableResolver">
                <property name="maxPageSize" value="100"/>
            </bean>
        </mvc:argument-resolvers>
        <mvc:path-matching suffix-pattern="false" path-matcher="pathMatcher"/>
    </mvc:annotation-driven>
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/dahe/login"/>
            <bean class="cn.dahe.cas.auth.interceptor.ErroLoginInterceptor">
                <property name="interval" value="#{1000*60*15}"/>
                <property name="retryCount" value="3"/>
            </bean>
        </mvc:interceptor>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <ref bean="ipInterceptor"/>
        </mvc:interceptor>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <ref bean="phoneInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>
    <bean id="checkFlowHandlerMapping" class="org.springframework.webflow.mvc.servlet.FlowHandlerMapping">
        <property name="flowRegistry" ref="checkFlowRegistry"/>
        <property name="order" value="1"/>
        <property name="interceptors">
            <array value-type="org.springframework.web.servlet.HandlerInterceptor">
                <ref bean="authenticationThrottle"/>
            </array>
        </property>
    </bean>
    <bean id="checkHandlerAdapter" class="org.jasig.cas.web.flow.SelectiveFlowHandlerAdapter">
        <property name="supportedFlowId" value="check"/>
        <property name="flowExecutor" ref="checkFlowExecutor"/>
        <property name="flowUrlHandler" ref="checkFlowUrlHandler"/>
    </bean>
    <bean id="checkFlowUrlHandler" class="org.jasig.cas.web.flow.CasDefaultFlowUrlHandler"/>
    <bean name="checkFlowExecutor" class="org.springframework.webflow.executor.FlowExecutorImpl"
          c:definitionLocator-ref="checkFlowRegistry"
          c:executionFactory-ref="checkFlowExecutionFactory"
          c:executionRepository-ref="checkFlowExecutionRepository"/>

    <bean name="checkFlowExecutionFactory" class="org.springframework.webflow.engine.impl.FlowExecutionImplFactory"
          p:executionKeyFactory-ref="checkFlowExecutionRepository"/>

    <bean id="checkFlowExecutionRepository" class="org.jasig.spring.webflow.plugin.ClientFlowExecutionRepository"
          c:flowExecutionFactory-ref="checkFlowExecutionFactory"
          c:flowDefinitionLocator-ref="checkFlowRegistry"
          c:transcoder-ref="checkFlowStateTranscoder"/>

    <bean id="checkFlowStateTranscoder" class="org.jasig.spring.webflow.plugin.EncryptedTranscoder"
          c:cipherBean-ref="loginFlowCipherBean" />

    <bean id="multipartResolver"
          class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="500000000"/>
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="maxInMemorySize" value="4096"/>
    </bean>

    <bean id="knife4jConfig" class="cn.dahe.cas.auth.config.Knife4jConfig" />
    <mvc:default-servlet-handler/>
    <mvc:resources mapping="/index.html" location="/resources/index.html"/>
    <mvc:resources mapping="/static/**" location="/resources/static/"/>
    <mvc:resources location="classpath:/META-INF/resources/" mapping="doc.html"/>
    <mvc:resources location="classpath:/META-INF/resources/webjars/" mapping="/webjars/**"/>
</beans>