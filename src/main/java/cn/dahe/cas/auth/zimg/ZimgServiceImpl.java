package cn.dahe.cas.auth.zimg;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/1/26
 */
@Service
public class ZimgServiceImpl implements ZimgService {

    private static final Logger logger = LoggerFactory.getLogger(ZimgServiceImpl.class);

    private static final String UPLOAD_ACTION = "upload";
    private static final String DELETE_ACTION = "admin";
    private static final String INFO_ACTION = "info";

    @Value("${ZIMG_URL}")
    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public ZimgServiceImpl() {
    }

    public ZimgServiceImpl(String url) {
        this.url = url;
    }

    @Override
    public String upload(InputStream inputStream, String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "文件名不能为空";
        }
        // TODO: 2018/1/26 判断此文件后缀是否符合我们的要
        String backImg = ZimgUploadUtil.upload(url + "/" + UPLOAD_ACTION, inputStream, fileName);
        int beginIndex = backImg.indexOf("href");
        String imgUrl = backImg.substring(beginIndex + 7, beginIndex + 39);
        return imgUrl;
    }

    @Override
    public String upload(InputStream inputStream, String fileName, String contentType) {
        String json = ZimgUploadUtil.upload(url + "/" + UPLOAD_ACTION, inputStream, fileName, contentType);
        Gson gson = new Gson();
        Map map = gson.fromJson(json, Map.class);
        Object ret = map.get("ret");
        if ("TRUE".equalsIgnoreCase(ret.toString())) {
            Map infoMap = (Map) map.get("info");
            return infoMap.get("md5").toString();
        }
        logger.info("upload error:" + ((Map) map.get("error")).get("message"));
        return null;
    }

    @Override
    public String uploadWithURL(String filePath) {
        try {
            return url + "/" + upload(FileUtils.openInputStream(new File(filePath)), null);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String delete(String md5) {
        if (StringUtils.isBlank(md5)) {
            return null;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("md5", md5);
        params.put("t", md5);
        String backstr = ZimgUploadUtil.get(url + "/" + DELETE_ACTION, params);
        return backstr;
    }

    @Override
    public ZimgRet info(String md5) {
        if (StringUtils.isBlank(md5)) {
            return null;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("md5", md5);
        String backjson = ZimgUploadUtil.get(url + "/" + INFO_ACTION, params);
        ZimgRet zt = JSON.parseObject(backjson, ZimgRet.class);
        return zt;
    }

    @Override
    public Reader download(String md5, ZimgInfo param) {
        if (StringUtils.isBlank(md5)) {
            return null;
        }
        Map<String, Object> params = Maps.newHashMap();
        if (param != null) {
            params.put("f", param.getFormat());
            params.put("g", param.isGrey());
            params.put("h", param.getHeight());
            params.put("w", param.getWidth());
            params.put("q", param.getQuality());
            params.put("x", param.getPositionX());
            params.put("y", param.getPositionY());
        }
        String backjson = ZimgUploadUtil.get(url + "/" + md5, params);
        return new StringReader(backjson);
    }
}
