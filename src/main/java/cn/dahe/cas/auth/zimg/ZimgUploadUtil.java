package cn.dahe.cas.auth.zimg;

import org.apache.http.HttpHost;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 * @date 2018/1/26
 */
public class ZimgUploadUtil {

    private static final Logger logger = LoggerFactory.getLogger(ZimgUploadUtil.class);


    public static String get(String url,Map<String,Object> params){
        if (params!=null&&params.size()>0){
            StringBuilder sb = new StringBuilder("?");
            for(Map.Entry<String,Object> entry:params.entrySet()){
                sb.append(entry.getKey()+"="+entry.getValue()+"&");
            }
//            params.forEach((k,v)->{
//                sb.append(k+"="+v+"&");
//            });

            url = url+sb.deleteCharAt(sb.length()-1).toString();
        }
        logger.info("zimg url get {}",url);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet(url);
        try {
            CloseableHttpResponse response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            logger.info("ZimgUploadUtil IOException {}",e.getMessage());
            e.printStackTrace();
        }
        return null;
    }


    /**
     * raw方式请求
     * @param url
     * @param inputStream
     * @param fileName
     * @param contentType
     * @return
     */
    public static String upload(String url,InputStream inputStream,String fileName,String contentType){
        logger.info("url {},fileName {}",url,fileName);
        CloseableHttpClient httpClient =  HttpClientBuilder.create()
                .disableAutomaticRetries().evictExpiredConnections().build();
        HttpPost httpPost = new HttpPost(url);
        EntityBuilder entityBuilder = EntityBuilder.create();
        entityBuilder.setStream(inputStream);
        httpPost.setEntity(entityBuilder.build());
        httpPost.setHeader("Content-Type",contentType);
        try {
            CloseableHttpResponse response = httpClient.execute(httpPost);
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            logger.info("ioexception e:"+e.getMessage());
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 传统方式请求
     * @param url
     * @param inputStream
     * @param fileName
     * @return
     */
    public static String upload(String url,InputStream inputStream,String fileName){
        logger.info("url {},fileName {}",url,fileName);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addBinaryBody(UUID.randomUUID().toString(),inputStream,ContentType.MULTIPART_FORM_DATA,fileName);
        builder.setCharset(Charset.forName("UTF-8"));
        builder.setContentType(ContentType.MULTIPART_FORM_DATA);

        httpPost.setEntity(builder.build());
        try {
            CloseableHttpResponse response = httpClient.execute(httpPost);
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            logger.info("ioexception e:"+e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

}
