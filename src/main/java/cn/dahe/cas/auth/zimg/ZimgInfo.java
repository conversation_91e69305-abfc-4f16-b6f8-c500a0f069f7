package cn.dahe.cas.auth.zimg;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/1/26
 */
public class ZimgInfo implements Serializable {

    private static final long serialVersionUID = 5887741972760354679L;

    private int width;

    private int height;

    /**
     * 大小
     */
    private int size;
    /**
     * position X
     */
    private int positionX;

    private int positionY;
    /**
     * rotate旋转角度
     */
    private int rotate;
    /**
     * format格式  jpeg  png   gif  webp
     */
    private String format;
    /**
     * 去除颜色   1
     */
    private boolean grey;
    /**
     * 图片质量
     */
    private int quality;


    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getPositionX() {
        return positionX;
    }

    public void setPositionX(int positionX) {
        this.positionX = positionX;
    }

    public int getPositionY() {
        return positionY;
    }

    public void setPositionY(int positionY) {
        this.positionY = positionY;
    }

    public int getRotate() {
        return rotate;
    }

    public void setRotate(int rotate) {
        this.rotate = rotate;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public boolean isGrey() {
        return grey;
    }

    public void setGrey(boolean grey) {
        this.grey = grey;
    }


    public int getQuality() {
        return quality;
    }

    public void setQuality(int quality) {
        this.quality = quality;
    }

    @Override
    public String toString() {
        return "ZimgInfo{" +
                "width=" + width +
                ", height=" + height +
                ", size=" + size +
                ", positionX=" + positionX +
                ", positionY=" + positionY +
                ", rotate=" + rotate +
                ", format='" + format + '\'' +
                ", grey=" + grey +
                ", quality=" + quality +
                '}';
    }
}
