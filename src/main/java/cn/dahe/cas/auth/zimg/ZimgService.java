package cn.dahe.cas.auth.zimg;

import java.io.InputStream;
import java.io.Reader;

/**
 * <AUTHOR>
 * @date 2018/1/26
 */
public interface ZimgService {

    /**
     * form方式上传
     * 上传文件
     *
     * @param inputStream 文件流
     * @param fileName    文件名 此文件名必须带后缀，否则会报无法识别的类型,文件名可以任意
     * @return
     */
    String upload(InputStream inputStream, String fileName);

    /**
     * raw方式上传
     *
     * @param inputStream
     * @param fileName    上传名字  可以不带后缀
     * @param contentType 上传文件类型  jpeg  jpg   png   gif  webp
     * @return
     */
    String upload(InputStream inputStream, String fileName, String contentType);


    String uploadWithURL(String filePath);


    /**
     * 删除zimg中的图片
     *
     * @param md5
     * @return
     */
    String delete(String md5);

    /**
     * 获取zimg中的图片信息
     *
     * @param md5
     * @return
     */
    ZimgRet info(String md5);

    /**
     * 下载zimg中的图片
     *
     * @param md5
     * @param param
     * @return
     */
    Reader download(String md5, ZimgInfo param);


}
