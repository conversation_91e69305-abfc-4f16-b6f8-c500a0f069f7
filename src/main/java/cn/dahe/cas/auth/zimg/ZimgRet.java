package cn.dahe.cas.auth.zimg;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/1/26
 */
public class ZimgRet implements Serializable {

    private static final long serialVersionUID = 6958139328905106546L;

    private boolean ret;

    private ZimgInfo info;

    public boolean isRet() {
        return ret;
    }

    public void setRet(boolean ret) {
        this.ret = ret;
    }

    public ZimgInfo getInfo() {
        return info;
    }

    public void setInfo(ZimgInfo info) {
        this.info = info;
    }


    @Override
    public String toString() {
        return "ZimgRet{" +
                "ret=" + ret +
                ", info=" + info +
                '}';
    }
}
