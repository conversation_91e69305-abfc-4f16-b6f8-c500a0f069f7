package cn.dahe.cas.auth.dto;

import cn.dahe.cas.auth.constants.SmsType;

/**
 * <AUTHOR>
 * @description 短信实体
 */
public class SsoSms {

    private String mobile;
    private String code;
    private SmsType type;
    private String token;
    private String name;
    private String pwd;

    private SsoSms(Builder builder) {
        this.mobile = builder.mobile;
        this.code = builder.code;
        this.type = builder.type;
        this.token = builder.token;
        this.name = builder.name;
        this.pwd = builder.pwd;
    }

    public static Builder newSsoSms() {
        return new Builder();
    }


    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getName() {

        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getToken() {

        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public SmsType getType() {
        return type;
    }

    public void setType(SmsType type) {
        this.type = type;
    }

    public String getCode() {

        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMobile() {

        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }


    public static final class Builder {
        private String mobile;
        private String code;
        private SmsType type;
        private String token;
        private String name;
        private String pwd;

        private Builder() {
        }

        public SsoSms build() {
            return new SsoSms(this);
        }

        public Builder mobile(String mobile) {
            this.mobile = mobile;
            return this;
        }

        public Builder code(String code) {
            this.code = code;
            return this;
        }

        public Builder type(SmsType type) {
            this.type = type;
            return this;
        }

        public Builder token(String token) {
            this.token = token;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder pwd(String pwd) {
            this.pwd = pwd;
            return this;
        }
    }

    @Override
    public String toString() {
        return "SsoSms{" +
                "mobile='" + mobile + '\'' +
                ", code='" + code + '\'' +
                ", token='" + token + '\'' +
                ", name='" + name + '\'' +
                ", pwd='" + pwd + '\'' +
                '}';
    }
}
