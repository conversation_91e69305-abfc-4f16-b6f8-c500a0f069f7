package cn.dahe.cas.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * on 2020/4/28.
 */
@Data
@ApiModel(description = "系统角色dto")
public class SiteRoleDto implements Serializable {

    @ApiModelProperty(value = "id")
    private int id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "角色集合")
    private List<ResourceDto> roleList;
}
