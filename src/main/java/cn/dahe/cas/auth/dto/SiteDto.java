package cn.dahe.cas.auth.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

/**
 * 给用户添加部门的树结构
 * <AUTHOR>
 * on 2017/01/30
 */
@JsonIgnoreProperties(ignoreUnknown=true)
public class SiteDto implements Serializable {

    private static final long serialVersionUID = 2717990792315686773L;

    private String siteId;
    private String siteName;
    private String siteCode;
    private String siteHost;

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getSiteHost() {
        return siteHost;
    }

    public void setSiteHost(String siteHost) {
        this.siteHost = siteHost;
    }
}
