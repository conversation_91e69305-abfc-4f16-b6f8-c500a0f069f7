package cn.dahe.cas.auth.dto;

import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;

import java.util.List;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/14 10:41
 * @Description:
 */
public class SsoUserDto extends User{

    private List<Site> systems;

    public List<Site> getSystems() {
        return systems;
    }

    public void setSystems(List<Site> systems) {
        this.systems = systems;
    }
}
