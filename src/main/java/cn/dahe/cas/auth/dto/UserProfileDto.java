package cn.dahe.cas.auth.dto;

import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created at 2018/4/17 16:01
 * @description:
 **/
@Data
public class UserProfileDto extends User implements Serializable{

    private static final long serialVersionUID = -704535490930514211L;

    private String siteName;

    private String departmentName;

    private List<Site> sites = new ArrayList<>();
}
