package cn.dahe.cas.auth.dto;

public class ResultUtil {
    public static <T> JsonResult<T> success(T data){
        JsonResult<T> success = new JsonResult<>();
        success.setSuccess(true);
        success.setMsg("成功");
        success.setObj(data);
        success.setStatus(1);
        return  success;
    }

    public static JsonResult success(){
        JsonResult<Object> success = new JsonResult<>();
        success.setSuccess(true);
        success.setMsg("成功");
        success.setObj(new Object());
        success.setStatus(1);
        return  success;
    }

    public static JsonResult fail(String message){
        JsonResult fail = new JsonResult();
        fail.setSuccess(false);
        fail.setMsg(message);
        fail.setStatus(0);
        fail.setError(0);
        return  fail;
    }

    public static <T> JsonResult<T> fail(String message,T data){
        JsonResult<T> fail = new JsonResult<>();
        fail.setSuccess(false);
        fail.setMsg(message);
        fail.setStatus(0);
        fail.setError(0);
        fail.setObj(data);
        return  fail;
    }

    public static JsonResult fail(String message,int code){
        JsonResult fail = fail(message);
        fail.setError(code);
        fail.setStatus(code);
        return  fail;
    }
}
