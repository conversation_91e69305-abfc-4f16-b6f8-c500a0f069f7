package cn.dahe.cas.auth.dto;

import cn.dahe.cas.auth.entity.Resource;
import cn.dahe.cas.auth.entity.Role;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;

import java.io.Serializable;
import java.util.List;

/**
 * 返回给客户端的用户信息，包括用户的基本信息和权限信息
 * <AUTHOR>
 */
public class UserInfo implements Serializable{

    private static final long serialVersionUID = 5486660503393476831L;

    private User user;
    private List<String> permissions;
    private List<String> roles;
    private List<Site> sites;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public List<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<String> permissions) {
        this.permissions = permissions;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public List<Site> getSites() {
        return sites;
    }

    public void setSites(List<Site> sites) {
        this.sites = sites;
    }
}
