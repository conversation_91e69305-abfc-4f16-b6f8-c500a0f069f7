package cn.dahe.cas.auth.dto;

import org.jasig.cas.authentication.principal.Service;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/8
 * @createTime 11:18
 * @info 登录站点实体
 */
public class LoginSite implements Serializable{

    private static final long serialVersionUID = 5603162552990911994L;

    private String username;
    private long time;
    private Service service;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public Service getService() {
        return service;
    }

    public void setService(Service service) {
        this.service = service;
    }
}
