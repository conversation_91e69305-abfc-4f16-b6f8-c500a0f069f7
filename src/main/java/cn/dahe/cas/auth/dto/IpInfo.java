package cn.dahe.cas.auth.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * ip信息，主要为外部提供服务
 */
public class IpInfo implements Serializable{

    private static final long serialVersionUID = -4383775360423610352L;

    private String ip;
    private String country;
    private String province;
    private String city;

    public IpInfo(){}

    public IpInfo(String ip,String country,String province,String city){
        this.ip = ip;
        this.country = country;
        this.province = province;
        this.city = city;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
}
