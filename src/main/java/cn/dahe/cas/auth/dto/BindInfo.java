package cn.dahe.cas.auth.dto;

import cn.dahe.cas.auth.constants.LoginType;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/5 10:57
 * @Description:
 */
public class BindInfo {
    private boolean binded;
    private LoginType type;

    public BindInfo(LoginType type) {
        this.type = type;
    }

    public BindInfo(boolean binded, LoginType type) {
        this.binded = binded;
        this.type = type;
    }

    public boolean isBinded() {
        return binded;
    }

    public void setBinded(boolean binded) {
        this.binded = binded;
    }

    public LoginType getType() {
        return type;
    }

    public void setType(LoginType type) {
        this.type = type;
    }
}
