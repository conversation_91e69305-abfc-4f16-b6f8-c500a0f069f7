package cn.dahe.cas.auth.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * on 2020/5/8.
 * 添加权限接收对象的dto
 */
@Data
public class AddResource implements Serializable {

    @ApiModelProperty(value = "id")
    private int id;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "权限名称不能为空")
    private String name;

    @ApiModelProperty(value = "父节点id")
    private Integer pid = 0;

    @ApiModelProperty(value = "权限字符")
    @NotBlank(message = "权限字符不能为空")
    private String permission;

    @ApiModelProperty(value = "资源地址")
    private String url;

    @ApiModelProperty(value = "站点id")
    @Min(value = 1, message = "站点错误")
    @NotNull(message = "站点id不能为空")
    private Integer siteId;

}
