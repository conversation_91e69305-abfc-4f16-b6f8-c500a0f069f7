package cn.dahe.cas.auth.dto;

import cn.dahe.cas.auth.annotion.PhoneNumber;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/10 10:14
 * @Description:
 */
public class WxBindPhoneParameter implements Serializable{

    @NotBlank
    @PhoneNumber
    private String phone;

    @NotBlank
    private String smsCode;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }
}
