package cn.dahe.cas.auth.dto;

import org.jasig.cas.authentication.principal.Principal;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * tgc包含的用户信息，为静态站点提供
 */
public class TgcInfo implements Serializable{

    private static final long serialVersionUID = 1150587320681227451L;

    private String loginUrl;
    private String logoutUrl;
    private String homeUrl;
    private Map<String,Object> principal;

    public String getLoginUrl() {
        return loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    public String getLogoutUrl() {
        return logoutUrl;
    }

    public void setLogoutUrl(String logoutUrl) {
        this.logoutUrl = logoutUrl;
    }

    public String getHomeUrl() {
        return homeUrl;
    }

    public void setHomeUrl(String homeUrl) {
        this.homeUrl = homeUrl;
    }

    public Map<String, Object> getPrincipal() {
        return principal;
    }

    public void setPrincipal(Map<String, Object> principal) {
        this.principal = principal;
    }
}
