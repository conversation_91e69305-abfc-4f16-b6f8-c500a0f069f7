package cn.dahe.cas.auth.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/22 17:54
 * @Description:
 */
public class Area implements Serializable{

    private String id;
    private String pid;
    private String name;
    private List<Area> children;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Area> getChildren() {
        return children;
    }

    public void setChildren(List<Area> children) {
        this.children = children;
    }
}
