package cn.dahe.cas.auth.dto;

import cn.dahe.cas.auth.annotion.*;
import cn.dahe.cas.auth.entity.User;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * 注册用户实体
 */
public class RegisterUser{

    @Sensitive(message = "用户名包含敏感词")
    @Size(min = 3,max = 15)
    @Username(message = "用户名不能以数字开头")
    private String username;
    @NotBlank(message = "密码不能为空")
    @Size(min = 6)
    @Password(message = "密码过于简单")
    private String password;
    @Size(min = 6)
    private String confirmPassword;
    private String checkCode;
    @PhoneNumber(message = "手机号码格式不正确")
    private String phone;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public User toUser(){
        User user = new User();
        user.setPassword(this.getPassword());
        user.setUsername(this.getUsername());
        user.setPhone(this.getPhone());
        return user;
    }
}
