package cn.dahe.cas.auth.dto;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/3/19
 * @createTime 12:19
 * @info 选择框类型信息,默认使用key和label作为选择框的字段信息
 * 使用泛型支持不同的key和label需求
 */
public class SelectType<K,V> {
    private K key;
    private V label;

    public SelectType(K key,V label){
        this.key = key;
        this.label = label;
    }

    public K getKey() {
        return key;
    }

    public void setKey(K key) {
        this.key = key;
    }

    public V getLabel() {
        return label;
    }

    public void setLabel(V label) {
        this.label = label;
    }
}
