package cn.dahe.cas.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * on 2020/04/27
 */
@Data
@ApiModel(description = "权限树dto")
public class ResourceDto implements Serializable {

    @ApiModelProperty(value = "id")
    private int id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父节点id")
    private int pid;

    @ApiModelProperty(value = "是否展开 false 不展开  true 展开")
    private boolean spread;

    @ApiModelProperty(value = "是否被选中 0不中，1选中  默认为0")
    private int checked;

}
