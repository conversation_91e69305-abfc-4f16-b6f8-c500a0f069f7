package cn.dahe.cas.auth.dto;

public class AliWeather {
    private String reason;
    private int error_code;
    private Result result;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public int getError_code() {
        return error_code;
    }

    public void setError_code(int error_code) {
        this.error_code = error_code;
    }

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }

    public static class Result{
        private Today today;

        public Today getToday() {
            return today;
        }

        public void setToday(Today today) {
            this.today = today;
        }
    }

    public static class Today{
        private String city;
        private String temperature;
        private String wind;
        private String weather;
        private WeatherId weather_id;

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getTemperature() {
            return temperature;
        }

        public void setTemperature(String temperature) {
            this.temperature = temperature;
        }

        public String getWind() {
            return wind;
        }

        public void setWind(String wind) {
            this.wind = wind;
        }

        public String getWeather() {
            return weather;
        }

        public void setWeather(String weather) {
            this.weather = weather;
        }

        public WeatherId getWeather_id() {
            return weather_id;
        }

        public void setWeather_id(WeatherId weather_id) {
            this.weather_id = weather_id;
        }
    }

    public static class WeatherId{
        private String fa;
        private String fb;

        public String getFa() {
            return fa;
        }

        public void setFa(String fa) {
            this.fa = fa;
        }

        public String getFb() {
            return fb;
        }

        public void setFb(String fb) {
            this.fb = fb;
        }
    }

}
