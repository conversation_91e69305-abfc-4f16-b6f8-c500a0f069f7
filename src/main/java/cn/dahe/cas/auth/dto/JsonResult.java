package cn.dahe.cas.auth.dto;

/**
 * 统一的json返回结果
 * <AUTHOR>
 * @param <T>
 */
public class JsonResult<T> {
    private boolean success;
    private String msg;
    private T obj;
    private int status;
    private int error;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getObj() {
        return obj;
    }

    public void setObj(T obj) {
        this.obj = obj;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getError() {
        return error;
    }

    public void setError(int error) {
        this.error = error;
    }
}
