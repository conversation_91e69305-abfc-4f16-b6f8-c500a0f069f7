package cn.dahe.cas.auth.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 天气dto
 */
public class WeatherDto implements Serializable{

	private static final long serialVersionUID = 6741953115411430116L;

	/**
	 * 城市
	 */
	private String city;
	/**
	 * 温度
	 */
	private String temp;
	/**
	 * 天气
	 */
	private String weather;
	/**
	 * 风向
	 */
	private String windDiec;
	/**
	 * 风级
	 */
	private String windLevel;
	/**
	 * 图标
	 */
	private String icon;

	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getTemp() {
		return temp;
	}
	public void setTemp(String temp) {
		this.temp = temp;
	}
	public String getWeather() {
		return weather;
	}
	public void setWeather(String weather) {
		this.weather = weather;
	}
	public String getWindDiec() {
		return windDiec;
	}
	public void setWindDiec(String windDiec) {
		this.windDiec = windDiec;
	}
	public String getWindLevel() {
		return windLevel;
	}
	public void setWindLevel(String windLevel) {
		this.windLevel = windLevel;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}
}
