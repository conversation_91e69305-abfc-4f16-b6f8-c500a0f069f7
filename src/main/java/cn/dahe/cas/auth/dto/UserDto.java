package cn.dahe.cas.auth.dto;

import cn.dahe.cas.auth.annotion.LogCompare;
import cn.dahe.cas.auth.constants.Sex;
import cn.dahe.cas.auth.constants.UserFlag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.beanutils.PropertyUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * on 2020/5/13.
 * 用户修改的dto，用于动态记录具体修改哪些字段
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDto implements Serializable {

    private static final long serialVersionUID = -167437001332556951L;

    @LogCompare(name = "姓名")
    private String truename;

    @LogCompare(name = "手机号")
    private String phone;

    @LogCompare(name = "email")
    private String email;

    @LogCompare(name = "所属站点")
    private String sid;

    @LogCompare(name = "所属部门")
    private int organization;

    @LogCompare(name = "行政编码")
    private String areaCode;

    @LogCompare(name = "职位")
    private int job;

    @LogCompare(name = "性别")
    private Sex sex;

    @LogCompare(name = "状态")
    private UserFlag flag;

    @LogCompare(name = "工作单位")
    private String company;

    @LogCompare(name = "开始时间")
    private Date startTime;

    @LogCompare(name = "结束时间")
    private Date endTime;

    @LogCompare(name = "商务经理")
    private String businessManager;

    @LogCompare(name = "商务经理电话")
    private String businessManagerPhone;

    /**
     * 将类对象转换成Map
     * @param entity 原对象
     * @return Map
     * @throws IllegalAccessException 类型转换时报错
     */
    private static Map<String, Object> changeValueToMap(Object entity) throws IllegalAccessException {
        Map<String, Object> resultMap = new HashMap<>(3);
        Field[] fields = entity.getClass().getDeclaredFields();
        for (Field field : fields) {
            String name = field.getName();
            if (PropertyUtils.isReadable(entity, name) && PropertyUtils.isWriteable(entity, name)) {
                if (field.isAnnotationPresent(LogCompare.class)) {
                    LogCompare anno = field.getAnnotation(LogCompare.class);
                    //获取private对象字段值
                    field.setAccessible(true);
                    resultMap.put(anno.name(), field.get(entity));
                }
            }
        }
        return resultMap;
    }

    public String comparatorObject(Object oldObj) throws IllegalAccessException {
        StringBuilder content = new StringBuilder();
        if (oldObj != null) {
            Map<String, Object> oldMap = changeValueToMap(oldObj);
            Map<String, Object> newMap = changeValueToMap(this);
            if (oldMap != null && !oldMap.isEmpty()) {
                for (Map.Entry<String, Object> entry : oldMap.entrySet()) {
                    Object oldValue = entry.getValue() == null ? "" : entry.getValue();
                    Object newValue = newMap.get(entry.getKey()) == null ? "" : newMap.get(entry.getKey());
                    if (!oldValue.equals(newValue)) {
                        content.append("[").append(entry.getKey()).append(":").append(oldValue).append("->").append(newValue).append("]");
                    }
                }
            }
        } else {
            content.append("-");
        }
        return content.toString();
    }
}
