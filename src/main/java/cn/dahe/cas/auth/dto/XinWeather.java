package cn.dahe.cas.auth.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2017/12/27
 * @createTime 14:52
 * @info 新知天气返回实体
 */
public class XinWeather {

    private List<W> results;

    public List<W> getResults() {
        return results;
    }

    public void setResults(List<W> results) {
        this.results = results;
    }

    public static class W{
        private Location location;
        private Now now;

        public Location getLocation() {
            return location;
        }

        public void setLocation(Location location) {
            this.location = location;
        }

        public Now getNow() {
            return now;
        }

        public void setNow(Now now) {
            this.now = now;
        }
    }

    public static class Location{
        private String id;
        private String name;
        private String country;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }
    }

    public static class Now{
        private String text;
        private String code;
        private String temperature;
        private String wind_direction;
        private String wind_speed;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getTemperature() {
            return temperature;
        }

        public void setTemperature(String temperature) {
            this.temperature = temperature;
        }

        public String getWind_direction() {
            return wind_direction;
        }

        public void setWind_direction(String wind_direction) {
            this.wind_direction = wind_direction;
        }

        public String getWind_speed() {
            return wind_speed;
        }

        public void setWind_speed(String wind_speed) {
            this.wind_speed = wind_speed;
        }
    }
}
