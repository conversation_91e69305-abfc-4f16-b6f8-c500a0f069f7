package cn.dahe.cas.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("充值表单参数")
public class PayDto implements Serializable {

    //充值记录id
    @ApiModelProperty(value = "充值记录id(非必填，修改时必填)",required = false,position = 0)
    private int payId;
    @ApiModelProperty(value = "充值人id(也就是客户id)",required = true,position = 1)
    private int customerId;
    @ApiModelProperty(value = "充值金额",required = true,position = 2)
    private double money;
    @ApiModelProperty(value = "开始时间戳",required = true,position = 3)
    private long startTime;
    @ApiModelProperty(value = "结束时间戳",required = true,position = 4)
    private long endTime;
    @ApiModelProperty(value = "站点角色集合",required = true,position = 5)
    private List<SiteRoleDto> siteRoleList = new ArrayList<>();
    @Data
    public static class SiteRoleDto{
        @ApiModelProperty(value = "站点id",required = true,position = 6)
        private int siteId;
        @ApiModelProperty(value = "站点角色Id",required = true,position = 7)
        private int roleId;
    }

}
