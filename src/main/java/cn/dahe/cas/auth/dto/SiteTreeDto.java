package cn.dahe.cas.auth.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 给用户添加部门的树结构
 * <AUTHOR>
 * on 2017/01/30
 */
public class SiteTreeDto implements Serializable {

    private static final long serialVersionUID = -682694919719783776L;

    private String id;
    private String name;
    private String pid;
    private List<SiteTreeDto> children = new ArrayList<>();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public List<SiteTreeDto> getChildren() {
        return children;
    }

    public void setChildren(List<SiteTreeDto> children) {
        this.children = children;
    }
}
