package cn.dahe.cas.auth.dto;

import java.util.ArrayList;
import java.util.List;

public class TreeNode implements Comparable{
    /**
     *节点id
     */
    private int id;
    /**
     *父节点id
     */
    private  int pid;
    /**
     *节点内容
     */
    private String name = "";
    /**
     *是否展开
     */
    private boolean spread = true;
    /**
     *子节点列表
     */
    private List<TreeNode> children = new ArrayList<>();
    /**
     *
     */
    private boolean isUser = false;
    /**
     *是否是节点
     */
    private boolean isParent;

    private int  maxParent;

    public int getMaxParent() {
        return maxParent;
    }

    public void setMaxParent(int maxParent) {
        this.maxParent = maxParent;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSpread() {
        return spread;
    }

    public void setSpread(boolean spread) {
        this.spread = spread;
    }

    public List<TreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<TreeNode> children) {
        this.children = children;
    }

    public boolean isUser() {
        return isUser;
    }

    public void setUser(boolean user) {
        isUser = user;
    }

    public boolean isParent() {
        return isParent;
    }

    public void setParent(boolean parent) {
        isParent = parent;
    }

    public TreeNode() {
    }

    public TreeNode(int id, int pid, String name) {
        this.id = id;
        this.pid = pid;
        this.name = name;
    }
    public TreeNode(int id, int pid, String name, boolean spread, boolean check) {
        this.id = id;
        this.pid = pid;
        this.name = name;
        this.spread = spread;
    }

    public TreeNode(int id, int pid, String name, boolean isUser) {
        this.id = id;
        this.pid = pid;
        this.name = name;
        this.isUser = isUser;
    }

    public TreeNode(int id, int pid, String name, boolean spread, List<TreeNode> children , boolean isUser, boolean isParent) {
        this.id = id;
        this.pid = pid;
        this.name = name;
        this.spread = spread;
        this.children = children;

        this.isUser = isUser;
        this.isParent = isParent;
    }

    public TreeNode(int id, int pid, String name, boolean spread, List<TreeNode> children, boolean isUser, boolean isParent, int maxParent) {
        this.id = id;
        this.pid = pid;
        this.name = name;
        this.spread = spread;
        this.children = children;
        this.isUser = isUser;
        this.isParent = isParent;
        this.maxParent = maxParent;
    }

    @Override
    public String toString() {
        return "TreeNode{" +
                "id=" + id +
                ", pid=" + pid +
                ", name='" + name + '\'' +
                ", spread=" + spread +
                ", children=" + children +
                ", isUser=" + isUser +
                ", isParent=" + isParent +
                ", maxParent=" + maxParent +
                '}';
    }

    @Override
    public int compareTo(Object o) {
        return id;
    }

}
