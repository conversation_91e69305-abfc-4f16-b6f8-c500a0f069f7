package cn.dahe.cas.auth.access;

import cn.dahe.cas.auth.entity.ForbidIp;
import cn.dahe.cas.auth.exception.AccessForbidException;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.repositories.ForbidIpRepository;
import cn.dahe.cas.auth.service.ForbidIpService;
import cn.dahe.cas.auth.util.PageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Stream;

/**
 * 基于ip地址的访问控制
 * ip地址元数据暂时从配置文件中读取
 */
@Component
public class IpAccessStrategy implements AccessStrategy,ForbidIpService {

    @Resource(name = "tokenRedisTemplate")
    private ZSetOperations<String,String> ipOperation;

    @Autowired
    private ForbidIpRepository repository;

    @Value("${forbid.ips}")
    private String fipKey;

    @PostConstruct
    public void init(){
        loadToRedis();
    }

    private void loadToRedis(){
        ipOperation.getOperations().delete(fipKey);
        List<ForbidIp> all = repository.findAll();
        if (all != null && all.size() > 0){
            for (ForbidIp forbidIp : all) {
                ipOperation.add(fipKey, forbidIp.getIpAddress(), System.currentTimeMillis());
            }
        }
    }

    @Override
    public void isAllow(String... param) throws AccessForbidException {
        Stream.of(param).forEach(s -> {
            Long exist = ipOperation.rank(fipKey,param[0]);
            if(exist!=null){
                throw new AccessForbidException("ip地址被禁止访问");
            }
        });
    }

    @Override
    public Page<String> getForbidIps(Pageable page) {
        List<String> ips = PageUtil.getPage(page,ipOperation,fipKey);
        Page<String> result = new PageImpl<>(ips,null,ipOperation.size(fipKey));
        return result;
    }

    @Override
    public void addIp(String ip) {
        ForbidIp forbidIp = repository.findByIpAddress(ip);
        if (forbidIp == null){
            forbidIp = new ForbidIp();
            forbidIp.setIpAddress(ip);
            repository.save(forbidIp);
            ipOperation.add(fipKey,ip,System.currentTimeMillis());
        }else {
            throw new SsoException("ip不可重复！");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(String ip) {
        repository.deleteByIpAddress(ip);
        ipOperation.remove(fipKey,ip);
    }

    @Override
    public Page<String> getForbidIps(Pageable page, String ip){
        Page<String> result = PageUtil.getDataByCondition(ipOperation, fipKey, ip);
        return result;
    }
}
