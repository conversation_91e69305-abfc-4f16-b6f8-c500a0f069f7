package cn.dahe.cas.auth.access;

import cn.dahe.cas.auth.entity.ForbidMobile;
import cn.dahe.cas.auth.exception.AccessForbidException;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.repositories.ForbidMobileRepository;
import cn.dahe.cas.auth.service.PhoneService;
import cn.dahe.cas.auth.util.PageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Component
public class PhoneAccessStrategy implements AccessStrategy,PhoneService{

    @Resource(name = "tokenRedisTemplate")
    private ZSetOperations<String,String> phoneOperation;

    @Autowired
    private ForbidMobileRepository repository;

    @Value("${forbid.phones}")
    private String fpKey;

    @PostConstruct
    public void init(){
        loadToRedis();
    }

    private void loadToRedis(){
        phoneOperation.getOperations().delete(fpKey);
        List<ForbidMobile> all = repository.findAll();
        if (all != null && all.size() > 0){
            for (ForbidMobile forbidMobile : all) {
                phoneOperation.add(fpKey, forbidMobile.getPhone(), System.currentTimeMillis());
            }
        }
    }

    @Override
    public void isAllow(String... param) throws AccessForbidException {
        Stream.of(param).forEach(s -> {
            Long exist = phoneOperation.rank(fpKey,param[0]);
            if(exist!=null){
                throw new AccessForbidException("手机号被禁用");
            }
        });
    }

    @Override
    public Page<String> getForbidPhones(Pageable page) {
        List<String> phones = PageUtil.getPage(page,phoneOperation,fpKey);
        Page<String> result = new PageImpl<>(phones,null,phoneOperation.size(fpKey));
        return result;
    }

    @Override
    public void addPhone(String phone) {
        ForbidMobile forbidMobile = repository.findByPhone(phone);
        if (forbidMobile == null){
            forbidMobile = new ForbidMobile();
            forbidMobile.setPhone(phone);
            repository.save(forbidMobile);
            phoneOperation.add(fpKey,phone,System.currentTimeMillis());
        }else {
            throw new SsoException("手机号不可重复！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(String phone) {
        repository.deleteByPhone(phone);
        phoneOperation.remove(fpKey,phone);
    }

    @Override
    public Page<String> getForbidPhones(Pageable page, String phone){
        Page<String> result = PageUtil.getDataByCondition(phoneOperation, fpKey, phone);
        return result;
    }
}
