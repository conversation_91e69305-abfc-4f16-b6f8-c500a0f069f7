package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.entity.SmsRecord;
import cn.dahe.cas.auth.event.SmsEvent;
import cn.dahe.cas.auth.service.SmsRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2017/12/23
 * @createTime 10:32
 * @info
 */
@Component
public class SmsTask implements ApplicationListener<SmsEvent>{

    @Autowired
    private SmsRecordService telbookService;

    @Override
    public void onApplicationEvent(SmsEvent event) {
        recordSms(event);
    }

    /**
     * 记录短信发送情况
     * @param event
     */
    @Async
    public void recordSms(SmsEvent event){
        SmsRecord smsRecord = new SmsRecord();
        smsRecord.setSendDate(new Date());
        smsRecord.setPhone(event.getPhone());
        smsRecord.setIp(event.getIp());
        smsRecord.setUsername(event.getPhone());
        telbookService.add(smsRecord);
    }
}
