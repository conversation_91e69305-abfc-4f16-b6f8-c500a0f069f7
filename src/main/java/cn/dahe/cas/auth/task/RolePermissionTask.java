package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.constants.SiteStatus;
import cn.dahe.cas.auth.dto.RoleDto;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.event.RolePermissionEvent;
import cn.dahe.cas.auth.message.RoleProducer;
import cn.dahe.cas.auth.service.ClearCache;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * on 2020/5/13.
 */
@Component
public class RolePermissionTask implements ApplicationListener<RolePermissionEvent> {

    @Autowired
    private ClearCache clearCache;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleProducer roleProducer;

    @Autowired
    private SiteService siteService;

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource(name = "hashTemplate")
    private HashOperations<String, String, RoleDto> cacheTemplate;

    @Value("${sm4.security.key}")
    private String sm4Key;

    private static final Logger logger = LoggerFactory.getLogger(RolePermissionTask.class);

    @Async
    @Override
    public void onApplicationEvent(RolePermissionEvent event) {
        HashMap<String, Integer> map = Maps.newHashMap();
        switch (event.getType()){
            case ROLE:
            case PERMISSION:
                map.put("userId", -1);
                int siteId = event.getSiteId();
                Site site = siteService.get(siteId);
                // 站点被禁用或者不存在的不发送消息
                if (site == null || site.getStatus() == SiteStatus.DENY){
                    break;
                }
                // 根据站点查找tag
                if (SsoStringUtil.isNotBlank(site.getLogTag())){
                    // 清除缓存
                    cacheTemplate.getOperations().delete(CacheKey.USER_ROLE + ":" + site.getLogTag());
                    //try {
                    //    roleProducer.sendMessage(site.getLogTag(), JSON.toJSONString(map));
                    //}catch (Exception exception){
                    //    logger.error("权限更改发送失败{}",exception.getMessage());
                    //}
                }
                break;
            case USER:
                int uid = Integer.parseInt(event.getUserId());
                map.put("userId", uid);
                // 遍历用户可通过的站点，查找tag
                List<Site> accessSite = siteService.getAccessSite(uid);
                for (Site site1 : accessSite) {
                    if (SsoStringUtil.isNotBlank(site1.getLogTag())){
                        RoleDto userProfileDto = userService.getBySiteAndUid(uid, site1.getId());
                        cacheTemplate.put(CacheKey.USER_ROLE + ":" + site1.getLogTag(), uid+"", userProfileDto);
                        //try {
                        //    roleProducer.sendMessage(site1.getLogTag(), JSON.toJSONString(map));
                        //}catch (Exception exception){
                        //    logger.error("权限更改发送失败{}",exception.getMessage());
                        //}
                        clearCache.clear(uid);
                    }
                }
                break;
            default:
                break;
        }
    }
}
