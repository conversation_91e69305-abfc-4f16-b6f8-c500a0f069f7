package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.constants.SiteStatus;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.event.SiteEvent;
import cn.dahe.cas.auth.service.SiteService;
import org.jasig.cas.services.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 * 对站点增删改进行监控处理
 */
@Component
public class SiteTask implements ApplicationListener<SiteEvent>{

    private static final Logger log = LoggerFactory.getLogger(SiteTask.class);


    @Autowired
    private ReloadableServicesManager servicesManager;

    @Autowired
    private SiteService siteService;

    private static final String LOGOUT_URL="/sso/logout";

    @Async
    @Override
    public void onApplicationEvent(SiteEvent siteEvent) {
        Site site = siteEvent.getSite();
        switch (siteEvent.getOperation()){
            case ADD:
                add(site);
                break;
            case EDIT:
                edit(site);
                break;
            case DELETE:
                delete(site);
                break;
            case FORBID:
                forbid(site);
                break;
            default:
                break;
        }
    }

    private void add(Site site){
        log.debug("添加站点{}",site.toString());
        RegexRegisteredService service = new RegexRegisteredService();
        service.setId(site.getId());
        service.setServiceId(generateServiceId(site.getUrl()));
        service.setName(site.getName());
        service.setDescription(site.getDescription());
        RegisteredServiceAttributeReleasePolicy policy = new ReturnAllAttributeReleasePolicy();
        service.setAttributeReleasePolicy(policy);
        servicesManager.save(service);
    }

    /**
     * 修改站点信息后将其从ServiceRegistryDao同步更改
     * @param site
     */
    private void edit(Site site){
        log.debug("修改站点{}",site.toString());
        RegisteredService service2 = servicesManager.findServiceBy(site.getId());
        if(service2 != null){
            servicesManager.delete(service2.getId());
        }
        RegexRegisteredService service = new RegexRegisteredService();
        service.setServiceId(generateServiceId(site.getUrl()));
        service.setId(site.getId());
        service.setName(site.getName());
        service.setDescription(site.getDescription());
        RegisteredServiceAttributeReleasePolicy policy = new ReturnAllAttributeReleasePolicy();
        service.setAttributeReleasePolicy(policy);
        servicesManager.save(service);
    }

    /**
     * 删除一个站点后需要将其从ServiceRegistryDao同步删除
     * @param site
     */
    public void delete(Site site){
        siteService.deleteAllUserSite(site.getId());
        RegisteredService service = servicesManager.findServiceBy(site.getId());
        if(service==null){
            log.debug("service for {} is null,may be the redis is clear uncorrectly",site.getName());
            return;
        }
        servicesManager.delete(service.getId());
    }

    private void forbid(Site site){
        AbstractRegisteredService service = (AbstractRegisteredService) servicesManager.findServiceBy(site.getId());
        servicesManager.delete(service.getId());
        DefaultRegisteredServiceAccessStrategy accessStrategy = new DefaultRegisteredServiceAccessStrategy();
        SiteStatus status = site.getStatus();
        switch (status){
            case ALLOW:
                accessStrategy.setSsoEnabled(true);
                accessStrategy.setEnabled(true);
                break;
            case DENY:
                accessStrategy.setSsoEnabled(false);
                accessStrategy.setEnabled(false);
                break;
            default:
                break;
        }
        service.setAccessStrategy(accessStrategy);
        servicesManager.save(service);
    }

    /**
     * 服务id生成，目前以regex作为注册服务，每个站点为一个单独的注册服务
     * @param url
     * @return
     */
    private String generateServiceId(String url){
        String regex = "^"+url+".*";
        log.debug("generate service id for {}   result is {}",url,regex);
        return regex;
    }

    /**
     * 生成每个服务的退出登录back channel接口地址
     * @param site
     * @return
     */
    private URL generateLogoutUrl(Site site){
        String url = site.getUrl().replaceAll("/$","");
        String logout = url+LOGOUT_URL;
        try {
            return new URL(logout);
        } catch (MalformedURLException e) {
            e.printStackTrace();
            return null;
        }
    }
}
