package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.event.UserSiteEvent;
import cn.dahe.cas.auth.repositories.UserRepository;
import cn.dahe.cas.auth.util.CommonUtil;
import cn.dahe.cas.auth.util.DateUtil;
import cn.dahe.cas.auth.util.OkHttpUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Service
public class UserSiteTaskImpl implements UserSiteTask {


    private static final Logger log = LoggerFactory.getLogger(UserSiteTaskImpl.class);

    @Autowired
    private UserRepository userRepository;


    @EventListener()
    @Override
    public void onApplicationEvent(UserSiteEvent userSiteEvent) throws Exception {
        User user = userRepository.findOne(userSiteEvent.getUserId());
        if (userSiteEvent.getSids().contains(98)) {
            log.debug("用户{}绑定系统发生变更，相关系统包含了风评AI 需要更新 风评权限和用户关联id", user.getUsername());
            // 查询用户信息  如果在 更新本地库中的  id 和 id,绑定应用
            Response getResponse = OkHttpUtils.newInstance().doGet("https://ai-box.dahe.cn/api/manager/users/?username=" + user.getPhone());
            try {
                String format = DateUtil.format(new Date(), "yyyy");
                String nextYearLastDay = String.valueOf(Integer.parseInt(format) + 10) + "-12-31";

                String string = getResponse.body().string();
                JSONObject jsonObject = JSONObject.parseObject(string);
                Object o = jsonObject.get("code");
                if ("2000".equals(o.toString())) {
                    Object o1 = jsonObject.get("data");
                    JSONObject jsonObject1 = JSONObject.parseObject(o1.toString());
                    JSONArray o2 = jsonObject1.getJSONArray("data");
                    Object o3 = o2.get(0);
                    JSONObject jsonObject3 = JSONObject.parseObject(o3.toString());
                    String string1 = jsonObject3.getString("id");
                    JSONArray applications = jsonObject3.getJSONArray("applications");
                    if (applications.size() > 0) {
                        for (Object o4 : applications) {
                            JSONObject jsonObject4 = JSONObject.parseObject(o4.toString());
                            if ("1".equals(jsonObject4.get("application"))) {
                                user.setUserApplications(jsonObject4.getInteger("id"));
                            }
                        }
                    }
                    // 远程开通对应风评服务
                    if (user.getUserApplications() == 0) {
                        Map<String, String> bdMap = new HashMap<>();
                        bdMap.put("user", string1);
                        bdMap.put("application", "1");
                        bdMap.put("expiration_date", nextYearLastDay);
                        Response bdResponse = OkHttpUtils.newInstance().doPostJson("https://ai-box.dahe.cn/api/manager/userapplications/add/", bdMap);
                        String bdString = bdResponse.body().string();
                        JSONObject bdJsonObject = JSONObject.parseObject(bdString);
                        Object bdO = bdJsonObject.get("code");
                        if ("200".equals(bdO)) {
                            user.setUserApplications(Integer.parseInt(jsonObject.get("data").toString()));
                            userRepository.saveAndFlush(user);
                        }
                    } else {
                        Map<String, String> map = new HashMap<>();
                        map.put("expiration_date", nextYearLastDay);
                        String url = "https://ai-box.dahe.cn/api/manager/userapplications/" + user.getUserApplications() + "/update_expiration/";
                        Response response = OkHttpUtils.newInstance().doPutJson(url, map);
                        try {
                            String string2 = response.body().string();
                            JSONObject jsonObject2 = JSONObject.parseObject(string2);
                            Object object = jsonObject2.get("code");
                            if ("4000".equals(object.toString())) {
                                log.debug("续费失败，原因：" + object);
                            }
                            if ("2000".equals(object.toString())) {
                                log.debug("续费成功");
                            }
                        } catch (Exception e) {
                            log.debug("续费失败，原因：" + e.getMessage());
                        }
                    }
                } else {
                    // 如果不在，添加
                    Map<String, String> map = new HashMap<>();
                    map.put("name", user.getTruename());
                    map.put("password", CommonUtil.getLastSixDigits(user.getPhone()));
                    map.put("username", user.getPhone());
                    Response response = OkHttpUtils.newInstance().doPostJson("https://ai-box.dahe.cn/api/manager/users/", map);
                    try {
                        String string1 = response.body().string();
                        JSONObject jsonObject1 = JSONObject.parseObject(string1);
                        Object o1 = jsonObject1.get("code");
                        if ("4000".equals(o1.toString())) {
                            log.debug("开通失败，原因：" + jsonObject.get("msg"));
                            return;
                        }
                        if ("200".equals(o1.toString())) {
                            Map<String, String> bdMap = new HashMap<>();
                            bdMap.put("user", jsonObject.get("data").toString());
                            bdMap.put("application", "1");
                            bdMap.put("expiration_date", nextYearLastDay);
                            Response bdResponse = OkHttpUtils.newInstance().doPostJson("https://ai-box.dahe.cn/api/manager/userapplications/add/", bdMap);
                            String bdString = bdResponse.body().string();
                            JSONObject bdJsonObject = JSONObject.parseObject(bdString);
                            Object bdO = bdJsonObject.get("code");
                            if ("200".equals(bdO)) {
                                user.setUserApplications(Integer.parseInt(jsonObject.get("data").toString()));
                                userRepository.saveAndFlush(user);
                            }
                        }
                        log.debug("开通失败，原因：" + jsonObject.get("msg"));
                    } catch (Exception e) {
                        log.debug("开通失败，原因：" + e.getMessage());
                    }
                }
            } catch (Exception e) {
                log.debug("开通失败，原因：" + e.getMessage());
            }
        } else {
            log.debug("用户{}绑定系统发生变更，相关系统未包含风评AI 需要更新 风评权限", user.getUsername());
            Response getResponse = OkHttpUtils.newInstance().doGet("https://ai-box.dahe.cn/api/manager/users/?username=" + user.getPhone());
            try {
                String string = getResponse.body().string();
                JSONObject jsonObject = JSONObject.parseObject(string);
                Object o = jsonObject.get("code");
                if ("2000".equals(o.toString())) {
                    Object o1 = jsonObject.get("data");
                    JSONObject jsonObject1 = JSONObject.parseObject(o1.toString());
                    JSONArray o2 = jsonObject1.getJSONArray("data");
                    Object o3 = o2.get(0);
                    JSONObject jsonObject3 = JSONObject.parseObject(o3.toString());
                    JSONArray applications = jsonObject3.getJSONArray("applications");
                    if (applications.size() > 0) {
                        // 更新用户的到期时间
                        Map<String, String> map = new HashMap<>();
                        Date beginTime = DateUtil.getBeginTime(1);
                        map.put("expiration_date", DateUtil.format(beginTime, "yyyy-MM-dd"));
                        String url = "https://ai-box.dahe.cn/api/manager/userapplications/" + user.getUserApplications() + "/update_expiration/";
                        Response response = OkHttpUtils.newInstance().doPutJson(url, map);
                        String string2 = response.body().string();
                        JSONObject jsonObject2 = JSONObject.parseObject(string2);
                        Object object = jsonObject2.get("code");
                        if ("4000".equals(object.toString())) {
                            log.debug("更新失败，原因：" + object);
                        }
                        if ("2000".equals(object.toString())) {
                            log.debug("更新成功");
                        }
                    }else {
                        log.debug("未开通，无需更新");
                    }
                } else {
                    log.debug("查询失败，原因：" + jsonObject);
                }
            } catch (Exception e) {
                log.debug("取消开通失败，原因：" + e.getMessage());
            }

        }
    }
}
