package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.service.ThirdService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时更新天气信息、
 * 3个小时更新一次
 * <AUTHOR>

@Component
public class WeatherUpdateTask {

    private static final transient Logger logger = LoggerFactory.getLogger(WeatherUpdateTask.class);

    @Autowired
    private ThirdService thirdService;

    /**
     * @Scheduled(fixedRate = 1000*60*60)
     * 定时清理天气缓存现在由redis自动完成
     */
    public void updateWeather(){
        thirdService.clearWeather();
    }
}
