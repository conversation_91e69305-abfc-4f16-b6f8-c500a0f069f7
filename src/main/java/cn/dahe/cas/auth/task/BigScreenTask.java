package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.repositories.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @Author: 杨振雨
 * @Date: 2020/4/17 09:06
 * @Description:
 */
@Component
public class BigScreenTask {

    @Qualifier("externalRedisTemplate")
    @Autowired
    private RedisTemplate<String,Integer> redisTemplate;

    @Autowired
    private UserRepository userRepository;

    @Scheduled(cron = "0 0 2 * * ?")
    @Async
    public void run(){
//        //向redis中填充统计数据
//        redisTemplate.opsForValue().set("bs:sso:back:count",userRepository.getUserCount());
        redisTemplate.opsForValue().set("bs:sso:back:count",0);
    }
}
