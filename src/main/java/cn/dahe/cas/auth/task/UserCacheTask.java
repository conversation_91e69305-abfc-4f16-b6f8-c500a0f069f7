package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.external.entity.SsoUser;
import cn.dahe.cas.auth.external.repository.SsoUserRepository;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.Sm4Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.ConversionService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * on 2021/1/26.
 */
@Component
public class UserCacheTask {

    @Autowired
    private SsoUserRepository ssoUserRepository;

    @Autowired
    private ConversionService conversionService;

    @Autowired
    private UserService userService;

    @Value("${sm4.security.key}")
    private String sm4Key;

    /**
     * 两个小时更新一次
     */
    @Scheduled(cron = "0 0 0/2 * * ?")
    @Async
    private void ensureExternalUserCache() {
        ssoUserRepository.clear();
        List<User> users = userService.get();
        users.forEach(user -> {
            try {
                user.setPhone(Sm4Util.decryptEcb(sm4Key, user.getPhone()));
                user.setTruename(Sm4Util.decryptEcb(sm4Key, user.getTruename()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            ssoUserRepository.add(conversionService.convert(user, SsoUser.class));
        });
    }
}
