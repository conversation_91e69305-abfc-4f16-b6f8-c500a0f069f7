package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.action.DuplicateLoginCheckAction;
import cn.dahe.cas.auth.config.RedisKey;
import org.jasig.cas.authentication.Authentication;
import org.jasig.cas.authentication.principal.Principal;
import org.jasig.cas.support.events.CasTicketGrantingTicketDestroyedEvent;
import org.jasig.cas.ticket.TicketGrantingTicket;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/19
 * @createTime 10:48
 * @info 对单点退出，需要将redis中其对应的缓存进行清除
 */
@Component
public class LogoutTask implements ApplicationListener<CasTicketGrantingTicketDestroyedEvent>{

    @Resource(name = "redisTemplate")
    private HashOperations<String,Integer,String> hashOperations;

    @Override
    public void onApplicationEvent(CasTicketGrantingTicketDestroyedEvent event) {
        TicketGrantingTicket ticket = event.getTicketGrantingTicket();
        Authentication authentication = ticket.getAuthentication();
        if(authentication==null){
            return;
        }
        Principal principal = authentication.getPrincipal();
        if(principal==null){
            return;
        }
        Map<String,Object> attributes = principal.getAttributes();
        if(attributes==null){
            return;
        }
        if(!attributes.containsKey(DuplicateLoginCheckAction.USER_ID)){
            return;
        }
        int uid = (int) attributes.get(DuplicateLoginCheckAction.USER_ID);
        String key = RedisKey.LOGINED_USER_KEY;
        if(!hashOperations.hasKey( RedisKey.LOGINED_USER_KEY,uid)){
            return;
        }
        String tgt = hashOperations.get(key,uid);
        if(ticket.getId().equals(tgt)){
            hashOperations.delete(key,uid);
        }
    }
}
