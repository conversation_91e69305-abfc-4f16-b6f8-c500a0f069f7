package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.service.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * on 2018/7/12.
 */
@Component
public class LogClearTask {
    @Resource(name = "redisTemplate")
    private ValueOperations<String,Integer> redisTemplate;

    @Value("${log.count}")
    private String logCount;
    @Value("${log.date}")
    private String logDate;
    @Value("${log.switch}")
    private String logSwitch;

    @Autowired
    private LogService logService;

    //凌晨两点清除日志
    @Scheduled(cron = "0 0 2 * * ?")
    @Async
    public void run(){
        if (redisTemplate.get(logSwitch) == 1){
            logService.deleteByCondition(redisTemplate.get(logDate), redisTemplate.get(logCount));
        }
    }
}
