package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.event.UserEvent;
import cn.dahe.cas.auth.external.entity.SsoUser;
import cn.dahe.cas.auth.external.repository.SsoUserRepository;
import cn.dahe.cas.auth.service.CasService;
import cn.dahe.cas.auth.service.LockUserService;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.SmsService;
import cn.dahe.cas.auth.util.Sm4Util;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/20
 * @createTime 13:59
 * @info
 */
@Service
public class UserTaskImpl implements UserTask {


    private static final Logger log = LoggerFactory.getLogger(UserTaskImpl.class);

    @Autowired
    private SiteService siteService;

    @Autowired
    private CasService casService;

    @Autowired
    private LockUserService lockUserService;

    @Autowired
    private SmsService smsService;

    @Autowired
    @Qualifier("ssoThreadPoolTaskExecutor")
    private TaskExecutor taskExecutor;

    @Value("#{${default.sites}}")
    private Map<String,String> sites;

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    private SsoUserRepository ssoUserRepository;

    @Autowired
    private ConversionService conversionService;

    /**
     * 只要用户信息变更，即将sso对子系统的用户返回信息缓存清空
     * @param userEvent
     */
    @CacheEvict(cacheNames = CacheKey.PERSON_CACHE_KEY,key = "#userEvent.user.uid+''",beforeInvocation = true)
    @EventListener()
    @Override
    public void onApplicationEvent(UserEvent userEvent) throws Exception {
        User user = userEvent.getUser();
        EntityOperation operation = userEvent.getOperation();
        log.debug("用户{}信息发生变更，操作为{}", user.getUsername(),operation.getDescription());
        switch (operation){
            case ADD:
                onAdd(user);
                break;
            case DELETE:
                delete(user);
            case EDIT:
                update(user);
                break;
            default:
                break;
        }
    }

    /**
     * 添加的用户需要同步到redis供外部系统使用
     * @param user
     */
    public void onAdd(User user) throws Exception {
        //添加默认站点
        siteService.addDefaultSiteToUser(user.getUid());
        user.setPhone(Sm4Util.decryptEcb(sm4Key, user.getPhone()));
        user.setTruename(Sm4Util.decryptEcb(sm4Key, user.getTruename()));
        ssoUserRepository.add(conversionService.convert(user, SsoUser.class));
        //给用户发送账号开通短信提醒,异步发送
        taskExecutor.execute(() -> {
            try {
                smsService.sendAccountCreateSms(user.getPhone());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    /**
     * 用户删除时同步对redis中相应信息进行清理
     * 通知子系统将用户下线
     * @param user
     */
    public void delete(User user){
        ssoUserRepository.delete(user.getUid());
        casService.logoutUser(user.getUid());
        lockUserService.unlock(user.getUid());
    }

    /**
     * 用户信息更新需要更新用户信息缓存
     * 对于被禁用的用户需要同步将其强制下线
     * @param user
     */
    public void update(User user) throws Exception {
        if(SecurityUtils.getSubject().isAuthenticated()){
            User old = (User) SecurityUtils.getSubject().getSession().getAttribute("user");
            if(user.getUid() == old.getUid()){
                //todo 解密
                user.setPhone(Sm4Util.decryptEcb(sm4Key, user.getPhone()));
                user.setTruename(Sm4Util.decryptEcb(sm4Key, user.getTruename()));
                SecurityUtils.getSubject().getSession().setAttribute("user",user);
            }
        }
        ssoUserRepository.add(conversionService.convert(user, SsoUser.class));
        switch (user.getFlag()){
            case FORBID:
                casService.logoutUser(user.getUid());
                break;
            default:
                break;
        }
    }


}
