package cn.dahe.cas.auth.task;

import cn.dahe.cas.auth.domain.LoginLog;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.event.AuthenticationEvent;
import cn.dahe.cas.auth.repositories.UserRepository;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.UserLoginLogService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.IpUtils;
import cn.dahe.cas.auth.util.Sm4Util;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Date;
import java.util.List;

import static java.time.temporal.ChronoUnit.DAYS;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/7 14:47
 * @Description:
 */
@Component
public class AuthenticationTask implements ApplicationListener<AuthenticationEvent>{

    private static final Logger log = LoggerFactory.getLogger(AuthenticationTask.class);

    @Autowired
    private UserService userService;

    @Autowired
    private UserLoginLogService loginLogService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private UserRepository userRepository;

    @Value("${cas.server}")
    private String defaultSiteUrl;
    @Value("${sm4.security.key}")
    private String sm4Key;

    @Override
    @Async
    public void onApplicationEvent(AuthenticationEvent event) {
        //缺省使用本系统站点
        if(StringUtils.isBlank(event.getSiteUrl())){
            event.setSiteUrl(defaultSiteUrl);
        }
        Site site = siteService.getOneSite(event.getSiteUrl());
        //由于异步处理，因此该时间会有误差，但是无谓，后期如果走队列需修改
        LoginLog.LoginLogBuilder loginLogBuilder = LoginLog.builder().createDate(new Date()).identify(event.getCredential().getId())
                .loginAddress(IpUtils.getRemoteIp(event.getRequest()))
                .type(event.getLoginType()).site(site.getId()).siteName(site.getName())
                .result(event.isSuccess()?0:1)
                .extras(JSON.toJSONString(event.getCredential()))
                .siteUrl(site.getUrl());
        //此处可以直接使用是否为null判断，需要数据源保证成功后才进行设置
        if(event.getUid()!=null){
            User user = userService.get(event.getUid());
            loginLogBuilder.uid(user.getUid());
            LoginLog loginLog = loginLogBuilder.build();
            try {
                loginLog.setPhone(Sm4Util.decryptEcb(sm4Key, user.getPhone()));
                loginLog.setUsername(user.getUsername());
            } catch (Exception e) {
                e.printStackTrace();
            }
            loginLogService.add(loginLog);
        }else {
            //冗余了一小段代码，不优美了
            LoginLog loginLog = loginLogBuilder.build();
            loginLog.setExtras(JSON.toJSONString(event.getCredential()));
            loginLogService.add(loginLog);
        }
        // todo 解密，日志里存储明文

    }

    @Async
    @Scheduled(cron = "0 0 9 * * ?")
    public void notifyLazyUser(){
        //todo 可加入工作日判断
        List<Integer> loginUserIds = loginLogService.findLoginUserAfter(Date.from(Instant.now().minus(30,DAYS)));
        List<Integer> allUserIds = userRepository.findAllId();
        allUserIds.removeAll(loginUserIds);
        //进行处理即可
        allUserIds.forEach(integer -> {
            //todo 进行相应处理，例如短信通知
            log.debug("超过30天未登录用户id:{}",integer);
        });
    }
}
