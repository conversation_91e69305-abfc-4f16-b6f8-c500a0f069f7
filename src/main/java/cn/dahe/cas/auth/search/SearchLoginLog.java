package cn.dahe.cas.auth.search;

import cn.dahe.cas.auth.constants.LoginType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date: 2019/8/9 10:59
 * Description: 登录日志搜索条件
 */
@Data
public class SearchLoginLog {
    private LoginType type;
    private String phone;
    private String username;
    private String keyword;
    private Date beginDate;
    private Date endDate;
    private String identify;
    private Integer result;
}
