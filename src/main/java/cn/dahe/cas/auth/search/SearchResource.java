package cn.dahe.cas.auth.search;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * on 2020/4/27.
 */
@Data
@ApiModel(description = "权限搜索条件")
public class SearchResource implements Serializable{

    @ApiModelProperty(value = "系统id")
    private Integer siteId;
    @ApiModelProperty(value = "权限字符/权限名称")
    private String keyword;
}
