package cn.dahe.cas.auth.search;

import cn.dahe.cas.auth.constants.LogType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * on 2018/1/23 14:02
 * 作为log的搜索条件
 */
@Data
public class SearchLog implements Serializable{

    private String source;
    private String title;
    private LogType type;

    private String requestUri;

    private String paramter;

    private Date beginDate;

    private Date endDate;

    private Integer uid;
}
