package cn.dahe.cas.auth.search;

import cn.dahe.cas.auth.constants.UserFlag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/19
 * @createTime 14:25
 * @info 作为user的搜索条件
 */
@ApiModel("查询用户信息参数")
@Data
public class SearchUser implements Serializable{

    @ApiModelProperty(value = "姓名")
    private String truename;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "状态:0正常，1禁用，-1删除")
    private UserFlag flag;
    @ApiModelProperty(value = "站点id")
    private String sid;
    @ApiModelProperty(hidden = true)
    private int organization=0;
    @ApiModelProperty(value = "角色id")
    private int roleId;
    @ApiModelProperty(value = "站点id")
    private Integer siteId;
    @ApiModelProperty(value = "用户类型：0普通客户，1管理员")
    private Integer type;
}
