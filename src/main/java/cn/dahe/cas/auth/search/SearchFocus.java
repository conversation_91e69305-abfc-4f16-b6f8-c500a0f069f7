package cn.dahe.cas.auth.search;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "重点人物、账号")
public class SearchFocus implements Serializable {

    @ApiModelProperty(value = "状态")
    private int status = -2;
    @ApiModelProperty(value = "关键字")
    private String keywords;
    @ApiModelProperty(value = "类型")
    private int type = -2;
}
