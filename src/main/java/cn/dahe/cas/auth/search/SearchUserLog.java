package cn.dahe.cas.auth.search;

import cn.dahe.cas.auth.constants.LogOperation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * on 2020/05/07
 * 用户修改日志搜索条件
 */
@ApiModel("查询用户日志参数")
@Data
public class SearchUserLog implements Serializable{

    @ApiModelProperty(value = "用户操作类型")
    private LogOperation operateType;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "开始时间")
    private Date beginDate;

    @ApiModelProperty(value = "结束时间")
    private Date endDate;

    @ApiModelProperty(value = "搜索内容")
    private String keyword;

}
