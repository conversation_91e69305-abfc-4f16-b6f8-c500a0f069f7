package cn.dahe.cas.auth.search;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> on 2020/4/27.
 */
@Data
@ApiModel(description = "角色搜索条件")
public class SearchRole implements Serializable {
    @ApiModelProperty(value = "系统id")
    private Integer siteId;
    @ApiModelProperty(value = "角色字符")
    private String sn;
    @ApiModelProperty(value = "角色名称")
    private String name;
}
