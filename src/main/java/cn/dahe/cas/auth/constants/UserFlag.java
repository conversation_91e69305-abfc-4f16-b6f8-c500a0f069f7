package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 * 转换UserFlag 和 Integer
 */
public enum UserFlag {
    ALLOW(0,"正常"),FORBID(1,"禁用"),DELETE(-1,"删除");

    private int flag = 0;
    private String des;

    UserFlag(int flag,String des){
        this.flag=flag;
        this.des=des;
    }

    public static UserFlag fromFlag(int value){
        UserFlag[] flags = UserFlag.values();
        for(UserFlag flag:flags){
            if(flag.getFlag()==value){
                return flag;
            }
        }
        return UserFlag.ALLOW;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
