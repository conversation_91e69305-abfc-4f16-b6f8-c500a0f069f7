package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 */
public enum UserType {
    CUSTOMER(0,"客户"),MANAGER(1,"管理员");

    private int type = 0;
    private String des;

    UserType(int type, String des) {
        this.type = type;
        this.des = des;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public static UserType get(int type){
        for(UserType t:UserType.values()){
            if(t.type==type){
                return t;
            }
        }
        return UserType.CUSTOMER;
    }
}
