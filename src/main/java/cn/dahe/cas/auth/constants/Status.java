package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/15
 * @createTime 16:03
 * @info 通用的状态枚举
 */
public enum  Status {

    ALLOW(0,"启用"),DENY(1,"禁用");

    private int status;
    private String des;

    Status(int status,String des){
        this.status = status;
        this.des=des;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public static Status get(int status){
        for(Status s:Status.values()){
            if(s.status==status){
                return s;
            }
        }
        return null;
    }
}
