package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 * createDate 2017/02/11
 */
public enum SiteDelete {
    ALLOW(0,"可删除"),DENY(1,"不可删");
    private int status;
    private String des;

    SiteDelete(int status, String des){
        this.status = status;
        this.des=des;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public static SiteDelete get(int status){
        for(SiteDelete s: SiteDelete.values()){
            if(s.status==status){
                return s;
            }
        }
        return null;
    }
}
