package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 * on 2020/05/06
 */
public enum LogOperation implements EnumInterface{
    USER_ADD(0,"用户添加"),
    USER_UPDATE(10,"用户修改"),
    USER_PHONE(20,"修改手机号"),
    USER_ICON(30,"修改头像"),
    USER_STATUS(40,"修改状态"),
    USER_ROLE(50,"用户修改权限"),
    USER_SITE(100,"用户修改系统"),
    USER_DELETE(120,"用户删除"),
    USER_BIND(130,"供应商账号绑定")
    ;


    private int code;
    private String description;

    LogOperation(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
