package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2017/12/19
 * @createTime 17:43
 * @info
 */
public enum  SiteStatus {
    ALLOW(0,"启用"),DENY(1,"禁用");
    private int status;
    private String des;

    SiteStatus(int status,String des){
        this.status = status;
        this.des=des;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public static SiteStatus get(int status){
        for(SiteStatus s:SiteStatus.values()){
            if(s.status==status){
                return s;
            }
        }
        return null;
    }
}
