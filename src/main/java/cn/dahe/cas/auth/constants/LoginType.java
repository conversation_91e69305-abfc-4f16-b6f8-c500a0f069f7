package cn.dahe.cas.auth.constants;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/4 14:18
 * @Description:
 */
public enum LoginType implements EnumInterface {

    PHONE(1, "手机号登录", "phone", false),

    WX(2, "微信扫码", "wxOpenId", true),


    ADMIN(3, "超管登录", "uid", true);

    private int type;
    private String name;
    private String field;
    private boolean show;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    @Override
    public int getCode() {
        return type;
    }

    @Override
    public String getDescription() {
        return name;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    LoginType(int type, String name, String field, boolean show) {
        this.type = type;
        this.name = name;
        this.field = field;
        this.show = show;
    }

    public static LoginType getByType(int type) {
        LoginType[] types = LoginType.values();
        for (LoginType loginType : types) {
            if (loginType.getType() == type) {
                return loginType;
            }
        }
        return null;
    }
}
