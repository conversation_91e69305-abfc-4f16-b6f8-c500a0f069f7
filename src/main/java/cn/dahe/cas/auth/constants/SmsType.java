package cn.dahe.cas.auth.constants;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/10 10:30
 * @Description:
 */
public enum  SmsType {

    LOGIN("3","登录"),
    UPDATE("4","更换手机号"),
    WX_BIND("5","绑定微信")
    ;

    private String type;
    private String description;

    SmsType(String type, String description) {
        this.type = type;
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static SmsType getType(String type){
        SmsType[] types = SmsType.values();
        for(SmsType smsType:types){
            if(smsType.getType().equals(type)){
                return smsType;
            }
        }
        return null;
    }
}
