package cn.dahe.cas.auth.constants;

/**
 * @Author: 杨振雨
 * @Date: 2020/5/7 18:29
 * @Description:
 */
public enum UserChangeType implements EnumInterface {

    BASE_INFORMATION(1, "基础信息"),

    ROLE(2, "角色变更");

    private int type;
    private String description;

    UserChangeType(int type, String description) {
        this.type = type;
        this.description = description;
    }


    @Override
    public int getCode() {
        return type;
    }

    @Override
    public String getDescription() {
        return description;
    }


}
