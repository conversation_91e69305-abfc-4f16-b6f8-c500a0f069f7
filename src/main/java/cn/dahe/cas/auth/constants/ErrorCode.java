package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2017/12/21
 * @createTime 10:55
 * @info
 * 需要兼容老系统接口
 * 新系统全部rest接口的code统一到该enum中
 */
public enum ErrorCode {
    NORMAL(0,"失败"),PARAM_LOST(2,"参数不全"),CODE_EXPIRATION(3,"验证码不正确或失效"),
    CODE_ERROR(4,"验证码不正确或失效"),USER_EXIST(5,"用户名已存在"),PHONE_REGISTERED(6,"手机号已注册"),
    LOGIN_ERROR(7,"密码或用户名错误"),USER_NOT_EXIST(8,"用户不存在"),PASSWORD_SAME(9,"新老密码相同"),
    DOMAIN_ERROR(10,"域名错误"),CODE_SENT(11,"验证码已发送，15分钟内有效！"),ACCESS_OFTEN(12,"请求过于频繁"),
    USERNAME_PASSWORD_NOT_MATCH(13,"用户名或密码错误"),ACCOUNT_LOCK(14,"此账户请求过于频繁，账号已锁定，请等1小时候在登陆"),
    DOMAIN_DENY(15,"来路不明"), SENSITIVE_WORD(16,"用户名包含敏感词"),PHONE_EXISTS(17,"手机号已存在"),
    PHONE_NOT_EXIST(18,"手机号不存在"),PHONE_FORMAT_ERRO(19,"手机号格式不正确"),
    USER_PHONE_NOT_MATCH(20,"手机号用户名不匹配"),IP_LOGIN_DENY(21,"ip禁止登陆"),PHONE_DENY(22,"手机号被禁用"),SMS_SEND_FAIL(23,"短信验证码发送失败");
    private int code;
    private String codeMsg;

    ErrorCode(int code,String codeMsg){
        this.code = code;
        this.codeMsg = codeMsg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getCodeMsg() {
        return codeMsg;
    }

    public void setCodeMsg(String codeMsg) {
        this.codeMsg = codeMsg;
    }
}
