package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/17
 * @createTime 16:24
 * @info
 */
public enum  Sex{
    MAN(0,"男"),WOMEN(1,"女");

    private int sex;
    private String des;

    Sex(int sex,String des){
        this.sex = sex;
        this.des=des;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public static Sex get(int sex){
        for(Sex s:Sex.values()){
            if(s.sex==sex){
                return s;
            }
        }
        return Sex.MAN;
    }
}
