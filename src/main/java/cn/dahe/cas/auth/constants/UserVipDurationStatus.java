package cn.dahe.cas.auth.constants;

public enum UserVipDurationStatus {

    NORMAL(0,"正常"),EXPIRED(1,"已过期"),STOPPED(2,"暂停");

    private int status;
    private String des;

    UserVipDurationStatus(int status, String des) {
        this.status = status;
        this.des = des;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public static UserVipDurationStatus get(int status){
        for(UserVipDurationStatus s:UserVipDurationStatus.values()){
            if(s.status==status){
                return s;
            }
        }
        return UserVipDurationStatus.STOPPED;
    }

}
