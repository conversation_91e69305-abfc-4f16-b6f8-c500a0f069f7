package cn.dahe.cas.auth.constants;

import cn.dahe.cas.auth.dto.SelectType;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/22
 * @createTime 16:27
 * @info
 */
public enum  LogType{

    ERROR("错误"), NORMAL("正常"), READ("读"), WRITE("写"), LOGIN("登录");

    LogType(String type){
        this.type = type;
    }

    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public static SelectType[] toSelectType(){
        SelectType[] types = new SelectType[LogType.values().length];
        for(LogType logType:LogType.values()){
            types[logType.ordinal()] = new SelectType(logType.ordinal(),logType.getType());
        }
        return types;
    }
}
