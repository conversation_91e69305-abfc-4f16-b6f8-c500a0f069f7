package cn.dahe.cas.auth.constants;

/**
 * <AUTHOR>
 */
public enum EntityOperation {

    ADD(1,"添加"),DELETE(2,"删除"),EDIT(3,"编辑"),QUERY(4,"查询"),FORBID(5,"禁用");

    private int id;
    private String description;

    EntityOperation(int id,String description){
        this.id=id;
        this.description = description;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
