package cn.dahe.cas.auth.perform;

import cn.dahe.cas.auth.util.OkHttpUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: LiYunLong
 * Date: 2022/3/3
 * Time: 13:55
 * Description: 履职平台同步数据
 */
@Slf4j
public class SynchronizeData {

    private static final String CLIENT_ID = "442477363830194176";

    private static final String CLIENT_SECRET = "XEyiPAq9XELFmPX0FY2zdjuubftN6fBWiDMZ0M3fsu4=";
    private static final String HOST = "http://************:22325/lzt";
    //将用户中心的用户信息同步到业务系统。建议在每天凌晨调用。
    private static final String PULL_ALL_USER = "/pull/allUser";

    //接口功能：获取用户中心的单个用户信息。
    private static final String PULL_SINGLE_USER = "/pull/singleUser";

    //接口功能：将用户中心的机构信息同步到业务系统。
    private static final String PULL_OFFICES_USER = "/pull/offices";
    //接口功能：将用户中心的机构信息同步到业务系统。
    private static final String POST = "POST";


    private static final String ERRCODE = "errcode";

    public static void main(String[] args) {
        pullAllUser();
    }


    private static void generateToken(Map<String, String> params, Map<String, String> headMap, String pullUrl) {

        TokenUtils.VerifyParam terifyParam = TokenUtils.createVerifyParam(
                new HashMap<>(),
                CLIENT_SECRET, POST,
                pullUrl);
        params.put("timestamp", String.valueOf(terifyParam.getTimestamp()));
        params.put("signature", terifyParam.getSignature());
        //作为第三方调用的客户标
        params.put("clientId", CLIENT_ID);
        //查询区域ID
        headMap.put("u-login-areaId", "410000");
    }


    //同步用户
    public static void pullAllUser() {
        Map<String, String> params = new HashMap<>();
        Map<String, String> headMap = new HashMap<>();
        generateToken(params, headMap, PULL_ALL_USER);
//      params.put("userType", "4");
        params.put("pageNo", "1");
        params.put("pageSize", "5");
        Response response = OkHttpUtils.newInstance().doPostAddHeader(HOST + PULL_ALL_USER, params, headMap);
        try {
            if (response == null || response.body() == null) {
                log.info("response:null");
            } else {
                String resultStr = response.body().string();
                JSONObject parseObject = JSON.parseObject(resultStr);
                String errcode = parseObject.getString(ERRCODE);
                if (errcode.equalsIgnoreCase("200")) {
                    JSONArray data = parseObject.getJSONArray("data");
                    List<WorkUser> workUsers = JSONArray.parseArray(data.toJSONString(), WorkUser.class);
                    for (WorkUser workUser : workUsers) {
                        log.info(workUser.getUserName());
                        pullSingleUser(workUser.getId());
                    }
                    log.info(resultStr);
                } else {
                    log.info(parseObject.getString("errmsg"));
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    //同步用户
    public static WorkUser pullSingleUser(String userId) {
        Map<String, String> params = new HashMap<>();
        Map<String, String> headMap = new HashMap<>();
        generateToken(params, headMap, PULL_SINGLE_USER);
        Response response = OkHttpUtils.newInstance().doPostAddHeader(HOST + PULL_SINGLE_USER + "?id=" + userId, params, headMap);
        try {
            if (response == null || response.body() == null) {
                log.info("response:null");
            } else {
                String resultStr = response.body().string();
                log.error(resultStr);
                JSONObject parseObject = JSONObject.parseObject(resultStr);
                String errcode = parseObject.getString(ERRCODE);
                if (errcode.equalsIgnoreCase("200")) {
                    WorkUser workUser = parseObject.getObject("data", WorkUser.class);
                    String mobile = workUser.getMobile();
                    //解密手机号
                    String aesMobile = DrinEncryptUtilsDemo.aesDecrypt(mobile);
                    workUser.setMobile(aesMobile);
                    log.info("pullSingleUser :同步后用户名：{}手机号：{},", aesMobile, workUser.getUserName());
                    return workUser;
                } else {
                    log.info(parseObject.getString("errmsg"));
                    return null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    //同步用户
    public static void pullOffices() {
        Map<String, String> params = new HashMap<>();
        Map<String, String> headMap = new HashMap<>();
        generateToken(params, headMap, PULL_OFFICES_USER);
        Response response = OkHttpUtils.newInstance().doPostAddHeader(HOST + PULL_OFFICES_USER, params, headMap);
        try {
            if (response == null || response.body() == null) {
                log.info("response:null");
            } else {
                String resultStr = response.body().string();
                log.error(resultStr);
                JSONObject parseObject = JSON.parseObject(resultStr);
                String errcode = parseObject.getString(ERRCODE);
                if (errcode.equalsIgnoreCase("200")) {
                    JSONArray data = parseObject.getJSONArray("data");
                    List<Offices> officesList = JSONArray.parseArray(data.toJSONString(), Offices.class);
                    for (Offices offices : officesList) {
                        log.info(offices.getName());
                    }
                } else {
                    log.info(parseObject.getString("errmsg"));
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
