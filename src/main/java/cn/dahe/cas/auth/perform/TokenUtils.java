package cn.dahe.cas.auth.perform;

import cn.dahe.cas.auth.util.OkHttpUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.Response;

import java.io.IOException;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;

public class TokenUtils {

    public static void main(String[] args) {
        // 调用示例
        // http://test.dc.cszysoft.com:19418/lzt/meetMeeting/listForData?
        // timestamp=1586239864
        // &signature=ecc387c51e8969bfd463945a5512b4afae24fcfd0fbc7c151500220e3df4d58c
        // &clientId=8880f4560ded4fdfad2bd72e02f327de
        // &param1=paramValue1
        // &param3=3
        Map<String, String> params = new HashMap<>();
        params.put("userType", "4");
        params.put("pageNo", "1");
        params.put("pageSize", "5");
//		params.put("id", "430273063922171904");
        VerifyParam terifyParam = createVerifyParam(new HashMap<>(), "7ndp3EmPwsx+Yq3W4NpoJ+WqkvlUJ5fm/eJvi6xaNbc=", "POST",
                "/pull/allUser");
        params.put("timestamp", String.valueOf(terifyParam.getTimestamp()));
        params.put("signature", terifyParam.getSignature());
        params.put("clientId", "441806861939769344");
        Map<String, String> headMap = new HashMap<>();
        headMap.put("u-login-areaId", "410100");
        // TODO 调用接口
        String host = "http://**************:21123/lzt";
        Response response = OkHttpUtils.newInstance().doPostAddHeader(host + "/pull/allUser", params, headMap);
        try {
            String resultStr = response.body().string();
            JSONObject parseObject = JSONObject.parseObject(resultStr);
            System.out.println(parseObject);
            JSONArray jsonArray = parseObject.getJSONArray("data");
            System.out.println(terifyParam.getTimestamp());
            System.out.println(terifyParam.getSignature());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static VerifyParam createVerifyParam(Map<String, Object> params, String clientSecret, String requestMethod,
                                                String apiName) {
        SignParam signParam = new SignParam(requestMethod, apiName, clientSecret, params);
        return new VerifyParam(signParam.getTimestamp(), createToken(signParam));
    }

    public static String createVerifyParam(String clientSecret, HttpServletRequest request) {
        return createToken(new SignParam(clientSecret, request));
    }

    private static String createToken(SignParam signParam) {
        StringBuffer sb = new StringBuffer();
        sb.append(signParam.getTimestamp());
        sb.append("-");
        sb.append(signParam.getRequestMethod());
        sb.append("-");
        sb.append(signParam.getApiName());
        sb.append("-");
        sb.append(signParam.getParamInfo());
        return sha256_HMAC(sb.toString(), signParam.getClientSecret());
    }

    private static String sha256_HMAC(String message, String secret) {
        String hash = "";
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            byte[] bytes = sha256_HMAC.doFinal(message.getBytes());
            hash = byteArrayToHexString(bytes);
        } catch (Exception e) {
            System.out.println("Error HmacSHA256 ===========" + e.getMessage());
        }
        return hash;
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String sTmp;
        for (int n = 0; b != null && n < b.length; n++) {
            sTmp = Integer.toHexString(b[n] & 0XFF);
            if (sTmp.length() == 1)
                hs.append('0');
            hs.append(sTmp);
        }
        return hs.toString().toLowerCase();
    }

    public static class SignParam {
        long timestamp;
        String clientSecret;
        String requestMethod;
        String apiName;
        Map<String, Object> params;
        int paramInfo;

        public SignParam(String requestMethod, String apiName, String clientSecret, Map<String, Object> params) {
            this.timestamp = System.currentTimeMillis() / 1000;
            this.clientSecret = clientSecret;
            this.requestMethod = requestMethod.toUpperCase();
            this.apiName = apiName;
            this.params = null != params ? params : new HashMap<>();
            this.paramInfo = requestMethod.equals("POST") ? 0 : getParamsInfo(this.params);
        }

        public SignParam(String clientSecret, HttpServletRequest request) {
            this.timestamp = Long.parseLong(request.getHeader("timestamp"));
            this.clientSecret = clientSecret;
            this.requestMethod = request.getMethod().toUpperCase();
            this.apiName = request.getRequestURI().replaceAll(request.getContextPath(), "");
            this.params = findParamsFormRequest(request);
            this.paramInfo = requestMethod.equals("POST") ? 0 : getParamsInfo(this.params);
        }

        public Map<String, Object> getParams() {
            return params;
        }

        public void setParams(Map<String, Object> params) {
            this.params = params;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public String getClientSecret() {
            return clientSecret;
        }

        public void setClientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
        }

        public String getRequestMethod() {
            return requestMethod;
        }

        public void setRequestMethod(String requestMethod) {
            this.requestMethod = requestMethod;
        }

        public String getApiName() {
            return apiName;
        }

        public void setApiName(String apiName) {
            this.apiName = apiName;
        }

        public int getParamInfo() {
            return paramInfo;
        }

        public void setParamInfo(int paramInfo) {
            this.paramInfo = paramInfo;
        }

        private static int getParamsInfo(Map<String, Object> params) {// k v timestamp=342432342
            String paramStr = "";
            Set<Entry<String, Object>> entrySet = params.entrySet();
            for (Entry<String, Object> entry : entrySet) {
                String name = entry.getKey();
                Object val = entry.getValue();
                if (null == val) {
                    continue;
                }
                List<String> asList = Arrays.asList("timestamp", "clientId", "signature");
                if (asList.stream().anyMatch(s -> name.equals(s))) {
                    continue;
                }
                paramStr += name;
            }
            if (null == paramStr || paramStr == "") {
                return 0;
            }
            char[] charArray = paramStr.toCharArray();
            int i = 0;
            for (char c : charArray) {
                i += c;
            }
            return i;
        }

        private Map<String, Object> findParamsFormRequest(HttpServletRequest request) {
            Map<String, Object> map = new HashMap<>();
            Enumeration<String> parameterNames = request.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String name = parameterNames.nextElement();
                String parameterValues = request.getParameter(name);
                if (null != parameterValues && parameterValues != "") {
                    map.put(name, parameterValues);
                }
            }
            return map;
        }

    }

    public static class VerifyParam {

        private long timestamp;

        private String signature;

        public VerifyParam(long timestamp, String signature) {
            this.timestamp = timestamp;
            this.signature = signature;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public String getSignature() {
            return signature;
        }

        public void setSignature(String signature) {
            this.signature = signature;
        }

        /**
         * @description 【】
         * <AUTHOR>
         * @DATE 2020年4月1日下午6:11:18
         */
        @Override
        public String toString() {
            return "VerifyParam [timestamp=" + timestamp + ", signature=" + signature + "]";
        }

    }
}
