package cn.dahe.cas.auth.perform;


import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.crypto.SecretKey;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


@Slf4j
public class JJWT {
    public static String key = "jwtSsoKeyForSystem";

    public static String resoveUserCenterUserId(String header) {
        if (StringUtils.isBlank(header)) {
            throw new RuntimeException("缺失访问令牌");
        }
        String token = header.substring("bearer ".length());
        Claims body = null;
        try {
            body = Jwts.parser().setSigningKey("jwtSsoKeyForzysoft20201229System".getBytes(StandardCharsets.UTF_8))
                    .parseClaimsJws(token).getBody();
        } catch (ExpiredJwtException e) {
            throw new RuntimeException("token已过期");
        } catch (Exception e) {
            body = null;
        }

        return Optional.ofNullable(body).map(bd -> bd.get("loginUserId", String.class))
                .orElseThrow(() -> new RuntimeException("token解析失败"));
    }

    public static final Claims decodeJwt(String jwt) {
        return decodeJwt(jwt, key);
    }

    public static final Claims decodeJwt(String jwt, String secretkeyCode) {
        MessageDigest messageDigest;
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            SecretKey secretkey = Keys.hmacShaKeyFor(messageDigest.digest(secretkeyCode.getBytes(StandardCharsets.UTF_8)));
            Claims claims = Jwts.parser().setSigningKey(secretkey).parseClaimsJws(jwt).getBody();
            return claims;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        String sing = "eyJhbGciOiJIUzI1NiJ9.eyJzc29LZXkiOiIzMHNhdTMwYTF2d2loNHQ0bjB2NTU0MzB2NjE4azQiLCJsb2dpbkFjY0ZsYWciOiJhZG1pbiIsInJvbGVzIjpbInNlY3VyaXR5X3Nyb2xlX2FkbWluIl0sImxvZ2luRmxhZyI6ImFkbWluIiwidXNlckxvZ2luSW5mbyI6eyJkaXNhYmxlZERhdGVUaW1lIjoxNjE3OTMyODk0OTgzLCJjaGFuZ2VQd3MiOjEsInBhc3N3b3JkIjoiNTMwZWU3MmU5ZWQwZTgwMTI1ZjRhOWM2Y2UyZGIxMDYxNTAyYjljZTc2MGRhNTc4Yjc5NTY2ZDhhZTI4ODE2ZiIsImxvZ2luSWQiOiJhZG1pbiIsInN0YXR1c0lkIjoibm9ybWFsIiwiY3JlYXRlUGFzc3dvcmREYXRlIjowLCJtb2RpZnlQYXNzd29yZERhdGUiOjAsImxvZ2luVXNlcklkIjoiYWRtaW4iLCJwYXJ0eUlkIjoiYWRtaW4iLCJ1c2VyT3ZlclRpbWUiOjB9LCJodW1hbiI6eyJiaXJ0aGRheSI6MCwiZmlyc3RuYW1lIjoi566h55CG5ZGYIiwib3JkZXJubyI6MCwiYWRtaW5MZXZlbCI6bnVsbCwiZ2VuZGVyIjoiMCIsImNyZWF0ZWRhdGUiOjAsImZpeGVkcGhvbmUiOm51bGwsIm9mZmljZWFkZHJlc3NpZCI6bnVsbCwiaWRObyI6bnVsbCwib3ZlckxlYWRlcklEIjpudWxsLCJkaWN2ZXJzaW9uIjoxLCJzdGF0dXNpZCI6IjAiLCJtb2JpbGVwaG9uZSI6bnVsbCwibWFpbmVtYWlsIjpudWxsLCJzdGF0ZSI6bnVsbCwiZXh0YXR0cjQiOm51bGwsImV4dGF0dHI1IjpudWxsLCJleHRhdHRyMiI6bnVsbCwiZGF0YW9iamVjdGlkIjpudWxsLCJleHRhdHRyMyI6bnVsbCwiZXh0YXR0cjYiOm51bGwsImhvbWVhZGRyZXNzIjpudWxsLCJpbnN0YW50bWVzc2FnZSI6bnVsbCwiY2FsbG5hbWUiOm51bGwsImRlZ3JlZSI6bnVsbCwibGFzdG5hbWUiOiIiLCJ3b3Jrbm8iOiIyIiwib2xkb3JkZXJubyI6bnVsbCwib3JnYW5OYW1lIjpudWxsLCJwaW55aW4iOm51bGwsInNlY3VyaXR5bGV2ZWwiOjEsIm90aGVyZW1haWwiOm51bGwsIm5hbWUiOiLnrqHnkIblkZgiLCJkdXR5IjpudWxsLCJvcmdhbklkIjpudWxsLCJleHRhdHRyIjpudWxsLCJjcmVhdGV1c2VybG9naW5pZCI6bnVsbCwicGFydHlpZCI6ImFkbWluIiwicGFydHl0eXBlaWQiOjB9LCJleHAiOjE2MTg1NjkwMDJ9.z8Ed9vwWXVpPpLVG2ZemTZirNnVgzKtrCVVMLpOsfVA";
        Claims decodeJwt = decodeJwt(sing);
        if (decodeJwt!=null){
            Object object = decodeJwt.get("userLoginInfo");
            if (object instanceof Map) {
                Map<?, ?> paramMap = (Map<?, ?>) object;
                Object x = paramMap.get("loginUserId");
                log.error("{}",x);
            }
            log.error("{}",object);
        }else {
            log.error("decodeJwt:null");
        }

    }
}
