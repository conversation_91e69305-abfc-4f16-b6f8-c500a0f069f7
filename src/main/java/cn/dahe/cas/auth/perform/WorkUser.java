package cn.dahe.cas.auth.perform;

import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 * User: LiYunLong
 * Date: 2022/3/3
 * Time: 14:52
 * Description:
 */
@Data
public class WorkUser {

    /**
     * {
     * "id": "434565193083650048",   用户id
     * "userName": "刘郑州", 用户姓名
     * "sex": "",  性别
     * "account": "liuzhengzhou",
     * "nation": "", 帐号
     * "mobile": "15TQsTbMJ+qjAB8keL/2qg==", 手机(AES加密）
     * "birthday": 0, 出年日期(long)
     * "isUsing": "是",
     * "isReceiveMsg": "否",
     * "age": 0,
     * "headImg": "http://**************:21123/lzt/img/def_head_img.jpg", 用户头像
     * "officeId": ""  用户部门id
     * },
     */

    private String id;

    private String userName;

    private String sex;

    private String account;

    private String nation;

    private String mobile;

    private String birthday;

    private String isUsing;

    private String isReceiveMsg;

    private String age;

    private String headImg;

    private String officeId;

}
