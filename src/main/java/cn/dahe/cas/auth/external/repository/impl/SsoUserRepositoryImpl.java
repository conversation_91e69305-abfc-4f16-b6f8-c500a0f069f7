package cn.dahe.cas.auth.external.repository.impl;


import cn.dahe.cas.auth.external.entity.SsoUser;
import cn.dahe.cas.auth.external.repository.SsoUserRepository;
import cn.dahe.cas.auth.util.PageUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/25
 * @createTime 20:14
 * @info
 */
@Service
public class SsoUserRepositoryImpl implements SsoUserRepository {

    @Value("${sso.user.key?:sso:external:user}")
    private String key;

    @Resource(name = "externalRedisTemplate")
    private HashOperations<String,Integer,SsoUser> operations;

    @Override
    public SsoUser findOne(Integer id) {
        return operations.get(key,id);
    }

    @Override
    public Iterable<SsoUser> findAll() {
        return operations.values(key);
    }

    @Override
    public Iterable<SsoUser> findAll(Iterable<Integer> uids) {
        List<SsoUser> userList = new ArrayList<>();
        Iterator<Integer> interator = uids.iterator();
        while (interator.hasNext()){
            SsoUser ssoUser = findOne(interator.next());
            if(ssoUser!=null){
                userList.add(ssoUser);
            }
        }
        return userList;
    }

    @Override
    public long count() {
        return operations.size(key);
    }

    @Override
    public Page<SsoUser> find(Pageable pageable) {
        return PageUtil.get(pageable,operations, key);
    }

    @Override
    public SsoUser add(SsoUser ssoUser) {
        Long delete = operations.delete(key, ssoUser.getUid(), ssoUser);
        operations.put(key,ssoUser.getUid(),ssoUser);
        SsoUser ssoUser1 = operations.get(key, ssoUser.getUid());
        return ssoUser;
    }

    @Override
    public int add(Collection<SsoUser> ssoUsers) {
        if(ssoUsers==null){
            return 0;
        }
        for(SsoUser ssoUser:ssoUsers){
            operations.put(key,ssoUser.getUid(),ssoUser);
        }
        return ssoUsers.size();
    }

    @Override
    public void delete(int id) {
        operations.delete(key,id);
    }

    @Override
    public void clear() {
        operations.getOperations().delete(key);
    }
}
