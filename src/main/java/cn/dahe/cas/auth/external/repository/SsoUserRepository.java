package cn.dahe.cas.auth.external.repository;


import cn.dahe.cas.auth.external.entity.SsoUser;

import java.util.Collection;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/25
 * @createTime 10:20
 * @info
 */
public interface SsoUserRepository extends BaseRepository<SsoUser,Integer> {
    @Override
    SsoUser add(SsoUser ssoUser);

    int add(Collection<SsoUser> ssoUsers);

    void delete(int id);

    void clear();
}
