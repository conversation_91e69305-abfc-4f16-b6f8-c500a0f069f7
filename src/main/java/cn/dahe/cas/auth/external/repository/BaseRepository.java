package cn.dahe.cas.auth.external.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/25
 * @createTime 20:35
 * @info
 */
public interface BaseRepository<T, ID extends Serializable> {
    T findOne(ID var1);
    Iterable<T> findAll();

    Iterable<T> findAll(Iterable<ID> var1);

    long count();

    Page<T> find(Pageable pageable);

    T add(T t);
}
