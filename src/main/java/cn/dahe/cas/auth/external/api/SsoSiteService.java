package cn.dahe.cas.auth.external.api;

import cn.dahe.cas.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/25
 * @createTime 11:01
 * @info 对外提供站点接口
 */
public interface SsoSiteService {

    /**
     * 分页获取站点下所有用户
     * @param id
     * @return
     */
    Page<User> getSiteUser(Pageable pageable, String id);

    /**
     * 不分页直接获取所有站点用户
     * @param id
     * @return
     */
    List<User> getSiteUser(String id);
}
