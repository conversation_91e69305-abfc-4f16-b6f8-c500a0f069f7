package cn.dahe.cas.auth.external.impl;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.external.api.SsoSiteService;
import cn.dahe.cas.auth.repositories.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/25
 * @createTime 11:15
 * @info
 */
@Service
public class SsoSiteServiceImpl implements SsoSiteService {

    @Autowired
    private UserRepository userRepository;

    /**
     * 直接从从数据库中读取并缓存到redis里
     * 分页形式
     * @param sid
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheKey.SITE_USER_KEY,key = "#sid+'-'+#pageable.pageNumber+'-'+#pageable.pageSize")
    public Page<User> getSiteUser(Pageable pageable,String sid) {
        return userRepository.findUserBySid(pageable,sid);
    }

    /**
     * 获取站点下用户，不分页
     * @param sid
     * @return
     */
    @Override
    @Cacheable(cacheNames = CacheKey.SITE_USER_KEY,key = "#sid")
    public List<User> getSiteUser(String sid) {
        return userRepository.findUserBySid(sid);
    }
}
