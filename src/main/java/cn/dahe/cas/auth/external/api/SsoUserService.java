package cn.dahe.cas.auth.external.api;

import cn.dahe.cas.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/25
 * @createTime 9:45
 * @info 对外提供sso用户信息，包括用户信息查询，用户数量信息、用户列表以及用户分页等
 * 原设计是将对外用户信息独立提供，即以SsoUser作为实体，考虑直接返回User实体
 */
public interface SsoUserService {

    /**
     * 根据用户id获取sso用户信息
     * @param id
     * @return
     */
    User getUser(int id);

    /**
     * 分页获取用户
     * @param pageable
     * @return
     */
    Page<User> getUser(Pageable pageable);

    /**
     * 根据用户id列表获取用户
     * @param uids
     * @return
     */
    List<User> getUser(List<Integer> uids);

    /**
     * 查询用户，可传入站点进行更精确查询
     * @param pageable
     * @param identify
     * @param sid
     * @return
     */
    Page<User> findUser(Pageable pageable,String identify,String sid);


    /**
     * 分页获取组织机构下用户,需要加入缓存
     * @param pageable
     * @param id
     * @return
     */
    Page<User> getDepartmentUser(Pageable pageable,int id);

    /**
     * 不分页获取组织机构下用户，需要加入缓存机制
     * @param id
     * @param id
     * @return
     */
    List<User> getDepartmentUser(int id);

    /**
     * 获取所有用户id
     * @return
     */
    List<Integer> getAllUserIds();

    /**
     * 根据角色id查询所属用户
     * @param roleId 角色id
     * @param keyword 关键字（手机号/姓名）
     * @return
     */
    List<User> getByRoleId(int roleId, String keyword);
}
