package cn.dahe.cas.auth.external.impl;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.entity.QUser;
import cn.dahe.cas.auth.entity.QUserRole;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.external.api.SsoUserService;
import cn.dahe.cas.auth.repositories.UserRepository;
import cn.dahe.cas.auth.search.SearchUser;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.CommonUtil;
import cn.dahe.cas.auth.util.Sm4Util;
import cn.dahe.cas.auth.util.SsoStringUtil;
import com.google.common.collect.Lists;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/25
 * @createTime 9:47
 * @info SsoUserService具体实现
 */
@Service
public class SsoUserServiceImpl implements SsoUserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Value("${sm4.security.key}")
    private String sm4Key;

    /**
     * 用户搜索加入缓存
     * @param id
     * @return
     */
    @Cacheable(value = CacheKey.SINGLE_USER_INFO_KEY,key = "#id+''")
    @Override
    public User getUser(int id) {
        return userRepository.findOne(id);
    }

    @Cacheable(value = CacheKey.PAGE_USER_INFO_KEY,key = "#pageable.pageNumber+'_'+#pageable.pageSize")
    @Override
    public Page<User> getUser(Pageable pageable) {
        SearchUser searchUser = new SearchUser();
        searchUser.setFlag(UserFlag.ALLOW);
        return userService.searchUser(pageable,searchUser);
    }

    @Override
    public List<User> getUser(List<Integer> uids) {
        return userRepository.findAll(uids);
    }


    /**
     * 第三方用户检索加上真实姓名模糊匹配
     * @param pageable
     * @param identify
     * @param sid
     * @return
     */
    @Override
    public Page<User> findUser(Pageable pageable,String identify,String sid) {
        SearchUser searchUser = new SearchUser();
        searchUser.setFlag(UserFlag.ALLOW);
        searchUser.setTruename(identify);
        if(SsoStringUtil.isBlank(sid)){
            searchUser.setSid(sid);
        }
        return userService.searchUser(pageable,searchUser);
    }

    /**
     * 缓存key以组织机构id和分页信息组成
     * @param pageable
     * @param id
     * @return
     */
    @Cacheable(cacheNames = CacheKey.DEPARTMENT_USER_KEY,key = "#id+'-'+#pageable.pageNumber+'-'+#pageable.pageSize")
    @Override
    public Page<User> getDepartmentUser(Pageable pageable,int id) {
        SearchUser searchUser = new SearchUser();
        searchUser.setFlag(UserFlag.ALLOW);
        searchUser.setOrganization(id);
        return userService.searchUser(pageable,searchUser);
    }

    /**
     * 缓存直接以组织机构id作为key
     * @param id
     * @return
     */
    @Cacheable(cacheNames = CacheKey.DEPARTMENT_USER_KEY,key = "#id+''")
    @Override
    public List<User> getDepartmentUser(int id) {
        SearchUser searchUser = new SearchUser();
        searchUser.setFlag(UserFlag.ALLOW);
        searchUser.setOrganization(id);
        return userService.searchUser(searchUser);
    }

    /**
     * 该接口必须做好缓存
     * @return
     */
    @Cacheable(cacheNames = CacheKey.ALL_USER_ID_KEY,key = "#root.methodName")
    @Override
    public List<Integer> getAllUserIds() {
        return userRepository.findAllId();
    }

    @Override
    public List<User> getByRoleId(int roleId, String keyword){
        QUser user = QUser.user;
        QUserRole userRole = QUserRole.userRole;
        List<Predicate> predicates = Lists.newArrayList();
        JPAQuery query = jpaQueryFactory.from(user)
                .from(userRole).where(user.uid.eq(userRole.uid));
        if (roleId > 0) {
            predicates.add(userRole.roleId.eq(roleId));
        }
        if(SsoStringUtil.isNotBlank(keyword)){
            if(CommonUtil.isPhoneNumber(keyword)){
                try {
                    predicates.add(user.phone.eq(Sm4Util.encryptEcb(sm4Key, keyword)));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }else {
                predicates.add(user.username.like("%" + keyword + "%"));
            }
        }
        query.where(predicates.toArray(new Predicate[predicates.size()])).orderBy(user.uid.desc());
        return query.fetch();
    }

}
