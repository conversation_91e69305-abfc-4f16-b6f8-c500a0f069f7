package cn.dahe.cas.auth.external.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/25
 * @createTime 11:03
 * @info
 */
public class SsoSite implements Serializable{

    private static final long serialVersionUID = 3177856901085765028L;

    @JsonProperty("siteId")
    private String sid;
    private String siteName;
    private Set<Integer> userIds;

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public Set<Integer> getUserIds() {
        return userIds;
    }

    public void setUserIds(Set<Integer> userIds) {
        this.userIds = userIds;
    }
}
