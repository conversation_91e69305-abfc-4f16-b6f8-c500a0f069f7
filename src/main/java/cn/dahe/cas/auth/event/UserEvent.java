package cn.dahe.cas.auth.event;

import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.entity.User;

/**
 * <AUTHOR>
 * 记录用户信息的变化事件
 */
public class UserEvent{

    private User user;

    private EntityOperation operation;

    public UserEvent(User user, EntityOperation operation) {
        this.user=user;
        this.operation = operation;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public EntityOperation getOperation() {
        return operation;
    }

    public void setOperation(EntityOperation operation) {
        this.operation = operation;
    }
}
