package cn.dahe.cas.auth.event;

import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.entity.Site;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * 单点登录站点相关事件，包括站点的增加、删除和状态改变
 */
public class SiteEvent extends ApplicationEvent{

    private Site site;

    private EntityOperation operation;

    public SiteEvent(Object source, Site site) {
        super(source);
        this.site = site;
        this.operation = EntityOperation.ADD;
    }

    public SiteEvent(Object source, Site site, EntityOperation operation) {
        super(source);
        this.site = site;
        this.operation = operation;
    }

    public Site getSite() {
        return site;
    }

    public void setSite(Site site) {
        this.site = site;
    }

    public EntityOperation getOperation() {
        return operation;
    }

    public void setOperation(EntityOperation operation) {
        this.operation = operation;
    }
}
