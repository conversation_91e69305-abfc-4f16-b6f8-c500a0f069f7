package cn.dahe.cas.auth.event;

import cn.dahe.cas.auth.constants.LoginType;
import org.jasig.cas.authentication.Credential;
import org.springframework.context.ApplicationEvent;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/7 14:48
 * @Description:
 */
public class AuthenticationEvent extends ApplicationEvent{

    private Credential credential;
    private LoginType loginType;
    private HttpServletRequest request;
    private Integer uid;
    private String siteUrl;
    private boolean success;

    public AuthenticationEvent(Credential credential, LoginType loginType, HttpServletRequest request, Integer uid, String siteUrl,boolean success) {
        super(credential);
        this.credential = credential;
        this.loginType = loginType;
        this.request = request;
        this.uid = uid;
        this.siteUrl = siteUrl;
        this.success = success;
    }

    public Credential getCredential() {
        return credential;
    }

    public void setCredential(Credential credential) {
        this.credential = credential;
    }

    public LoginType getLoginType() {
        return loginType;
    }

    public void setLoginType(LoginType loginType) {
        this.loginType = loginType;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public String getSiteUrl() {
        return siteUrl;
    }

    public void setSiteUrl(String siteUrl) {
        this.siteUrl = siteUrl;
    }

    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
