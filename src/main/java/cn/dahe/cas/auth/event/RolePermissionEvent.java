package cn.dahe.cas.auth.event;


import cn.dahe.cas.auth.constants.RolePermissionType;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date Created at 2018/6/21 10:50
 * @description:
 **/
@Data
public class RolePermissionEvent extends ApplicationEvent{

    private String userId;

    private int siteId;

    //默认为修改单个用户角色权限
    private RolePermissionType type = RolePermissionType.USER;

    public RolePermissionEvent(String userId) {
        super(userId);
        this.userId = userId;
    }

    public RolePermissionEvent(int siteId, RolePermissionType type) {
        super(type);
        this.siteId = siteId;
        this.type = type;
    }
}
