package cn.dahe.cas.auth.event;

import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2017/12/22
 * @createTime 18:25
 * @info 短信发送时间，主要用于记录短信发送情况
 */
public class SmsEvent extends ApplicationEvent{

    private String ua;
    private String ip;
    private String phone;
    private int type;

    public SmsEvent(Object source,String ua,String ip,String phone,int type){
        super(source);
        this.ua=ua;
        this.ip=ip;
        this.phone=phone;
        this.type=type;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
