package cn.dahe.cas.auth.event;

import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.entity.LogSource;

/**
 * <AUTHOR>
 * @date Created at 2018/6/20 18:56
 * @description: 日志源事件
 **/
public class LogSourceEvent {

    private LogSource logSource;
    private EntityOperation operation;

    public LogSourceEvent(LogSource logSource, EntityOperation operation) {
        this.logSource = logSource;
        this.operation = operation;
    }

    public LogSource getLogSource() {
        return logSource;
    }

    public void setLogSource(LogSource logSource) {
        this.logSource = logSource;
    }

    public EntityOperation getOperation() {
        return operation;
    }

    public void setOperation(EntityOperation operation) {
        this.operation = operation;
    }
}
