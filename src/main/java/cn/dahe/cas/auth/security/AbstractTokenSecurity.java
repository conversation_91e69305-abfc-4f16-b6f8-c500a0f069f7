package cn.dahe.cas.auth.security;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/27
 * @createTime 14:40
 * @info 维护全系统接口所使用的token
 *
 */
public abstract class AbstractTokenSecurity implements TokenSecurity {

    /**
     * token过期时间,单位毫秒
     */
    private long expired;

    private static final long DEFAULT_EXPIRED = 2*60*60*1000;

    public AbstractTokenSecurity(){
        this.expired = DEFAULT_EXPIRED;
    }

    public AbstractTokenSecurity(long expired){
        this.expired = expired;
    }

    /**
     * 负责生成token
     * @return
     */
    protected abstract String createToken();

    public long getExpired() {
        return expired;
    }

    public void setExpired(long expired) {
        this.expired = expired;
    }
}
