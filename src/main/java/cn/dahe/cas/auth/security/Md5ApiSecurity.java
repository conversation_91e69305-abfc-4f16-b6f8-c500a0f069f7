package cn.dahe.cas.auth.security;

import org.apache.shiro.crypto.hash.Md5Hash;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class Md5ApiSecurity implements ApiSecurity{

    public static final String SALT = "dahe:sso:salt";

    public static final long DEFAULT_EXPIRSE = 1000*60*10;

    private long expire = DEFAULT_EXPIRSE;

    public long getExpire() {
        return expire;
    }

    public void setExpire(long expire) {
        this.expire = expire;
    }

    /**
     * 签名算法
     * 1、将参数名以及参数值进行排序
     * 2、拼接排序后的参数名以及参数值，并将salt拼接在字符串末尾
     * 3、使用salt对拼接后的字符串进行md5
     * @param params
     * @return
     */
    @Override
    public String sign(Map<String, String> params) {
        List<String> atts = new ArrayList<>();
        for (String m:params.keySet()){
            atts.add(m);
            atts.add(params.get(m));
        }
        String[] strs = atts.toArray(new String[atts.size()]);
        Arrays.sort(strs);
        StringBuilder br = new StringBuilder();
        br.append(SALT);
        for (String s:strs){
            br.append(s);
        }
        Md5Hash hash = new Md5Hash(br.toString(),SALT);
        return hash.toBase64();
    }

    @Override
    public boolean isExpirse(long timestap) {
        long current = System.currentTimeMillis();
        //防止传入非法的时间戳
        if(current<=timestap){
            return true;
        }
        if(current-timestap>expire){
            return true;
        }
        return false;
    }
}
