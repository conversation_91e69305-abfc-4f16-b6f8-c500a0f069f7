package cn.dahe.cas.auth.security;

import cn.dahe.cas.auth.util.Md532;
import org.apache.shiro.authc.credential.CredentialsMatcher;
import org.apache.shiro.crypto.hash.Hash;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 */
@Service
public class Md5Encry implements PasswordEncry{

    private static final String SMS_CODE_SOURCE = "1234567890";

    @Value("${sms.code.length}")
    private int smsCodeLength;

    @Value("${code.char}")
    private String code;

    @Value("${password.md5.hashIterations}")
    private int hashIterations;

    private String smsCode = SMS_CODE_SOURCE;

    private static final String SALT = "salt";

    @Override
    public String encry(String origin, String salt) {
        Hash hash = new Md5Hash(origin,salt,hashIterations);
        String hashedPasswordBase64 = hash.toBase64();
        return hashedPasswordBase64;
    }



    @Override
    public String generatePassword(int length) {
        return random(length,code);
    }

    @Override
    public String generateCode() {
        return random(smsCodeLength,smsCode);
    }

    @Override
    public String generateUsername(int lenght) {
        return random(lenght,code);
    }

    private String random(int length,String source){
        StringBuilder result = new StringBuilder();
        Random random = new Random();
        for(int i=0;i<length;i++){
            int pos = random.nextInt(source.length());
            char point = source.charAt(pos);
            result.append(point);
        }
        return result.toString();
    }
}
