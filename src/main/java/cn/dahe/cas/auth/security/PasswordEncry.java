package cn.dahe.cas.auth.security;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PasswordEncry {

    /**
     * 密码加密的简化接口，只使用salt
     * @param origin
     * @param salt
     * @return
     */
    String encry(String origin,String salt);

    /**
     * 生成指定长度的密码
     * @param length
     * @return
     */
    String generatePassword(int length);

    /**
     * 生成验证码
     * @return
     */
    String generateCode();

    /**
     * 生成随机字符串
     */
    String generateUsername(int lenght);
}
