package cn.dahe.cas.auth.security;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/27
 * @createTime 14:55
 * @info
 */
public interface TokenSecurity {
    /**
     * 刷新token
     */
    void refreshToken();

    /**
     * 获取当前token
     */
    String getToken();

    /**
     * 验证token
     * @param token
     * @return
     */
    boolean validateToken(String token);

    /**
     * 根据参数生成token
     * @param params
     * @return
     */
    String tokenParameters(String ...params);
}
