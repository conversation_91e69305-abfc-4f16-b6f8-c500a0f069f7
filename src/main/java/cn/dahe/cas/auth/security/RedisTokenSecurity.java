package cn.dahe.cas.auth.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.TaskScheduler;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/27
 * @createTime 14:50
 * @info
 */
public class RedisTokenSecurity extends AbstractTokenSecurity{

    @Resource(name = "tokenRedisTemplate")
    private ValueOperations<String,String> operations;

    @Value("${sso.token.key}")
    private String tokenKey;

    @Autowired
    private TaskScheduler taskScheduler;

    @Value("${sso.token.prefix:sso}")
    private String tokenPrefix;

    @PostConstruct
    public void scheduleTokenRefresh(){
        taskScheduler.scheduleAtFixedRate(() -> refreshToken(),getExpired());
    }

    @Override
    public void refreshToken(){
        String token = createToken();
        operations.set(tokenKey,token,getExpired(), TimeUnit.MILLISECONDS);
    }

    @Override
    public String getToken() {
        return operations.get(tokenKey);
    }

    @Override
    public boolean validateToken(String token) {
        String result = tokenParameters();
        return token.equals(result);
    }

    @Override
    public String tokenParameters(String... params) {
        String redisToken = getToken();
        DateFormat format = new SimpleDateFormat("yyyyMMdd");
        String date = format.format(new Date());
        String toMd5 = redisToken+date;
        Md5Hash md5Hash = new Md5Hash(toMd5);
        return md5Hash.toHex().toUpperCase();
    }


    /**
     * token生成，生成失败时直接使用uuid代替
     * @return
     */
    @Override
    protected String createToken() {
        try {
            Algorithm algorithm = Algorithm.HMAC256("secret");
            String token = JWT.create()
                    .withIssuer(UUID.randomUUID().toString())
                    .sign(algorithm);
            return token;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return UUID.randomUUID().toString();
    }

    @PreDestroy
    public void closeTokenRefresh(){

    }
}
