package cn.dahe.cas.auth.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * 日趋工具类
 *
 * <AUTHOR>
 */
public class DateUtil {

    private static Logger logger = LoggerFactory.getLogger(DateUtil.class);
    /**
     * 日期转为字符串
     *
     * @param date    日期
     * @param pattern 规则
     * @return
     */
    public static String format(Date date, String pattern) {
        if (date == null) {
            return "null";
        }
        if (pattern == null || pattern.equals("") || pattern.equals("null")) {
            pattern = "yyyy-MM-dd  HH:mm:ss";
        }
        return new SimpleDateFormat(pattern).format(date);
    }
    /**
     * 字符串转为日期
     *
     * @param date    字符串
     * @param pattern 规则
     * @return
     */
    public static Date format(String date, String pattern) {
        if (date == null || date.equals("") || date.equals("null")) {
            return new Date();
        }
        if (pattern == null || pattern.equals("") || pattern.equals("null")) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        Date d = null;
        try {
            d = new SimpleDateFormat(pattern).parse(date);
        } catch (ParseException e) {
            logger.info("string to date is error!!!");
        }
        return d;
    }
    public static Date format(String date) {
        return format(date, null);
    }

    private static Date subDay(Date endTime, int n) {
        if (n <= 0) {
            return endTime;
        }
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(endTime);
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - n );
        return calendar.getTime();
    }

    public static String getDate(long time) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(time);
        return format.format(date);
    }

    /**
     * 得到要求的开始时间
     *
     * @param days 从今天开始向前days天
     */
    public static Date getBeginTime(int days) {
        if (days == -1) {
            return null;
        }
        return subDay(new Date(), days);
    }


}
