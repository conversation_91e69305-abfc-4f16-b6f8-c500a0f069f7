package cn.dahe.cas.auth.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MiduUtil {

    /**
     * 从字符串中提取appId的值
     * @param input 输入字符串
     * @return appId的值，如果未找到则返回null
     */
    public static String getAppId(String input) {
        return extractValueByKey(input, "appId");
    }

    /**
     * 从字符串中提取appSecret的值
     * @param input 输入字符串
     * @return appSecret的值，如果未找到则返回null
     */
    public static String getAppSecret(String input) {
        return extractValueByKey(input, "appSecret");
    }

    /**
     * 从字符串中提取systemId的值
     * @param input 输入字符串
     * @return systemId的值，如果未找到则返回null
     */
    public static String getSystemId(String input) {
        return extractValueByKey(input, "systemId");
    }

    /**
     * 从字符串中提取channel的值
     * @param input 输入字符串
     * @return channel的值，如果未找到则返回null
     */
    public static String getChannel(String input) {
        return extractValueByKey(input, "channel");
    }

    /**
     * 从字符串中提取username的值
     * @param input 输入字符串
     * @return username的值，如果未找到则返回null
     */
    public static String getUsername(String input) {
        return extractValueByKey(input, "username");
    }

    /**
     * 通用方法：根据键名从字符串中提取对应的值
     * @param input 输入字符串
     * @param key 要提取的键名
     * @return 对应的值，如果未找到则返回null
     */
    private static String extractValueByKey(String input, String key) {
        if (input == null || key == null) {
            return null;
        }

        // 构建正则表达式，匹配[key]=[value]格式
        String regex = key + "\\=\\[([^\\]]+)\\]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 如果找到匹配项，返回第一个捕获组的值
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    /**
     * 验证字符串是否符合特定格式
     * 格式: midu:znb:appId=[value] appSecret=[value] systemId=[value] channel=[value],
     * @param input 需要验证的字符串
     * @return 是否符合格式要求
     */
    public static boolean isValidFormat(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }

        // 移除末尾的逗号和空格
        input = input.trim();
        if (input.endsWith(",")) {
            input = input.substring(0, input.length() - 1).trim();
        }

        // 分割字符串
        String[] parts = input.split(",");

        for (String part : parts) {
            part = part.trim();
            if (!validatePart(part)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证单个部分是否符合格式
     * @param part 单个部分字符串
     * @return 是否符合格式
     */
    private static boolean validatePart(String part) {
        // 检查前缀
        if (!part.contains("midu:zyb:") && !part.contains("midu:znb:")) {
            return false;
        }

        // 提取键值对部分
        String keyValuePart = part.substring(8);

        // 验证所有必要的键是否存在
        return keyValuePart.matches(".*appId=\\[.*?\\].*") &&
                keyValuePart.matches(".*appSecret=\\[.*?\\].*") &&
                keyValuePart.matches(".*systemId=\\[.*?\\].*") &&
                keyValuePart.matches(".*channel=\\[.*?\\].*");
    }

}
