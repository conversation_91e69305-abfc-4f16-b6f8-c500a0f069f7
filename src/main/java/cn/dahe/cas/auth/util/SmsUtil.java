package cn.dahe.cas.auth.util;

import cn.dahe.cas.auth.exception.SsoException;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.Date;
import java.util.Random;

/**
 * Author: zhanggaofei
 * Date: 2019/10/24
 */

public class SmsUtil {

    private static SmsUtil INSTANCE = null;
    private static int SMS_TYPE = 1;
    private static String SMS_TOKEN = "Zvwzkz#JTh1";
    private static String LOGIN_SMS = "https://sms.dahe.cn/dahe/sms/yqtj";

    public static String sendLoginSmsCode(String mobile){
        String code = genSmsCode();
        return sendLoginSmsCode(mobile, code);
    }

    public static String sendLoginSmsCode(String mobile,String code){
        String msg = "";
        try {
            System.out.println("============开始发送验证码（"+code+"）==============>");
            long startSendTime = System.currentTimeMillis();
            String yyyyMMdd = DateUtil.format(new Date(startSendTime), "yyyyMMdd");
            String body = HttpUtil.createPost(LOGIN_SMS)
                    .form("mobile",mobile)
                    .form("token",getLoginSmsToken(yyyyMMdd))
                    .form("type",SMS_TYPE)
                    .form("code",code)
                    .header("startTime",yyyyMMdd)
                    .execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            System.out.println("============发送验证码回执信息（"+jsonObject.toString()+"）==============>");
            if (jsonObject.getInt("result") == 200) {
                System.out.println("============发送验证码成功（"+code+"）==============>");
                return code;
            }
            msg = jsonObject.getStr("msg");
            throw new SsoException(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private static String getLoginSmsToken(String yyyyMMdd){
        return SecureUtil.md5(SMS_TOKEN + yyyyMMdd).toUpperCase();
    }

    public static String genSmsCode() {
        Random random = new Random();
        String result = "";
        for (int i = 0; i < 6; i++) {
            result += random.nextInt(10);
        }
        return result;
    }

}
