package cn.dahe.cas.auth.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;

public class SmsTokenUtil {
    public static String getMD5token(String token){
        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        String date = df.format(calendar.getTime());
        String md5token = Md532.encryption(token+date);
        return md5token.toUpperCase();
    }
}
