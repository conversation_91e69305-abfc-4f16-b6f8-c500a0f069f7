package cn.dahe.cas.auth.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class ParamsUtil {
    /**
     * 设置请求参数
     * @param paramMap
     */
    public static String getParamsString(Map<String, String[]> paramMap) {
        if (paramMap == null){
            return "";
        }
        StringBuilder params = new StringBuilder();
        for (Map.Entry<String, String[]> param : ((Map<String, String[]>)paramMap).entrySet()){
            params.append(("".equals(params.toString()) ? "" : "&") + param.getKey() + "=");
            String paramValue = (param.getValue() != null && param.getValue().length > 0 ? StringUtils.join(param.getValue(),"") : "");
            params.append(StringUtils.abbreviate(StringUtils.endsWithIgnoreCase(param.getKey(), "password") ? "" : paramValue, 100));
        }
        return params.toString();
    }
}
