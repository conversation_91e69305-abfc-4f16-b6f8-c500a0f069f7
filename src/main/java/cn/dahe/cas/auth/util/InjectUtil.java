package cn.dahe.cas.auth.util;

import cn.dahe.cas.auth.service.WordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class InjectUtil{

    private static InjectUtil injectUtil;

    @Autowired
    private WordService wordService;

    @PostConstruct
    public void init(){
        injectUtil = this;
    }

    public static WordService getWordService(){
        return injectUtil.wordService;
    }
}
