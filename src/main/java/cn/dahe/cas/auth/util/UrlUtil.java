package cn.dahe.cas.auth.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 */
public class UrlUtil {

    private static final Logger log = LoggerFactory.getLogger(UrlUtil.class);
    public static final String URL_SEPERATE="/";

    public static boolean isValidate(String url){
        if(StringUtils.isEmpty(url)){
            return false;
        }
        String[] parts = StringUtils.delimitedListToStringArray(url, URL_SEPERATE);
        if(parts.length<3){
            return false;
        }
        return true;
    }

    public static String getHost(String url){
        log.info("UrlUtil!!!!!!!!!!!!!!!!:{} ",url);
        String replace = url.split("/callback")[0];
        log.info("UrlUtil!!!!!!!!!!!!!!!!2:{} ",replace);
        return replace;
    }
}
