package cn.dahe.cas.auth.util;

import org.springframework.core.convert.ConversionService;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2017/12/22
 * @createTime 10:46
 * @info
 */
public class ListUtil {
    /**
     * 获取list中最后一条数据
     * @param origin
     * @param <T>
     * @return
     */
    public static <T> T getLast(List<T> origin){
        if(origin==null||origin.size()==0){
            return null;
        }
        return origin.get(origin.size()-1);
    }

    public static <T> List<T> toList(Iterable<T> origin){
        List<T> result = new ArrayList<T>();
        for(T t:origin){
            result.add(t);
        }
        return result;
    }

    public static <T> T getFirst(Set<T> origin){
        if(origin==null||origin.size()==0){
            return null;
        }
        return origin.iterator().next();
    }

    public static <S,T> List<T> convert(List<S> source, Class<T> clz, ConversionService conversionService){
        List<T> result = new ArrayList<>();
        for(S s:source){
            result.add(conversionService.convert(s,clz));
        }
        return result;
    }

    public static <T> boolean isEmpty(Collection<T> list){
        return list==null||list.size()==0;
    }
}
