package cn.dahe.cas.auth.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * <AUTHOR>
 * @date 2023/2/8
 * @note
 */
public class Oauth2Util_Midu_Zhineng {

    private static String prefix = "https://api-open.mdsuoji.com/api/login/v1/ssoLogin";
    //private static String appId = "dhcszh5740";
    //private static String appSecret = "bbc7d14224f44a64a6592a8a14a6b3a5";
    //private static String username = "dahznsp";
    //private static String channel = "6332";

    /**
     * 2.1.获取授权码
     * @param appId
     */
    public static String getCode(String appId) {
        String body = HttpUtil.createPost("https://api-open-wx-www.yqt365.com/dataapp/api/oauth2/authorize")
                .form("appId", appId)
                .form("responseType", "code")
                .form("state", System.currentTimeMillis())
                .execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(body);
        if (jsonObject.getStr("code").equals("0000")) {
            JSONObject authorizeCode = jsonObject.getJSONObject("authorizeCode");
            return authorizeCode.getStr("authorizeCode");
        }
        return null;
    }

    /**
     * 2.2.获取access_token
     * @param appId
     */
    public static String getAccessToken(String appId, String appSecret, String code) {
        if (code != null) {
            String body = HttpUtil.createPost("https://api-open-wx-www.yqt365.com/dataapp/api/oauth2/token")
                    .form("appId", appId)
                    .form("appSecret", appSecret)
                    .form("grantType", "authorization_code")
                    .form("authorizeCode", code)
                    .execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            if (jsonObject.getStr("code").equals("0000")) {
                JSONObject authorizeCode = jsonObject.getJSONObject("accessToken");
                String accessToken = authorizeCode.getStr("accessToken");
                return accessToken;
            }
        }
        return null;
    }

    public static String getAccessUrl(String appId, String appSecret, String systemId, String username, String channel){
        String timeStamp = String.valueOf(System.currentTimeMillis());
        String code = getCode(appId);
        String accessToken = getAccessToken(appId,appSecret,code);
        return prefix +
                "?timeStamp="+timeStamp+
                "&systemId="+systemId+
                "&clientUserName="+username+
                "&channel="+channel+
                "&accessToken="+accessToken;
    }

}
