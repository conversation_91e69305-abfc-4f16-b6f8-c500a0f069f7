package cn.dahe.cas.auth.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Calendar;


public class SecurityUtil {
    private static final Log logger = LogFactory.getLog(SecurityUtil.class);
    private static final Base64.Encoder ENCODER = Base64.getEncoder();
    private static final Base64.Decoder DECODER = Base64.getDecoder();

    /**
     * 加密-base64
     *
     * @param str
     * @return
     */
    public static String base64Encode(String str) {
        try {
            return ENCODER.encodeToString(str.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            logger.info(str + " 加密失败");
            e.printStackTrace();
            //加密失败返回原字符串
            return str;
        }
    }

    /**
     * MD5 加密
     *
     * @param password
     * @return
     */
    public static String md5(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(password.getBytes());
            String md5str = new BigInteger(1, md.digest()).toString(16);
            return StringUtils.leftPad(md5str, 32, "0");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String md5(String username, String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(username.getBytes());
            md.update(password.getBytes());
            String md5str = new BigInteger(1, md.digest()).toString(16);
            return StringUtils.leftPad(md5str, 32, "0");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 对大河短信接口进行签名
     * @param token
     * @return
     */
    public static String signDaheSms(String token){
        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        String date = df.format(calendar.getTime());
        String md5token = Md532.encryption(token+date);
        return md5token.toUpperCase();
    }


    public static String encryptFPMd5(String phone) {
        String currentDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        String dataToEncrypt = phone + currentDate + "r9DfH6jKp3L2N7Mq";
        return md5(dataToEncrypt);
    }
}
