package cn.dahe.cas.auth.util;

import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.entity.RemoteUser;
import cn.dahe.cas.auth.entity.RemoteUserZy;
import cn.dahe.cas.auth.entity.User;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.json.JSONUtil;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public class DbUtil {

    public static void initRemoteUser() throws Exception {
        List<Entity> users = Db.use().findAll("t_user");
        final int[] i = {0};
        users.forEach(el->{
            i[0]++;
            RemoteUser remoteUser = el.toBean(RemoteUser.class);
            User tmp = new User();
            tmp.setUid(remoteUser.getId());
            tmp.setUsername(remoteUser.getUsername());
            try {
                tmp.setTruename(Sm4Util.encryptEcb("341210E10E7E409F2DCCAE01EBB4843E",remoteUser.getUsername()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                tmp.setPhone(Sm4Util.encryptEcb("341210E10E7E409F2DCCAE01EBB4843E",remoteUser.getPhone()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            tmp.setPassword(remoteUser.getPassword());
            tmp.setOrganization(remoteUser.getDepId());
            tmp.setWxOpenId(remoteUser.getOpenId());
            tmp.setCreateTime(remoteUser.getCreateTime());
            int enable = remoteUser.getEnable();
            int isDel = remoteUser.getIsDel();
            if (isDel == 1) {
                tmp.setFlag(UserFlag.DELETE);
            } else if (enable == 0) {
                tmp.setFlag(UserFlag.FORBID);
            } else {
                tmp.setFlag(UserFlag.ALLOW);
            }
            try {
                List<Entity> query = Db.use().query("select * from user where phone = ?", tmp.getPhone());
                if (query == null || query.size() <= 0) {
                    Entity e = new Entity();
                    e.setTableName("user");
                    e.set("uid",tmp.getUid());
                    e.set("username",tmp.getUsername());
                    e.set("true_name",tmp.getTruename());
                    e.set("phone",tmp.getPhone());
                    e.set("password",tmp.getPassword());
                    e.set("organization",tmp.getOrganization());
                    e.set("wxOpenId",tmp.getWxOpenId());
                    e.set("create_time",tmp.getCreateTime());
                    e.set("job",0);
                    e.set("sex",0);
                    e.set("type",0);
                    e.set("flag",tmp.getFlag().getFlag());
                    e.set("icon","https://img.henan.gov.cn/d820c52949c8176da706255be07f2c35");
                    List<Entity> tmpQuery = Db.use().query("select id,p_id,name from department where id=?",remoteUser.getDepId());
                    for (int j = 0; j < tmpQuery.size(); j++) {
                        String name = tmpQuery.get(j).getStr("name");
                        int pid = tmpQuery.get(j).getInt("p_id");
                        if (pid == 1) {
                            name = ("大河舆情/"+name);
                        } else if (pid == 2) {
                            name = ("大河网/" + name);
                        }
                        e.set("company",name);
                    }
                    Db.use().insert(e);
                    // todo 设置站点角色访问权限，后续要考虑
                    System.out.println("i>>>>>>>>="+i[0]);

                    ;   Entity e2 = new Entity();
                    e2.setTableName("user_site");
                    e2.set("user_id",tmp.getUid());
                    e2.set("site_id",72);
                    Db.use().insert(e2);

                    Entity e3 = new Entity();
                    e3.setTableName("user_site");
                    e3.set("user_id",tmp.getUid());
                    e3.set("site_id",92);
                    Db.use().insert(e3);
                }
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        });
    }

    public static void initMiduZyUser() throws Exception {
        List<Entity> users = Db.use().findAll("md_user_zy");
        final int[] i = {0};
        users.forEach(el->{
            i[0]++;
            RemoteUserZy remoteUser = el.toBean(RemoteUserZy.class);
            User tmp = new User();
            tmp.setUsername(remoteUser.getName());
            try {
                tmp.setTruename(Sm4Util.encryptEcb("341210E10E7E409F2DCCAE01EBB4843E",remoteUser.getName()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                tmp.setPhone(Sm4Util.encryptEcb("341210E10E7E409F2DCCAE01EBB4843E",remoteUser.getPhone()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            tmp.setPassword("");
            tmp.setOrganization(0);
            tmp.setWxOpenId("");
            tmp.setCreateTime(new Date());

            tmp.setFlag(UserFlag.ALLOW);
            tmp.setMdAppId(remoteUser.getAppId());
            tmp.setMdAppSecret(remoteUser.getAppSecret());
            tmp.setMdAppSystemId(remoteUser.getSystemId());
            tmp.setMdAppUsername(remoteUser.getUsername());
            tmp.setMdAppChannel(remoteUser.getChannel());
            try {
                List<Entity> query = Db.use().query("select * from user where phone = ?", tmp.getPhone());
                if (query == null || query.size() <= 0) {
                    Entity e = new Entity();
                    e.setTableName("user");
                    e.set("username", tmp.getUsername());
                    e.set("true_name", tmp.getTruename());
                    e.set("phone", tmp.getPhone());
                    e.set("password", tmp.getPassword());
                    e.set("organization", tmp.getOrganization());
                    e.set("wxOpenId", tmp.getWxOpenId());
                    e.set("create_time", tmp.getCreateTime());
                    e.set("job", 0);
                    e.set("sex", 0);
                    e.set("type", 0);
                    e.set("flag", tmp.getFlag().getFlag());
                    e.set("icon", "https://img.henan.gov.cn/d820c52949c8176da706255be07f2c35");

                    e.set("md_app_id", tmp.getMdAppId());
                    e.set("md_app_secret", tmp.getMdAppSecret());
                    e.set("md_app_system_id", tmp.getMdAppSystemId());
                    e.set("md_app_username", tmp.getMdAppUsername());
                    e.set("md_app_channel", tmp.getMdAppChannel());
                    e.set("from_type", "1@"+ JSONUtil.toJsonStr(remoteUser));

                    Long uid = Db.use().insertForGeneratedKey(e);
                    // todo 设置站点角色访问权限，后续要考虑
                    System.out.println("i>>>>>>>>=" + i[0]);

                    ;
                    Entity e2 = new Entity();
                    e2.setTableName("user_site");
                    e2.set("user_id", uid);
                    e2.set("site_id", 72);
                    Db.use().insert(e2);

                    Entity e3 = new Entity();
                    e3.setTableName("user_site");
                    e3.set("user_id", uid);
                    e3.set("site_id", 93);
                    Db.use().insert(e3);
                } else {
                    Entity entity = query.get(0);
                    String from_type = entity.getStr("from_type");
                    String new_type = "1@"+ JSONUtil.toJsonStr(remoteUser);
                    if (!from_type.contains(new_type)) {
                        Db.use().update(Entity.create("user").set("from_type",from_type+" , "+new_type),
                                Entity.create("user").set("uid",entity.getInt("uid")));
                    }
                }
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        });
    }

    public static void initMiduZnUser() throws Exception {
        List<Entity> users = Db.use().findAll("md_user_zn");
        final int[] i = {0};
        users.forEach(el->{
            i[0]++;
            RemoteUserZy remoteUser = el.toBean(RemoteUserZy.class);
            User tmp = new User();
            tmp.setUsername(remoteUser.getName());
            try {
                tmp.setTruename(Sm4Util.encryptEcb("341210E10E7E409F2DCCAE01EBB4843E",remoteUser.getName()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                tmp.setPhone(Sm4Util.encryptEcb("341210E10E7E409F2DCCAE01EBB4843E",remoteUser.getPhone()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            tmp.setPassword("");
            tmp.setOrganization(0);
            tmp.setWxOpenId("");
            tmp.setCreateTime(new Date());

            tmp.setFlag(UserFlag.ALLOW);
            tmp.setMdAppId(remoteUser.getAppId());
            tmp.setMdAppSecret(remoteUser.getAppSecret());
            tmp.setMdAppUsername(remoteUser.getUsername());
            tmp.setMdAppChannel(remoteUser.getChannel());
            try {
                List<Entity> query = Db.use().query("select * from user where phone = ?", tmp.getPhone());
                if (query == null || query.size() <= 0) {
                    Entity e = new Entity();
                    e.setTableName("user");
                    e.set("username", tmp.getUsername());
                    e.set("true_name", tmp.getTruename());
                    e.set("phone", tmp.getPhone());
                    e.set("password", tmp.getPassword());
                    e.set("organization", tmp.getOrganization());
                    e.set("wxOpenId", tmp.getWxOpenId());
                    e.set("create_time", tmp.getCreateTime());
                    e.set("job", 0);
                    e.set("sex", 0);
                    e.set("type", 0);
                    e.set("flag", tmp.getFlag().getFlag());
                    e.set("icon", "https://img.henan.gov.cn/d820c52949c8176da706255be07f2c35");

                    e.set("md_app_id", tmp.getMdAppId());
                    e.set("md_app_secret", tmp.getMdAppSecret());
                    e.set("md_app_system_id", tmp.getMdAppSystemId());
                    e.set("md_app_username", tmp.getMdAppUsername());
                    e.set("md_app_channel", tmp.getMdAppChannel());
                    e.set("from_type", "2@"+ JSONUtil.toJsonStr(remoteUser));

                    Long uid = Db.use().insertForGeneratedKey(e);
                    // todo 设置站点角色访问权限，后续要考虑
                    System.out.println("i>>>>>>>>=" + i[0]);

                    ;
                    Entity e2 = new Entity();
                    e2.setTableName("user_site");
                    e2.set("user_id", uid);
                    e2.set("site_id", 72);
                    Db.use().insert(e2);

                    Entity e3 = new Entity();
                    e3.setTableName("user_site");
                    e3.set("user_id", uid);
                    e3.set("site_id", 94);
                    Db.use().insert(e3);
                } else {
                    Entity entity = query.get(0);
                    String from_type = entity.getStr("from_type");
                    String new_type = "2@"+ JSONUtil.toJsonStr(remoteUser);
                    if (!from_type.contains(new_type)) {
                        Db.use().update(Entity.create("user").set("from_type",from_type+" , "+new_type),
                                Entity.create("user").set("uid",entity.getInt("uid")));
                    }
                }
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        });
    }

    public static void main(String[] args) throws Exception {
        initMiduZyUser();
        initMiduZnUser();
    }
}
