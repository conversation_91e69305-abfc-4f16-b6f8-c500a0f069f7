package cn.dahe.cas.auth.util;


import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class OkHttpUtils {

    private static final Logger logger = LoggerFactory.getLogger(OkHttpUtils.class);

    private static OkHttpClient mOkHttpClient;

    //2.暴露出一个方法，返回当前类的对象
    private static OkHttpUtils mInstance;

    public static OkHttpUtils newInstance() {
        if (mInstance == null) {
            //实例化对象
            //加上一个同步锁，只能有一个执行路径进入
            synchronized (OkHttpUtils.class) {
                if (mInstance == null) {
                    mInstance = new OkHttpUtils();
                }
                if (mOkHttpClient == null) {
                    mOkHttpClient = new OkHttpClient();
                }
            }
        }
        return mInstance;
    }


    /**
     * 带连接时间的实例
     *
     * @param seconds
     * @return
     */
    public static OkHttpUtils newsTimeOutInstance(int seconds) {
        if (mInstance == null) {
            //实例化对象
            //加上一个同步锁，只能有一个执行路径进入
            synchronized (OkHttpUtils.class) {
                if (mInstance == null) {
                    mInstance = new OkHttpUtils();
                }
                if (mOkHttpClient == null) {
                    mOkHttpClient = new OkHttpClient.Builder().readTimeout(seconds, TimeUnit.SECONDS).build();
                }
            }
        }
        return mInstance;
    }

    /**
     * Get请求
     *
     * @param url
     */
    public Response doGet(String url) {
        Response response = null;
        Request request = new Request.Builder()
                .url(url)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            logger.info(">>>>>>>>>>>>okhttp get请求异常 ");
            e.printStackTrace();
        }
        return response;
    }

    public Response doGet(String url, Map<String, String> header) {
        Response response = null;
        Request request = new Request.Builder()
                .headers(setHeaders(header))
                .url(url)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            logger.info(">>>>>>>>>>>>okhttp get请求异常 ");
            e.printStackTrace();
        }
        return response;
    }

    /**
     * Post请求发送键值对数据
     *
     * @param
     * @param url
     * @param mapParams
     */
    public Response doPost(String url, Map<String, String> mapParams) {
        Response response = null;
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : mapParams.keySet()) {
            builder.add(key, mapParams.get(key));
        }
        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    public Response baiduPost(String url, String param) {
        Response response = null;
        RequestBody body = RequestBody.create(MediaType.parse("text/plain; charset=utf-8")
                , param);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }



    /*public Response zimgPost(String url, String contentType, InputStream inputStream) {
        Response response = null;
        RequestBody body = RequestBody.create(contentType,);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }
*/

    /**
     * Post请求发送JSON数据
     *
     * @param url
     * @param jsonParams
     */
    public Response doPost(String url, String jsonParams) {
        Response response = null;
        RequestBody body = RequestBody.create(MediaType.parse("application/form; charset=utf-8")
                , jsonParams);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (Exception e) {
            logger.info(">>>>>>>>>>okhttp post超时。");
            return null;
        }
        return response;
    }

    /**
     * 加token的post请求
     *
     * @param url
     * @param jsonParams
     * @param token
     * @return
     */
    public Response doPostJson(String url, String jsonParams, String token) {
        Response response = null;
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8")
                , jsonParams);
        Request request = new Request.Builder().addHeader("token", token)
                .url(url)
                .post(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (Exception e) {
            logger.info(">>>>>>>>>>okhttp post超时。");
            return null;
        }
        return response;
    }

    public Response doPutJson(String url, Map<String, String> mapParams) {
        Response response = null;
        // 将Map转换为JSON字符串
        String json = convertMapToJson(mapParams);
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request request = new Request.Builder()
                .url(url)
                .put(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }
    public Response doPostJson(String url, Map<String, String> mapParams) {
        Response response = null;
        // 将Map转换为JSON字符串
        String json = convertMapToJson(mapParams);
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    private String convertMapToJson(Map<String, String> map) {
        StringBuilder json = new StringBuilder("{");
        for (Map.Entry<String, String> entry : map.entrySet()) {
            json.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\",");
        }
        if (json.length() > 1) {
            json.setLength(json.length() - 1); // 去掉最后一个逗号
        }
        json.append("}");
        return json.toString();
    }

    /**
     * 带header头的post请求
     *
     * @param url       请求地址
     * @param mapParams 参数
     * @param header    header
     */
    public Response doPostAddHeader(String url, Map<String, String> mapParams, Map<String, String> header) {
        Response response;
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : mapParams.keySet()) {
            builder.add(key, mapParams.get(key));
        }
        Request request = new Request.Builder()
                .headers(setHeaders(header))
                .url(url)
                .post(builder.build())
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (Exception e) {
            logger.info(">>>>>>>>>> okhttp post超时。");
            return null;
        }
        return response;
    }


    public interface HttpCallBack {
        void onError(Response response, IOException e);

        void onSuccess(Response response, String result);
    }

    public void asyncGet(String url, final HttpCallBack httpCallBack) {
        final Request request = new Request.Builder().url(url).build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                logger.info(">>>>>>>>>>>>>>>>>>asyncGeet onFialure,msg {}", e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                final String result = response.body().string();
                if (httpCallBack != null) {
                    httpCallBack.onSuccess(response, result);
                }
            }
        });
    }


    public void asyncPost(String url, RequestBody formBody, final HttpCallBack httpCallBack) {
        final Request request = new Request.Builder().url(url).post(formBody).build();
        mOkHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                logger.info(">>>>>>>>>>>>>asyncPost onFailure,msg {}", e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                final String result = response.body().string();
                if (httpCallBack != null) {
                    httpCallBack.onSuccess(response, result);
                }
            }
        });
        /*try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }*/
    }

    /**
     * 添加header
     *
     * @param header header
     */
    private Headers setHeaders(Map<String, String> header) {
        Headers headers;
        Headers.Builder headersBuilder = new Headers.Builder();
        if (header != null) {
            Iterator<String> iterator = header.keySet().iterator();
            String key;
            while (iterator.hasNext()) {
                key = iterator.next();
                headersBuilder.add(key, header.get(key));
            }
        }
        headers = headersBuilder.build();
        return headers;
    }

    /**
     * Post请求发送键值对数据
     *
     * @param
     * @param url
     * @param mapParams
     */
    public Response doPostHeader(String url, Map<String, String> mapParams, String token) {
        Response response = null;
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : mapParams.keySet()) {
            builder.add(key, mapParams.get(key));
        }
        Request request = new Request.Builder().addHeader("token", token)
                .url(url)
                .post(builder.build())
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 含文件
     *
     * @param url
     * @param mapParams
     * @param multipartFile
     * @return
     */
    public Response doPostFile(String url, Map<String, String> mapParams, MultipartFile multipartFile) {
        Response response = null;
        MultipartBody.Builder bodyBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);

        for (String key : mapParams.keySet()) {
            bodyBuilder.addFormDataPart(key, mapParams.get(key));
        }
        String fileDir = "/data/wwwroot/yqtsso_uploadfile/";
        File file = multipartFileToFile(fileDir, multipartFile);
        bodyBuilder.addFormDataPart("file", multipartFile.getOriginalFilename(), RequestBody.create(MediaType.parse("application/*"), file));
        Request request = new Request.Builder()
                .url(url)
                .post(bodyBuilder.build())
                .addHeader("Content-Type", "multipart/form-data")
                .build();
        try {
            response = mOkHttpClient.newCall(request).execute();
            logger.info("post（文件）请求成功");
            String filePath = fileDir + multipartFile.getOriginalFilename();
            Path path = Paths.get(filePath);

            try {
                // 使用Files类的delete方法，它会自动处理Path资源，无需显式关闭。
                Files.delete(path);
                logger.info("post（文件）格式转换后删除成功，文件名：{}", path.getFileName());
            } catch (IOException e) {
                logger.info("post（文件）格式转换后删除失败，文件名：{}，异常：{}", path.getFileName(), e.getMessage());
            }
        } catch (IOException e) {
            logger.warn("post（文件）请求异常：{}", e.getMessage());
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 文件格式转换
     *
     * @param fileDir       文件路径前缀
     * @param multipartFile 文件
     * @return 转换后的File
     */
    private File multipartFileToFile(String fileDir, MultipartFile multipartFile) {
        File convFile = new File(fileDir + multipartFile.getOriginalFilename());
        Path filePath = Paths.get(convFile.getAbsolutePath());
        try {
            Files.copy(multipartFile.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            logger.info("post（文件）格式转换成功");
        } catch (IOException e) {
            logger.info("post（文件）格式转换失败：{}", e.getMessage());
            e.printStackTrace();
        }
        return convFile;
    }
}
