package cn.dahe.cas.auth.util;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.*;


public class JJWT {
	private static String key = "jwtSsoKeyForYqtSoft20221206System";
	private static String prefix = "Bearer ";
	private static String coreKey = "loginUserId";
	private static long duration = 24*3600*1000L;

	public static JSONObject resoveUserCenterUserJson(String token) {
		if (StringUtils.isBlank(token)) {
			throw new RuntimeException("缺失访问令牌");
		}
		token = Base64.decodeStr(token);
		System.out.println("token:" + token);
		Claims body = null;
		try {
			body = decodeJwt(token);
		} catch (ExpiredJwtException e) {
			throw new RuntimeException("token已过期");
		} catch (Exception e) {
			body = null;
		}
		String subject = body.getSubject();
		if (!JSONUtil.isJson(subject)) {
			return null;
		}
		JSONObject json = new JSONObject(subject);
		return json;
	}

	public static String resoveUserCenterUserId(String header) {
		if (StringUtils.isBlank(header)) {
			throw new RuntimeException("缺失访问令牌");
		}
		header = URLDecoder.decode(header, StandardCharsets.UTF_8);
		String token = header.substring(prefix.length());
		Claims body = null;
		try {
			body = decodeJwt(token);
		} catch (ExpiredJwtException e) {
			throw new RuntimeException("token已过期");
		} catch (Exception e) {
			body = null;
		}

		return Optional.ofNullable(body).map(bd -> bd.get(coreKey, String.class))
				.orElseThrow(() -> new RuntimeException("token解析失败"));
	}

	public static final Claims decodeJwt(String jwt) {
		return decodeJwt(jwt, key);
	}

	public static final Claims decodeJwt(String jwt, String secretkeyCode) {
		try {
			SecretKey secretkey = Keys.hmacShaKeyFor(key.getBytes(StandardCharsets.UTF_8));
			Claims claims = Jwts.parser().setSigningKey(secretkey).parseClaimsJws(jwt).getBody();
			return claims;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 有效期30天
	 * @param userId
	 * @return
	 */
	 public static String generateByUserId(String userId){
		 Map<String,Object> claim=new HashMap<>();
		 claim.put(coreKey,userId);
		 SecretKey secretkey = Keys.hmacShaKeyFor(key.getBytes(StandardCharsets.UTF_8));
		 String compact = Jwts.builder()
				 .setClaims(claim)
				 .setExpiration(new Date(System.currentTimeMillis() + duration))
				 .setIssuedAt(new Date()).signWith(secretkey).compact();
		 return prefix+compact;
	 }

}
