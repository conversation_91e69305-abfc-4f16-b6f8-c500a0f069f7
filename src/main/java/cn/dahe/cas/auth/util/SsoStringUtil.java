package cn.dahe.cas.auth.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SsoStringUtil extends StringUtils{

    private static final String REG_EX_SPACE = "\\s*|\t|\r|\n";

    /**
     * 判断字符串是否由同一字符组成
     * @param origin
     * @return
     */
    public static boolean isAllSame(String origin){
        String[] arrs = origin.split("");
        String first = arrs[0];
        for(String s:arrs){
            if(!s.equals(first)){
                return false;
            }
        }
        return true;
    }

    public static String safeSub(String origin,int begin,int end){
        if(StringUtils.isEmpty(origin)){
            return "";
        }
        int length = origin.length();
        end = end>length?length:end;
        return origin.substring(begin,end);
    }

    /**
     * 判断是否含有特殊字符
     * @param username
     * @return
     */
    public static boolean isConSpeCharacters(String username){
        if(username.replaceAll("[\u4e00-\u9fa5]*[a-z]*[A-Z]*\\d*_*", "").length()==0){
            return false;
        }
        return true;
    }

    /**
     * 过滤特殊字符
     * @param username
     * @return
     */
    public static String replaceSpeCharcters(String username){
        return username.replaceAll("[\u4e00-\u9fa5]*[a-z]*[A-Z]*\\d*_*\\s*", "");
    }

    /**
     * 过滤空格回车标签
     * @param htmlStr 字符串
     * @return 过滤空格之后的
     */
    public static String delSpace(String htmlStr){
        Pattern patternSpace = Pattern.compile(REG_EX_SPACE, Pattern.CASE_INSENSITIVE);
        Matcher matcherSpace = patternSpace.matcher(htmlStr);
        htmlStr = matcherSpace.replaceAll("");
        return htmlStr.trim();
    }

    /**
     * 提取每个汉字的首字母
     * @param str 汉子输入
     * @return String 拼音缩写输出
     */
    public static String getPinYinHeadChar(String str) {
        String convert = "";
        for (int j = 0; j < str.length(); j++) {
            char word = str.charAt(j);
            // 提取汉字的首字母
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(word);
            if (pinyinArray != null) {
                convert += pinyinArray[0].charAt(0);
            } else {
                convert += word;
            }
        }
        return convert;
    }
}
