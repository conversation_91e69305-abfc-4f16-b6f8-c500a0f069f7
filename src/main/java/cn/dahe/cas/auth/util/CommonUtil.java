package cn.dahe.cas.auth.util;

import cn.dahe.cas.auth.config.URL;
import cn.dahe.cas.auth.exception.SsoException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;

public class CommonUtil {
    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);
    private static final String pattern = "^((1[3,5,8][0-9])|166|(14[5,7,9])|(17[0,1,2,3,5,6,7,8])|(19[1,6,7,8,9]))\\d{8}$";

    public static boolean isPhoneNumber(String number){
            if(number==null){
                return false;
            }
            return number.matches(pattern);
    }
    public static String getLastSixDigits(String phoneNumber) {
        if (phoneNumber == null) {
            return null;
        }
        int length = phoneNumber.length();
        if (length >= 6) {
            return phoneNumber.substring(length - 6);
        }
        return phoneNumber;
    }
    public static boolean checkAjax(HttpServletRequest request){
        String accept = request.getHeader("accept");
        return accept != null && accept.contains("application/json") || (request.getHeader("X-Requested-With") != null && request.getHeader("X-Requested-With").contains("XMLHttpRequest"));
    }

    public static boolean check(HttpServletRequest request){
        boolean smsFlag = CommonUtil.checkAjax(request);
        if (!smsFlag){
            log.error("!!!!!!!!!!!!!loginController sms is not ajax");
            throw new SsoException("请求方式不对");
        }
        String ua = request.getHeader("User-Agent");
        String refer = request.getHeader("Referer");
        if (StringUtils.isAnyBlank(ua,refer)){
            log.error("!!!!LoginController ua {},refer {}",ua,refer);
            //throw new SsoException("参数不对");
        }
        if(refer.matches(URL.REFERER)){
            return true;
        }else{
            log.error("!!!!!send sms refer {}",refer);
            //throw new SsoException( "参数不对");
        }
        return true;
    }
}
