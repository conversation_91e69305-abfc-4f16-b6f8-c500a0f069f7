package cn.dahe.cas.auth.util;


public class QingboUtil {


    /**
     * 验证字符串是否符合特定格式
     * 格式: midu:znb:appId=[value] appSecret=[value] systemId=[value] channel=[value],
     * @param input 需要验证的字符串
     * @return 是否符合格式要求
     */
    public static boolean isValidFormat(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }

        // 移除末尾的逗号和空格
        input = input.trim();
        if (input.endsWith(",")) {
            input = input.substring(0, input.length() - 1).trim();
        }

        // 分割字符串
        String[] parts = input.split(",");

        for (String part : parts) {
            part = part.trim();
            if (!validatePart(part)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证单个部分是否符合格式
     * @param part 单个部分字符串
     * @return 是否符合格式
     */
    private static boolean validatePart(String part) {
        // 检查前缀
        if (part.contains("qingbo:wxb:")) {
            return true;
        }
        // 验证所有必要的键是否存在
        return false;
    }

}
