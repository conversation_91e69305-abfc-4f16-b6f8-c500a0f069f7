package cn.dahe.cas.auth.util;

import java.security.SecureRandom;
import java.util.UUID;

public class IdGen {
	
	private static SecureRandom random = new SecureRandom();
	
	
	public static String uuid(){
		return UUID.randomUUID().toString().replace("-", "");
	}
	
	public static long randomLong(){
		return Math.abs(random.nextLong());
	}
	
	/**
	 * 产生加密因子
	 * @return
	 */
	public static String generSalt(){
		return UUID.randomUUID().toString().replace("-", "").substring(0, 6);
	}

	/**
	 * 产生自增整数id
     * 并发超过1000会出现重复问题
	 */
	public static long ai(){
		return System.currentTimeMillis();
	}

}
