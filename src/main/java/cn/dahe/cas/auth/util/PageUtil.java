package cn.dahe.cas.auth.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class PageUtil {

    public static int[] getSE(Pageable page,int total){
        int[] se = {-1,-1};
        int start = page.getPageSize()*(page.getPageNumber()-1);
        int end = page.getPageSize()*(page.getPageNumber())-1;
        if(total<start){
            return se;
        }
        if(total-1<end){
            end = total-1;
        }
        se[0]=start;
        se[1]=end;
        return se;
    }

    /**
     * 分页,page从0开始
     * @param page
     * @param <T>
     * @return
     */
    public static <T> List<T> getPage(Pageable page,List<T> total){
        int start = page.getPageSize()*(page.getPageNumber());
        int end = page.getPageSize()*(page.getPageNumber()+1);
        if(total.size()<start){
            return Lists.newArrayList();
        }
        if(total.size()<end){
            end = total.size();
        }
        return total.subList(start,end);
    }

    /**
     * 分页
     * @param page
     * @param <K,V>
     * @return
     */
    public static <K,V> List<V> getPage(Pageable page, ZSetOperations<K,V> zSetOperations, K key){
        int[] index = getSE(page,zSetOperations.size(key).intValue());
        if(index[0]==-1){
            return Lists.newArrayList();
        }
        return Lists.newArrayList(zSetOperations.range(key,index[0],index[1]));
    }

    public static <K,T> Set<T> getPageForZset(Pageable pageable, ZSetOperations<K,T> operations, K key){
        int[] info = getSE(pageable,operations.size(key).intValue());
        if(info[0]==-1){
            return Sets.newHashSet();
        }
        return operations.reverseRange(key,info[0],info[1]);
    }

    public static <H,HK,HV> List<HV> getPageForHash(Pageable pageable, HashOperations<H,HK,HV> operations, H key){
        List<HK> keys = getPage(pageable,Lists.newArrayList(operations.keys(key)));
        return operations.multiGet(key,keys);
    }

    public static Page<String> getDataByCondition(ZSetOperations<String,String> operation, String var1, String var2){
        List<String> list = new ArrayList<>();
        Long rank = operation.rank(var1, var2);
        if(rank!=null){
            Set<String> range = operation.range(var1, rank, rank);
            list.addAll(range);
        }
        Page<String> result = new PageImpl<>(list,null,list.size());
        return result;
    }

    /**
     * 分页获取hash值
     * @param pageable
     * @param operations
     * @param <H>
     * @param <HK>
     * @param <HV>
     * @return
     */
    public static <H,HK,HV> Page<HV> get(Pageable pageable, HashOperations<H,HK,HV> operations, H key){
        List<HK> keys = getPage(pageable, new ArrayList<>(operations.keys(key)));
        List<HV> result = operations.multiGet(key,keys);
        return new PageImpl<>(result,pageable,operations.size(key));
    }

    public static <K,V> Page<V> get(Pageable pageable,ZSetOperations<K,V> operations,K key){
        int[] se = getSE(pageable,operations.size(key).intValue());
        if(se[0]==-1){
            return new PageImpl(Lists.newArrayList(),null,operations.size(key));
        }
        Set<V> result = operations.range(key,se[0],se[1]);
        return new PageImpl(Lists.newArrayList(result),null,operations.size(key));
    }

    /**
     * 使用泛型对page对象进行转换
     * @param source
     * @param clz
     * @param conversionService
     * @param <S>
     * @param <T>
     * @return
     */
    public static <S,T> Page<T> convert(Page<S> source, final Class<T> clz, final ConversionService conversionService){
        return source.map(s -> conversionService.convert(s, clz));
    }
}
