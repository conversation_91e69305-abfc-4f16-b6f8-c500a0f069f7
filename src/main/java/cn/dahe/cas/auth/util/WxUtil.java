package cn.dahe.cas.auth.util;

import cn.dahe.cas.auth.exception.GetWxInfoException;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/5 10:04
 * @Description:
 */
public class WxUtil {

    private static Logger logger = LoggerFactory.getLogger(WxUtil.class);

    //微信登录结果
    public static class WxResult{

        private String access_token;
        private String openid;
        private String unionid;
        private Integer errcode;
        private String errmsg;

        public String getAccess_token() {
            return access_token;
        }

        public void setAccess_token(String access_token) {
            this.access_token = access_token;
        }

        public String getOpenid() {
            return openid;
        }

        public void setOpenid(String openid) {
            this.openid = openid;
        }

        public String getUnionid() {
            return unionid;
        }

        public void setUnionid(String unionid) {
            this.unionid = unionid;
        }

        public Integer getErrcode() {
            return errcode;
        }

        public void setErrcode(Integer errcode) {
            this.errcode = errcode;
        }

        public String getErrmsg() {
            return errmsg;
        }

        public void setErrmsg(String errmsg) {
            this.errmsg = errmsg;
        }

        @Override
        public String toString() {
            return "WxResult{" +
                    "access_token='" + access_token + '\'' +
                    ", openid='" + openid + '\'' +
                    ", unionid='" + unionid + '\'' +
                    ", errcode=" + errcode +
                    ", errmsg='" + errmsg + '\'' +
                    '}';
        }
    }

    public static WxResult getWxResult(String tokenUrl,String appId,String appSecret,String code) throws GetWxInfoException {
        WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
        RestTemplate restTemplate = context.getBean(RestTemplate.class);
        try {
            ResponseEntity<String> result = restTemplate.getForEntity(tokenUrl,String.class,appId,appSecret,code);
            //失败的话直接跳转到登录页面
            WxResult body = JSON.parseObject(result.getBody(),WxResult.class);
            if(null!=body.getErrcode()){
                logger.error("获取微信信息失败，code:{},msg:{}",body.getErrcode(),body.getErrmsg());
                throw new GetWxInfoException();
            }
            return body;
        }catch (RestClientException e){
            logger.error("获取微信用户信息失败 ",e);
            throw new GetWxInfoException();
        }
    }
}
