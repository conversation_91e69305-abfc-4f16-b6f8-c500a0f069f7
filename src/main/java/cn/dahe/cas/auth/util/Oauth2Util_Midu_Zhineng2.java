package cn.dahe.cas.auth.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/2/8
 * @note
 */
public class Oauth2Util_Midu_Zhineng2 {

    private static String prefix = "https://api-open.mdsuoji.com/api/login/v1/ssoLogin";

    /**
     * 2.1.获取授权码
     * @param appId
     */
    public static String getCode(String appId, long timeStamp) {
        String body = HttpUtil.createPost("https://cloud-gateway.midu.com/no/authentication/login/authorize")
                .form("appId", appId)
                .form("responseType", "code")
                .form("state", timeStamp)
                .execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(body);
        if (jsonObject.getStr("code").equals("0000")) {
            JSONObject authorizeCode = jsonObject.getJSONObject("authorizeCode");
            return authorizeCode.getStr("authorizeCode");
        }
        return null;
    }

    /**
     * 2.2.获取access_token
     * @param appId
     */
    public static String getAccessToken(String appId, String appSecret, String code) {
        if (code != null) {
            String body = HttpUtil.createPost("https://cloud-gateway.midu.com/no/authentication/login/token")
                    .form("appId", appId)
                    .form("appSecret", appSecret)
                    .form("grantType", "authorization_code")
                    .form("authorizeCode", code)
                    .execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            if (jsonObject.getStr("code").equals("0000")) {
                JSONObject authorizeCode = jsonObject.getJSONObject("accessToken");
                String accessToken = authorizeCode.getStr("accessToken");
                return accessToken;
            }
        }
        return null;
    }

    public static String getAccessUrl(String appId, String appSecret, String systemId, String username, String channel){
        long timeStamp = System.currentTimeMillis();
        String code = getCode(appId,timeStamp);
        String accessToken = getAccessToken(appId,appSecret,code);
        Map<String, Object> params = new HashMap<>();
        params.put("timeStamp", timeStamp);
        params.put("systemId", systemId);
        params.put("clientUserName", username);
        params.put("channel", channel);
        params.put("accessToken", accessToken);
        params.put("directUrl", "");
        return prefix +
                "?timeStamp="+timeStamp+
                "&systemId="+systemId+
                "&clientUserName="+username+
                "&channel="+channel+
                "&accessToken="+accessToken+
                "&directUrl="+
                "&encryptCode="+encryptCode(appSecret,params);
    }

    public static String getSecurityUrl(String appId, String appSecret, String systemId, String username, String channel){
        HttpResponse response = HttpUtil.createGet(getAccessUrl(appId, appSecret, systemId, username, channel)).execute();
        String location = response.header("location");
        return location;
    }

    private static String encryptCode(String appSecret, Map<String, Object> params) {
        String salt = appSecret;
        if (!StrUtil.isEmpty(salt) && params != null && params.size() > 0) {
            // 排序
            List<String> paramKeyList = new ArrayList(params.keySet());
            Collections.sort(paramKeyList);

            // 拼接
            StringBuilder sb = new StringBuilder();
            for (String k : paramKeyList) {
                sb.append("&").append(k).append("=").append(String.valueOf(params.get(k)));
            }
            String jointParams = sb.toString().replaceFirst("&", "");
            System.out.println("encryptCode = [" + salt + jointParams + "]");

            // 加密
            String encryptCode = MD5Encode(salt + jointParams);
            return encryptCode;
        }
        return null;
    }

    private static String MD5Encode(String origin) {
        String resultString = null;
        try {
            resultString = origin;
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(resultString.getBytes("UTF-8"));
            resultString = byteArrayToHexString(md.digest());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultString;
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuilder resultSb = new StringBuilder();
        for (byte aB : b) {
            resultSb.append(byteToHexString(aB));
        }
        return resultSb.toString();
    }
    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n = 256 + n;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }
    private final static String[] hexDigits = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

//    public static void main(String[] args) {
//        //appId=[znbmjqcs] appSecret=[kDBnltxHsrLf4cFklVUNLdPTrYF8X00hpilkZJ58Wg] systemId=[znbmjqcs] channel=[29816]
//        String appid = "znbmjqcs";
//        String appSecret = "kDBnltxHsrLf4cFklVUNLdPTrYF8X00hpilkZJ58Wg";
//        String username = "znbmjqcs";
//        String systemId = "znbmjqcs";
//        String channel = "29816";
//        System.out.printf(getAccessUrl(appid, appSecret, systemId, username, channel));
//    }

}
