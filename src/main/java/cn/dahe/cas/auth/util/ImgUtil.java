package cn.dahe.cas.auth.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 提取文章图片
 * <AUTHOR>
 *
 */
public class ImgUtil {
	
	private static final Logger logger = LoggerFactory.getLogger(ImgUtil.class);
	
	public static String parseImg(String conent){
		if(StringUtils.isBlank(conent)){
			return null;
		}
		String reg = "<img.*?src=\"(.*?)\".*?>";
		Pattern p = Pattern.compile(reg);
		Matcher m = p.matcher(conent);
		List<String> imgs = new ArrayList<String>();
		String forceimg = "";
		while(m.find()){
			forceimg = m.group(1);
			imgs.add(forceimg);
		}
		if(StringUtils.isBlank(forceimg)){
			return null;
		}
		int zimg = imgRandom(imgs.size());
		if(zimg==imgs.size()){
			return null;
		}
		forceimg = imgs.get(zimg);
		return forceimg;
	}
	
	
	public static int imgRandom(int sum){
		Random ram= new Random();
		int rt = ram.nextInt(sum);
		return rt;
	}

	/**
	 * 将url的http或者https协议去除，以自适应环境
	 * @param url
	 * @return
	 */
	public static String replaceHttp(String url){
		if(StringUtils.isBlank(url)){
			return url;
		}
		return url.replaceAll("^(http|https):","");
	}

	/**
	 * 将url的//替换为正常的协议，是replaceHttp的逆操作
	 * @param url
	 * @return
	 */
	public static String addHttp(String url){
		if(StringUtils.isBlank(url)){
			return url;
		}
		return url.replaceAll("^//","https://");
	}

}
