package cn.dahe.cas.auth.realm;

import org.pac4j.core.store.AbstractStore;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/2/5
 * @createTime 15:01
 * @info 使用reids实现store
 */
@Component
public class RedisStore extends AbstractStore<String,Object> {

    @Resource(name = "externalRedisTemplate")
    private ValueOperations<String,Object> operations;

    public static final String PREFIX = "sso:store:";

    @Override
    protected Object internalGet(String key) {
        return operations.get(getKey(key));
    }

    @Override
    protected void internalSet(String key, Object value) {
        operations.set(getKey(key),value);
    }

    @Override
    protected void internalRemove(String key) {
        operations.getOperations().delete(getKey(key));
    }

    private String getKey(String key){
        return PREFIX+key;
    }
}
