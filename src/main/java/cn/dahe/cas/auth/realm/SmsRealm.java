package cn.dahe.cas.auth.realm;

import cn.dahe.cas.auth.config.<PERSON>ache<PERSON>ey;
import cn.dahe.cas.auth.constants.SmsType;
import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.dto.RoleDto;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.*;
import cn.dahe.cas.auth.util.Sm4Util;
import cn.dahe.cas.auth.util.SsoStringUtil;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * 认证方式：手机号+验证码

 */
@Slf4j
@Component
public class SmsRealm extends AuthorizingRealm {

    private static Logger logger = LoggerFactory.getLogger(SmsRealm.class);

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Value("${cas.server}")
    private String casServer;

    @Value("${test_env}")
    private boolean testEnv;

    @Autowired
    private SiteService siteService;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private VerfiyCodeService verfiyCodeService;

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource(name = "hashTemplate")
    private HashOperations<String, String, RoleDto> cacheTemplate;

    public static final String NAME = "smsRealm";

    public SmsRealm() {
        super();
        setName(NAME);
    }


    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {
        if (principalCollection == null) {
            throw new AuthorizationException("sms PrincipalCollection method argument cannot be null.");
        }
        int uid = (int) getAvailablePrincipal(principalCollection);
        Site oneSite = siteService.getOneSite(casServer);
        //  username 和 siteId查询，区分系统
        List<String> roles = roleService.getSnByUserId(uid, oneSite.getId());
        List<String> permission = new ArrayList();
        if(roles.indexOf("admin")>-1){
            permission = resourceService.getPermission(oneSite.getId());
        }else{
            permission = resourceService.getPermission(uid, oneSite.getId());
        }
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        info.setRoles(new HashSet<>(roles));
        info.setStringPermissions(new HashSet<>(permission));
        return info;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        UsernamePasswordToken upToken = (UsernamePasswordToken) authenticationToken;
        String username = upToken.getUsername();
        String password = new String((char[]) authenticationToken.getCredentials());
        if (username == null) {
            throw new AccountException("用户名不能为空");
        }
        String encryptPhone ="";
        try {
            encryptPhone = Sm4Util.encryptEcb(sm4Key, username);
        } catch (Exception e) {
            e.printStackTrace();
        }
        User user = userService.getUserByPhone(encryptPhone);
        if (user == null) {
            throw new UnknownAccountException("该账号未注册");
        }
        UserFlag flag = user.getFlag();
        switch (flag){
            case FORBID:
                throw new DisabledAccountException("帐号被禁用！");
            default:
                break;
        }
        //验证码校验
        boolean check = false;
        try {
            if (testEnv) {
                check = "6666".equals(password) || verfiyCodeService.isValid(username, password, SmsType.LOGIN);
            } else {
                check = verfiyCodeService.isValid(username, password, SmsType.LOGIN);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!check) {
            throw new UnknownAccountException("验证码错误！");
        }
        //判断用户有效期
        Date now = new Date();
        log.info("-------------->用户：{}，登录的当前时间：{}",user.getUsername(), DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
        if(user.getStartTime() != null && user.getEndTime() != null && (now.getTime() < user.getStartTime().getTime() || now.getTime() > user.getEndTime().getTime())){
            throw new SsoException("账号已过期，请及时续费！");
        }
        //验证码验证成功，一次之后短信即失效
        verfiyCodeService.deleteCode(username, SmsType.LOGIN);
        logger.debug("get authentication info from sms");
        //  用户可通过的站点的角色权限放入缓存,供子系统查询
        List<Site> accessSite = siteService.getAccessSite(user.getUid());
        if (accessSite != null && !accessSite.isEmpty()){
            for (Site site : accessSite) {
                if (SsoStringUtil.isNotBlank(site.getLogTag())){
                    RoleDto userProfileDto = userService.getBySiteAndUid(user.getUid(), site.getId());
                    cacheTemplate.put(CacheKey.USER_ROLE + ":" + site.getLogTag(), user.getUid()+"", userProfileDto);
                }
            }
        }
        //主principal为用户名
        SimpleAuthenticationInfo info = new SimpleAuthenticationInfo(user.getUid(), password, getName());
        return info;
    }
    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof SmsAuthencationToken;
    }
}
