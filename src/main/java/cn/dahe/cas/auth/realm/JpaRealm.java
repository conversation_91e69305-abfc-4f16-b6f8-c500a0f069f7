package cn.dahe.cas.auth.realm;

import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.ResourceService;
import cn.dahe.cas.auth.service.RoleService;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.UserService;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.DisabledAccountException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.ByteSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * 认证方式两种：手机号和用户名
 * 密码存储进行md5加密
 */
@Component
public class JpaRealm extends AuthorizingRealm {

    private static Logger logger = LoggerFactory.getLogger(JpaRealm.class);

    @Value("${cas.server}")
    private String casServer;

    @Autowired
    private SiteService siteService;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private ResourceService resourceService;

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {
        if (principalCollection == null) {
            throw new AuthorizationException("PrincipalCollection method argument cannot be null.");
        }
        String username = (String) getAvailablePrincipal(principalCollection);
        User user = userService.getUserByPhone(username);
        Site oneSite = siteService.getOneSite(casServer);
        //  username 和 siteId查询，区分系统
        List<String> roles = roleService.getSnByUserId(user.getUid(), oneSite.getId());
        List<String> permission = resourceService.getPermission(user.getUid(), oneSite.getId());
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        info.setRoles(new HashSet<>(roles));
        info.setStringPermissions(new HashSet<>(new HashSet<>(permission)));
        return info;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        UsernamePasswordToken upToken = (UsernamePasswordToken) authenticationToken;
        String username = upToken.getUsername();
        if (username == null) {
            throw new AccountException("用户名不能为空");
        }
        //手机号或者用户名
        User user = userService.getUserByUsername(username);
        if (user == null) {
            user = userService.getUserByPhone(username);
            if (user == null) {
                throw new UnknownAccountException("No account found for user [" + username + "]");
            }
        }
        UserFlag flag = user.getFlag();
        switch (flag){
            case FORBID:
                throw new DisabledAccountException("帐号被禁用");
            default:
                break;
        }
        logger.debug("get authentication info from jpa");
        //主principal为用户名
        SimpleAuthenticationInfo info = new SimpleAuthenticationInfo(user.getUsername(), user.getPassword(), getName());
        info.setCredentialsSalt(ByteSource.Util.bytes(user.getSalt()));
        return info;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof AdminAuthencationToken;
    }
}
