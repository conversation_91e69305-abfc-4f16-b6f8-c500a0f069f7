package cn.dahe.cas.auth.realm;

import org.apache.shiro.crypto.AbstractSymmetricCipherService;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.Key;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * on 2021/06/17
 */
public class MySymmetricCipherService extends AbstractSymmetricCipherService {
    protected MySymmetricCipherService(String algorithmName) {
        super(algorithmName);
    }

    public static byte[] generateNewKeyFromSuper() {
        KeyGenerator kg;
        try {
            kg = KeyGenerator.getInstance("AES");
        } catch (NoSuchAlgorithmException var5) {
            String msg = "Unable to acquire AES algorithm.  This is required to function.";
            throw new IllegalStateException(msg, var5);
        }

        kg.init(128);
        SecretKey key = kg.generateKey();
        return key.getEncoded();
    }



    /**
     * 使用shiro官方的生成
     * org.apache.shiro.crypto.AbstractSymmetricCipherService#generateNewKey()
     * @return
     */
    public static byte[] getCipherKey() {
        MySymmetricCipherService mySymmetricCipherService = new MySymmetricCipherService("AES");
        Key gKey = mySymmetricCipherService.generateNewKey();
        return gKey.getEncoded();
    }

}
