package cn.dahe.cas.auth.realm;

import org.apache.shiro.session.Session;
import org.apache.shiro.session.UnknownSessionException;
import org.apache.shiro.session.mgt.eis.AbstractSessionDAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * 基于redis实现shiro的sessionDao
 */
@Component("redisSessionDao")
public class RedisSessionDao extends AbstractSessionDAO{

    @Value("${redis.prefix}")
    private String prefix;

    private static final Logger log = LoggerFactory.getLogger(RedisSessionDao.class);

    public static final String DEFAULT_KEY = "shiro-activeSessionCache";

    private String key = DEFAULT_KEY;

    @Resource(name = "redisTemplate")
    private HashOperations<String,Serializable,Session> redisTemplate;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    @PostConstruct
    public void init(){
        this.key = prefix+this.key;
    }

    @Override
    protected Serializable doCreate(Session session) {
        Serializable sessionId = generateSessionId(session);
        assignSessionId(session,sessionId);
        storeSession(sessionId,session);
        return sessionId;
    }

    private Session storeSession(Serializable id,Session session){
        if(id==null){
            throw new NullPointerException("id argument cannot be null.");
        }
        redisTemplate.put(key,id,session);
        return session;
    }

    @Override
    protected Session doReadSession(Serializable serializable) {
        return redisTemplate.get(key,serializable);
    }

    @Override
    public void update(Session session) throws UnknownSessionException {
        storeSession(session.getId(),session);
    }

    @Override
    public void delete(Session session) {
        if (session == null) {
            throw new NullPointerException("session argument cannot be null.");
        }
        Serializable id = session.getId();
        redisTemplate.delete(key,id);
    }

    @Override
    public Collection<Session> getActiveSessions() {
        return redisTemplate.values(key);
    }
}
