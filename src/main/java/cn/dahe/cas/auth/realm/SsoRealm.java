package cn.dahe.cas.auth.realm;

import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.entity.User_;
import cn.dahe.cas.auth.service.ClearCache;
import cn.dahe.cas.auth.service.ResourceService;
import cn.dahe.cas.auth.service.RoleService;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.Sm4Util;
import io.buji.pac4j.realm.Pac4jRealm;
import io.buji.pac4j.subject.Pac4jPrincipal;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.pac4j.core.profile.CommonProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/23
 * @createTime 10:25
 * @info 支持cas 3.0协议
 */
@Component
public class SsoRealm extends Pac4jRealm implements ClearCache {

    @Value("${cas.server}")
    private String casServer;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    private SiteService siteService;

    @Autowired
    private ResourceService resourceService;

    private static final Logger logger = LoggerFactory.getLogger(SsoRealm.class);

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {
        if (principalCollection == null) {
            throw new AuthorizationException("PrincipalCollection method argument cannot be null.");
        }
        int uid = (int) getAvailablePrincipal(principalCollection);
        Site oneSite = siteService.getOneSite(casServer);
        //  username 和 siteId查询，区分系统
        List<String> roles = roleService.getSnByUserId(uid, oneSite.getId());
        List<String> permission = new ArrayList<>();
        if(roles.indexOf("admin")>-1){
            permission = resourceService.getPermission(oneSite.getId());
        }else{
            permission = resourceService.getPermission(uid, oneSite.getId());
        }
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        info.setRoles(new HashSet<>(roles));
        info.setStringPermissions(new HashSet<>(permission));
        return info;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        AuthenticationInfo info = super.doGetAuthenticationInfo(authenticationToken);
        final PrincipalCollection principals = info.getPrincipals();
        if(principals==null){
            throw new AuthenticationException("PrincipalCollection principals can not be null");
        }
        final Pac4jPrincipal principal = principals.oneByType(Pac4jPrincipal.class);
        CommonProfile profile = principal.getProfile();
        Map<String,Object> attributes = profile.getAttributes();
        int uid = Integer.valueOf(attributes.get(User_.uid.getName()).toString());
        User user = userService.get(uid);
        //todo 解密
        try {
            user.setPhone(Sm4Util.decryptEcb(sm4Key, user.getPhone()));
            user.setTruename(Sm4Util.decryptEcb(sm4Key, user.getTruename()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        SecurityUtils.getSubject().getSession().setAttribute("user",user);
        return new SimpleAuthenticationInfo(user.getUid(),authenticationToken.getCredentials(),getName());
    }

    @Override
    public void clear(int uid){
        logger.info("<<<<<<<<<<<<<<<<<<<<<<start cache");
        if (uid == -1){
            getAuthorizationCache().clear();

        }else {
            String realmName = getName();
            PrincipalCollection principals = new SimplePrincipalCollection(uid, realmName);
            clearCachedAuthorizationInfo(principals);
        }
    }
}
