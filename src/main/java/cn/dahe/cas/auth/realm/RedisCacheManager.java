package cn.dahe.cas.auth.realm;

import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheException;
import org.apache.shiro.cache.CacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 基于redis实现shiro的cachemanger接口
 */
@Component
public class RedisCacheManager implements CacheManager{

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${redis.prefix}")
    private String prefix;

    @Override
    public <K, V> Cache<K, V> getCache(String name) throws CacheException {
        return new RedisCache<>(prefix+name,redisTemplate.opsForHash());
    }

}
