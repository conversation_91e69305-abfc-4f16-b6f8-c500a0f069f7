package cn.dahe.cas.auth.realm;

import cn.dahe.cas.auth.exception.SsoException;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * 基于redis实现shiro cache接口
 */
public class RedisCache<H,HK,HV> implements Cache<HK,HV>{

    private HashOperations<H,HK,HV> hashOperations;
    private H name;

    public RedisCache(H name, HashOperations redisTemplate){
        if(StringUtils.isEmpty(name)){
            throw new SsoException("argument name can not be null");
        }
        this.name = name;
        this.hashOperations = redisTemplate;
    }

    @Override
    public HV get(HK key) throws CacheException {
        return hashOperations.get(name,key);
    }

    @Override
    public HV put(HK key, HV value) throws CacheException {
        HV previous = hashOperations.get(name,key);
        hashOperations.put(name,key,value);
        return previous;
    }

    @Override
    public HV remove(HK key) throws CacheException {
        HV previous = hashOperations.get(name,key);
        hashOperations.delete(name,key);
        return previous;
    }

    @Override
    public void clear() throws CacheException {
        hashOperations.getOperations().delete(name);
    }

    @Override
    public int size() {
        return hashOperations.size(name).intValue();
    }

    @Override
    public Set<HK> keys() {
        Set<HK> keys = hashOperations.keys(name);
        if(!isEmpty(keys)){
            return Collections.unmodifiableSet(keys);
        }
        return Collections.emptySet();
    }

    @Override
    public Collection<HV> values() {
        List<HV> values = hashOperations.values(name);
        if(!isEmpty(values)){
            return Collections.unmodifiableList(values);
        }
        return Collections.emptyList();
    }

    private boolean isEmpty(Collection c){
        return c==null||c.isEmpty();
    }
}
