package cn.dahe.cas.auth.realm;

import org.apache.shiro.mgt.DefaultSessionStorageEvaluator;
import org.apache.shiro.mgt.SessionStorageEvaluator;
import org.apache.shiro.subject.Subject;

/**
 * 预留类，用于无状态认证
 * <AUTHOR>
 */
public class FactorSessionStorgeEvaluator extends DefaultSessionStorageEvaluator{
    @Override
    public boolean isSessionStorageEnabled(Subject subject) {
        return (subject != null && subject.getSession(false) != null) || isSessionStorageEnabled();
    }
}
