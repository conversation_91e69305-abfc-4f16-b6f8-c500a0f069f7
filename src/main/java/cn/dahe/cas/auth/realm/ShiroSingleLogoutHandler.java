package cn.dahe.cas.auth.realm;

import org.apache.shiro.session.Session;
import org.apache.shiro.session.SessionListenerAdapter;
import org.apache.shiro.subject.Subject;
import org.pac4j.cas.logout.CasLogoutHandler;
import org.pac4j.core.context.WebContext;
import org.pac4j.core.context.session.SessionStore;
import org.pac4j.core.profile.ProfileManager;
import org.pac4j.core.store.Store;
import org.pac4j.core.util.CommonHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/2/5
 * @createTime 10:53
 * @info 由于pac4j并未实现对shiro环境下单点退出功能，因此进行该实现
 * 实现原理为在进行service ticket的校验时，记录service tickete与shiro
 * session id之间的对应关系，在系统接收到退出通知时，根据该对应关系找到
 * shiro session id，通过session id构建subject，执行subject退出操作
 * 目前对于非native形式的session会有问题
 * 另外store使用Guava进行存储，重启或者缓存数量过多时会造成对应关系丢失
 * session过期或者主动销毁后进行store清理
 */
@Component
public class ShiroSingleLogoutHandler<C extends WebContext> extends SessionListenerAdapter implements CasLogoutHandler<C> {

    private static final Logger logger = LoggerFactory.getLogger(ShiroSingleLogoutHandler.class);

    @Autowired
    private Store<String,Object> store;

    private boolean destroySession = true;

    public static final String SERVICE_TICKET_KEY = "st";

    /**
     * 将shiro session id与cas ticket进行关联
     * 直接将关联关系存储到guava中，该机制存在问题
     * 需要设计一种完善的对应关系存储机制
     *
     * @param context
     * @param ticket
     */
    @Override
    public void recordSession(C context, String ticket) {
        final SessionStore sessionStore = context.getSessionStore();
        if (sessionStore == null) {
            logger.debug("无sessionStore可用");
            return;
        }
        //shiro生成的sessionId，shiro生成session id为Serializable类型，此处直接转成string，也会有潜在问题
        final String sessionId = sessionStore.getOrCreateSessionId(context);
        store.set(ticket, sessionId);
        //在session里放入ticketId方便删除
        sessionStore.set(context, SERVICE_TICKET_KEY, ticket);
    }

    /**
     * 不支持前端单点退出
     *
     * @param context
     * @param ticket
     */
    @Override
    public void destroySessionFront(C context, String ticket) {
        store.remove(ticket);

        final SessionStore sessionStore = context.getSessionStore();
        if (sessionStore == null) {
            logger.error("No session store available for this web context");
        } else {
            final String currentSessionId = sessionStore.getOrCreateSessionId(context);
            logger.debug("currentSessionId: {}", currentSessionId);
            final String sessionToTicket = (String) store.get(currentSessionId);
            logger.debug("-> ticket: {}", ticket);
            store.remove(currentSessionId);

            if (CommonHelper.areEquals(ticket, sessionToTicket)) {
                destroy(context, sessionStore, "front");
            } else {
                logger.error("The user profiles (and session) can not be destroyed for CAS front channel logout because the provided "
                        + "ticket is not the same as the one linked to the current session");
            }
        }
    }

    /**
     * 在shiro环境下，直接根据存储的session id与ticket管理关系找到subject，执行退出操作
     *
     * @param context
     * @param ticket
     */
    @Override
    public void destroySessionBack(C context, String ticket) {
        //此处获取到sessionId
        final Object trackableSession = store.get(ticket);
        logger.debug("ticket: {} -> trackableSession: {}", ticket, trackableSession);
        if (trackableSession == null) {
            logger.error("No trackable session found for back channel logout. Either the session store does not support to track session "
                    + "or it has expired from the store and the store settings must be updated (expired data)");
        } else {
            store.remove(ticket);
            try {
                Subject subject = new Subject.Builder().sessionId(trackableSession.toString()).buildSubject();
                subject.logout();
            } catch (Exception e) {
                e.printStackTrace();
            }
            // renew context with the original session store
            final SessionStore sessionStore = context.getSessionStore();
            if (sessionStore == null) {
                logger.error("No session store available for this web context");
            } else {
                final SessionStore<C> newSessionStore = sessionStore.buildFromTrackableSession(context, trackableSession);
                if (newSessionStore != null) {
                    logger.debug("newSesionStore: {}", newSessionStore);
                    context.setSessionStore(newSessionStore);
                    final String sessionId = newSessionStore.getOrCreateSessionId(context);
                    logger.debug("remove sessionId: {}", sessionId);
                    String st = sessionStore.get(context, SERVICE_TICKET_KEY).toString();
                    store.remove(st);
                    destroy(context, newSessionStore, "back");
                } else {
                    logger.error("The session store should be able to build a new session store from the tracked session");
                }
            }
        }
    }

    /**
     * 重新创建session
     *
     * @param oldSessionId
     * @param context
     */
    @Override
    public void renewSession(String oldSessionId, C context) {
        final String ticket = (String) store.get(oldSessionId);
        logger.debug("oldSessionId: {} -> ticket: {}", oldSessionId, ticket);
        if (ticket != null) {
            store.remove(ticket);
            store.remove(oldSessionId);
            recordSession(context, ticket);
        }
    }

    protected void destroy(final C context, final SessionStore sessionStore, final String channel) {
        // remove profiles
        final ProfileManager manager = new ProfileManager(context);
        manager.logout();
        logger.debug("destroy the user profiles");
        // and optionally the web session
        if (destroySession) {
            logger.debug("destroy the whole session");
            final boolean invalidated = sessionStore.destroySession(context);
            if (!invalidated) {
                logger.error("The session has not been invalidated for {} channel logout", channel);
            }
        }
    }

    @Override
    public void onStop(Session session) {
        String st = session.getAttribute(SERVICE_TICKET_KEY).toString();
        store.remove(st);
    }

    @Override
    public void onExpiration(Session session) {
        this.onStop(session);
    }

    public boolean isDestroySession() {
        return destroySession;
    }

    public void setDestroySession(boolean destroySession) {
        this.destroySession = destroySession;
    }
}
