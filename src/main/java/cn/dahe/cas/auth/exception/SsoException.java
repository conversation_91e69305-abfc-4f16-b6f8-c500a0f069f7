package cn.dahe.cas.auth.exception;

import cn.dahe.cas.auth.constants.ErrorCode;

/**
 * <AUTHOR>
 * sso系统的根异常
 */
public class SsoException extends RuntimeException{

    /**
     * sso系统错误码，所有sso的异常都在该枚举中进行定义
     */
    private ErrorCode code = ErrorCode.NORMAL;

    public SsoException(){
        super();
    }

    public SsoException(String message){
        super(message);
    }

    public SsoException(Throwable t){
        super(t);
    }

    public SsoException(ErrorCode code){
        this(code.getCodeMsg());
        this.code = code;
    }

    public SsoException(String message,Throwable t){
        super(message,t);
    }

    public ErrorCode getCode() {
        return code;
    }

    public void setCode(ErrorCode code) {
        this.code = code;
    }
}
