package cn.dahe.cas.auth.exception;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/16
 * @createTime 13:35
 * @info 无站点访问权限
 */
public class SitePermissionLackException extends SsoException{

    public SitePermissionLackException(){
        super("无站点访问权限");
    }

    public SitePermissionLackException(String msg){
        super(msg);
    }

    public SitePermissionLackException(Throwable t){
        super(t);
    }
}
