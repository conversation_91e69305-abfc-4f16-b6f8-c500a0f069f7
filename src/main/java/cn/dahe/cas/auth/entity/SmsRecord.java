package cn.dahe.cas.auth.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 短信发送记录表
 * <AUTHOR>
 *
 */
@Entity
@Table(name="sms_record")
public class SmsRecord {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;
	private String phone;
	private String ip;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="send_date")
	private Date  sendDate;
	/**
	 * 短信发送用户名
	 */
	private String username;
	

	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getIp() {
		return ip;
	}
	public void setIp(String ip) {
		this.ip = ip;
	}

	public Date getSendDate() {
		return sendDate;
	}
	public void setSendDate(Date sendDate) {
		this.sendDate = sendDate;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public SmsRecord() {
		super();
	}
	public SmsRecord(int id, String phone, String ip, Date sendDate,
					 String username, String ua) {
		super();
		this.id = id;
		this.phone = phone;
		this.ip = ip;
		this.sendDate = sendDate;
		this.username = username;
	}

	public SmsRecord(int id, String phone, String ip, Date sendDate,
					 String username, String ua, int type) {
		super();
		this.id = id;
		this.phone = phone;
		this.ip = ip;
		this.sendDate = sendDate;
		this.username = username;
	}
	
	
	
	

}
