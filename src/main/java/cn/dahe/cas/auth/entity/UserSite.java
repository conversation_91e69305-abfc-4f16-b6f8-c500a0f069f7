package cn.dahe.cas.auth.entity;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/16
 * @createTime 15:33
 * @info 用户可访问站点对应表，存储禁用站点还是允许站点需根据实际情况进行处理
 */
@Entity
@Table(name = "user_site")
public class UserSite{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "user_id")
    private int userId;

    @Column(name = "site_id")
    private int siteId;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getSiteId() {
        return siteId;
    }

    public void setSiteId(int siteId) {
        this.siteId = siteId;
    }
}
