package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.LogType;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * 该日志需要管理所有子系统的日志，因此需要加入日志来源字段
 * 前后端的用户id可能重复，添加标志位进行区分 2018/3/16
 */
@Entity
@Table(name = "log_system")
public class Log{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    private String source;

    /**
     * 日志类型
     */
    @Enumerated
    private LogType type;
    /**
     * 日志标题
     */
    private String title;
    /**
     * 请求地址
     */
    @Column(name = "remote_address")
    private String remoteAddress;
    /**
     * URI
     */
    @Column(name = "request_uri")
    private String requestUri;
    /**
     * 请求方式
     */
    private String method;
    /**
     * 提交参数
     */
    @Lob
    @Basic(fetch = FetchType.EAGER)
    @Column(name = "paramters", columnDefinition = "TEXT", nullable = true)
    private String paramters;
    /**
     * 异常
     */
    @Column(length = 500)
    private String exception;
    /**
     * 操作开始时间
     */
    @Column(name = "operate_data")
    private Date operateDate;
    /**
     * 操作结束时间
     */
    private long timeout;
    /**
     * 操作用户ID
     */
    @Column(name = "user_id")
    private int uid;

    private String username;

    private String os;

    private String ua;

    /**
     * 用户标志位 0后端 1前端
     */
    @Column(name = "front_back_flag",columnDefinition = "INT DEFAULT 0")
    private int frontbackflag;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public LogType getType() {
        return type;
    }

    public void setType(LogType type) {
        this.type = type;
    }

    public String getTitle() {
        return StringUtils.isBlank(title) ? title : title.trim();
    }
    public void setTitle(String title) {
        this.title = title;
    }


    public String getRequestUri() {
        return StringUtils.isBlank(requestUri) ? requestUri : requestUri.trim();
    }
    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }


    public String getMethod() {
        return StringUtils.isBlank(method) ? method : method.trim();
    }
    public void setMethod(String method) {
        this.method = method;
    }


    public String getException() {
        return StringUtils.isBlank(exception) ? exception : exception.trim();
    }
    public void setException(String exception) {
        this.exception = exception;
    }


    public Date getOperateDate() {
        return operateDate;
    }
    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public long getTimeout() {
        return timeout;
    }

    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRemoteAddress() {
        return remoteAddress;
    }

    public void setRemoteAddress(String remoteAddress) {
        this.remoteAddress = remoteAddress;
    }

    public String getParamters() {
        return paramters;
    }

    public void setParamters(String paramters) {
        this.paramters = paramters;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public int getFrontbackflag() {
        return frontbackflag;
    }

    public void setFrontbackflag(int frontbackflag) {
        this.frontbackflag = frontbackflag;
    }
}
