package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.converter.UserFlagConverter;

import javax.persistence.*;

/**
 * <AUTHOR>
 * on 2018/3/28
 */
@Entity
@Table(name = "log_source")
public class LogSource {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    /**
     * 来源
     */
    private String source;
    /**
     * 描述
     */
    private String description;
    /**
     * 状态
     */
    @Convert(converter = UserFlagConverter.class)
    @Column(columnDefinition = "tinyint")
    private UserFlag status = UserFlag.ALLOW;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UserFlag getStatus() {
        return status;
    }

    public void setStatus(UserFlag status) {
        this.status = status;
    }
}
