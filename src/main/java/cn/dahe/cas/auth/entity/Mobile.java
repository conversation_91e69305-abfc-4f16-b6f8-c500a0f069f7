package cn.dahe.cas.auth.entity;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Entity
@Table(name="mobile")
public class Mobile implements Serializable{

	private static final long serialVersionUID = 8479010594072122285L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 手机省份
	 */
	private String province;
	/**
	 * 手机所需城市
	 */
	private String city;
	/**
	 * 手机所在区域代码
	 */
	@Column(name="area_code")
	private String areaCode;
	/**
	 * 手机所在区域邮编
	 */
	@Column(name="post_code")
	private String postCode;

	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getPostCode() {
		return postCode;
	}
	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
}
