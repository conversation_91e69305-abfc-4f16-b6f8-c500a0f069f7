package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.annotion.PhoneNumber;
import cn.dahe.cas.auth.annotion.Sensitive;
import cn.dahe.cas.auth.annotion.Username;
import cn.dahe.cas.auth.constants.Sex;
import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.constants.UserType;
import cn.dahe.cas.auth.converter.UserFlagConverter;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.URL;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 敏感信息加密方案：
 * 加密信息
 * 手机号(phone)|真实姓名(truename)
 * 用户名兼容方案
 * 用户名直接复制于真实姓名，且作为后续一切对外展示元素
 */

@ApiModel("客户或管理员表单信息")
@Entity
@Table(name = "user")
@Data
public class User implements Serializable {

    protected static final long serialVersionUID = 3100111186246830766L;

    @ApiModelProperty(value = "用户id(修改时必填)",required = false,position = 0)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected int uid;

    @ApiModelProperty(value = "账户名称",required = true,position = 1)
    @Sensitive(message = "包含敏感词")
    @Username(message = "用户名不能以数字开头")
    protected String username;

    @ApiModelProperty(value = "账户密码",required = false,position = 2)
    @JSONField(serialize = false)
    protected String password;

    @ApiModelProperty(value = "手机号",required = true,position = 3)
    @PhoneNumber()
    protected String phone;

    @ApiModelProperty(value = "头像",required = false,position = 4)
    @URL(message = "头像格式不正确")
    protected String icon;

    @ApiModelProperty(value = "加密因子",required = false,position = 5)
    @JSONField(serialize = false)
    @Column(name = "authentication")
    protected String salt;

    @ApiModelProperty(value = "性别",required = false,position = 6)
    @Column(columnDefinition = "tinyint")
    @Enumerated
    protected Sex sex = Sex.MAN;

    @ApiModelProperty(value = "邮箱",required = false,position = 7)
    @Email
    protected String email;

    @ApiModelProperty(value = "注册时间",required = false,position = 8)
    @CreationTimestamp
    @Column(name = "create_time", updatable = false)
    protected Date createTime;

    @ApiModelProperty(value = "修改时间",required = false,position = 9)
    @UpdateTimestamp
    @Column(name = "update_time")
    protected Date updateTime;

    @ApiModelProperty(value = "状态:0正常，1禁用，-1删除",required = false,position = 10)
    @Convert(converter = UserFlagConverter.class)
    @Column(columnDefinition = "tinyint")
    protected UserFlag flag = UserFlag.ALLOW;

    @ApiModelProperty(value = "真实姓名",required = false,position = 11)
    @Sensitive(message = "包含敏感词")
    @Username(message = "真实名不能以数字开头")
    @Column(name = "true_name")
    protected String truename;

    @ApiModelProperty(value = "站点id",required = false,position = 12)
    @Column(name = "department")
    protected String sid;

    @ApiModelProperty(value = "组织机构-->部门",required = false,position = 13)
    @Column(name = "organization", columnDefinition = "INT DEFAULT 0")
    protected int organization = 0;

    @ApiModelProperty(value = "职位",required = false,position = 14)
    protected int job = 0;

    @ApiModelProperty(value = "微信唯一标识码",required = false,position = 15)
    private String wxOpenId;

    @ApiModelProperty(value = "区域编码",required = false,position = 16)
    @Column(name = "area_code")
    private String areaCode;

    @ApiModelProperty(value = "同步id",required = false,position = 17)
    @Column(name = "synchronize_id")
    private String synchronizeId;

    @ApiModelProperty(value = "用户类型：0普通客户，1管理员",required = false,position = 18)
    @Column(name = "type", columnDefinition = "INT DEFAULT 0")
    protected UserType type = UserType.CUSTOMER;

    @ApiModelProperty(value = "工作单位",required = false,position = 19)
    private String company;

    @ApiModelProperty(value = "开始时间")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "start_time")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "end_time")
    private Date endTime;

    @ApiModelProperty(value = "商务经理",required = false,position = 19)
    @Column(name = "business_manager")
    private String businessManager;

    @ApiModelProperty(value = "商务经理电话",required = false,position = 19)
    @Column(name = "business_manager_phone")
    private String businessManagerPhone;

    @ApiModelProperty(value = "应用id")
    @Column(name = "md_app_id")
    private String mdAppId;

    @ApiModelProperty(value = "应用秘钥")
    @Column(name = "md_app_secret")
    private String mdAppSecret;

    @ApiModelProperty(value = "系统id")
    @Column(name = "md_app_system_id")
    private String mdAppSystemId;

    @ApiModelProperty(value = "账号")
    @Column(name = "md_app_username")
    private String mdAppUsername;

    @ApiModelProperty(value = "频道")
    @Column(name = "md_app_channel")
    private String mdAppChannel;

    @ApiModelProperty(value = "来源类型")
    @Lob
    @Basic(fetch = FetchType.EAGER)
    @Column(name = "from_type", columnDefinition = "TEXT", nullable = true)
    private String fromType;

    @ApiModelProperty(value = "风评ai交互标识")
    @Column(name = "user_applications")
    @JsonIgnore
    private int userApplications;

    @Lob
    @Column(name = "third_account_info", columnDefinition = "TEXT", nullable = true)
    private String thirdAccountInfo;

    @Transient
    private int isEffect = 0;

    @Transient
    private String permissions;
}
