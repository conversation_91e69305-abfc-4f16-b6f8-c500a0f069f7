package cn.dahe.cas.auth.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "md_user_zn")
public class RemoteUserZn implements Serializable {

    protected static final long serialVersionUID = 3100111186246830766L;

    @Id
    @Column(name = "app_id")
    private String appId;
    @Column(name = "app_secret")
    private String appSecret;

    @Column(name = "system_id")
    private String systemId;

    @Column(name = "username")
    private String username;

    @Column(name = "name")
    private String name;

    @Column(name = "channel")
    private String channel;

    @Column(name = "phone")
    private String phone;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public String toString() {
        return "RemoteUserZn{" +
                "appId='" + appId + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", systemId='" + systemId + '\'' +
                ", username='" + username + '\'' +
                ", name='" + name + '\'' +
                ", channel='" + channel + '\'' +
                ", phone='" + phone + '\'' +
                '}';
    }
}
