package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.annotion.IpAddress;
import org.hibernate.validator.constraints.URL;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * on 2018/6/11.
 */
@Entity
@Table(name = "forbid_ip")
public class ForbidIp {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @IpAddress(message = "ip地址格式不正确")
    @Column(name = "ip_address")
    private String ipAddress;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
