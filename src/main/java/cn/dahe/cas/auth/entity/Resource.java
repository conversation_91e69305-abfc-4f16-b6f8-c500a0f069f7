package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.Status;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "resource")
@Data
@ApiModel(value = "Resource",description = "权限实体")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Resource implements Serializable{

    private static final long serialVersionUID = 7055956632567377598L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "上级权限id")
    @Column(columnDefinition = "INT DEFAULT 0")
    @Min(value = 0,message = "上级权限id不能小于0")
    private int pid;

    @ApiModelProperty(value = "上级权限名称")
    private String pname;

    @ApiModelProperty(value = "授权字符")
    @NotBlank(message = "授权字符不能为空")
    @Column(length = 50)
    private String permission;

    @ApiModelProperty(value = "授权路径")
    private String url;

    @ApiModelProperty(value = "状态")
    @Enumerated(EnumType.ORDINAL)
    @Column(columnDefinition = "tinyint")
    private Status status = Status.ALLOW;

    @ApiModelProperty(name = "siteId",value = "系统id")
    @Column(name = "site_id",columnDefinition = "INT DEFAULT 0")
    @Min(value = 1, message = "请传入站点id")
    private int siteId;

    @ApiModelProperty(name = "siteName",value = "系统名字")
    @Column(name = "site_name")
    private String siteName;

    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time",updatable = false)
    private Date createTime;

    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "创建用户id")
    @Column(name = "create_user_id",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int createUserId;

    @ApiModelProperty(value = "最后修改时间")
    @UpdateTimestamp
    @Column(name = "modify_time")
    private Date modifyTime;

    @LastModifiedBy
    @ApiModelProperty(value = "最后修改人Id")
    @Column(name = "modify_user_id",columnDefinition = "INT DEFAULT 0")
    private int modifyUserId;

}
