package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.Status;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Date;

/**
 * 重点人物
 * 前台填报
 * 后台进行查阅和操作
 */
@Entity
@Table(name = "service_consult")
@Data
@ApiModel(description = "重点人物")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceConsult implements Serializable{


    @ApiModelProperty(value = "ID",required = false,position = 0)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @ApiModelProperty(value = "咨询类型",required = false,position = 1)
    private int type;

    @ApiModelProperty(value = "咨询单位",position = 2)
    private String unit;

    @ApiModelProperty(value = "咨询人",required = false,position = 3)
    @Column(name = "consult_user_id")
    private int consultUserId;

    @ApiModelProperty(value = "咨询人姓名",required = false,position = 3)
    @Column(name = "consult_user_name")
    private String consultUserName;

    @ApiModelProperty(value = "咨询人联系方式",required = false,position = 4)
    @Column(name = "consult_user_link")
    private String consultUserLink;

    @ApiModelProperty(value = "状态",required = false,position = 5)
    @Enumerated(EnumType.ORDINAL)
    @Column(columnDefinition = "tinyint")
    private Status status = Status.ALLOW;

    @ApiModelProperty(value = "咨询时间",required = false,position = 6)
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @Column(name = "consult_time",updatable = true)
    private Date consultTime;

    @ApiModelProperty(value = "咨询次数",required = false,position = 7)
    @Column(columnDefinition = "1")
    private int count;
}
