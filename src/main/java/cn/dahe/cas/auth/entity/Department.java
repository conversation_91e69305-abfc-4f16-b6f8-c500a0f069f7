package cn.dahe.cas.auth.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "department")
public class Department {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(name = "id", value = "id")
    private int id;
    /**
     * 组织名称
     */
    @Column(name = "name")
    @ApiModelProperty(name = "name", value = "组织名称", required = true)
    private String name = "";

    /**
     * 联系电话
     */
    @Column(name = "phone")
    @ApiModelProperty(name = "phone", value = "联系电话")
    private String phone = "";
    /**
     * 职能描述
     */
    @Lob
    @Basic(fetch = FetchType.EAGER)
    @Column(columnDefinition = "TEXT")
    @ApiModelProperty(name = "text", value = "组织机构职能描述")
    private String text = "";

    /**
     * 父id
     */
    @Column(name = "pid", columnDefinition = "INT DEFAULT 0")
    @ApiModelProperty(name = "pid", value = "pid")
    private int pid;

    /**
     * 状态码 1--正常，-1--删除
     */
    @Column(columnDefinition = "TINYINT")
    @ApiModelProperty(name = "status", value = "部门状态")
    private int status = 1;
    /**
     * 排序
     */
    @Column(name = "seq", columnDefinition = "INT DEFAULT 0")
    @ApiModelProperty(name = "seq", value = "部门排序")
    private int seq = 0;
    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createDate", value = "创建时间")
    private Date createDate ;
}
