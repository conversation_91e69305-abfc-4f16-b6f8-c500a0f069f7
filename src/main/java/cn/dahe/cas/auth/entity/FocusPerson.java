package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.Status;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.SafeHtml;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Date;

/**
 * 重点人物
 * 前台填报
 * 后台进行查阅和操作
 */
@Entity
@Table(name = "focus_person")
@Data
@ApiModel(description = "重点人物")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FocusPerson implements Serializable{


    @ApiModelProperty(value = "ID",required = false,position = 0)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @ApiModelProperty(value = "重点人物姓名",required = true,position = 1)
    @Column(nullable = false)
    @NotBlank(message = "重点人物姓名不能为空")
    private String name;

    @ApiModelProperty(value = "职务",required = false,position = 2)
    private String duty;

    @ApiModelProperty(value = "地域关键词",required = false,position = 3)
    @Column(name = "area_keywords", length = 500)
    private String areaKeywords;

    @ApiModelProperty(value = "主体关键词",required = false,position = 4)
    @Column(name = "subject_keywords", length = 500)
    private String subjectKeywords;

    @ApiModelProperty(value = "事件关键词",required = false,position = 5)
    @Column(name = "event_keywords", length = 500)
    private String eventKeywords;

    @ApiModelProperty(value = "状态",required = false,position = 6)
    @Enumerated(EnumType.ORDINAL)
    @Column(columnDefinition = "tinyint")
    private Status status = Status.ALLOW;


    @ApiModelProperty(value = "创建时间",required = false,position = 7)
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @Column(name = "create_time",updatable = false)
    private Date createTime;

    @ApiModelProperty(value = "创建人姓名",required = false,position = 7)
    @Column(name = "create_user_name")
    private String createUserName;

    @ApiModelProperty(value = "创建用户id",required = false,position = 8)
    @Column(name = "create_user_id",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int createUserId;

    @ApiModelProperty(value = "添加单位",required = false,position = 9)
    @Column(name = "create_unit")
    private String createUnit;

    @ApiModelProperty(value = "最后修改时间",required = false,position = 10)
    @UpdateTimestamp
    @Column(name = "modify_time")
    private Date modifyTime;

    @LastModifiedBy
    @ApiModelProperty(value = "最后修改人Id",required = false,position = 11)
    @Column(name = "modify_user_id",columnDefinition = "INT DEFAULT 0")
    private int modifyUserId;
}
