package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.LoginType;
import cn.dahe.cas.auth.converter.LoginTypeDbConverter;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: 杨振雨
 * @Date: 2019/7/8 14:48
 * 存储到非关系型数据库更合适
 * @Description:
 */
@Entity
@Table(name = "log_login")
public class LoginLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    /**
     * 登录类型，考虑使用枚举值
     */
    @Convert(converter = LoginTypeDbConverter.class)
    private LoginType type;

    /**
     * 登录标识，不同登录方式所用标识不同，手机号登录对应手机号，微信登录对应openId
     */
    private String identify;

    /**
     * 登录用户id
     */
    private int uid;

    /**
     * 冗余手机号字段
     */
    private String phone;

    /**
     * 冗余姓名字段
     */
    private String username;

    /**
     * 附加信息，不如mongodb自然
     */
    private String extras;

    private int site;

    @Column(name = "site_name")
    private String siteName;

    @Column(name = "site_url")
    private String siteUrl;

    /**
     * 登录时间
     */
    @CreatedDate
    @Column(name = "login_time")
    private Date createDate;

    @Column(name = "login_address",length = 128)
    private String loginAddress;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public LoginType getType() {
        return type;
    }

    public void setType(LoginType type) {
        this.type = type;
    }

    public String getIdentify() {
        return identify;
    }

    public void setIdentify(String identify) {
        this.identify = identify;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getExtras() {
        return extras;
    }

    public void setExtras(String extras) {
        this.extras = extras;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getLoginAddress() {
        return loginAddress;
    }

    public void setLoginAddress(String loginAddress) {
        this.loginAddress = loginAddress;
    }

    public int getSite() {
        return site;
    }

    public void setSite(int site) {
        this.site = site;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteUrl() {
        return siteUrl;
    }

    public void setSiteUrl(String siteUrl) {
        this.siteUrl = siteUrl;
    }
}
