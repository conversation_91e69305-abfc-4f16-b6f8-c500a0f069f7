package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.UserVipDurationStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "user_vip_duration")
@Data
@ApiModel(description = "会员周期表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserVipDuration implements Serializable{

    private static final long serialVersionUID = -3889791850249462576L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @ApiModelProperty(value = "客户ID")
    @NotBlank(message = "客户ID不能为空")
    @Column(name = "customer_id",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int customerId;

    @ApiModelProperty(value = "站点ID")
    @NotBlank(message = "站点ID不能为空")
    @Column(name = "site_id",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int siteId;

    @ApiModelProperty(value = "角色ID")
    @NotBlank(message = "角色ID不能为空")
    @Column(name = "role_id",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int roleId;

    @Column(columnDefinition = "tinyint")
    @Enumerated
    protected UserVipDurationStatus status = UserVipDurationStatus.NORMAL;

    @ApiModelProperty(value = "最早开始时间")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @Column(name = "min_start_time")
    private Date minStartTime;

    @ApiModelProperty(value = "最迟结束时间")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @Column(name = "max_end_time")
    private Date maxEndTime;

    @ApiModelProperty(value = "创建时间")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @Column(name = "create_time",updatable = false)
    private Date createTime;

    @ApiModelProperty(value = "最后修改时间")
    @UpdateTimestamp
    @Column(name = "update_time")
    private Date updateTime;

    @ApiModelProperty(value = "最后修改人Id")
    @LastModifiedBy
    @Column(name = "update_uid",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int updateUid;

    @Transient
    private String updateUsername;
    @Transient
    private String customerName;
    @Transient
    private String siteName;
    @Transient
    private String roleName;

}
