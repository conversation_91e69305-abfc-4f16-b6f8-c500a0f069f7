package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.annotion.PhoneNumber;
import org.hibernate.validator.constraints.URL;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * on 2018/6/11.
 */
@Entity
@Table(name = "forbid_mobile")
public class ForbidMobile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @PhoneNumber(message = "手机号格式不正确")
    private String phone;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
