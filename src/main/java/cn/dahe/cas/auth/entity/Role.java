package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.Status;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.SafeHtml;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "role")
@Data
@ApiModel(description = "角色实体")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Role implements Serializable{

    private static final long serialVersionUID = -3889791850249462576L;

    @ApiModelProperty(value = "角色ID",required = false,position = 0)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @ApiModelProperty(value = "角色名称",required = true,position = 1)
    @Column(nullable = false)
    @NotBlank(message = "角色名称不能为空")
    @SafeHtml(message = "角色名称含有非法字符",whitelistType = SafeHtml.WhiteListType.NONE)
    private String name;

    @ApiModelProperty(value = "sn",required = false,position = 2)
    @Column(name = "sn", length = 50)
    private String sn;

    @ApiModelProperty(value = "状态",required = false,position = 3)
    @Enumerated(EnumType.ORDINAL)
    @Column(columnDefinition = "tinyint")
    private Status status = Status.ALLOW;

    @ApiModelProperty(value = "系统id",required = true,position = 4)
    @Column(name = "site_id",columnDefinition = "INT DEFAULT 0")
    @Min(value = 1, message = "请传入站点id")
    private int siteId;

    @ApiModelProperty(value = "系统名字",required = false,position = 5)
    @Column(name = "site_name")
    private String siteName;

    @ApiModelProperty(value = "创建时间",required = false,position = 6)
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @Column(name = "create_time",updatable = false)
    private Date createTime;

    @ApiModelProperty(value = "创建人姓名",required = false,position = 7)
    @Column(name = "create_user_name")
    private String createUserName;

    @ApiModelProperty(value = "创建用户id",required = false,position = 8)
    @Column(name = "create_user_id",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int createUserId;

    @ApiModelProperty(value = "最后修改时间",required = false,position = 9)
    @UpdateTimestamp
    @Column(name = "modify_time")
    private Date modifyTime;

    @LastModifiedBy
    @ApiModelProperty(value = "最后修改人Id",required = false,position = 10)
    @Column(name = "modify_user_id",columnDefinition = "INT DEFAULT 0")
    private int modifyUserId;

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof Role){
            Role temp = (Role) obj;
            return name.equals(temp.getName())&&temp.getId()==id;
        }
        return super.equals(obj);
    }
}
