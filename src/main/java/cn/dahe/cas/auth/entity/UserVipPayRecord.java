package cn.dahe.cas.auth.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "user_vip_pay_record")
@Data
@ApiModel(description = "用户充值记录")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserVipPayRecord implements Serializable{

    private static final long serialVersionUID = -3889791850249462576L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @ApiModelProperty(value = "创建人ID")
    @NotBlank(message = "创建人ID不能为空")
    @Column(name = "create_uid",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int createUid;

    @ApiModelProperty(value = "客户ID")
    @NotBlank(message = "客户ID不能为空")
    @Column(name = "customer_id",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int customerId;

    @ApiModelProperty(value = "充值金额")
    @Column(name = "money",columnDefinition = "DOUBLE DEFAULT 0")
    @Min(0)
    private double money;

    @ApiModelProperty(value = "开始时间")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "start_time")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "end_time")
    private Date endTime;

    @ApiModelProperty(value = "创建时间")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @Column(name = "create_time",updatable = false)
    private Date createTime;

    @ApiModelProperty(value = "最后修改时间")
    @UpdateTimestamp
    @Column(name = "update_time")
    private Date updateTime;

    @ApiModelProperty(value = "最后修改人Id")
    @LastModifiedBy
    @Column(name = "update_uid",columnDefinition = "INT DEFAULT 0")
    @Min(0)
    private int updateUid;

    @Transient
    private String updateUsername;
    @Transient
    private String createUsername;
    @Transient
    private String customerName;

}
