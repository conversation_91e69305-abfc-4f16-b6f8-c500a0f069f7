package cn.dahe.cas.auth.entity;

import cn.dahe.cas.auth.constants.SiteDelete;
import cn.dahe.cas.auth.constants.SiteStatus;
import cn.dahe.cas.auth.constants.Status;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.URL;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

/**
 * sso管理的站点，可以认为是sso client
 * <AUTHOR>
 */
@ApiModel("sso管理的站点")
@Entity
@Table(name = "site")
@Data
public class Site {

    @ApiModelProperty(value = "站点ID",required = false,position = 0)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    /**
     * 站点名称
     */
    @ApiModelProperty(value = "站点名称",required = true,position = 1)
    @NotBlank
    private String name;

    /**
     * 站点地址，完整的站点域名，除80端口外需完整的端口
     */
    @ApiModelProperty(value = "站点地址",required = true,position = 2)
    @URL
    private String url;

    /**
     * 首页地址
     */
    @ApiModelProperty(value = "首页地址",required = false,position = 3)
    @Column(name = "index_url")
    @URL
    private String indexUrl;

    @ApiModelProperty(value = "创建时间",required = false,position = 4)
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    @Column(name = "create_time",updatable = false)
    private Date createTime;

    @ApiModelProperty(value = "站点描述性信息",required = true,position = 5)
    private String description;

    @ApiModelProperty(value = "系统图标",required = false,position = 6)
    @Column(name = "image_url")
    private String imageUrl;

    @ApiModelProperty(value = "是否显示",required = false,position = 7)
    @Enumerated(EnumType.ORDINAL)
    @Column(columnDefinition = "tinyint",name = "is_show")
    private Status isShow = Status.ALLOW;

    @ApiModelProperty(value = "排序（权重）",required = false,position = 8)
    private int weight = 0;

    @ApiModelProperty(value = "站点状态",required = false,position = 9)
    @Enumerated(EnumType.ORDINAL)
    @Column(columnDefinition = "tinyint")
    private SiteStatus status = SiteStatus.ALLOW;

    @ApiModelProperty(value = "站点是否可删除",required = false,position = 10)
    @Enumerated
    @Column(columnDefinition = "tinyint",name = "is_delete")
    private SiteDelete isdelete = SiteDelete.ALLOW;

    @ApiModelProperty(value = "日志标签",required = false,position = 11)
    @Column(name = "log_tag")
    private String logTag;

    @Override
    public String toString(){
        return new ToStringBuilder(this)
                .append("id",id)
                .append("name",name)
                .append("url",url)
                .toString();
    }
}
