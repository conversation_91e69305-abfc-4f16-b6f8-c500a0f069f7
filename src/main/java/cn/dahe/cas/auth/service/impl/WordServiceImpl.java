package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.service.WordService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class WordServiceImpl implements WordService{

    private List<String> sensitivesWords = new ArrayList<>();
    private List<String> keyWords = new ArrayList<>();

    @Value("${keys}")
    private String keys;

    @Value("${keyword}")
    private String keyWord;

    @PostConstruct
    public void init(){
        String[] all = StringUtils.delimitedListToStringArray(keys,"&");
        sensitivesWords = Arrays.asList(all);
        keyWords = Arrays.asList(StringUtils.delimitedListToStringArray(keyWord,"&"));
    }

    /**
     * 判断是否是敏感词
     * @param word
     * @return
     */
    @Override
    public boolean sensitive(String word) {
        for(String sensitivesWord:sensitivesWords){
            if(word.contains(sensitivesWord)){
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean keyWord(String word) {
        for(String keyword:keyWords){
            if(word.startsWith(keyword)){
                return true;
            }
        }
        return false;
    }
}
