package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.dto.LoginSite;
import org.jasig.cas.authentication.principal.Service;
import org.jasig.cas.ticket.ServiceTicket;
import org.jasig.cas.ticket.Ticket;
import org.jasig.cas.ticket.TicketGrantingTicket;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface TicketService {
    Collection<Ticket> getTickets();
    List<Ticket> getTgts();
    List<Ticket> getSts();
    Page<Ticket> getTgts(Pageable page);
    Page<Ticket> getSts(Pageable page);
    List<Service> getServices();
    Page<LoginSite> getServices(Pageable page);
    Set<String> stKeys();
    Set<String> tgtKeys();
    ServiceTicket getSt(String key);
    TicketGrantingTicket getTgt(String key);
    void removeTgt(String key);
    void fix();
}
