package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.domain.Log;
import cn.dahe.cas.auth.search.SearchLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * on 2020/5/20.
 */
public interface SystemLogService {

    /**
     * 添加系统日志
     * @param log 日志
     * @return 添加的日志
     */
    Log add(Log log);

    /**
     * 添加系统日志
     * @param logs 日志
     * @return 添加成功数量
     */
    void add(List<Log> logs);

    /**
     * 分页获取用户修改记录
     * @param search 搜索条件
     * @param pageable 分页
     * @return 用户日志记录
     */
    Page<Log> page(SearchLog search, Pageable pageable);
}
