package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.UserVipDuration;
import cn.dahe.cas.auth.repositories.UserVipDurationRepository;
import cn.dahe.cas.auth.service.UserVipDurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class UserVipDurationServiceImpl extends BaseServiceImpl<UserVipDuration, Integer, UserVipDurationRepository> implements UserVipDurationService {

    @Autowired
    private UserVipDurationRepository userVipDurationRepository;

    public Page<Map<String,Object>> getPage(Pageable pageable, UserVipDuration param) {
        return userVipDurationRepository.pageList(param.getCustomerName(),pageable);
    }

    @Override
    public UserVipDuration getBySiteIdAndRoleId(int customerId, int siteId, int roleId) {
        List<UserVipDuration> userVipDurations = userVipDurationRepository.getBySiteIdAndRoleId(customerId, siteId, roleId);
        if (userVipDurations == null || userVipDurations.size() <= 0) {
            return null;
        }
        return userVipDurations.get(0);
    }

    @Override
    public List<UserVipDuration> getByCustomerId(int customerId) {
        return userVipDurationRepository.getByCustomerId(customerId);
    }

}
