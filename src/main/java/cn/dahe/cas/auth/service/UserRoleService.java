package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.UserRole;

import java.util.List;

/**
 * <AUTHOR>
 * on 2018/04/28
 */
public interface UserRoleService extends BaseService<UserRole,Integer>{
    /**
     * 根据用户id删除用户角色关系
     * @param uid 用户id
     */
    void deleteByUid(int uid);

    /**
     *
     * 添加用户角色关系
     * @param uid 用户滴
     * @param roleIds 角色id
     * @return 结果
     */
    boolean addRole(int uid, int[] roleIds);

    List<UserRole> findByUid(int uid);

    /*******************************************************************/
    /**
     * 根据用户id和角色id查找对应关系
     * @param uid 用户id
     * @param roleId 角色id
     * @return 用户角色对应关系
     */
    UserRole getOne(int uid, int roleId);

}
