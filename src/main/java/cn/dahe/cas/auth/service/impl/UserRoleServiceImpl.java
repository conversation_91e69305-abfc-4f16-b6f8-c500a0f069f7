package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.UserRole;
import cn.dahe.cas.auth.repositories.UserRoleRepository;
import cn.dahe.cas.auth.service.UserRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class UserRoleServiceImpl extends BaseServiceImpl<UserRole,Integer,UserRoleRepository> implements UserRoleService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByUid(int uid){
        repository.deleteAllByUid(uid);
    }

    @Override
    public boolean addRole(int uid, int[] roleIds){
        if (roleIds == null){
            return true;
        }
        if (roleIds.length >0){
            for (int roleId : roleIds) {
                UserRole userRole=new UserRole();
                userRole.setRoleId(roleId);
                userRole.setUid(uid);
                repository.save(userRole);
            }
        }
        return true;
    }

    @Override
    public List<UserRole> findByUid(int uid){
        return repository.findByUid(uid);
    }

    @Override
    public UserRole getOne(int uid, int roleId){
        return repository.findByUidAndRoleId(uid, roleId);
    }
}
