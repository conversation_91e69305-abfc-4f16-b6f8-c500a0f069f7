package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.annotion.AccessLimit;
import cn.dahe.cas.auth.service.CacheService;
import cn.dahe.cas.auth.util.IpUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * chenmf on 2018/4/13.
 */
@Service
public class CacheServiceImpl implements CacheService {
    private static final String LIMIT_KEY = "sso:access-limit:";

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private RedisTemplate<String,Integer> redisTemplate;

    @Override
    public String getKey(JoinPoint joinPoint, MethodSignature signature) {
        StringBuilder builder = new StringBuilder();
        String ip = IpUtils.getRemoteIp(request);
        String url = request.getRequestURI();
        Object[] args = joinPoint.getArgs();
        String[] names=signature.getParameterNames();
        Method method = signature.getMethod();
        AccessLimit limit = method.getAnnotation(AccessLimit.class);
        List<String> checks = Arrays.asList(limit.limitParams());
        builder.append(LIMIT_KEY);
        builder.append(limit.title()+":");
        builder.append(ip);
        builder.append(":");
        builder.append(url);
        builder.append(":");
        for(int i=0;i<names.length;i++){
            if(checks.contains(names[i])){
                builder.append(String.valueOf(args[i]));
                builder.append(":");
            }
        }
        //基于限定参数进行key构建
        //基于ip对url的访问进行控制
        return builder.toString().replaceAll(":$","");
    }

    @Override
    public Set<String> getKey(String key){
        ValueOperations<String,Integer> valueOperations = redisTemplate.opsForValue();
        Set<String> keys = null;
        try {
            keys = valueOperations.getOperations().keys(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> returnKeys = new HashSet<>();
        for (String s : keys) {
            if (valueOperations.get(s) >= 3){
                returnKeys.add(s);
            }
        }
        return returnKeys;
    }
    @Override
    public boolean deleteKey(String key) {
        ValueOperations<String,Integer> valueOperations = redisTemplate.opsForValue();
        if(valueOperations.getOperations().hasKey(key)){
            valueOperations.getOperations().delete(key);
        }
        return true;
    }
}
