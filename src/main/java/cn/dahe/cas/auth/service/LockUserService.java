package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * @Author: 杨振雨
 * @Date: 2018/11/13 18:48
 * @Description:
 */
public interface LockUserService {
    /**
     * 解锁用户
     * @param uid
     * @return
     */
    boolean unlock(int uid);

    /**
     *
     * @param pageable
     * @return
     */
    Page<User> getLockUsers(Pageable pageable);
}
