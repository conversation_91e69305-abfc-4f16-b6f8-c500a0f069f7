package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.Role;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.search.SearchRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface RoleService extends BaseService<Role,Integer>{

    // todo 由于需要把子系统的权限集中在sso，接口全部进行系统隔离（区分siteID）******2020/04/26**********

    /**
     * 角色添加
     * @param role 角色
     * @param user 用户
     * @return 角色
     */
    Role addRole(Role role, User user);

    /**
     * 根据系统id查找正常使用的角色
     * @param siteId （系统id）站点id
     * @return 角色
     */
    List<Role> findBySiteId(int siteId);

    /**
     * 根据站点id和sn查找角色
     * @param siteId 站点id
     * @param sn sn
     * @return 角色
     */
    Role findBySiteIdAndSn(int siteId, String sn);

    /**
     * 分页查找角色（可以按照条件）
     * @param pageable 分页
     * @param searchRole 搜索条件
     * @return 角色
     */
    Page<Role> getPage (Pageable pageable, SearchRole searchRole);

    /**
     * 根据用户id和站点id查找sn
     * @param userId 用户id
     * @param siteId 站点id
     * @return sn的集合
     */
    List<String> getSnByUserId(int userId, int siteId);
}
