package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.dto.IpInfo;
import cn.dahe.cas.auth.service.IpService;
import cn.dahe.cas.auth.util.IpUtils;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import cn.dahe.cas.auth.ip.IP;

/**
 * <AUTHOR>
 */
@Service
public class IpServiceImpl implements IpService{

    private static final Logger log = LoggerFactory.getLogger(IpServiceImpl.class);

    @Value("${ipip.token}")
    private String token;

    @Value("${ipip.addr}")
    private String api;

    @Value("${ipnotallow}")
    private String ipNotAllow;

    /**
     * 如果无法查询到ip城市信息，直接默认为郑州
     */
    public static final String DEFAULT_CITY = "郑州";

    @Autowired
    private ResourceLoader loader;

    private List<String> notAllowArea = new ArrayList<>();

    public static final String IP_DATA_PATH = "classpath:ip.dat";

    @Override
    public String getIpAddress(HttpServletRequest request) {
        return IpUtils.getRemoteIp(request);
    }

    @PostConstruct
    public void init(){
        notAllowArea = Arrays.asList(StringUtils.delimitedListToStringArray(ipNotAllow,"&"));
        //加载ip数据库
        IP.enableFileWatch = true;
        Resource resource = loader.getResource(IP_DATA_PATH);
        try {
            String data = resource.getFile().getPath();
            IP.load(data);
        } catch (IOException e) {
            log.error("the ip data file can not find at path {}",IP_DATA_PATH);
            throw new Error("无法找到ip数据库文件,请确保文件");
        }
    }

    @Override
    @Cacheable(CacheKey.IP_INFO_KEY)
    public IpInfo getIpInfo(String ip) {
        String[] info = IP.find(ip);
        IpInfo ipInfo = new IpInfo(ip,info[0],info[1],info[2]);
        if(SsoStringUtil.isBlank(ipInfo.getCity())){
            ipInfo.setCity(DEFAULT_CITY);
        }
        return ipInfo;
    }

    /**
     * ip地址是否允许注册
     * @param ip
     * @return
     */
    @Override
    public boolean allowRegister(String ip) {
        if(SsoStringUtil.isBlank(ip)){
            return false;
        }
        IpInfo info = getIpInfo(ip);
        return !notAllowArea.contains(info.getProvince());
    }
}
