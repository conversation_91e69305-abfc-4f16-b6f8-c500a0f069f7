package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.constants.LogOperation;
import cn.dahe.cas.auth.domain.UserLog;
import cn.dahe.cas.auth.search.SearchUserLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * on 2020/5/7.
 */
public interface UserLogService{
    /**
     * 添加日志
     * @param userLog 用户日志
     * @return userLog
     */
    UserLog add(UserLog userLog);

    /**
     * 分页获取用户修改记录
     * @param search 搜索条件
     * @param pageable 分页
     * @return 用户日志记录
     */
    Page<UserLog> page(SearchUserLog search, Pageable pageable);

    /**
     * 获取操作类型
     * @return 惭怍类型
     */
    List<LogOperation> getOperateType();
}
