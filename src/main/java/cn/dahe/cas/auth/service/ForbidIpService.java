package cn.dahe.cas.auth.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ForbidIpService {
    Page<String> getForbidIps(Pageable page);
    void addIp(String ip);
    void remove(String ip);

    /**
     * 按照条件查询禁用Ip
     * @param page
     * @param ip
     * @return
     */
    Page<String> getForbidIps(Pageable page, String ip);
}
