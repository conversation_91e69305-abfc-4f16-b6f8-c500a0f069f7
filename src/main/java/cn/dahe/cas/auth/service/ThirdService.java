package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.WeatherDto;

import java.io.UnsupportedEncodingException;

/**
 * 第三方服务，比如天气、手机号查询等
 */
public interface ThirdService {
    /**
     * 获取城市天气信息
     * 实现类建议加入缓存
     * @param city
     * @return
     */
    Object getWeather(String city) throws UnsupportedEncodingException;

    /**
     * 清理天气缓存信息
     */
    void clearWeather();
}
