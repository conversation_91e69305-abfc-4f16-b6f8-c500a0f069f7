package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.LoginLog;
import cn.dahe.cas.auth.entity.LoginLog_;
import cn.dahe.cas.auth.repositories.LoginLogRepository;
import cn.dahe.cas.auth.search.SearchLoginLog;
import cn.dahe.cas.auth.service.LoginLogService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Date: 2019/8/7 13:46
 * Description:
 */
@Service
public class LoginLogServiceImpl extends BaseServiceImpl<LoginLog,Integer,LoginLogRepository> implements LoginLogService{
    @Override
    public Page<LoginLog> search(Pageable pageable, SearchLoginLog searchLoginLog) {
        Specification<LoginLog> specification = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if(SsoStringUtil.isNotBlank(searchLoginLog.getPhone())){
                predicates.add(cb.equal(root.get(LoginLog_.phone),searchLoginLog.getPhone()));
            }
            if(SsoStringUtil.isNotBlank(searchLoginLog.getUsername())){
                predicates.add(cb.equal(root.get(LoginLog_.username),searchLoginLog.getUsername()));
            }
            if(searchLoginLog.getType() != null){
                predicates.add(cb.equal(root.get(LoginLog_.type),searchLoginLog.getType()));
            }
            if(searchLoginLog.getBeginDate()!=null){
                predicates.add(cb.greaterThan(root.get(LoginLog_.createDate),searchLoginLog.getBeginDate()));
            }
            if(searchLoginLog.getEndDate()!=null){
                predicates.add(cb.lessThan(root.get(LoginLog_.createDate),searchLoginLog.getEndDate()));
            }
            if(predicates.size()==0){
                return null;
            }
            return cb.and(predicates.toArray(new Predicate[predicates.size()]));
        };
        return repository.findAll(specification,pageable);
    }

    @Override
    public Page<LoginLog> searchLog(Pageable pageable, int id){
        Specification<LoginLog> specification = (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if(id > 0){
                predicates.add(builder.greaterThan(root.get(LoginLog_.id),id));
            }
            if(predicates.size()==0){
                return null;
            }
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
        pageable = new PageRequest(pageable.getPageNumber(), pageable.getPageSize(),
                new Sort(Sort.Direction.ASC, "id"));
        return repository.findAll(specification, pageable);
    }
}
