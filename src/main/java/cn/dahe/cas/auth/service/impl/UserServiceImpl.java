package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.config.<PERSON><PERSON>ey;
import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.dto.RoleDto;
import cn.dahe.cas.auth.dto.UserInfo;
import cn.dahe.cas.auth.entity.*;
import cn.dahe.cas.auth.event.UserEvent;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.repositories.ResourceRepository;
import cn.dahe.cas.auth.repositories.RoleRepository;
import cn.dahe.cas.auth.repositories.UserRepository;
import cn.dahe.cas.auth.search.SearchUser;
import cn.dahe.cas.auth.security.PasswordEncry;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.Sm4Util;
import cn.dahe.cas.auth.util.SsoStringUtil;
import com.google.common.collect.Lists;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.jsoup.helper.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * Created by Administrator on 2017/10/16.
 */
@Service
public class UserServiceImpl extends BaseServiceImpl<User, Integer, UserRepository> implements UserService {

    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private PasswordEncry passwordEncry;
    @Value("${init.icon}")
    private String icon;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    /**
     * 用户删除后直接将站点用户、部门用户以及单个用户的缓存删除
     *
     * @param id
     */
    @Caching(evict = {@CacheEvict(cacheNames = {CacheKey.SITE_USER_KEY,
            CacheKey.DEPARTMENT_USER_KEY,
            CacheKey.PAGE_USER_INFO_KEY,
            CacheKey.ALL_USER_ID_KEY}, allEntries = true),
            @CacheEvict(cacheNames = CacheKey.SINGLE_USER_INFO_KEY, key = "#id+''")})
    @Override
    public void delete(Integer id) {
        User entity = get(id);
        if (entity != null) {
            publisher.publishEvent(new UserEvent(entity, EntityOperation.DELETE));
        }
        super.delete(id);
    }

    /**
     * 默认添加对个人中心的访问权限
     * 清除缓存并将个人信息添加到缓存中
     *
     * @param entity
     * @return
     */
    @Caching(evict = {@CacheEvict(cacheNames = {CacheKey.SITE_USER_KEY,
            CacheKey.DEPARTMENT_USER_KEY,
            CacheKey.PAGE_USER_INFO_KEY,
            CacheKey.ALL_USER_ID_KEY}, allEntries = true)},
            put = {@CachePut(cacheNames = CacheKey.SINGLE_USER_INFO_KEY, key = "#entity.uid+''")})
    @Override
    public User add(User entity) {
        log.debug("添加用户 id {} username {} ", entity.getUid(), entity.getUsername());
        User user = getUserByPhone(entity.getPhone());
        if (user != null) {
            throw new SsoException("该手机号已存在！");
        }
        String icon = entity.getIcon();
        if (StringUtil.isBlank(icon)){
            entity.setIcon(this.icon);
        }
        //entity.setSalt(IdGen.generSalt());
        //entity.setPassword(passwordEncry.encry(entity.getPassword(),entity.getSalt()));
        entity = repository.save(entity);
        log.debug("添加用户 id {} username {} 完成，发布事件", entity.getUid(), entity.getUsername());
        UserEvent event = new UserEvent(entity, EntityOperation.ADD);
        publisher.publishEvent(event);
        return entity;
    }

    //用户更新后将缓存进行更新
    @Caching(evict = {@CacheEvict(cacheNames = {CacheKey.SITE_USER_KEY,
            CacheKey.DEPARTMENT_USER_KEY, CacheKey.PAGE_USER_INFO_KEY,
            CacheKey.ALL_USER_ID_KEY}, allEntries = true)},
            put = {@CachePut(cacheNames = CacheKey.SINGLE_USER_INFO_KEY, key = "#user.uid+''")})
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(User user) {
        User one = repository.findByPhone(user.getPhone());
        if (one != null && user.getUid() != one.getUid()) {
            throw new SsoException("该手机号已存在！");
        }
        super.update(user);
        // todo 信息加密  此处暂时去掉
        UserEvent event = new UserEvent(user, EntityOperation.EDIT);
        publisher.publishEvent(event);
    }

    @Override
    public User getUserByWx(String openid) {
        return repository.findByWxOpenId(openid);
    }

    @Override
    public User getUser(String identify) {
        return repository.findUserByUsernameOrPhone(identify, identify);
    }

    @Override
    public Page<User> getUser(Pageable pageable, String identify, String sid) {
        if (SsoStringUtil.isBlank(sid)) {
            return repository.findUserByUsernameOrPhone(pageable, identify, identify);
        }
        return repository.findUserByUsernameOrPhoneAndSid(pageable, identify, identify, sid);
    }

    @Override
    public Page<User> getDepartmentUser(Pageable pageable, String identify, int departmentId) {
        if (SsoStringUtil.isBlank(identify)) {
            return repository.findUserByOrganization(pageable, departmentId);
        }
        return repository.findUserByOrganizationAndTruenameContainingOrPhone(pageable, departmentId, identify, identify);
    }

    @Override
    public User getUserByUsername(String username) {
        return repository.findByUsername(username);
    }

    @Override
    public User getUserByPhone(String phone) {
        return repository.findByPhone(phone);
    }

    @Override
    public UserInfo getDetail(User user, int siteId) {
        UserInfo info = new UserInfo();
        info.setUser(user);
        List<String> roles = roleRepository.findSnByUserId(user.getUid(), siteId);
        List<String> rrs = Lists.newArrayList();
        if (roles.indexOf("admin") > -1) {
            rrs.addAll(resourceRepository.getPermission(siteId));
        } else {
            rrs.addAll(resourceRepository.getPermissionByUid(user.getUid(), siteId));
        }
        info.setRoles(roles);
        info.setPermissions(rrs);
        return info;
    }

    @Override
    public boolean exist(String username) {
        return repository.findByUsername(username) != null;
    }

    @Override
    public boolean existPhone(String phone) {
        return repository.findByPhone(phone) != null;
    }

    @Override
    public boolean isPasswordCorrect(User user, String password) {
        String encry = passwordEncry.encry(password, user.getSalt());
        if (encry.equals(user.getPassword())) {
            return true;
        }
        return false;
    }

    @Override
    public Page<User> searchUser(Pageable pageable, final SearchUser searchUser) {
        Specification<User> specification = buildSpecification(searchUser);
        pageable = new PageRequest(pageable.getPageNumber(), pageable.getPageSize(),
                new Sort(Sort.Direction.DESC, "uid"));
        if (specification == null) {
            return repository.findAll(pageable);
        }
        return repository.findAll(specification, pageable);
    }

    @Override
    public List<User> searchUser(SearchUser searchUser) {
        Specification<User> specification = buildSpecification(searchUser);
        return repository.findAll(specification);
    }

    private Specification<User> buildSpecification(SearchUser searchUser) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (SsoStringUtil.isNotBlank(searchUser.getTruename())) {
                try {
                    Predicate name = cb.like(root.get(User_.username), "%" + searchUser.getTruename() + "%");
                    Predicate phone = cb.equal(root.get(User_.phone), Sm4Util.encryptEcb(sm4Key, searchUser.getTruename()));
                    predicates.add(cb.or(name, phone));
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            //if (searchUser.getType() != null) {
            //    predicates.add(cb.equal(root.get(User_.type), searchUser.getType()));
            //}
            if (searchUser.getFlag() != null) {
                predicates.add(cb.equal(root.get(User_.flag), searchUser.getFlag()));
            }
            if (SsoStringUtil.isNotBlank(searchUser.getSid())) {
                predicates.add(cb.equal(root.get(User_.sid), searchUser.getSid()));
            }
            if (searchUser.getOrganization() != 0) {
                predicates.add(cb.equal(root.get(User_.organization), searchUser.getOrganization()));
            }
            if (predicates.size() == 0) {
                return null;
            }
            return cb.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    private <T> JPQLQuery<T> applyPagination(Pageable pageable, JPQLQuery<T> query) {
        query.offset(pageable.getOffset());
        query.limit(pageable.getPageSize());
        return query;
    }

    @Override
    public Page<User> findUserByRoleId(Pageable pageable, SearchUser searchUser) {
        QUser user = QUser.user;
        QUserRole userRole = QUserRole.userRole;
        List<com.querydsl.core.types.Predicate> predicates = Lists.newArrayList();
        JPAQuery query = jpaQueryFactory.from(user)
                .from(userRole).where(user.uid.eq(userRole.uid));
        if (searchUser.getRoleId() > 0) {
            predicates.add(userRole.roleId.eq(searchUser.getRoleId()));
        }
        if (SsoStringUtil.isNotBlank(searchUser.getTruename())) {
            predicates.add(user.username.like("%" + searchUser.getTruename() + "%"));
        }
        if (SsoStringUtil.isNotBlank(searchUser.getPhone())) {
            try {
                predicates.add(user.phone.eq(Sm4Util.encryptEcb(sm4Key, searchUser.getPhone())));
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        query.where(predicates.toArray(new com.querydsl.core.types.Predicate[predicates.size()])).orderBy(user.uid.desc());
        applyPagination(pageable, query);
        long count = query.fetchCount();
        return PageableExecutionUtils.getPage(query.fetch(), pageable, () -> count);
    }

    @Override
    public Page<User> findBySiteId(Pageable pageable, int siteId) {
        QUser user = QUser.user;
        QUserSite userSite = QUserSite.userSite;
        List<com.querydsl.core.types.Predicate> predicates = Lists.newArrayList();
        JPAQuery query = jpaQueryFactory.from(user)
                .from(userSite).where(user.uid.eq(userSite.userId));
        if (siteId > 0) {
            predicates.add(userSite.siteId.eq(siteId));
        }
        query.where(predicates.toArray(new com.querydsl.core.types.Predicate[predicates.size()])).orderBy(user.uid.desc());
        applyPagination(pageable, query);
        long count = query.fetchCount();
        return PageableExecutionUtils.getPage(query.fetch(), pageable, () -> count);
    }

    @Override
    public RoleDto getBySiteAndUid(int userId, int siteId) {
        RoleDto userProfileDto = new RoleDto();
        userProfileDto.setUid(userId);
        // 查该用户该站点的所有角色和权限
        List<String> snList = roleRepository.findSnByUserId(userId, siteId);
        userProfileDto.setSn(snList);
        if (snList.contains("admin")) {
            List<String> permission = resourceRepository.getPermission(siteId);
            userProfileDto.setPermission(permission);
        } else {
            List<String> permission = resourceRepository.getPermissionByUid(userId, siteId);
            userProfileDto.setPermission(permission);
        }
        return userProfileDto;
    }

    @Override
    public User getBySynchronizeId(String synchronizeId) {
        return repository.findBySynchronizeId(synchronizeId);
    }
}
