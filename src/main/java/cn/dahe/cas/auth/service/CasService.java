package cn.dahe.cas.auth.service;

import org.jasig.cas.services.RegisteredService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * cas相关服务
 */
public interface CasService {
    /**
     * 分页获取注册服务
     * @return
     */
    Page<RegisteredService> getServices(Pageable pageable);

    /**
     * 生成tgt
     * @param value
     * @param ua
     * @param origin
     * @return
     */
    String getTgt(String value,String ua,String origin);

    void deleteService(int id);

    /**
     * 将用户强制退出cas登录
     * @param uid
     */
    void logoutUser(int uid);

    /**
     * 将所有用户强制退出登录
     */
    void clearUsers();
}
