package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.Log;
import cn.dahe.cas.auth.search.SearchLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface LogService extends BaseService<Log,Integer>{
    /**
     * 按照来源、类型和时间搜索日志记录
     * @param pageable
     * @param searchLog
     * @return
     */
    Page<Log> searchLog(Pageable pageable, SearchLog searchLog);

    /**
     * 按照来源、类型和时间搜索日志记录，不分页
     * @param searchLog
     * @return
     */
    List<Log> searchUserLog(int uid, SearchLog searchLog);

    /**
     * 根据截至日期或者前N条数据删除
     * @param date 保留的时间天数
     * @param count 保留的数量
     */
    void deleteByCondition(int date, int count);

    /**
     * 获取清理日志的条件（保留的天数和保留的日志数量）
     * @return 数量结果
     */
    Map<String,Integer> getClearLogCondition();

    /**
     * 设置清理日志的条件
     * @param count 保留的日志数量
     * @param date 保留最近的天数
     * @param timeClean 是否开启定时清理日志功能
     */
    void saveClearLogCondition(int count, int date, int timeClean);

    /**
     * 迁移数据（根据id升序）
     * @param pageable 分页
     * @param id 日志id
     * @return 日志
     */
    Page<Log> searchLog(Pageable pageable, int id);
}
