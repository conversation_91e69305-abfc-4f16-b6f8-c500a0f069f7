package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.constants.SmsType;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.dto.SsoSms;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.SmsService;
import cn.dahe.cas.auth.service.VerfiyCodeService;
import cn.dahe.cas.auth.util.SmsUtil;
import cn.hutool.core.util.RandomUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;

@Service
public class SmsServiceImpl implements SmsService {

    private static final Logger log = LoggerFactory.getLogger(SmsServiceImpl.class);

    private static final String DAHE_SMS_API = "https://sms.dahe.cn/dahe/gov/t1";

    @Value("${sms.api}")
    private String smsApi;

    @Value("${sms.sso.url}")
    private String ssoSmsApi;

    @Value("${z1024.api}")
    private String z1024Api;

    @Value("${sms.username}")
    private String username;

    @Value("${sms.password}")
    private String password;

    @Value("${sms.code.template}")
    private String smsTemplate;

    @Value("${sms.find.template}")
    private String findTemplate;

    @Value("${sms.spnumber}")
    private String smsNumber;

    @Value("${sms.interval}")
    private int timeout;

    @Value("${sms.allow.host}")
    private String allowHost;

    @Value("${sms.sso.token}")
    private String smsToken;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private VerfiyCodeService verfiyCodeService;

    /**
     * 发送短信，对短信发送进行日志记录
     *
     * @param phone
     * @return
     */
    @Override
    public int sendAccountCreateSms(String phone) {
//        String sign = SecurityUtil.signDaheSms("m-dahe-gov");
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//
//        MultiValueMap<String, String> formBody= new LinkedMultiValueMap<>();
//        formBody.add("mobile",phone);
//        formBody.add("token",sign);
//        HttpEntity<MultiValueMap<String,String>> entity = new HttpEntity<>(formBody, headers);
//        try {
//            Object result = restTemplate.postForEntity(DAHE_SMS_API,entity,Object.class);
//            log.debug("短信发送结果"+result);
//        }catch (Exception e){
//            log.error("发送账号开通短信失败",e);
//        }
        return 0;
    }

    @Override
    public JsonResult sendSms(String phone, String token, String type) {
        SsoSms ssoSms = SsoSms.newSsoSms().mobile(phone).type(SmsType.LOGIN).build();
        sendSsoSms(ssoSms);
        return ResultUtil.success();
    }

    @Override
    public void sendSsoSms(SsoSms ssosms) throws SsoException {
        long t0 = System.currentTimeMillis();
        ssosms.setCode(RandomUtil.randomNumbers(6));
        verfiyCodeService.saveCode(ssosms.getMobile(),ssosms.getCode());
        log.debug("开始调用远程接口发送{}", System.currentTimeMillis());
        long t1 = System.currentTimeMillis();
        log.info("============================>保存验证码耗时{}ms", (t1 - t0));
        sendSSOSmsRequest(ssosms);
        long t2 = System.currentTimeMillis();
        log.info("============================>远程请求耗时{}ms", (t2 - t1));
    }

    private static void sendSSOSmsRequest(SsoSms ssosms) throws SsoException {
        String mobile = ssosms.getMobile();
        String code = ssosms.getCode();
        SmsUtil.sendLoginSmsCode(mobile, code);
    }
}
