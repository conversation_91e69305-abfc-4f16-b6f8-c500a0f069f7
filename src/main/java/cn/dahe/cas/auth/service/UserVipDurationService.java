package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.UserVipDuration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface UserVipDurationService extends BaseService<UserVipDuration,Integer>{

    Page<Map<String,Object>> getPage (Pageable pageable, UserVipDuration param);

    UserVipDuration getBySiteIdAndRoleId(int customerId, int siteId, int roleId);

    List<UserVipDuration> getByCustomerId(int customerId);
}
