package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.SmsRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * 短信发送记录
 */
public interface SmsRecordService extends BaseService<SmsRecord,Integer>{
    /**
     * 根据手机号查询短信记录
     * @param phone
     * @return
     */
    Page<SmsRecord> search(String phone,Pageable page);
}
