package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.dto.Area;
import cn.dahe.cas.auth.service.AreaService;
import cn.dahe.cas.auth.util.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.core.io.Resource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/22 17:57
 * @Description: 根据区划规律将该服务优化到极致
 * 整体为树结构（普通树）
 * 编号规律（暂定）
 *  总位数：12位
 *  后一级的前n位为前一级编号去0
 * 市级：四位补0
 * 县级：6位补0
 *
 */
public class AreaServiceImpl implements AreaService {

    private final List<Area> areas;

    public AreaServiceImpl(Resource areasJson) throws IOException {
        areas = JSON.parseObject(StreamUtils.copyToString(areasJson.getInputStream(),Charset.forName("UTF-8")),new TypeReference<List<Area>>(){}.getType());
    }

    @Override
    public List<Area> getAreas(Integer level,String parentCode){
        return areas;
    }

    @Override
    public Area getByCode(String code) {
        Area result = null;
        //递归向上查找
        for(Area area:areas){
            result = getByCodeFromArea(code,area);
            if(result!=null){
                break;
            }
        }
        return result;
    }

    @Override
    public Area getTop(String code) {
        //记录树的根节点
        Area result = null;
        //递归向上查找
        for(Area area:areas){
            result = getByCodeFromArea(code,area);
            if(result!=null){
                //真是妙啊
                result = area;
                break;
            }
        }
        return result;
    }

    /**
     * 递归方式进行树查找
     * @param code
     * @param area
     * @return
     */
    private Area getByCodeFromArea(String code,Area area){
        Area result = null;
        if(area.getId().equals(code)){
            return area;
        }
        //若无则继续查找子节点
        if(ListUtil.isEmpty(area.getChildren())){
            return null;
        }
        List<Area> children = area.getChildren();
        for(Area child:children){
            result = getByCodeFromArea(code,child);
            if(result!=null){
                break;
            }
        }
        return result;
    }
}
