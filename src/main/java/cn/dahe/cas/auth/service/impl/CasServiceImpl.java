package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.cas.PageServiceRegistryDao;
import cn.dahe.cas.auth.config.RedisKey;
import cn.dahe.cas.auth.service.CasService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.CentralAuthenticationService;
import org.jasig.cas.CipherExecutor;
import org.jasig.cas.logout.LogoutRequest;
import org.jasig.cas.services.RegisteredService;
import org.jasig.cas.services.ServicesManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CasServiceImpl implements CasService{

    private static final Logger log = LoggerFactory.getLogger(CasServiceImpl.class);

    private static final char COOKIE_FIELD_SEPARATOR = '@';

    @Autowired
    private PageServiceRegistryDao serviceRegistryDao;

    @Autowired
    @Qualifier("defaultCookieCipherExecutor")
    private CipherExecutor<String,String> cipherExecutor;

    @Autowired
    private ServicesManager servicesManager;

    @Autowired
    private CentralAuthenticationService centralAuthenticationService;

    @Resource(name = "redisTemplate")
    private HashOperations<String,Integer,String> hashOperations;

    @Override
    public Page<RegisteredService> getServices(Pageable pageable) {
        return serviceRegistryDao.getServices(pageable);
    }

    @Override
    public String getTgt(String cookie,String ua,String origin) {
        String cookieValue = cipherExecutor.decode(cookie);
        if(SsoStringUtil.isBlank(cookieValue)){
            log.debug("Retrieved decoded cookie value is blank. Failed to decode cookie");
            return null;
        }
        final String[] cookieParts = cookieValue.split(String.valueOf(COOKIE_FIELD_SEPARATOR));
        final String value = cookieParts[0];
        String remoteAddr = cookieParts[1];
        String userAgent = cookieParts[2];
        if (!StringUtils.isBlank(value) && !StringUtils.isBlank(remoteAddr) && !StringUtils.isBlank(userAgent)) {
            if (!remoteAddr.equals(origin)) {
                throw new IllegalStateException("非法票据");
            } else if (!userAgent.equals(ua)) {
                throw new IllegalStateException("非法票据");
            } else {
                return value;
            }
        } else {
            throw new IllegalStateException("Invalid cookie. Required fields are empty");
        }
    }

    @Override
    public void deleteService(int id) {
        servicesManager.delete(id);
    }

    @Override
    public void logoutUser(int uid) {
        boolean logged = hashOperations.hasKey(RedisKey.LOGINED_USER_KEY,uid);
        if(logged){
            //进行退出操作
            //首先将用户登录情况清除
            String tgt = hashOperations.get(RedisKey.LOGINED_USER_KEY,uid);
            hashOperations.delete(RedisKey.LOGINED_USER_KEY,uid);
            //销毁tgt并返回所有已登陆子系统的退出登录地址，此时由于用户可能执行了正常登录逻辑因此无tgt信息，注意判断
            log.debug("退出的tgt打印：{}",tgt);
            if(SsoStringUtil.isBlank(tgt)){
                return;
            }
            final List<LogoutRequest> logoutRequests = centralAuthenticationService.destroyTicketGrantingTicket(tgt);
            log.debug("踢出上个登录用户，用户id为{},退出站点数量{}",uid,logoutRequests.size());
        }
    }

    @Override
    public void clearUsers() {
        List<String> tgts = hashOperations.values(RedisKey.LOGINED_USER_KEY);
        hashOperations.getOperations().delete(RedisKey.LOGINED_USER_KEY);
        for(String tgt:tgts){
            centralAuthenticationService.destroyTicketGrantingTicket(tgt);
        }
    }
}
