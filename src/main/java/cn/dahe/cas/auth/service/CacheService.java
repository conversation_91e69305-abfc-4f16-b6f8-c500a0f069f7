package cn.dahe.cas.auth.service;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * on 2018/4/13.
 */
public interface CacheService {
    /**
     * 获取被锁住的key（key自定义）
     * @param joinPoint
     * @param signature
     * @return
     */
    String getKey(JoinPoint joinPoint, MethodSignature signature);

    /**
     * 查询缓存
     * @param key
     * @return
     */
    Set<String> getKey(String key);

    /**
     * 根据key清除缓存
     * @param key
     * @return
     */
    boolean deleteKey(String key);
}
