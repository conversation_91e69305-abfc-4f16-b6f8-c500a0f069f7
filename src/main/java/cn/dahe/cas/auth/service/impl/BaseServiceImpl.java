package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @param <T>
 * @param <R>
 * @info 提供基础的服务
 */
public class BaseServiceImpl<T,ID extends Number,R extends JpaRepository<T,ID>> implements BaseService<T,ID>{

    @Autowired
    protected R repository;


    @Override
    public void delete(T t) {
        repository.delete(t);
    }

    @Override
    public void delete(ID id) {
        repository.delete(id);
    }

    @Override
    public void update(T t) {
        repository.save(t);
    }

    @Override
    public T add(T t) {
        return repository.save(t);
    }

    @Override
    public Page<T> get(Pageable pageable) {
        return repository.findAll(pageable);
    }

    @Override
    public List<T> get() {
        return repository.findAll();
    }

    @Override
    public T get(ID id) {
        return repository.findOne(id);
    }

    @Override
    public int add(List<T> ts) {
        return repository.save(ts).size();
    }

}
