package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.dto.SiteDto;
import cn.dahe.cas.auth.dto.SiteTreeDto;
import cn.dahe.cas.auth.service.SiteTreeService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * on 2018/1/30.s
 */
@Service
public class SiteTreeServiceImpl implements SiteTreeService {

    @Resource(name = "treeRedisTemplate")
    private ValueOperations<String,List<SiteTreeDto>> treeOperations;

    @Resource(name = "treeRedisTemplate")
    private HashOperations<String,String,List<SiteTreeDto>> hashOperations;

    @Resource(name = "departmentTemplate")
    private HashOperations<String,String,SiteTreeDto> departOperations;

    @Resource(name = "siteTemplate")
    private HashOperations<String,String,SiteDto> siteOperations;

    @Value("${tree_key}")
    private String treeKey;
    @Value("${department_key}")
    private String depKey;
    @Value("${cms_site}")
    private String sitelist;
    @Value("${cms_organization}")
    private String organizationlist;

    @Override
    public List<SiteTreeDto> getallSite() {
        return treeOperations.get(treeKey);
    }
    @Override
    public List<SiteTreeDto> getDepartment(String sid){
        return hashOperations.get(depKey, sid);
    }

    @Override
    public SiteDto getSite(String sid){
        return siteOperations.get(sitelist,sid);
    }

    @Override
    public List<SiteDto> getSite() {
        Map<String,SiteDto> results = siteOperations.entries(sitelist);
        if(results==null){
            return Lists.newArrayList();
        }
        return Lists.newArrayList(results.values());
    }

    @Override
    public SiteTreeDto getOneDepart(String departId){
        return departOperations.get(organizationlist,departId);
    }
}
