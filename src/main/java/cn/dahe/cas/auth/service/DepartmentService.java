package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.dto.TreeNode;
import cn.dahe.cas.auth.entity.Department;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface DepartmentService extends BaseService<Department,Integer>{

    /**
     * 检查名称和父ID是否已存在
     * @param name 部门名称
     * @param pid 父ID
     * @return 是否存在
     */
    boolean existsByNameAndPid(String name, Integer pid);

    /**
     * 检查名称和父ID是否已存在（排除指定ID）
     * @param name 部门名称
     * @param pid 父ID
     * @param id 排除的ID
     * @return 是否存在
     */
    boolean existsByNameAndPidAndIdNot(String name, Integer pid, Integer id);

    /**
     * 根据条件分页查询
     * @param keywords 关键词
     * @param pid 父ID
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Department> findByConditions(String keywords, Integer pid, Pageable pageable);

    /**
     * 根据父ID和状态查询
     * @param pid 父ID
     * @param status 状态
     * @return 部门列表
     */
    List<Department> findByPidAndStatus(Integer pid, Integer status);

    /**
     * 更新排序
     * @param newIds 新的ID顺序
     * @param seqIds 旧的ID顺序
     * @return 是否成功
     */
    boolean updateSeq(String newIds, String seqIds);

    /**
     * 检查是否有子部门
     * @param id 部门ID
     * @return 是否有子部门
     */
    boolean hasChildren(Integer id);

    /**
     * 检查是否会形成循环引用
     * @param id 部门ID
     * @param pid 父ID
     * @return 是否会形成循环引用
     */
    boolean wouldCreateCircularReference(Integer id, Integer pid);

    /**
     * 获取部门树形结构
     * @return 部门树
     */
    List<TreeNode> getDepartmentTree();

    List<Department> listAll();

}
