package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.RoleResource;
import cn.dahe.cas.auth.repositories.RoleResourceRepository;
import cn.dahe.cas.auth.service.RoleResourceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * on 2020/4/23.
 */
@Service
public class RoleResourceServiceImpl extends BaseServiceImpl<RoleResource, Integer, RoleResourceRepository>
        implements RoleResourceService{

    @Override
    public List<RoleResource> getByRoleId(int roleId){
        return repository.findByRoleId(roleId);
    }

    @Override
    public int deleteByRoleId(int roleId){
        return repository.deleteByRoleId(roleId);
    }

    @Override
    public RoleResource getOne(int roleId, int resId){
        return repository.findByRoleIdAndAndResId(roleId, resId);
    }
}
