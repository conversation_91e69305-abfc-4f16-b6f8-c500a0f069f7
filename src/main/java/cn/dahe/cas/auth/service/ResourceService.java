package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.dto.ResourceTree;
import cn.dahe.cas.auth.entity.Resource;
import cn.dahe.cas.auth.entity.Role;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.search.SearchResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * on 2020/04/27
 */
public interface ResourceService extends BaseService<Resource,Integer>{
    void grantToRole(Role role, Resource resource);

    // todo 由于需要把子系统的权限集中在sso，接口全部进行系统隔离（区分siteID）******2020/04/26**********

    /**
     * 添加权限
     * @param resource 权限
     * @param user 用户
     * @return 权限
     */
    Resource add(Resource resource, User user);

    /**
     * 编辑权限
     * @param resource 权限
     * @param userId 用户id
     * @return 权限
     */
    Resource edit(Resource resource, int userId);

    /**
     * 更改权限的状态
     * @param id 权限id
     * @param userId 用户id
     * @return 成功与否
     */
    boolean editStatus(int id, int userId);

    /**
     * 根据父ID查找子权限
     * @param pid 父id
     * @return 权限集合
     */
    List<Resource> findByPid(int pid);

    /**
     * 根据权限字符和系统id查找权限
     * @param permission 权限字符
     * @param siteId 系统id
     * @return 权限
     */
    Resource getByPermissionAndSiteId(String permission, int siteId);

    /**
     * 分页查找权限（可以按照条件）
     * @param pageable 分页
     * @param searchResource 搜索条件
     * @return 权限
     */
    Page<Resource> getPage(Pageable pageable, SearchResource searchResource);

    /**
     * 根据系统id查找所有正常的权限
     * @param siteId 系统id
     * @return 权限
     */
    List<Resource> getBySiteId(int siteId);

    /**
     * 根据用户和站点查找权限字符
     * @param uid uid
     * @param siteId 站点id
     * @return 权限字符集合
     */
    List<String> getPermission(int uid, int siteId);

    /**
     * 根据站点查找权限字符
     * @param siteId 站点id
     * @return 权限字符集合
     */
    List<String> getPermission(int siteId);

    /**
     * 根据站点id和角色id封装成权限树
     * @param siteId 站点id
     * @param pid 父id
     * @return 权限树
     */
    List<ResourceTree> listTree(int pid, int siteId);

    /**
     * 根据角色id查找角色下的权限
     * @param roleId 角色id
     * @return 权限
     */
    List<Resource> listByRoleId(int roleId);
}
