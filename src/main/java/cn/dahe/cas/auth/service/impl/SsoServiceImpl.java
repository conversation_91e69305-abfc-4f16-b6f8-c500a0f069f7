package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.SsoService;
import cn.dahe.cas.auth.service.UserService;
import org.jasig.cas.CentralAuthenticationService;
import org.jasig.cas.authentication.*;
import org.jasig.cas.support.rest.CredentialFactory;
import org.jasig.cas.ticket.AbstractTicketException;
import org.jasig.cas.ticket.InvalidTicketException;
import org.jasig.cas.ticket.TicketGrantingTicket;
import org.jasig.cas.web.support.CookieRetrievingCookieGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2017/12/22
 * @createTime 10:05
 * @info
 */
@Service
public class SsoServiceImpl implements SsoService{

    public static final String USERNAME_KEY = "username";

    @Autowired
    private CentralAuthenticationService centralAuthenticationService;

    @Autowired
    private UserService userService;

    @Autowired(required=false)
    private AuthenticationSystemSupport authenticationSystemSupport = new DefaultAuthenticationSystemSupport();

    @Autowired(required = false)
    private final CredentialFactory credentialFactory = new SsoServiceImpl.DefaultCredentialFactory();

    @Autowired
    private CookieRetrievingCookieGenerator ticketGrantingTicketCookieGenerator;

    @Override
    @Cacheable(CacheKey.TGT_AUTH_INFO_KEY)
    public Map<String, Object> getUserInfo(String tgt) {
        try {
            final TicketGrantingTicket ticket = centralAuthenticationService.getTicket(tgt, TicketGrantingTicket.class);
            return ticket.getAuthentication().getPrincipal().getAttributes();
        } catch (InvalidTicketException e) {
            return null;
        }
    }

    @Override
    @Cacheable(CacheKey.TGT_USER_KEY)
    public User getUser(String tgt){
        Map<String,Object> attributes = getUserInfo(tgt);
        if(attributes.containsKey(USERNAME_KEY)){
            String username = attributes.get(USERNAME_KEY).toString();
            return userService.getUserByUsername(username);
        }
        return null;
    }

    @Override
    public void login(HttpServletRequest request, HttpServletResponse response, MultiValueMap<String, String> requestBody) {
        try {
            final Credential credential = this.credentialFactory.fromRequestBody(requestBody);
            final AuthenticationContextBuilder builder = new DefaultAuthenticationContextBuilder(
                    this.authenticationSystemSupport.getPrincipalElectionStrategy());
            final AuthenticationTransaction transaction =
                    AuthenticationTransaction.wrap(credential);
            this.authenticationSystemSupport.getAuthenticationTransactionManager().handle(transaction,  builder);
            final AuthenticationContext authenticationContext = builder.build();
            final TicketGrantingTicket tgtId = this.centralAuthenticationService.createTicketGrantingTicket(authenticationContext);
            //TGT生成为TGC并添加客户端cookie中
            this.ticketGrantingTicketCookieGenerator.addCookie(request, response, tgtId.getId());
        } catch (AuthenticationException e) {
            e.printStackTrace();
        } catch (AbstractTicketException e) {
            e.printStackTrace();
        }
    }

    private static class DefaultCredentialFactory implements CredentialFactory {
        @Override
        public Credential fromRequestBody(@NotNull final MultiValueMap<String, String> requestBody) {
            final String username = requestBody.getFirst("username");
            final String password = requestBody.getFirst("password");
            if(username == null || password == null) {
                throw new SsoServiceImpl.BadRequestException("Invalid payload. 'username' and 'password' form fields are required.");
            }
            return new UsernamePasswordCredential(requestBody.getFirst("username"), requestBody.getFirst("password"));
        }
    }

    private static class BadRequestException extends IllegalArgumentException {
        private static final long serialVersionUID = 6852720596988243487L;
        BadRequestException(final String msg) {
            super(msg);
        }
    }
}
