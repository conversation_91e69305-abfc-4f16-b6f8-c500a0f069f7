package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.LoginLog;
import cn.dahe.cas.auth.search.SearchLoginLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/7 13:46
 * @Description:
 */
public interface LoginLogService extends BaseService<LoginLog,Integer>{
    Page<LoginLog> search(Pageable pageable, SearchLoginLog searchLoginLog);
    /**
     * 迁移数据（根据id升序）
     * @param pageable 分页
     * @param id 登录日志id
     * @return 登录日志
     */
    Page<LoginLog> searchLog(Pageable pageable, int id);
}
