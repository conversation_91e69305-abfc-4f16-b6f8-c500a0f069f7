package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.dto.SiteDto;
import cn.dahe.cas.auth.dto.SiteTreeDto;

import java.util.List;

/**
 * <AUTHOR>
 * on 2018/1/30.
 */
public interface SiteTreeService {
    /**
     * 从cms获取站点树
     * @return
     */
    List<SiteTreeDto> getallSite();

    /**
     * 从 cms根据站点id获取组织机构（部门）
     * @param sid
     * @return
     */
    List<SiteTreeDto> getDepartment(String sid);

    /**
     * 根据站点id获取站点
     */
    SiteDto getSite(String sid);

    /**
     * 直接获取所有站点
     * @return
     */
    List<SiteDto> getSite();
    /**
     * 根据组织机构（部门）id获取组织机构
     */
    SiteTreeDto getOneDepart(String departId);
}
