package cn.dahe.cas.auth.service;


import cn.dahe.cas.auth.entity.ServiceConsult;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.search.SearchFocus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ServiceConsultService extends BaseService<ServiceConsult,Integer>{


    /**
     * 分页获取 重点人物
     *
     * @param pageable
     * @param searchFocus
     * @param user
     * @return
     */
    Page<ServiceConsult> getPage(Pageable pageable, SearchFocus searchFocus, User user);

    /**
     * @param user
     */
    boolean addServiceConsult(int type, User user);
}
