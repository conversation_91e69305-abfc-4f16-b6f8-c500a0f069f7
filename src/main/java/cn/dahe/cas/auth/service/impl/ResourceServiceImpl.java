package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.dto.ResourceTree;
import cn.dahe.cas.auth.entity.Resource;
import cn.dahe.cas.auth.entity.Resource_;
import cn.dahe.cas.auth.entity.Role;
import cn.dahe.cas.auth.entity.RoleResource;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.repositories.ResourceRepository;
import cn.dahe.cas.auth.repositories.RoleResourceRepository;
import cn.dahe.cas.auth.repositories.SiteRepository;
import cn.dahe.cas.auth.search.SearchResource;
import cn.dahe.cas.auth.service.ResourceService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * on 2020/04/27
 */
@Service
public class ResourceServiceImpl extends BaseServiceImpl<Resource, Integer, ResourceRepository> implements ResourceService {


    @Autowired
    private RoleResourceRepository roleResourceRepository;

    @Autowired
    private SiteRepository siteRepository;

    @Override
    public void grantToRole(Role role, Resource resource) {
        RoleResource rr = new RoleResource();
        rr.setResId(resource.getId());
        rr.setRoleId(role.getId());
        roleResourceRepository.save(rr);
    }

    @Override
    public Resource add(Resource resource, User user) {
        resource.setName(SsoStringUtil.delSpace(resource.getName()));
        resource.setStatus(Status.ALLOW);
        if (resource.getPid() != 0) {
            Resource old = repository.findOne(resource.getPid());
            resource.setPname(old == null ? "" : old.getName());
        }
        Resource byParams = repository.getByPermissionAndSiteId(resource.getPermission(), resource.getSiteId());
        if (byParams != null) {
            throw new SsoException("授权字符不能重复");
        }
        Site one = siteRepository.findOne(resource.getSiteId());
        if (one == null) {
            throw new SsoException("该站点不存在");
        }
        resource.setSiteName(one.getName());
        resource.setModifyUserId(user.getUid());
        resource.setCreateUserId(user.getUid());
        resource.setCreateUserName(user.getTruename());
        return repository.save(resource);
    }

    @Override
    public Resource edit(Resource resource, int userId) {
        resource.setName(SsoStringUtil.delSpace(resource.getName()));
        if (resource.getPid() == resource.getId()) {
            throw new SsoException("请正确选择上级权限");
        }
        Resource old = repository.findOne(resource.getId());
        Resource byParams = repository.getByPermissionAndSiteId(resource.getPermission(), resource.getSiteId());
        if (byParams != null && byParams.getId() != old.getId()) {
            throw new SsoException("授权字符不能重复");
        }
        old.setName(resource.getName());
        old.setPermission(resource.getPermission());
        old.setUrl(resource.getUrl());
        old.setPid(resource.getPid());
        old.setPname(resource.getPname());
        if (resource.getPid() != 0) {
            Resource parent = repository.findOne(resource.getPid());
            old.setPname(parent == null ? "" : parent.getName());
        }
        resource.setModifyUserId(userId);
        return repository.save(old);
    }

    @Override
    public boolean editStatus(int id, int userId) {
        Resource one = repository.findOne(id);
        if (one == null) {
            throw new SsoException("权限不存在");
        }
        Status status = one.getStatus();
        Status updateStatus = null;
        if (status == Status.DENY) {
            updateStatus = Status.ALLOW;
        } else if (one.getStatus() == Status.ALLOW) {
            updateStatus = Status.DENY;
        }
        one.setStatus(updateStatus);
        one.setModifyUserId(userId);
        repository.save(one);
        //更新下面子权限的状态
        List<Resource> authorities = repository.getByPid(id);
        if (authorities != null && authorities.size() > 0) {
            for (Resource old : authorities) {
                old.setStatus(updateStatus);
                old.setModifyUserId(userId);
                repository.save(old);
            }
        }
        return true;
    }

    @Override
    public List<Resource> findByPid(int pid) {
        return repository.getByPid(pid);
    }

    @Override
    public Resource getByPermissionAndSiteId(String permission, int siteId) {
        return repository.getByPermissionAndSiteId(permission, siteId);
    }

    @Override
    public Page<Resource> getPage(Pageable pageable, SearchResource searchResource) {
        Specification<Resource> specification = new Specification<Resource>() {
            @Override
            public Predicate toPredicate(Root<Resource> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
                List<Predicate> predicates = new ArrayList<>();
                if (SsoStringUtil.isNotBlank(searchResource.getKeyword())) {
                    Predicate permission = builder.equal(root.get(Resource_.permission), searchResource.getKeyword());
                    Predicate name = builder.like(root.get(Resource_.name), "%" + searchResource.getKeyword() + "%");
                    predicates.add(builder.or(permission,name));
                }
                if (searchResource.getSiteId() != null) {
                    predicates.add(builder.equal(root.get(Resource_.siteId), searchResource.getSiteId()));
                }
                if (predicates.size() == 0) {
                    return null;
                }
                return builder.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        pageable = new PageRequest(pageable.getPageNumber(), pageable.getPageSize(),
                new Sort(Sort.Direction.DESC, "id"));
        return repository.findAll(specification, pageable);
    }

    @Override
    public List<Resource> getBySiteId(int siteId) {
        return repository.getBySiteIdAndStatus(siteId, Status.ALLOW);
    }

    @Override
    public List<String> getPermission(int uid, int siteId){
        return repository.getPermissionByUid(uid, siteId);
    }
    @Override
    public List<String> getPermission(int siteId){
        return repository.getPermission(siteId);
    }
    @Override
    public List<ResourceTree> listTree(int siteId, int pid){
        List<ResourceTree> treeNodeList = new ArrayList<>();
        List<Resource> resources = repository.findBySiteIdAndStatusAndPid(siteId, Status.ALLOW, pid);
        for (Resource resource : resources) {
            ResourceTree build = ResourceTree.builder().id(resource.getId()).name(resource.getName()).spread(false)
                    .siteId(resource.getSiteId()).pid(resource.getPid()).checked(0).build();
            treeNodeList.add(build);
        }
        return listChild(treeNodeList);
    }

    private List<ResourceTree>  listChild(List<ResourceTree> treeNodeList){
        if(treeNodeList != null && treeNodeList.size() > 0){
            treeNodeList.forEach(treeNode ->{
                List<ResourceTree> depTreeNodes = new ArrayList<>();
                List<Resource> resourcess = repository.findBySiteIdAndStatusAndPid(treeNode.getSiteId(), Status.ALLOW, treeNode.getId());
                if (resourcess != null && !resourcess.isEmpty()) {
                    resourcess.forEach(resource -> {
                        ResourceTree build = ResourceTree.builder().id(resource.getId()).name(resource.getName()).spread(false)
                                .siteId(resource.getSiteId()).pid(resource.getPid()).checked(0).build();
                        depTreeNodes.add(build);
                    });
                    treeNode.setChildren(depTreeNodes);
                    listChild(depTreeNodes);
                }
            });
        }
        return treeNodeList;
    }

    @Override
    public List<Resource> listByRoleId(int roleId){
        return repository.getByRoleId(roleId);
    }
}
