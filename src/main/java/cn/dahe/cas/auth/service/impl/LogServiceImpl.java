package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.Log;
import cn.dahe.cas.auth.entity.Log_;
import cn.dahe.cas.auth.repositories.LogRepository;
import cn.dahe.cas.auth.search.SearchLog;
import cn.dahe.cas.auth.service.LogService;
import cn.dahe.cas.auth.util.DateUtil;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class LogServiceImpl extends BaseServiceImpl<Log,Integer,LogRepository> implements LogService {

    @Resource(name = "redisTemplate")
    private ValueOperations<String,Integer> redisTemplate;

    @Value("${log.count}")
    private String logCount;
    @Value("${log.date}")
    private String logDate;
    @Value("${log.switch}")
    private String logSwitch;

    @Override
    public Page<Log> searchLog(Pageable pageable, final SearchLog searchLog) {
        return repository.findAll(buildSearchPredicate(searchLog), pageable);
    }

    @Override
    public List<Log> searchUserLog(int uid, SearchLog searchLog) {
        searchLog.setUid(uid);
        return repository.findAll(buildSearchPredicate(searchLog));
    }

    private Specification<Log> buildSearchPredicate(SearchLog searchLog){
        return (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if(SsoStringUtil.isNotBlank(searchLog.getSource())){
                predicates.add(builder.equal(root.get(Log_.source),searchLog.getSource()));
            }
            if(SsoStringUtil.isNotBlank(searchLog.getTitle())){
                predicates.add(builder.equal(root.get(Log_.title),searchLog.getTitle()));
            }
            if(searchLog.getType() != null){
                predicates.add(builder.equal(root.get(Log_.type),searchLog.getType()));
            }
            if(searchLog.getBeginDate() != null && searchLog.getEndDate() != null){
                predicates.add(builder.between(root.get(Log_.operateDate),searchLog.getBeginDate(),searchLog.getEndDate()));
            }
            if(searchLog.getUid()!=null){
                predicates.add(builder.equal(root.get(Log_.uid),searchLog.getUid()));
            }
            if(StringUtils.isNotBlank(searchLog.getRequestUri())){
                predicates.add(builder.equal(root.get(Log_.requestUri),searchLog.getRequestUri()));
            }
            if(StringUtils.isNotBlank(searchLog.getParamter())){
                predicates.add(builder.like(root.get(Log_.paramters),"%"+searchLog.getParamter()+"%"));
            }
            if(predicates.size()==0){
                return null;
            }
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCondition(int date, int count){
        // 保留的条数优先级高
        // 先按照保留的天数（日期）进行删除，再按照保留的条数进行清理一次
        Date beginDate = DateUtil.getBeginTime(date);
        repository.deleteByOperateDateLessThan(beginDate);
        long top = repository.count() - count;
        if (top > 0){
            int id = repository.findTopByOrderByIdDesc().getId();
            repository.deleteByIdLessThan(id-count);
        }
    }

    @Override
    public Map<String,Integer> getClearLogCondition(){
        Map<String, Integer> map = new HashMap<>(2);
        map.put("logcount",redisTemplate.get(logCount));
        map.put("logdate",redisTemplate.get(logDate));
        map.put("logswitch",redisTemplate.get(logSwitch));
        return map;
    }

    @Override
    public void saveClearLogCondition(int count, int date, int timeClean){
        redisTemplate.set(logCount, count);
        redisTemplate.set(logDate, date);
        redisTemplate.set(logSwitch, timeClean);
    }

    @Override
    public Page<Log> searchLog(Pageable pageable, int id){
        Specification<Log> specification = new Specification<Log>() {
            @Override
            public Predicate toPredicate(Root<Log> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
                List<Predicate> predicates = new ArrayList<>();
                if(id > 0){
                    predicates.add(builder.greaterThan(root.get(Log_.id),id));
                }
                if(predicates.size()==0){
                    return null;
                }
                return builder.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        pageable = new PageRequest(pageable.getPageNumber(), pageable.getPageSize(),
                new Sort(Sort.Direction.ASC, "id"));
        return repository.findAll(specification, pageable);
    }
}
