package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.RoleResource;

import java.util.List;

/**
 * <AUTHOR>
 * on 2020/04/23
 */
public interface RoleResourceService extends BaseService<RoleResource,Integer> {
    /**
     * 根据角色id查找角色权限对应关系列表
     * @param roleId 角色id
     * @return 角色权限
     */
    List<RoleResource> getByRoleId(int roleId);

    /**
     * 根据角色id删除权限
     * @param roleId 角色id
     * @return 删除结果
     */
    int deleteByRoleId(int roleId);

    /**
     * 根据角色id和权限id查找对应关系
     * @param roleId 角色id
     * @param resId 权限id
     * @return 对应关系
     */
    RoleResource getOne(int roleId, int resId);
}
