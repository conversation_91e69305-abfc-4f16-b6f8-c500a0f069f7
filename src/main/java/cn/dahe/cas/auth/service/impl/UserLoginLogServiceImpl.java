package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.domain.LoginLog;
import cn.dahe.cas.auth.repositories.UserLoginLogRepository;
import cn.dahe.cas.auth.search.SearchLoginLog;
import cn.dahe.cas.auth.service.UserLoginLogService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * on 2020/05/28 13:46
 */
@Service
public class UserLoginLogServiceImpl implements UserLoginLogService {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private UserLoginLogRepository repository;

    @Override
    public LoginLog add(LoginLog loginLog){
        //return repository.save(loginLog);
        return null;
    }

    @Override
    public Page<LoginLog> search(Pageable pageable, SearchLoginLog searchLoginLog) {
        List<Criteria> criteriaList = Lists.newArrayList();
        if(SsoStringUtil.isNotBlank(searchLoginLog.getPhone())){
            criteriaList.add(Criteria.where("phone").is(searchLoginLog.getPhone()));
        }
        if(SsoStringUtil.isNotBlank(searchLoginLog.getUsername())){
            criteriaList.add(Criteria.where("username").is(searchLoginLog.getUsername()));
        }
        if(SsoStringUtil.isNotBlank(searchLoginLog.getKeyword())){
            String builder = ".*" +
                    searchLoginLog.getKeyword() +
                    ".*";
            criteriaList.add(Criteria.where("extras").regex(builder));
        }
        if(searchLoginLog.getType() != null){
            criteriaList.add(Criteria.where("type").is(searchLoginLog.getType()));
        }
        if(searchLoginLog.getBeginDate()!=null){
            criteriaList.add(Criteria.where("createDate").gte(searchLoginLog.getBeginDate()));
        }
        if(searchLoginLog.getEndDate()!=null){
            criteriaList.add(Criteria.where("createDate").lte(searchLoginLog.getEndDate()));
        }
        if(searchLoginLog.getResult()!=null){
            criteriaList.add(Criteria.where("result").is(searchLoginLog.getResult()));
        }
        Criteria criteria=new Criteria();
        if(criteriaList.size() > 0){
            Criteria[] arr=new Criteria[criteriaList.size()];
            criteriaList.toArray(arr);
            criteria.andOperator(arr);
        }
        Query query = new Query(criteria);
        query.with(new Sort(Sort.Direction.DESC, "createDate"));
        long count = mongoTemplate.count(query, LoginLog.class);
        query.skip(pageable.getPageSize() * pageable.getPageNumber());
        query.limit(pageable.getPageSize());
        List<LoginLog> logs = mongoTemplate.find(query, LoginLog.class);
        return PageableExecutionUtils.getPage(logs, pageable,() -> count);
    }

    @Override
    public List<Integer> findLoginUserAfter(Date date){
        return mongoTemplate.getCollection("login_log")
                .distinct("uid", new Query().addCriteria(Criteria.where("createDate").gte(date)).getQueryObject());
    }
}
