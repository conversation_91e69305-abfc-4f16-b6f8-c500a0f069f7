package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.UserJob;
import cn.dahe.cas.auth.repositories.UserJobRepository;
import cn.dahe.cas.auth.service.UserJobService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * on 2018/6/15.
 */
@Service
public class UserJobServiceImpl extends BaseServiceImpl<UserJob,Integer,UserJobRepository> implements UserJobService{
    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    @Override
    public boolean exist(String job){
        if (repository.findByJob(job) == null){
            return false;
        }
        return true;
    }

}
