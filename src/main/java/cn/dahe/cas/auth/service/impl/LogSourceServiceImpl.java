package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.entity.LogSource;
import cn.dahe.cas.auth.repositories.LogSourceRepository;
import cn.dahe.cas.auth.service.LogSourceService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * on 2018/03/28
 */
@Service
public class LogSourceServiceImpl extends BaseServiceImpl<LogSource,Integer,LogSourceRepository> implements LogSourceService {
    @Override
    public List<String> listAllLogSource(){
        return Lists.transform(repository.findLogSourceByStatus(UserFlag.ALLOW), logSource -> logSource.getSource());
    }
    @Override
    public LogSource exist(String source){
        return repository.findLogSourceBySource(source);
    }
}
