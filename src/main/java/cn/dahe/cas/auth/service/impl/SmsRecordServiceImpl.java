package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.SmsRecord;
import cn.dahe.cas.auth.repositories.SmsRecordRepository;
import cn.dahe.cas.auth.service.SmsRecordService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class SmsRecordServiceImpl extends BaseServiceImpl<SmsRecord,Integer, SmsRecordRepository> implements SmsRecordService {
    @Override
    public Page<SmsRecord> search(String phone, Pageable page) {
        return repository.findByPhone(page,phone);
    }
}
