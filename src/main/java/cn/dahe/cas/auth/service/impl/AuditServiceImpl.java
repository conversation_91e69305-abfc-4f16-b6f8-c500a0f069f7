package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.Audit;
import cn.dahe.cas.auth.repositories.AuditRepository;
import cn.dahe.cas.auth.service.AuditService;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/13
 * @createTime 16:16
 * @info
 */
public class AuditServiceImpl extends BaseServiceImpl<Audit,Long,AuditRepository> implements AuditService{
}
