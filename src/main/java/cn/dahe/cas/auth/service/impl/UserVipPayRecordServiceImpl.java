package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.UserVipPayRecord;
import cn.dahe.cas.auth.repositories.UserVipPayRecordRepository;
import cn.dahe.cas.auth.service.UserVipPayRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

@Service
public class UserVipPayRecordServiceImpl extends BaseServiceImpl<UserVipPayRecord, Integer, UserVipPayRecordRepository> implements UserVipPayRecordService {

    @Autowired
    private UserVipPayRecordRepository userVipPayRecordRepository;

    public Page<Map<String,Object>> getPage(Pageable pageable, UserVipPayRecord param) {
        return userVipPayRecordRepository.pageList(param.getCustomerName(),pageable);
    }

    @Override
    public Date getMinStartTime(int customerId) {
        return userVipPayRecordRepository.getMinStartTime(customerId);
    }

    @Override
    public Date getMaxEndTime(int customerId) {
        return userVipPayRecordRepository.getMaxEndTime(customerId);
    }

}
