package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.User;
import org.springframework.util.MultiValueMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2017/12/22
 * @createTime 10:05
 * @info
 */
public interface SsoService {

    /**
     * 根据tgt获取认证信息，加入缓存
     * @param tgt
     * @return
     */
    Map<String,Object> getUserInfo(String tgt);

    /**
     * 直接根据tgt获取用户信息
     * 实现需要加入缓存
     * @param tgt
     * @return
     */
    User getUser(String tgt);

    void login(HttpServletRequest request, HttpServletResponse response, MultiValueMap<String, String> requestBody);
}
