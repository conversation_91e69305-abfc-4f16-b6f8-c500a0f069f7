package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.constants.RolePermissionNames;
import cn.dahe.cas.auth.constants.SiteDelete;
import cn.dahe.cas.auth.constants.SiteStatus;
import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.Site_;
import cn.dahe.cas.auth.entity.UserSite;
import cn.dahe.cas.auth.event.SiteEvent;
import cn.dahe.cas.auth.event.UserEvent;
import cn.dahe.cas.auth.event.UserSiteEvent;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.realm.SmsRealm;
import cn.dahe.cas.auth.repositories.SiteRepository;
import cn.dahe.cas.auth.repositories.UserSiteRepository;
import cn.dahe.cas.auth.search.SearchSite;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import com.google.common.collect.Lists;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SiteServiceImpl extends BaseServiceImpl<Site,Integer,SiteRepository> implements SiteService{

    private static final Logger log = LoggerFactory.getLogger(SiteServiceImpl.class);

    @Autowired
    private UserSiteRepository userSiteRepository;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private SecurityManager securityManager;

    @Value("#{${default.sites}}")
    private Map<String,String> sites;

    @Override
    public Site add(Site site){
        Site s = super.add(site);
        publisher.publishEvent(new SiteEvent(this,s, EntityOperation.ADD));
        return s;
    }

    @Override
    public void update(Site site) {
        super.update(site);
        Site s = super.get(site.getId());
        publisher.publishEvent(new SiteEvent(this,s, EntityOperation.EDIT));
    }

    @Override
    public void delete(Site site){
        if(site==null){
            return;
        }
        if(SiteDelete.DENY == site.getIsdelete()){
            throw new SsoException(site.getName()+"站点不能删除");
        }
//        for(String name:sites.keySet()){
//            if(name.equals(site.getName())){
//                throw new SsoException("默认站点不能删除");
//            }
//        }
        delete(site.getId());
    }

    @Override
    public Site forbidOrAllow(int id) {
        Site site = get(id);
        if(site!=null){
            SiteStatus status = site.getStatus();
            switch (status){
                case DENY:
                    site.setStatus(SiteStatus.ALLOW);
                    break;
                case ALLOW:
                    site.setStatus(SiteStatus.DENY);
                    break;
                default:
                    break;
            }
            update(site);
        }
        return site;
    }

    @Override
    public Page<Site> getByName(Pageable pageable, String siteName){
        return repository.findSiteByName(pageable, siteName);
    }
    @Override
    public boolean exist(Site site) {
        return repository.findByUrlOrName(site.getUrl(),site.getName())!=null;
    }

    @Override
    public boolean exist(String siteName) {
        return repository.findByName(siteName)!=null;
    }

    @Override
    public Site getOne(String siteName){
        return repository.findByUrlOrName(siteName, siteName);
    }

    @Override
    public Site getOneSite(String url){
        return repository.findByUrlOrName(url, url);
    }

    /**
     * 对于超级管理员直接赋予访问权限
     * @param userId
     * @param sid 站点id
     * @return
     */
    @Override
    public boolean canAccess(int userId, int sid) {
        log.debug("检测用户{}是否拥有对站点{}的访问权限",userId,sid);
        PrincipalCollection principals = new SimplePrincipalCollection(userId, SmsRealm.NAME);
        Subject subject = new Subject.Builder(securityManager).principals(principals).buildSubject();
        if(subject.hasRole(RolePermissionNames.ADMIN_ROLE)){
            return true;
        }
        UserSite userSite = userSiteRepository.findByUserIdAndSiteId(userId,sid);
        return userSite==null?false:true;
    }

    @Override
    public List<Site> getAccessSite(int uid) {
        List<UserSite> userSiteList = userSiteRepository.findByUserId(uid);
        List<Integer> ids = Lists.transform(userSiteList, UserSite::getSiteId);
        return repository.findAll(ids);
    }

    @Override
    public List<Site> getShowSite(int uid){
        List<UserSite> userSiteList = userSiteRepository.findByUserId(uid);
        List<Integer> ids = Lists.transform(userSiteList, userSite ->userSite.getSiteId());
        return repository.findAllByIdInAndIsShowOrderByWeightDesc(ids, Status.ALLOW);
    }

    @Override
    public List<Site> getShowSite(){
        return repository.findAllByIsShowOrderByWeightDesc(Status.ALLOW);
    }
    @Override
    public List<UserSite> getUserSite(int userId){
        return userSiteRepository.findByUserId(userId);
    }

    @Override
    public boolean addAccessSite(int userId, List<Integer> sids) {
        sids = sids.stream().distinct().collect(Collectors.toList());
        for (int sid : sids) {
            UserSite userSite=new UserSite();
            userSite.setSiteId(sid);
            userSite.setUserId(userId);
            userSiteRepository.save(userSite);
        }
        //20250610 风评AI 检测开通与关闭时 需异步开通对应系统的权限
        publisher.publishEvent(new UserSiteEvent(userId,sids));
        return true;
    }

    @Override
    public boolean addAccessSite(int userId, int sid) {
        UserSite userSite=new UserSite();
        userSite.setSiteId(Integer.valueOf(sid));
        userSite.setUserId(userId);
        userSiteRepository.save(userSite);
        return true;
    }

    @Override
    public boolean deleteAccessSite(int userId, int sid) {
        userSiteRepository.deleteByUserIdOrSiteId(userId,sid);
        return true;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUserSite(int userId, int sid){
        userSiteRepository.deleteByUserIdAndSiteId(userId, sid);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteUserSite(int userId, int[] sids) {
        userSiteRepository.deleteAllByUserIdAndSiteIdIn(userId,sids);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllSite(int userId) {
        userSiteRepository.deleteAllByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllUserSite(int sid) {
        userSiteRepository.deleteAllBySiteId(sid);
    }

    @Override
    public void addDefaultSites() {
        if(sites==null){
            return;
        }
        for(String name:sites.keySet()){
            if(!exist(name)){
                Site site = new Site();
                site.setName(name);
                site.setUrl(sites.get(name));
                site.setDescription("默认站点，删除请谨慎");
                add(site);
            }else{
                //进行更新以保证在默认站点非正常修改情况下不会造成不更新问题
                Site site = getOne(name);
                site.setUrl(sites.get(name));
                update(site);
            }
        }
    }

    @Override
    public void addDefaultSiteToUser(int userId) {
        //将默认站点添加到可访问站点
        if(sites==null){
            return;
        }
        //for(String name:sites.keySet()){
        //    Site site = repository.findByName(name);
        //    addAccessSite(userId,site.getId());
        //}
        //分配默认站点，暂时不分配风评助手，因为涉及认证问题
        List<Site> allCannotDeleteSites = repository.findAllByIsdelete(SiteDelete.DENY);
        List<Integer> allDefaultSites = allCannotDeleteSites.stream().
                filter(el -> el.getName().equals("local")).
                map(Site::getId).collect(Collectors.toList());
        addAccessSite(userId,allDefaultSites);
    }

    @Override
    public List<String> getLogNames() {
        return Lists.transform(get(), site -> site.getName());
    }

    @Override
    public Page<Site> searchSite(Pageable pageable, final SearchSite searchSite){
        Specification<Site> specification = new Specification<Site>() {
            @Override
            public Predicate toPredicate(Root<Site> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
                List<Predicate> predicates = new ArrayList<>();
                if(SsoStringUtil.isNotBlank(searchSite.getName())){
                    predicates.add(builder.like(root.get(Site_.name),"%"+searchSite.getName()+"%"));
                }
                if(SsoStringUtil.isNotBlank(searchSite.getName())){
                    predicates.add(builder.equal(root.get(Site_.url),searchSite.getName()));
                }
                if(predicates.size()==0){
                    return null;
                }
                return builder.or(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        return repository.findAll(specification, pageable);
    }

    @Override
    public List<Site> getDefaultSites() {
        return repository.findAllByIsdelete(SiteDelete.DENY);
    }

    @Override
    public Site getByLogTag(String tag){
        return repository.findByLogTag(tag);
    }
}
