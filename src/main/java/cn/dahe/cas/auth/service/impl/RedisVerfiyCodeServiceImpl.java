package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.constants.ErrorCode;
import cn.dahe.cas.auth.constants.SmsType;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.VerfiyCodeService;
import cn.dahe.cas.auth.util.SecurityUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class RedisVerfiyCodeServiceImpl implements VerfiyCodeService {

    private static final Logger log = LoggerFactory.getLogger(RedisVerfiyCodeServiceImpl.class);

    @Value("${sms.expires}")
    private int expires;

    @Value("${sms.code}")
    private String sms_code;

    @Value("${sms.yz}")
    private String sms_yz;

    @Value("${sms.bind.wx}")
    private String sms_bind_wx;

    @Resource(name = "tokenRedisTemplate")
    private ValueOperations<String, String> operations;

    @Override
    public String saveCode(String phone, String code) {
        if (operations.getOperations().hasKey(sms_code + phone)) {
            throw new SsoException(ErrorCode.CODE_SENT);
        }
        operations.set(sms_code + phone, code, expires, TimeUnit.SECONDS);
        return code;
    }

    @Override
    public boolean isValid(String phone, String code, SmsType type) {
        String key = returnKey(type);
        log.error("手机号{} 验证码{} 类型{} key{}",phone,code,type,key);
        boolean exists = operations.getOperations().hasKey(key+phone);
        if(exists){
            log.error("手机号{} 验证码{} 类型{} 存在，值为{}",phone,code,type,operations.get(key + phone));
            return operations.get(key + phone).equals(code);
        }
        log.error("手机号{} 类型{} key{} 不存在",phone,type,key);
        return false;
    }

    @Override
    public boolean isValid(String phone, SmsType type) {
        String basePhone = phone;
        String key = returnKey(type);
        boolean exist = operations.getOperations().hasKey(key + basePhone);
        return exist;
    }

    public String returnKey(SmsType type){
        String key="";
        switch (type){
            case LOGIN:
                key += sms_code;
                break;
            case UPDATE:
                key += sms_yz;
                break;
            case WX_BIND:
                key += sms_bind_wx;
            default:
                break;
        }
        return key;
    }

    @Override
    public void deleteCode(String phone, SmsType type) {
        String basePhone = SecurityUtil.base64Encode(phone);
        String key = returnKey(type);
        operations.getOperations().delete(key + basePhone);
    }
}
