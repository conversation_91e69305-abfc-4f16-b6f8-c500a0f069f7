package cn.dahe.cas.auth.service;


import cn.dahe.cas.auth.entity.FocusPerson;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.search.SearchFocus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface FocusPersonService extends BaseService<FocusPerson, Integer> {


    /**
     * 分页获取 重点人物
     *
     * @param pageable
     * @param searchFocus
     * @param user
     * @return
     */
    Page<FocusPerson> getPage(Pageable pageable, SearchFocus searchFocus, User user);

    /**
     * 根据用户获取关键词
     *
     * @param userId
     * @return
     */
    List<FocusPerson> listByUserId(Integer userId);
}
