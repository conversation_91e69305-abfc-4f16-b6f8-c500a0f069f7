package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.entity.ServiceConsult;
import cn.dahe.cas.auth.entity.ServiceConsult_;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.repositories.ServiceConsultRepository;
import cn.dahe.cas.auth.search.SearchFocus;
import cn.dahe.cas.auth.service.ServiceConsultService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ServiceConsultServiceImpl extends BaseServiceImpl<ServiceConsult,Integer, ServiceConsultRepository> implements ServiceConsultService {
    @Override
    public Page<ServiceConsult> getPage(Pageable pageable, SearchFocus searchFocus, User user) {
        Specification<ServiceConsult> specification = new Specification<ServiceConsult>() {
            @Override
            public Predicate toPredicate(Root<ServiceConsult> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
                List<Predicate> predicates = new ArrayList<>();
                if (SsoStringUtil.isNotBlank(searchFocus.getKeywords())) {
                    Predicate name = builder.like(root.get(ServiceConsult_.CONSULT_USER_NAME), "%" + searchFocus.getKeywords() + "%");
                    predicates.add(name);
//                    predicates.add(builder.or(sn,name));
                }
                if (searchFocus.getType() != -2) {
                    predicates.add(builder.equal(root.get(ServiceConsult_.TYPE), searchFocus.getType()));
                }
                if (predicates.size() == 0) {
                    return null;
                }
                return builder.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        pageable = new PageRequest(pageable.getPageNumber(), pageable.getPageSize(),
                new Sort(Sort.Direction.DESC, ServiceConsult_.CONSULT_TIME));
        return repository.findAll(specification, pageable);
    }

    @Override
    public boolean addServiceConsult(int type, User user) {
        ServiceConsult consult = repository.findByConsultUserIdAndType(user.getUid(),type);
        if(consult != null){
            consult.setCount(consult.getCount() + 1);
            consult.setConsultTime(new Date());
        }else {
            consult = new ServiceConsult();
            consult.setConsultUserId(user.getUid());
            consult.setConsultUserName(user.getTruename());
            consult.setConsultUserLink(user.getPhone());
            consult.setUnit(user.getCompany());
            consult.setType(type);
            consult.setCount(1);
            consult.setConsultTime(new Date());
        }
        consult = repository.saveAndFlush(consult);
        return consult.getId() > 0;
    }
}
