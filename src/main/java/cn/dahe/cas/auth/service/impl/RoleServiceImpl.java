package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.entity.Role;
import cn.dahe.cas.auth.entity.Role_;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.repositories.ResourceRepository;
import cn.dahe.cas.auth.repositories.RoleRepository;
import cn.dahe.cas.auth.repositories.RoleResourceRepository;
import cn.dahe.cas.auth.repositories.SiteRepository;
import cn.dahe.cas.auth.repositories.UserRoleRepository;
import cn.dahe.cas.auth.search.SearchRole;
import cn.dahe.cas.auth.service.RoleService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@Service
public class RoleServiceImpl extends BaseServiceImpl<Role,Integer,RoleRepository> implements RoleService{

    @Autowired
    private RoleResourceRepository roleResourceRepository;

    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private UserRoleRepository userRoleRepository;

    @Autowired
    private SiteRepository siteRepository;

    @Override
    public Role addRole(Role role, User user){
        role.setName(SsoStringUtil.delSpace(role.getName()));
        String sn = SsoStringUtil.getPinYinHeadChar(role.getName());
        Role old = repository.findBySiteIdAndSn(role.getSiteId(), sn);
        if (old != null) {
            throw new SsoException("角色sn已存在");
        }
        role.setSn(sn);
        Site one = siteRepository.findOne(role.getSiteId());
        if (one == null){
            throw new SsoException("该站点不存在");
        }
        role.setSiteName(one.getName());
        role.setModifyUserId(user.getUid());
        role.setCreateUserId(user.getUid());
        role.setCreateUserName(user.getTruename());
        role.setStatus(Status.ALLOW);
        return repository.save(role);
    }

    @Override
    public List<Role> findBySiteId(int siteId){
        return repository.findBySiteIdAndStatus(siteId, Status.ALLOW);
    }

    @Override
    public Role findBySiteIdAndSn(int siteId, String sn){
        return repository.findBySiteIdAndSn(siteId, sn);
    }

    @Override
    public Page<Role> getPage (Pageable pageable, SearchRole searchRole){
        Specification<Role> specification = new Specification<Role>() {
            @Override
            public Predicate toPredicate(Root<Role> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
                List<Predicate> predicates = new ArrayList<>();
                if (SsoStringUtil.isNotBlank(searchRole.getName())) {
                    Predicate name = builder.like(root.get(Role_.name), "%" + searchRole.getName() + "%");
                    Predicate sn = builder.equal(root.get(Role_.sn), searchRole.getName());
                    predicates.add(builder.or(sn,name));
                }
                if (searchRole.getSiteId() != null) {
                    predicates.add(builder.equal(root.get(Role_.siteId), searchRole.getSiteId()));
                }
                if (predicates.size() == 0) {
                    return null;
                }
                return builder.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        pageable = new PageRequest(pageable.getPageNumber(), pageable.getPageSize(),
                new Sort(Sort.Direction.DESC, "id"));
        return repository.findAll(specification, pageable);
    }

    @Override
    public List<String> getSnByUserId(int userId, int siteId){
        return repository.findSnByUserId(userId, siteId);
    }

}
