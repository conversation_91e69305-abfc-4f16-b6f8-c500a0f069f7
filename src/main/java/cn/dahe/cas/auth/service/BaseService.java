package cn.dahe.cas.auth.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @param <T>
 * 基础的服务接口
 */
public interface BaseService<T,ID extends Serializable> {
    /**
     * 删除一个实体
     * @param t
     */
    void delete(T t);

    /**
     * 根据id删除实体
     * @param id
     */
    void delete(ID id);

    /**
     * 更新实体
     * @param t
     */
    void update(T t);

    /**
     * 添加实体
     * @param t
     * @return
     */
    T add(T t);

    /**
     * 分页获取实体
     * @param pageable
     * @return
     */
    Page<T> get(Pageable pageable);

    /**
     * 获取全部实体，尽量使用分页方式
     * @return
     */
    List<T> get();

    /**
     * 根据id获取对应实体
     * @param id
     * @return
     */
    T get(ID id);

    /**
     *
     */
    int add(List<T> tList);
}
