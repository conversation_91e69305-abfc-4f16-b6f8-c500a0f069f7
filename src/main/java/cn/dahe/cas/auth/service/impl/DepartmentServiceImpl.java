package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.dto.TreeNode;
import cn.dahe.cas.auth.entity.Department;
import cn.dahe.cas.auth.repositories.DepartmentRepository;
import cn.dahe.cas.auth.service.DepartmentService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Transactional
public class DepartmentServiceImpl extends BaseServiceImpl<Department, Integer, DepartmentRepository> implements DepartmentService {

    @Override
    public boolean existsByNameAndPid(String name, Integer pid) {
        return repository.existsByNameAndPid(name, pid);
    }

    @Override
    public boolean existsByNameAndPidAndIdNot(String name, Integer pid, Integer id) {
        return repository.existsByNameAndPidAndIdNot(name, pid, id);
    }

    @Override
    public Page<Department> findByConditions(String keywords, Integer pid, Pageable pageable) {
        Specification<Department> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 状态不为删除状态
            predicates.add(criteriaBuilder.notEqual(root.get("status"), -1));

            // 父ID条件
            if (pid != null) {
                predicates.add(criteriaBuilder.equal(root.get("pid"), pid));
            }

            // 关键词搜索（部门名称）
            if (StringUtils.hasText(keywords)) {
                predicates.add(criteriaBuilder.like(root.get("name"), "%" + keywords + "%"));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        return repository.findAll(spec, pageable);
    }

    @Override
    public List<Department> findByPidAndStatus(Integer pid, Integer status) {
        return repository.findByPidAndStatusOrderBySeqAscIdAsc(pid, status);
    }

    @Override
    public boolean updateSeq(String newIds, String seqIds) {
        try {
            if (!StringUtils.hasText(newIds)) {
                return false;
            }

            String[] idArray = newIds.split(",");
            for (int i = 0; i < idArray.length; i++) {
                Integer id = Integer.parseInt(idArray[i].trim());
                Department department = repository.getOne(id);
                if (department != null) {
                    department.setSeq(i + 1);
                    repository.save(department);
                }
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean hasChildren(Integer id) {
        return repository.countByPidAndStatus(id, 1) > 0;
    }

    @Override
    public boolean wouldCreateCircularReference(Integer id, Integer pid) {
        if (id.equals(pid)) {
            return true;
        }

        // 递归检查pid的所有父级
        Department parent = repository.getOne(pid);
        while (parent != null && parent.getPid() > 0) {
            if (parent.getPid() == id) {
                return true;
            }
            parent = repository.getOne(parent.getPid());
        }

        return false;
    }

    @Override
    public List<TreeNode> getDepartmentTree() {
        List<TreeNode> treeNodes = Lists.newArrayList();
        List<Department> allDepartments = repository.findByStatusOrderBySeqAscIdAsc(1);
        for (Department staticResourceGroup : allDepartments) {
            TreeNode treeNode = new TreeNode();
            treeNode.setId(staticResourceGroup.getId());
            treeNode.setName(staticResourceGroup.getName());
            treeNode.setPid(0);
            treeNode.setSpread(false);
            treeNode.setParent(true);
            treeNodes.add(treeNode);
        }
        initStaticResourceTreeNode(treeNodes);
        return treeNodes;
    }

    @Override
    public List<Department> listAll() {
        return repository.findByStatus(1);
    }

    private void initStaticResourceTreeNode(List<TreeNode> treeNodes) {
        for (TreeNode treeNode : treeNodes) {
            List<Department> groupList = repository.findByPid(treeNode.getId());
            if (CollectionUtils.isNotEmpty(groupList)) {
                List<TreeNode> children = Lists.newArrayList();
                for (Department resourceGroup : groupList) {
                    TreeNode child = new TreeNode();
                    child.setId(resourceGroup.getId());
                    child.setName(resourceGroup.getName());
                    child.setPid(resourceGroup.getPid());
                    child.setSpread(false);
                    child.setParent(false);
                    children.add(child);
                }
                treeNode.setChildren(children);
                initStaticResourceTreeNode(children);
            }
        }
    }
}