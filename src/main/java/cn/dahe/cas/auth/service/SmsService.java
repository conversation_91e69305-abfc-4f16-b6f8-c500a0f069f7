package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.SsoSms;
import cn.dahe.cas.auth.exception.SsoException;

/**
 * <AUTHOR>
 * the servie about send message
 * the service must be security
 */
public interface SmsService {
    /**
     * 发送账号开通提醒
     * @param phone
     * @return
     */
    int sendAccountCreateSms(String phone);

    /**
     * chenmf
     * fasong 请求验证码接口并在本地记录
     * @param phone
     * @param token
     * @param type
     * @return
     */
    JsonResult sendSms(String phone, String token, String type);

    /**
     * 发送短信
     * @param ssosms
     * @throws SsoException
     */
    void sendSsoSms(SsoSms ssosms) throws SsoException;
}
