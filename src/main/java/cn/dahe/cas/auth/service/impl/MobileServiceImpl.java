package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.entity.Mobile;
import cn.dahe.cas.auth.repositories.MobileRepository;
import cn.dahe.cas.auth.service.MobileService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 提供手机号相关服务
 */
@Service
public class MobileServiceImpl extends BaseServiceImpl<Mobile,Integer,MobileRepository> implements MobileService{

    @Value("${exceptProvinceKeys}")
    private String allow;

    private List<String> allowAreas = new ArrayList<>();

    @PostConstruct
    public void init(){
        allowAreas = Arrays.asList(StringUtils.delimitedListToStringArray(allow,"&"));
    }

    @Cacheable(CacheKey.PHONE_INFO_KEY)
    @Override
    public Mobile getMobileByNumber(String number) {
        String query = number.substring(0,7);
        return repository.findByMobile(query);
    }

    @Override
    public boolean allowRegister(String phone) {
        Mobile mobile = getMobileByNumber(phone);
        //查询不到手机号信息时认为可以直接通过
        if(mobile==null){
            return true;
        }
        return allowAreas.contains(mobile.getProvince());
    }
}
