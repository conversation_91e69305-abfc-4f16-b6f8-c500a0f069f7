package cn.dahe.cas.auth.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * 主要对禁用手机号处理，包括添加和删除禁用手机号
 */
public interface PhoneService {
    /**
     * 分页获取禁用手机号
     * @param page
     * @return
     */
    Page<String> getForbidPhones(Pageable page);
    /**
     * 添加禁用手机号
     * @param phone
     */
    void addPhone(String phone);

    /**
     * 删除禁用手机号
     * @param phone
     */
    void remove(String phone);

    /**
     * 根据手机号查找禁用用户
     * @param page
     * @param phone
     * @return
     */
    Page<String> getForbidPhones(Pageable page, String phone);
}
