package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.UserSite;
import cn.dahe.cas.auth.search.SearchSite;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * 站点管理服务
 * 包括用户站点关系也在该服务进行处理
 */
public interface SiteService extends BaseService<Site,Integer>{
    /**
     * 禁用或启用站点
     * @param id
     * @return
     */
    Site forbidOrAllow(int id);

    /**
     * 按照条件分页查询
     * @param pageable
     * @param siteName
     * @return
     */
    Page<Site> getByName(Pageable pageable, String siteName);

    /**
     * 判断站点是否存在
     * @param site
     * @return
     */
    boolean exist(Site site);

    /**
     * 通过名称判断站点是否存在
     * @param siteName
     * @return
     */
    boolean exist(String siteName);

    /**
     * 通过名称查询站点信息
     * @param siteName
     * @return
     */
    Site getOne(String siteName);
    /**
     * 通过名称查询站点信息
     * @param url
     * @return
     */
    Site getOneSite(String url);

    /**
     * 判断用户对该站点的访问权限
     * @param uid
     * @param sid 站点id
     * @return
     */
    boolean canAccess(int uid, int sid);

    /**
     * 获取用户可访问站点列表，不再进行分页
     * @param uid
     * @return
     */
    List<Site> getAccessSite(int uid);

    /**
     * 根据用户id获取用户中心显示的站点列表
     * @param uid 用户id
     * @return 站点信息列表
     */
    List<Site> getShowSite(int uid);

    /**
     * 展示所有显示的站点列表
     * @return 站点列表
     */
    List<Site> getShowSite();

    /**
     * 根据userId获取用户可访问的站点信息
     * @param userId
     * @return
     */
    List<UserSite> getUserSite(int userId);

    /**
     * 添加用户可访问的站点
     * @param userId
     * @param sids
     * @return
     */
    boolean addAccessSite(int userId,List<Integer> sids);

    boolean addAccessSite(int userId,int sid);

    /**
     * 根据用户Id或者站点名删除用户可访问站点
     * @param userId
     * @param sid
     * @return
     */
    boolean deleteAccessSite(int userId, int sid);

    /**
     * 根据用户Id和站点名删除用户可访问站点
     * @param userId
     * @param sid
     * @return
     */
    boolean deleteUserSite(int userId, int sid);

    /**
     * 根据用户Id和站点名删除用户可访问站点
     * @param userId
     * @param sids
     * @return
     */
    boolean deleteUserSite(int userId, int[] sids);

    /**
     * 删除某个用户的所有可用站点
     * @param userId
     */
    void deleteAllSite(int userId);

    /**
     * 根据站点名删除用户站点中的所有相关记录
     * @param sid
     */
    void deleteAllUserSite(int sid);

    /**
     * 添加默认站点
     */
    void addDefaultSites();

    /**
     * 为用户添加默认站点
     */
    void addDefaultSiteToUser(int userId);

    /**
     * 获取所有站点对应的日志队列tag
     * @return
     */
    List<String> getLogNames();

    /**
     * 动态查询获取系统
     * @param pageable
     * @param searchSite
     * @return
     */
    Page<Site> searchSite(Pageable pageable, final SearchSite searchSite);

    List<Site> getDefaultSites();

    /**
     * 根据标签查找系统
     * @param tag 日志标签
     * @return 系统
     */
    Site getByLogTag(String tag);

}
