package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.domain.LoginLog;
import cn.dahe.cas.auth.search.SearchLoginLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * on 2020/05/28
 */
public interface UserLoginLogService {

    /**
     * 添加系登录日志
     * @param loginLog 登录日志
     * @return 添加的日志
     */
    LoginLog add(LoginLog loginLog);

    /**
     * 分页根据条件查询登录日志
     * @param pageable 分页
     * @param searchLoginLog 查询条件
     * @return 登录日志
     */
    Page<LoginLog> search(Pageable pageable, SearchLoginLog searchLoginLog);

    /**
     * 某个日期之前未登录的用户id
     * @param date 日期
     * @return 用户id
     */
    List<Integer> findLoginUserAfter(Date date);
}
