package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.dto.IpInfo;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public interface IpService {
    /**
     * 从HttpServletRequest中获取ip地址
     * @param request
     * @return
     */
    public String getIpAddress(HttpServletRequest request);

    /**
     * 根据ip地址获取ip信息
     * 实现加入缓存机制
     * @param ip
     * @return
     */
    public IpInfo getIpInfo(String ip);

    /**
     * 判断ip是否运行注册
     * @param ip
     * @return
     */
    public boolean allowRegister(String ip);
}
