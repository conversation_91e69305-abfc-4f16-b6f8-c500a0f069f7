package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.domain.Log;
import cn.dahe.cas.auth.repositories.SystemLogRepository;
import cn.dahe.cas.auth.search.SearchLog;
import cn.dahe.cas.auth.service.SystemLogService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.TextCriteria;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * on 2020/5/20.
 */
@Service
public class SystemLogServiceImpl implements SystemLogService {

    @Autowired
    private SystemLogRepository repository;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Log add(Log log){
        return repository.save(log);
    }

    @Override
    public void add(List<Log> logs) {
        repository.save(logs);
    }

    @Override
    public Page<Log> page(SearchLog search, Pageable pageable){
        List<Criteria> criteriaList = Lists.newArrayList();
        if (search.getUid() != null && search.getUid() > 0){
            criteriaList.add(Criteria.where("uid").is(search.getUid()));
        }
        if(SsoStringUtil.isNotBlank(search.getSource())){
            criteriaList.add(Criteria.where("source").is(search.getSource()));
        }
        if (search.getBeginDate() != null){
            criteriaList.add(Criteria.where("operateDate").gte(search.getBeginDate()));
        }
        if (search.getEndDate() != null){
            criteriaList.add(Criteria.where("operateDate").lte(search.getEndDate()));
        }
        if (search.getType() != null){
            criteriaList.add(Criteria.where("type").is(search.getType()));
        }
        if (SsoStringUtil.isNotBlank(search.getTitle())){
            criteriaList.add(Criteria.where("title").is(search.getTitle()));
        }
        if(StringUtils.isNotBlank(search.getRequestUri())){
            criteriaList.add(Criteria.where("requestUri").is(search.getRequestUri()));
        }
        Criteria criteria=new Criteria();
        if(criteriaList.size() > 0){
            Criteria[] arr=new Criteria[criteriaList.size()];
            criteriaList.toArray(arr);
            criteria.andOperator(arr);
        }
        Query query = new Query(criteria);
        // text-search
        if(StringUtils.isNotBlank(search.getParamter())){
            TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(search.getParamter());
            query.addCriteria(textCriteria);
        }
        query.with(new Sort(Sort.Direction.DESC, "operateDate"));
        long count = mongoTemplate.count(query, Log.class);
        query.skip(pageable.getPageSize() * pageable.getPageNumber());
        query.limit(pageable.getPageSize());
        List<Log> logs = mongoTemplate.find(query, Log.class);
        return PageableExecutionUtils.getPage(logs, pageable,() -> count);
    }
}
