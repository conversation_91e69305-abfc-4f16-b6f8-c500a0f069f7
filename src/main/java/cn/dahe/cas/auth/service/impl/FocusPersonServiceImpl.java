package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.constants.UserType;
import cn.dahe.cas.auth.entity.FocusPerson;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.repositories.FocusPersonRepository;
import cn.dahe.cas.auth.search.SearchFocus;
import cn.dahe.cas.auth.service.FocusPersonService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@Service
public class FocusPersonServiceImpl extends BaseServiceImpl<FocusPerson, Integer, FocusPersonRepository> implements FocusPersonService {
    @Override
    public Page<FocusPerson> getPage(Pageable pageable, SearchFocus searchFocus, User user) {
        Specification<FocusPerson> specification = new Specification<FocusPerson>() {
            @Override
            public Predicate toPredicate(Root<FocusPerson> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
                List<Predicate> predicates = new ArrayList<>();
                if (SsoStringUtil.isNotBlank(searchFocus.getKeywords())) {
                    Predicate name = builder.like(root.get("name"), "%" + searchFocus.getKeywords() + "%");
                    predicates.add(name);
//                    predicates.add(builder.or(sn,name));
                }
                // 判断管理员
                if(user.getType() != UserType.MANAGER){
                    predicates.add(builder.equal(root.get("create_user_id"), user.getUid()));
                }
                if (searchFocus.getStatus() != -2) {
                    predicates.add(builder.equal(root.get("status"), searchFocus.getStatus()));
                }
                if (predicates.size() == 0) {
                    return null;
                }
                return builder.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };
        pageable = new PageRequest(pageable.getPageNumber(), pageable.getPageSize(),
                new Sort(Sort.Direction.DESC, "id"));
        return repository.findAll(specification, pageable);
    }

    @Override
    public List<FocusPerson> listByUserId(Integer userId) {
        return repository.findAllByCreateUserIdAndStatus(userId, Status.ALLOW);
    }
}
