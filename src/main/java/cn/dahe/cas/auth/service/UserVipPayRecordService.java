package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.entity.UserVipPayRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.Map;

public interface UserVipPayRecordService extends BaseService<UserVipPayRecord,Integer>{

    Page<Map<String,Object>> getPage (Pageable pageable, UserVipPayRecord param);

    Date getMinStartTime(int customerId);

    Date getMaxEndTime(int customerId);
}
