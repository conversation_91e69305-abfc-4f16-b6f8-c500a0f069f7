package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.constants.SmsType;

public interface VerfiyCodeService {
    String saveCode(String phone, String code);
    /**
     * 判断登录验证码是否正确
     * @param phone
     * @param code
     * @return
     */
    boolean isValid(String phone, String code, SmsType type);

    /**
     * 判断验证码是否失效
     * @param phone
     * @return
     */
    boolean isValid(String phone, SmsType type);

    /**
     * 删除验证码
     * @param phone 手机号
     * @param type 类型
     */
    void deleteCode(String phone, SmsType type);
}
