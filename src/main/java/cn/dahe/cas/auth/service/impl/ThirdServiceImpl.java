package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.ThirdService;
import cn.dahe.cas.auth.util.OkHttpUtils;
import com.alibaba.fastjson.JSON;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ThirdServiceImpl implements ThirdService {

    private static final Logger logger = LoggerFactory.getLogger(ThirdServiceImpl.class);

    @Override
    @Cacheable(CacheKey.WEATHER_INFO_KEY)
    public Object getWeather(String city) throws UnsupportedEncodingException {
        throw new SsoException("天气信息获取失败，请联系技术人员！");
    }

    @Override
    @CacheEvict(CacheKey.WEATHER_INFO_KEY)
    public void clearWeather() {
        logger.debug("清理天气缓存");
    }
}
