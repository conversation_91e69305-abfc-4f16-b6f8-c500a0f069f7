package cn.dahe.cas.auth.service;

import cn.dahe.cas.auth.dto.RoleDto;
import cn.dahe.cas.auth.dto.UserInfo;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.search.SearchUser;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * Created by Administrator on 2017/10/16.
 */
public interface UserService extends BaseService<User, Integer> {

    /**
     * 获取微信绑定用户
     *
     * @param openid
     * @return
     */
    User getUserByWx(String openid);

    /**
     * 获取用户
     *
     * @param identify
     * @return
     */
    User getUser(String identify);

    /**
     * 获取某站点下用户
     *
     * @param identify
     * @param sid
     * @return
     */
    Page<User> getUser(Pageable pageable, String identify, String sid);

    /**
     * 获取某组织机构下用户
     *
     * @param identify
     * @param departmentId
     * @return
     */
    Page<User> getDepartmentUser(Pageable pageable, String identify, int departmentId);

    /**
     * 通过用户名获取用户
     *
     * @param username
     * @return
     */
    User getUserByUsername(String username);

    /**
     * 通过手机号获取用户
     * 目前手机号不唯一
     *
     * @param phone
     * @return
     */
    User getUserByPhone(String phone);

    /**
     * 获取用户详情，包括权限、角色等信息
     *
     * @param user
     * @param siteId
     * @return
     */
    UserInfo getDetail(User user, int siteId);

    /**
     * 判断用户是否存在
     *
     * @param username
     * @return
     */
    boolean exist(String username);

    /**
     * 判断手机号是否存在
     *
     * @param phone
     * @return
     */
    boolean existPhone(String phone);


    /**
     * 判断用户密码是否正确
     *
     * @param user
     * @param password
     * @return
     */
    boolean isPasswordCorrect(User user, String password);

    /**
     * 查询用户
     *
     * @param pageable
     * @param searchUser
     * @return
     */
    Page<User> searchUser(Pageable pageable, SearchUser searchUser);

    /**
     * 查询用户 不分页
     *
     * @param searchUser
     * @return
     */
    List<User> searchUser(SearchUser searchUser);

    /**
     * 查找角色下的正常用户
     *
     * @param pageable   分页
     * @param searchUser 查询条件
     * @return 用户列表
     */
    Page<User> findUserByRoleId(Pageable pageable, SearchUser searchUser);

    /**
     * 根据可通过系统id查找用户
     *
     * @param pageable 分页
     * @param siteId   系统id
     * @return 用户列表
     */
    Page<User> findBySiteId(Pageable pageable, int siteId);

    /**
     * 根据站点和用户查找具体的信息（包括sn 和permission）
     *
     * @param userId 用户id
     * @param siteId 站点id
     * @return 用户信息
     */
    RoleDto getBySiteAndUid(int userId, int siteId);


    User getBySynchronizeId(String synchronizeId);
}
