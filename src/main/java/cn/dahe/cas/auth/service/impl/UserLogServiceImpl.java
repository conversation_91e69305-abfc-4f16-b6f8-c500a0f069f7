package cn.dahe.cas.auth.service.impl;

import cn.dahe.cas.auth.constants.LogOperation;
import cn.dahe.cas.auth.domain.UserLog;
import cn.dahe.cas.auth.repositories.UserLogRepository;
import cn.dahe.cas.auth.search.SearchUserLog;
import cn.dahe.cas.auth.service.UserLogService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * on 2020/5/7.
 */
@Service
public class UserLogServiceImpl implements UserLogService {

    @Autowired
    private UserLogRepository repository;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public UserLog add(UserLog userLog){
        return repository.save(userLog);
    }

    @Override
    public Page<UserLog> page(SearchUserLog search, Pageable pageable){
        List<Criteria> criteriaList = Lists.newArrayList();
        if (search.getUserId() != null && search.getUserId() > 0){
            criteriaList.add(Criteria.where("userId").is(search.getUserId()));
        }
        if (search.getBeginDate() != null){
            criteriaList.add(Criteria.where("operateDate").gte(search.getBeginDate()));
        }
        if (search.getEndDate() != null){
            criteriaList.add(Criteria.where("operateDate").lte(search.getEndDate()));
        }
        if (search.getOperateType() != null){
            criteriaList.add(Criteria.where("operateType").is(search.getOperateType()));
        }
        if (SsoStringUtil.isNotBlank(search.getKeyword())){
            criteriaList.add(Criteria.where("operateDescription").regex(Pattern.compile(".*" + search.getKeyword() + ".*$",Pattern.CASE_INSENSITIVE)));
        }
        Criteria criteria=new Criteria();
        if(criteriaList.size() > 0){
            Criteria[] arr=new Criteria[criteriaList.size()];
            criteriaList.toArray(arr);
            criteria.andOperator(arr);
        }
        Query query = new Query(criteria);
        query.with(new Sort(Sort.Direction.DESC, "operateDate"));
        pageable = new PageRequest(pageable.getPageNumber(), pageable.getPageSize());
        long count = mongoTemplate.count(query, UserLog.class);
        query.skip(pageable.getPageSize() * pageable.getPageNumber());
        query.limit(pageable.getPageSize());
        List<UserLog> userLogs = mongoTemplate.find(query, UserLog.class);
        return PageableExecutionUtils.getPage(userLogs, pageable,() -> count);
    }

    @Override
    public List<LogOperation> getOperateType(){
        return Arrays.asList(LogOperation.values());
    }
}