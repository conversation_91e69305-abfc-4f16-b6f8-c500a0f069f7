//package cn.dahe.cas.auth.factory;
//
//import cn.dahe.cas.auth.dto.CacheCloudResult;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.beans.factory.config.AbstractFactoryBean;
//import org.springframework.core.env.MapPropertySource;
//import org.springframework.core.env.PropertySource;
//import org.springframework.data.redis.connection.RedisClusterConfiguration;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
//import org.springframework.util.StringUtils;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @email <EMAIL>
// * @createDate 2018/3/20
// * @createTime 18:17
// * @info 由于redis所有的集群信息都动态获取，因此使用spring工厂bean进行redis connection factory的创建
// */
//public class CacheCloudRedisFactoryBean extends AbstractFactoryBean<RedisConnectionFactory>{
//
//    @Autowired
//    private ClusterInfo clusterInfo;
//
//    @Value("${cache_cloud.url}")
//    private String cacheCloudApi;
//    @Value("${cache_cloud.appid}")
//    private String appId;
//
//    private static final String DELIMITER = " ";
//    private static final String PROPERTY_NAME = "cache_cloud";
//
//    @Override
//    public Class<?> getObjectType() {
//        return JedisConnectionFactory.class;
//    }
//
//    @Override
//    protected RedisConnectionFactory createInstance() throws Exception {
//        CacheCloudResult result = clusterInfo.returnResult();
//        String[] nodes = StringUtils.delimitedListToStringArray(result.getShardInfo(),DELIMITER);
//        Map<String,Object> properties = new HashMap<>(2);
//        properties.put("spring.redis.cluster.nodes",StringUtils.arrayToCommaDelimitedString(nodes));
//        properties.put("spring.redis.cluster.password",result.getPassword());
//        PropertySource source = new MapPropertySource(PROPERTY_NAME,properties);
//        JedisConnectionFactory connectionFactory = new JedisConnectionFactory(new RedisClusterConfiguration(source));
//        connectionFactory.setPassword(result.getPassword());
//        //手动调用spring bean的生命周期方法，过于hack，惴惴不安，心惊肉跳，七上八下，小鹿乱撞
//        connectionFactory.afterPropertiesSet();
//        return connectionFactory;
//    }
//
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        if(StringUtils.isEmpty(cacheCloudApi)){
//            throw new RuntimeException("cacheCloudApi can not be null");
//        }
//        if(StringUtils.isEmpty(appId)){
//            throw new RuntimeException("appId can not be null");
//        }
//        super.afterPropertiesSet();
//    }
//}
