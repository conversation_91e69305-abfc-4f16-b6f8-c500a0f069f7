package cn.dahe.cas.auth.factory;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;

/**
 * <AUTHOR>
 * Date: 2018/12/13 18:44
 * Description:
 */
@Component
public class JpaQueryFactoryBean implements FactoryBean<JPAQueryFactory> {

    @Autowired
    private EntityManager entityManager;

    @Override
    public JPAQueryFactory getObject() throws Exception {
        return new JPAQueryFactory(entityManager);
    }

    @Override
    public Class<?> getObjectType() {
        return JPAQueryFactory.class;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }
}
