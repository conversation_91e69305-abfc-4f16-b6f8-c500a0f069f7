//package cn.dahe.cas.auth.factory;
//
//import cn.dahe.cas.auth.dto.CacheCloudResult;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestTemplate;
//
///**
// * <AUTHOR>
// * on 2018/3/21.
// */
//@Component("clusterInfo")
//public class ClusterInfoImpl implements ClusterInfo {
//
//    private static final Logger log = LoggerFactory.getLogger(ClusterInfoImpl.class);
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    @Value("${cache_cloud.api}")
//    private String api;
//
//    @Override
//    public CacheCloudResult returnResult(){
//        CacheCloudResult result = restTemplate.getForObject(api, CacheCloudResult.class);
//        log.error("集群信息 message {} num {} status {} info {}",result.getMessage(),result.getShardNum(),result.getStatus(),result.getShardInfo());
//        return result;
//    }
//}
