package cn.dahe.cas.auth.filter;

import com.google.common.base.Function;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date Created at 2018/8/15 14:03
 * @description:
 **/
@Component
public class XssFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        chain.doFilter(new XssRequestWrapper((HttpServletRequest)request),response);
    }

    @Override
    public void destroy() {

    }


    public class XssRequestWrapper extends HttpServletRequestWrapper {
        /**
         * Constructs a request object wrapping the given request.
         *
         * @param request
         * @throws IllegalArgumentException if the request is null
         */
        public XssRequestWrapper(HttpServletRequest request) {
            super(request);
        }

        @Override
        public String getParameter(String name) {
            String result = super.getParameter(name);
            if(StringUtils.isBlank(result)){
                return result;
            }
            return HtmlUtils.htmlEscape(result);
        }

        @Override
        public String[] getParameterValues(String name) {
            String[] results = super.getParameterValues(name);
            if(results==null){
                return results;
            }
            return Arrays.asList(results).stream().map((Function<String, String>) s -> HtmlUtils.htmlEscape(s)).toArray(String[]::new);
        }
    }
}
