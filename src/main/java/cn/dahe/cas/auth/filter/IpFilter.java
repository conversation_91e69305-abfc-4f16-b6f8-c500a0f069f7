package cn.dahe.cas.auth.filter;

import cn.dahe.cas.auth.access.AccessStrategy;
import cn.dahe.cas.auth.util.IpUtils;
import org.apache.shiro.web.servlet.AdviceFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * ipfilter,对禁用的ip进行访问过滤
 */
@Component("ipFilter")
public class IpFilter extends AdviceFilter{

    @Autowired
    @Qualifier("ipAccessStrategy")
    private AccessStrategy accessStrategy;

    @Override
    public boolean preHandle(ServletRequest request, ServletResponse response){
        String ip = IpUtils.getRemoteIp((HttpServletRequest) request);
        accessStrategy.isAllow(ip);
        return true;
    }
}
