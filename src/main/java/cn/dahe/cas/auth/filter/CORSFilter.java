package cn.dahe.cas.auth.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class CORSFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // TODO Auto-generated method stub

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse resp = (HttpServletResponse)response;
        HttpServletRequest req = (HttpServletRequest)request;
        String origin = req.getHeader("Origin");
        //这种方式可以灵活的配置Access-Control-Allow-Origin
        //Access-Control-Allow-Origin的值不要写*号，因为对于带有cookie的跨域请求，浏览器不支持这种宽松的策略
        resp.setHeader("Access-Control-Allow-Origin", origin);//可以访问的域
        resp.setHeader("Access-Control-Allow-Credentials", "true");//如果操作cookie，必须加上这句话
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        // TODO Auto-generated method stub

    }

}
