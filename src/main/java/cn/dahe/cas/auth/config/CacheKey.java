package cn.dahe.cas.auth.config;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/2
 * @createTime 16:45
 * @info
 */
public final class CacheKey {
    /**
     * ip地址信息缓存key
     */
    public static final String IP_INFO_KEY = "sso:ip:info";

    /**
     * tgt到用户认证信息的缓存
     */
    public static final String TGT_AUTH_INFO_KEY = "sso:tgt:authinfo";

    /**
     * tgt到用户认证信息的缓存
     */
    public static final String TGT_USER_KEY = "sso:tgt:user";

    /**
     * 手机号信息缓存
     */
    public static final String PHONE_INFO_KEY = "sso:mobile:info";

    /**
     * 天气信息缓存
     */
    public static final String WEATHER_INFO_KEY = "sso:weather:info";

    /**
     * CachePersonAttributeDao缓存
     */
    public static final String PERSON_CACHE_KEY = "sso:person:attribute";

    /**
     * 站点下用户缓存
     */
    public static final String SITE_USER_KEY="sso:site:users";

    /**
     * 组织机构下用户缓存
     */
    public static final String DEPARTMENT_USER_KEY="sso:department:users";

    /**
     * 单个用户信息缓存
     */
    public static final String SINGLE_USER_INFO_KEY="sso:single_user:info";

    /**
     * 所有用户id信息
     */
    public static final String ALL_USER_ID_KEY="sso:user_ids";

    /**
     * 分页用户信息缓存
     */
    public static final String PAGE_USER_INFO_KEY="sso:page_user:info";

    /**
     * 用户角色
     */
    public static final String USER_ROLE="sso:role";
}
