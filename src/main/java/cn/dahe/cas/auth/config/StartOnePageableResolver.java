package cn.dahe.cas.auth.config;

import org.springframework.core.MethodParameter;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableHandlerMethodArgumentResolver;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * @Author: 杨振雨
 * @Date: 2018/11/19 15:29
 * @Description: 好烦哦，怎么优雅处理前后台页码不一致问题
 */
public class StartOnePageableResolver extends PageableHandlerMethodArgumentResolver {
    @Override
    public Pageable resolveArgument(MethodParameter methodParameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        Pageable pageable = super.resolveArgument(methodParameter, mavContainer, webRequest, binderFactory);
        return new  PageRequest(pageable.getPageNumber()==0?0:pageable.getPageNumber()-1,pageable.getPageSize(),pageable.getSort());
    }
}
