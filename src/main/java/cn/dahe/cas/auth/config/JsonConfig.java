package cn.dahe.cas.auth.config;

import cn.dahe.cas.auth.constants.LogOperation;
import cn.dahe.cas.auth.constants.LoginType;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import org.springframework.context.annotation.Bean;

/**
 * @Author: 杨振雨
 * @Date: 2019/7/17 11:45
 * @Description:
 */
public class JsonConfig {
    @Bean
    public FastJsonConfig fastJsonConfig(){
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty);
        SerializeConfig serializeConfig = SerializeConfig.globalInstance;
        serializeConfig.configEnumAsJavaBean(LoginType.class, LoginStatisticsPeriod.class, LogStatisticsPeriod.class, LogOperation.class);
        fastJsonConfig.setSerializeConfig(serializeConfig);
        return fastJsonConfig;
    }
}
