package cn.dahe.cas.auth.action;

import cn.dahe.cas.auth.cas.AdminOauthCredential;
import cn.dahe.cas.auth.cas.OauthCredential;
import cn.dahe.cas.auth.constants.LoginType;
import cn.dahe.cas.auth.constants.SiteDelete;
import cn.dahe.cas.auth.constants.WxConstant;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.WxNeedBindException;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.JJWT;
import cn.dahe.cas.auth.util.Sm4Util;
import cn.dahe.cas.auth.util.WxUtil;
import cn.dahe.cas.auth.util.WxUtil.WxResult;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jasig.cas.CentralAuthenticationService;
import org.jasig.cas.authentication.AuthenticationContext;
import org.jasig.cas.authentication.AuthenticationContextBuilder;
import org.jasig.cas.authentication.AuthenticationSystemSupport;
import org.jasig.cas.authentication.AuthenticationTransaction;
import org.jasig.cas.authentication.Credential;
import org.jasig.cas.authentication.DefaultAuthenticationContextBuilder;
import org.jasig.cas.authentication.DefaultAuthenticationSystemSupport;
import org.jasig.cas.authentication.principal.Service;
import org.jasig.cas.ticket.TicketGrantingTicket;
import org.jasig.cas.web.support.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.webflow.action.MultiAction;
import org.springframework.webflow.execution.Event;
import org.springframework.webflow.execution.RequestContext;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/3 15:47
 * @Description: 微信登录处理
 * 流程：
 * 1 根据传递的的code以及配置的appId和appSecret获取openid
 * 2 根据openid（是否完全固定）查询用户绑定信息
 * 3 若未绑定用户，进行绑定（子流程）
 * 4 已绑定则进行正常流程（登录成功）
 */
@Component("wxLoginAction")
public class WxLoginAction extends MultiAction{

    private static final Logger logger = LoggerFactory.getLogger(WxLoginAction.class);

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    private UserService userService;

    @Autowired
    private SiteService siteService;

    /** Core we delegate to for handling all ticket related tasks. */
    @NotNull
    @Autowired
    @Qualifier("centralAuthenticationService")
    private CentralAuthenticationService centralAuthenticationService;

    @NotNull
    @Autowired(required=false)
    @Qualifier("defaultAuthenticationSystemSupport")
    private AuthenticationSystemSupport authenticationSystemSupport = new DefaultAuthenticationSystemSupport();

    //用于判断登录方式为微信
    private static final String WX_CODE = "code";
    //js回调
    private static final String JS_CALLBACK = "jsCallback";

    public Event loginByCode(RequestContext context) throws Exception {
        final HttpServletRequest request = WebUtils.getHttpServletRequest(context);

        //多种登录认证
        String queryString = request.getQueryString();
        logger.debug("start check common login queryString:{} ", queryString);
        final Service serviceNew = WebUtils.getService(context);
        if (SecurityUtils.getSubject().isAuthenticated()) {
            return success();
        }
        String token = request.getHeader("token");
        if (StrUtil.isBlank(token)) {
            token = request.getParameter("token");
        }
        logger.debug("start check one time login token:{}",token);
        if (StrUtil.isNotBlank(token)) {
            JSONObject userJson = JJWT.resoveUserCenterUserJson(token);
            logger.debug("start check one time login phone:{}",userJson.toString());
            String phone = userJson.getStr("phone");
            String username = userJson.getStr("username");
            if (StrUtil.isBlank(phone)) {
                return no();
            }
            if (StrUtil.isBlank(username)) {
                return no();
            }
            String encPhone = Sm4Util.encryptEcb(sm4Key, phone);
            String encUsername = Sm4Util.encryptEcb(sm4Key, username);
            User user = userService.getUserByPhone(encPhone);
            if (user == null) {
                user = new User();
                user.setFromType(userJson.getStr("sysName"));
                user.setPhone(encPhone);
                user.setTruename(encUsername);
                user.setUsername(username);
                user = userService.add(user);
            }
            logger.debug("start check one time login user:{}", JSONUtil.toJsonPrettyStr(user));
            final Credential credential = new AdminOauthCredential(String.valueOf(user.getUid()), LoginType.ADMIN);
            final AuthenticationContextBuilder builder = new DefaultAuthenticationContextBuilder(
                    this.authenticationSystemSupport.getPrincipalElectionStrategy());
            final AuthenticationTransaction transaction =
                    AuthenticationTransaction.wrap(credential);
            this.authenticationSystemSupport.getAuthenticationTransactionManager().handle(transaction, builder);
            final AuthenticationContext authenticationContext = builder.build(serviceNew);
            final TicketGrantingTicket tgt = this.centralAuthenticationService.createTicketGrantingTicket(authenticationContext);
            WebUtils.putTicketGrantingTicketInScopes(context, tgt);
            context.getFlowScope().put("credential", credential);
            return success();
        }

        //是否含有wx字段
        final String code = request.getParameter(WX_CODE);
        final Service service =  WebUtils.getService(context);
        logger.debug("start check wx login code:{} service:{}",code,service);
        if(StringUtils.isNotBlank(code)&&service!=null){
            //注意进行异常处理
            WxResult body = WxUtil.getWxResult(WxConstant.TOKEN_URL,WxConstant.APP_ID,WxConstant.APP_SECRET,code);
            //根据openid判断是否已经绑定，若未绑定则跳转到绑定页面并将openid传入其中
            logger.debug("获取到微信登录用户信息{}",body);
            String openid = body.getOpenid();
            User user = userService.getUserByWx(openid);
            final String callback = request.getParameter(JS_CALLBACK);
            context.getFlowScope().put("jsCallback",callback);
            //未进行绑定
            if(user==null){
                //需要将openid传到下一个流程节点中以进行备用，考虑更好的方式
                context.getFlowScope().put("openid",openid);
                //抛出需要绑定异常，是否合适呢，我以为然
                throw new WxNeedBindException();
            }
            //已绑定用户直接进行正常认证即可，其实可以走偷懒流程，即模拟一个可认证用户，暂时先走正规流程，略感多此一举
            final Credential credential = new OauthCredential(body.getOpenid(), LoginType.WX);
            final AuthenticationContextBuilder builder = new DefaultAuthenticationContextBuilder(
                    this.authenticationSystemSupport.getPrincipalElectionStrategy());
            final AuthenticationTransaction transaction =
                    AuthenticationTransaction.wrap(credential);
            this.authenticationSystemSupport.getAuthenticationTransactionManager().handle(transaction,  builder);
            final AuthenticationContext authenticationContext = builder.build(service);
            final TicketGrantingTicket tgt = this.centralAuthenticationService.createTicketGrantingTicket(authenticationContext);
            WebUtils.putTicketGrantingTicketInScopes(context, tgt);
            context.getFlowScope().put("credential",credential);
            return success();
        }
        //非微信登录直接进行下一步流程即可，不可认为是错误，不予处理，直接走正常流程
        logger.debug("非微信登录");
        return no();
    }

    public Event loginByOpenId(RequestContext context,String openid){
        final Service service =  WebUtils.getService(context);
        logger.debug("直接使用openid进行登录openid:{}  service:{}",openid,service);
        //已绑定用户直接进行正常认证即可，其实可以走偷懒流程，即模拟一个可认证用户，暂时先走正规流程，略感多此一举
        final Credential credential = new OauthCredential(openid, LoginType.WX);
        final AuthenticationContextBuilder builder = new DefaultAuthenticationContextBuilder(
                this.authenticationSystemSupport.getPrincipalElectionStrategy());
        final AuthenticationTransaction transaction =
                AuthenticationTransaction.wrap(credential);
        try {
            this.authenticationSystemSupport.getAuthenticationTransactionManager().handle(transaction,  builder);
            final AuthenticationContext authenticationContext = builder.build(service);
            final TicketGrantingTicket tgt = this.centralAuthenticationService.createTicketGrantingTicket(authenticationContext);
            WebUtils.putTicketGrantingTicketInScopes(context, tgt);
        } catch (Exception e) {
            logger.error("openid登录发生错误",e);
            e.printStackTrace();
            return error(e);
        }
        return success();
    }

}
