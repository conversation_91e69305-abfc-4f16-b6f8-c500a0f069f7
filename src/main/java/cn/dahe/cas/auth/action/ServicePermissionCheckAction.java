package cn.dahe.cas.auth.action;

import cn.dahe.cas.auth.entity.User_;
import cn.dahe.cas.auth.exception.GetUserFromTgtException;
import cn.dahe.cas.auth.exception.SitePermissionLackException;
import cn.dahe.cas.auth.service.SiteService;
import org.jasig.cas.authentication.principal.Service;
import org.jasig.cas.services.RegisteredService;
import org.jasig.cas.services.ServicesManager;
import org.jasig.cas.ticket.registry.TicketRegistrySupport;
import org.jasig.cas.web.support.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.binding.message.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.webflow.action.AbstractAction;
import org.springframework.webflow.execution.Event;
import org.springframework.webflow.execution.RequestContext;

import java.util.Map;

/**
 * <AUTHOR>
 * 检查用户对某个站点是否拥有访问权限，主要针对后端sso用户,错误需要向前台进行展示
 */

@Component
public class ServicePermissionCheckAction extends AbstractAction{

    private static final String USER_ID = User_.uid.getName();

    @Autowired
    @Qualifier("defaultTicketRegistrySupport")
    private TicketRegistrySupport ticketRegistrySupport;

    @Autowired
    private SiteService siteService;

    @Autowired
    @Qualifier("servicesManager")
    private ServicesManager servicesManager;

    private static final Logger logger = LoggerFactory.getLogger(ServicePermissionCheckAction.class);

    @Override
    protected Event doExecute(RequestContext requestContext) throws Exception {
        final Service service = WebUtils.getService(requestContext);
        if(service==null){
            throw new SitePermissionLackException("服务为空，参数不合法");
        }
        logger.debug("开始进行服务{}权限检测",service);
        final String ticketGrantingTicket = WebUtils.getTicketGrantingTicketId(requestContext);
        int uid = getUserId(ticketGrantingTicket);
        //判断某个用户是否拥有对服务的访问权限
        RegisteredService registeredService = servicesManager.findServiceBy(service);
        if(registeredService==null){
            logger.error("无法获取相应service{}",service.getId());
            MessageBuilder builder = new MessageBuilder()
                    .error()
                    .defaultText("服务未注册");
            requestContext.getMessageContext().addMessage(builder.build());
            throw new SitePermissionLackException(service.getId()+"服务未注册，请联系系统管理员进行添加");
        }
        boolean access = siteService.canAccess(uid, (int) registeredService.getId());
        logger.debug("获取到的服务信息{}",registeredService);
        if(!access){
            logger.error("用户{}对站点{}无访问权限",uid,registeredService.getId());
            MessageBuilder builder = new MessageBuilder()
                    .error()
                    .defaultText("当前用户对站点"+registeredService.getDescription()+"没有访问权限，请联系系统管理员进行添加");
            requestContext.getMessageContext().addMessage(builder.build());
            throw new SitePermissionLackException("当前用户对站点---"+registeredService.getDescription()+"---没有访问权限，请联系系统管理员进行权限添加");
        }
        return success();
    }

    private int getUserId(String tgt){
        final Map<String,Object> attributes = ticketRegistrySupport.getPrincipalAttributesFrom(tgt);
        if(attributes!=null&&attributes.containsKey(USER_ID)){
            return (int) attributes.get(USER_ID);
        }
        logger.error("无法从{}中获取用户信息",tgt);
        throw new GetUserFromTgtException("无法获取用户信息");
    }
}
