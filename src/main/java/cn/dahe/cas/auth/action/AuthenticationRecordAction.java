package cn.dahe.cas.auth.action;

import cn.dahe.cas.auth.constants.LoginType;
import cn.dahe.cas.auth.event.AuthenticationEvent;
import cn.dahe.cas.auth.exception.GetUserFromTgtException;
import cn.dahe.cas.auth.task.AuthenticationTask;
import cn.dahe.cas.auth.util.UrlUtil;
import org.jasig.cas.authentication.Credential;
import org.jasig.cas.authentication.principal.Principal;
import org.jasig.cas.authentication.principal.WebApplicationService;
import org.jasig.cas.ticket.registry.TicketRegistrySupport;
import org.jasig.cas.web.support.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.webflow.execution.RequestContext;

import java.util.Map;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/7 11:30
 * @Description: 记录认证日志，尽可能保留详细信息以便作为数据源进行分析,持久化走异步，可容忍部分意外丢失
 */
@Component
public class AuthenticationRecordAction {

    private static final Logger log = LoggerFactory.getLogger(AuthenticationRecordAction.class);

    @Autowired
    @Qualifier("defaultTicketRegistrySupport")
    private TicketRegistrySupport ticketRegistrySupport;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Qualifier("ssoThreadPoolTaskExecutor")
    @Autowired
    private TaskExecutor taskExecutor;

    @Autowired
    private AuthenticationTask authenticationTask;

    /**
     * 认证类型，可望文生义
     */
    private static final String AUTHENTICATION_TYPE = "authentication_type";

    public void recordLoginInformation(RequestContext requestContext){
        String tgt = WebUtils.getTicketGrantingTicketId(requestContext);
        Credential credential = WebUtils.getCredential(requestContext);
        WebApplicationService applicationService = WebUtils.getService(requestContext);
        String url = applicationService!=null?UrlUtil.getHost(applicationService.getId()):null;
        log.info("记录认证信息url:{}",url);
        taskExecutor.execute(() -> {
            int uid = getUserId(tgt);
            LoginType loginType = LoginType.PHONE;
            AuthenticationEvent event = new AuthenticationEvent(credential,loginType,WebUtils.getHttpServletRequest(requestContext),uid,url,true);
            authenticationTask.onApplicationEvent(event);
        });

    }

    public void recordLoginFailure(RequestContext requestContext){
        Credential credential = WebUtils.getCredential(requestContext);
        WebApplicationService applicationService = WebUtils.getService(requestContext);
        LoginType loginType = requestContext.getFlowScope().get(AUTHENTICATION_TYPE,LoginType.class);
        String url = applicationService!=null?UrlUtil.getHost(applicationService.getId()):null;
        publisher.publishEvent(new AuthenticationEvent(credential,loginType,WebUtils.getHttpServletRequest(),null,url,false));
    }

    private int getUserId(String tgt) throws GetUserFromTgtException{
        final Principal principal = ticketRegistrySupport.getAuthenticatedPrincipalFrom(tgt);
        if(principal==null){
            log.error("从tgt：{}中获取不到principal",tgt);
            throw new GetUserFromTgtException("无法获取用户信息");
        }
        Map<String,Object> attributes = principal.getAttributes();
        if(attributes.containsKey("uid")){
            return (int) attributes.get("uid");
        }
        throw new GetUserFromTgtException("无法获取用户信息");
    }
}
