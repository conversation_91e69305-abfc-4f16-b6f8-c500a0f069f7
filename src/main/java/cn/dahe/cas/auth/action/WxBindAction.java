package cn.dahe.cas.auth.action;

import cn.dahe.cas.auth.constants.SmsType;
import cn.dahe.cas.auth.dto.WxBindPhoneParameter;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.service.VerfiyCodeService;
import cn.dahe.cas.auth.util.Sm4Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.webflow.execution.RequestContext;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/3 17:11
 * @Description: 微信绑定，后期完善，加入频率限制等
 */
@Component("wxBindAction")
public class WxBindAction{

    private static final Logger logger = LoggerFactory.getLogger(WxBindAction.class);

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    private UserService userService;

    @Autowired
    private VerfiyCodeService verfiyCodeService;

    public boolean bind(WxBindPhoneParameter parameter,RequestContext context) throws Exception {
        //获取openid用于绑定操作
        final String openid = context.getFlowScope().get("openid",String.class);
        final String phone = parameter.getPhone();
        final String smsCode = parameter.getSmsCode();

        logger.debug("微信绑定用户操作 openid:{}  phone:{} code:{} ",openid,phone,smsCode);
        //进行合法性校验
        User user = userService.getUserByPhone(Sm4Util.encryptEcb(sm4Key, phone));
        if(user==null){
            setBindErrorMessage("用户不存在",context);
            return false;
        }
        if(StringUtils.isNotBlank(user.getWxOpenId())){
            setBindErrorMessage("该手机号已经绑定其他微信,请先进行解绑",context);
            return false;
        }
        //用户手机号查找不到处理
        //验证码校验
        boolean validate = verfiyCodeService.isValid(phone,smsCode, SmsType.WX_BIND);
        if(validate){
            user.setWxOpenId(openid);
            userService.update(user);
            //验证码验证成功，一次之后短信即失效
            verfiyCodeService.deleteCode(phone, SmsType.WX_BIND);
            //可向外输出用户id供下一个流程使用
            return true;
        }
        //绑定错误，供前端展示
        setBindErrorMessage("绑定失败,验证码错误",context);
        return false;
    }

    private void setBindErrorMessage(String message,RequestContext context){
        context.getViewScope().put("bindError",message);
    }
}
