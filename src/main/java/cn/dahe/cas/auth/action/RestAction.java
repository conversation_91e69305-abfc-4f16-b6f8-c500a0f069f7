package cn.dahe.cas.auth.action;

import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.webflow.action.AbstractAction;
import org.springframework.webflow.execution.RequestContext;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * webflow返回json数据的实现
 */

public abstract class RestAction extends AbstractAction{

    @Autowired
    private HttpMessageConverter converter;

    protected void json(JsonResult result,RequestContext requestContext){
        HttpServletResponse response = (HttpServletResponse) requestContext.getExternalContext().getNativeResponse();
        ServletServerHttpResponse outputMessage = new ServletServerHttpResponse(response);
        try {
            converter.write(result, MediaType.APPLICATION_JSON,outputMessage);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            requestContext.getExternalContext().recordResponseComplete();
        }
    }
}
