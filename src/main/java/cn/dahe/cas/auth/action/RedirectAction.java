package cn.dahe.cas.auth.action;

import org.jasig.cas.web.support.WebUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.webflow.action.AbstractAction;
import org.springframework.webflow.execution.Event;
import org.springframework.webflow.execution.RequestContext;

/**
 * <AUTHOR>
 * 对重定向进行检测，主要针对check-webflow即静态站点的登录
 * 静态站点登录时获取其重定向地址，方便下一步登录成功后的跳转
 * 如果无重定向地址，默认跳转到sso用户主页
 */
@Component
public class RedirectAction extends AbstractAction{

    @Value("${cas.home}")
    private String home;

    public static final String REDIRECT_PARA = "redirect";

    @Override
    protected Event doExecute(RequestContext requestContext) throws Exception {
        String redirect = WebUtils.getHttpServletRequest().getParameter(REDIRECT_PARA);
        if(StringUtils.isEmpty(redirect)){
            redirect = home;
        }
        requestContext.getFlowScope().put(REDIRECT_PARA,redirect);
        return success();
    }
}
