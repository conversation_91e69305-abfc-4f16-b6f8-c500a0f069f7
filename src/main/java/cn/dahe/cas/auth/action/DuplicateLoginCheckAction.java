package cn.dahe.cas.auth.action;

import cn.dahe.cas.auth.config.RedisKey;
import cn.dahe.cas.auth.entity.User_;
import cn.dahe.cas.auth.exception.GetUserFromTgtException;
import cn.dahe.cas.auth.service.CasService;
import org.jasig.cas.CentralAuthenticationService;
import org.jasig.cas.authentication.principal.Principal;
import org.jasig.cas.logout.LogoutRequest;
import org.jasig.cas.ticket.registry.TicketRegistrySupport;
import org.jasig.cas.web.support.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.webflow.action.MultiAction;
import org.springframework.webflow.execution.Event;
import org.springframework.webflow.execution.RequestContext;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/18
 * @createTime 20:57
 * @info 重复登录检测，对于重复登录用户直接强制下线
 * todo 在并发量增加时会出现明显的性能下降
 */
@Component
public class DuplicateLoginCheckAction extends MultiAction{

    private static final Logger log = LoggerFactory.getLogger(DuplicateLoginCheckAction.class);

    public static final String USER_ID = User_.uid.getName();

    @Autowired
    @Qualifier("defaultTicketRegistrySupport")
    private TicketRegistrySupport ticketRegistrySupport;

    @Autowired
    private CentralAuthenticationService centralAuthenticationService;

    @Resource(name = "redisTemplate")
    private HashOperations<String,Integer,String> hashOperations;

    @Autowired
    private CasService casService;

    /**
     * 检测重复登录
     * @param requestContext
     * @return
     * @throws Exception
     */
    public Event check(RequestContext requestContext) throws Exception {
        String tgt = WebUtils.getTicketGrantingTicketId(requestContext);
        if(StringUtils.isEmpty(tgt)){
            return success();
        }
        //查询用户id
        int uid = getUserId(tgt);
        //根据uid将用户踢出
        casService.logoutUser(uid);
        return success();
    }

    private int getUserId(String tgt){
        final Principal principal = ticketRegistrySupport.getAuthenticatedPrincipalFrom(tgt);
        if(principal==null){
            log.error("无法从tgt：{}中获取用户信息,tgt可能已经失效",tgt);
            throw new GetUserFromTgtException("无法获取用户信息");
        }
        Map<String,Object> attributes = principal.getAttributes();
        log.info("========================attributes{}",attributes);
        if(attributes.containsKey(USER_ID)){
            return (int) attributes.get(USER_ID);
        }
        log.error("无法从tgt：{}中获取用户id信息，不包含该属性",tgt);
        throw new GetUserFromTgtException("无法获取用户信息");
    }

    /**
     * 异步踢出用户，加入审计
     * @param uid
     * @param tgt
     */
    @Async
    public void logout(int uid,String tgt){
        //首先将用户登录情况清除
        hashOperations.delete(RedisKey.LOGINED_USER_KEY,uid);
        //销毁tgt并返回所有已登陆子系统的退出登录地址，此时由于用户可能执行了正常登录逻辑因此无tgt信息，注意判断
        final List<LogoutRequest> logoutRequests = centralAuthenticationService.destroyTicketGrantingTicket(tgt);
        log.debug("踢出上个登录用户，用户id为{},退出站点数量{}",uid,logoutRequests.size());
    }

    /**
     * 流程执行完毕后记录用户登录信息
     * @param
     */
    public void record(RequestContext context){
        final String tgt = WebUtils.getTicketGrantingTicketId(context);
        try{
            int uid = getUserId(tgt);
            hashOperations.put(RedisKey.LOGINED_USER_KEY,uid,tgt);
        }catch (GetUserFromTgtException e){
            log.error("记录用户登录信息出错",e);
        }
    }
}
