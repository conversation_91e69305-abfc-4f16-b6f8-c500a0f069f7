package cn.dahe.cas.auth.action;

import cn.dahe.cas.auth.realm.ImageCodeCredential;
import cn.dahe.cas.auth.util.CommonUtil;
import com.octo.captcha.service.CaptchaService;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.authentication.Credential;
import org.jasig.cas.web.support.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.binding.message.MessageBuilder;
import org.springframework.binding.message.MessageContext;
import org.springframework.stereotype.Component;
import org.springframework.webflow.execution.RequestContext;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * <AUTHOR>
 * on 2018/1/26.
 */
@Component("captchaAction")
public class CaptchaAction{
    @Autowired
    private CaptchaService captchaService;

    public final  String validayorCode(RequestContext context, Credential credential, MessageContext messageContext)
            throws Exception {
        ImageCodeCredential upc = (ImageCodeCredential)credential;
        if (StringUtils.isNotBlank(upc.getUsername())){
            if (! CommonUtil.isPhoneNumber(upc.getUsername())){
                final MessageBuilder builder = new MessageBuilder()
                        .error()
                        .defaultText("账号格式不正确");
                messageContext.addMessage(builder.build());
                return "error";
            }
        }
        //第一次登陆showCode为false
        Boolean showCode = context.getFlowScope().get("showCode", Boolean.class);
        if( !showCode){
            return "success";
        }else {
            final HttpServletRequest request = WebUtils.getHttpServletRequest(context);
            HttpSession session = request.getSession();
            //获取表单输入验证码
            String captcha = upc.getImageCode();
            boolean correct = captchaService.validateResponseForID(session.getId(), captcha);
            if (!correct) {
                final MessageBuilder builder = new MessageBuilder()
                        .error()
                        .defaultText("图形验证码错误");
                messageContext.addMessage(builder.build());
                return "error";
            }
            return "success";
        }
    }
}
