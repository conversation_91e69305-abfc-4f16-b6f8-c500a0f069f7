package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.config.URL;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.dto.TgcInfo;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SitePermissionLackException;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.CasService;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.SsoService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.Sm4Util;
import org.hibernate.validator.constraints.NotBlank;
import org.jasig.cas.CentralAuthenticationService;
import org.jasig.cas.authentication.*;
import org.jasig.cas.authentication.principal.Principal;
import org.jasig.cas.authentication.principal.Service;
import org.jasig.cas.authentication.principal.ServiceFactory;
import org.jasig.cas.ticket.AbstractTicketException;
import org.jasig.cas.ticket.InvalidTicketException;
import org.jasig.cas.ticket.ServiceTicket;
import org.jasig.cas.ticket.TicketGrantingTicket;
import org.jasig.cas.ticket.registry.TicketRegistrySupport;
import org.jasig.cas.validation.Assertion;
import org.jasig.cas.web.support.CookieRetrievingCookieGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.text.MessageFormat;
import java.util.Map;

/**
 * <AUTHOR>
 * 该类修改需要对cas的机制有比较深入了解，请慎重，不要任性
 */
@Controller
@RequestMapping("/dahe/sso")
@Validated
public class SsoController {

    @Autowired
    private CentralAuthenticationService centralAuthenticationService;

    @Autowired
    private CookieRetrievingCookieGenerator ticketGrantingTicketCookieGenerator;

    @Autowired
    private SsoService ssoService;

    @Autowired
    private CasService casService;

    @Autowired
    private SiteService siteService;

    @Autowired
    @Qualifier("defaultTicketRegistrySupport")
    private TicketRegistrySupport ticketRegistrySupport;

    @NotNull
    @Autowired(required=false)
    @Qualifier("defaultAuthenticationSystemSupport")
    private AuthenticationSystemSupport authenticationSystemSupport = new DefaultAuthenticationSystemSupport();

    @Autowired
    @Qualifier("webApplicationServiceFactory")
    private ServiceFactory serviceFactory;

    @Autowired
    private UserService userService;

    @Value("${tgc.domain}")
    private String allowDomain;

    @Value("${cas.static.login}")
    private String loginUrl;

    @Value("${cas.server.logout}")
    private String logoutUrl;

    @Value("${cas.home}")
    private String homeUrl;

    @Value("${sm4.security.key}")
    private String sm4Key;

    /**
     * 该接口为静态站点提供获取当前sso登录用户信息功能
     * 由于该接口会被静态页面大量访问，需要做好缓存
     * 可以直接使用tgc做缓存，忽略常规的tgt检查
     * @param request
     * @param service
     * @return
     */
    @RequestMapping(path = "/info")
    @RestResponseBody
    public JsonResult validate(HttpServletRequest request,String service){
        String tgt = ticketGrantingTicketCookieGenerator.retrieveCookieValue(request);
        TgcInfo tgcInfo = new TgcInfo();
        tgcInfo.setLogoutUrl(logoutUrl);
        tgcInfo.setHomeUrl(homeUrl);
        if(StringUtils.isEmpty(service)){
            service = homeUrl;
        }
        tgcInfo.setLoginUrl(MessageFormat.format(loginUrl,new Object[]{service,service}));
        if(StringUtils.isEmpty(tgt)){
           return ResultUtil.fail("未登录",tgcInfo);
        }
        Map<String,Object> attrs = ssoService.getUserInfo(tgt);
        tgcInfo.setPrincipal(attrs);
        return ResultUtil.success(tgcInfo);
    }

    @RequestMapping(path = "/check")
    @RestResponseBody
    public int validate(HttpServletRequest request,HttpServletResponse response){
        String tgt = ticketGrantingTicketCookieGenerator.retrieveCookieValue(request);
        try {
            final TicketGrantingTicket ticket = centralAuthenticationService.getTicket(tgt, TicketGrantingTicket.class);
            return 200;
        } catch (InvalidTicketException e) {
            e.printStackTrace();
        }
        return 500;
    }

    /**
     * 该接口暂时不对外开放
     * @param request
     * @param tgc
     * @param ua
     * @param origin
     * @return
     */
    @RequestMapping(path = "/userinfo",method = RequestMethod.POST)
    @RestResponseBody
    public Principal getUserInfo(HttpServletRequest request,@NotBlank String tgc,@NotBlank String ua,@NotBlank String origin){
        //此处需要进行来源校验，防止接口被随意调用
        String from = allowDomain;
        if(!from.equals(allowDomain)){
            throw new SsoException("不允许访问");
        }
        String tgt = casService.getTgt(tgc,ua,origin);
        if(StringUtils.isEmpty(tgt)){
            throw new SsoException("未登录");
        }
        try {
            final TicketGrantingTicket ticket = centralAuthenticationService.getTicket(tgt, TicketGrantingTicket.class);
            return ticket.getAuthentication().getPrincipal();
        } catch (InvalidTicketException e) {
            throw new SsoException(e.getMessage());
        }
    }

    /**
     * 为第三方提供的登录接口，将cas认证流程集中到用户名和密码之上
     * 修改该方法要小心翼翼
     * @param request
     * @param username
     * @param password
     * @return
     */
    @LogAction(action = "直接进行登录")
    @RequestMapping(path = "/login",method = RequestMethod.POST)
    @RestResponseBody
    public Principal loginByCas(HttpServletRequest request,int sid,String username,String password) throws Exception {
        username = Sm4Util.encryptEcb(sm4Key, username);
        User user = userService.getUser(username);
        if(user==null){
            throw new SsoException("认证失败");
        }
        boolean access = siteService.canAccess(user.getUid(),sid);
        if(!access){
            throw new SitePermissionLackException("无站点访问权限");
        }
        //首先检查用户对站点的可访问性
        Credential credential = new UsernamePasswordCredential(username,password);
        //生成tgt
        final AuthenticationContextBuilder builder = new DefaultAuthenticationContextBuilder(authenticationSystemSupport.getPrincipalElectionStrategy());
        final AuthenticationTransaction transaction = AuthenticationTransaction.wrap(credential);
        try {
            authenticationSystemSupport.getAuthenticationTransactionManager().handle(transaction,builder);
            final Service service = serviceFactory.createService(request);
            final AuthenticationContext authenticationContext = builder.build(service);
            final TicketGrantingTicket tgt = centralAuthenticationService.createTicketGrantingTicket(authenticationContext);
            //tgt生成后继续进行st生成
            final Authentication authentication = ticketRegistrySupport.getAuthenticationFrom(tgt.getId());
            if(authentication==null){
                throw new AuthenticationException();
            }
            final ServiceTicket serviceTicket = centralAuthenticationService.grantServiceTicket(tgt.getId(),service,authenticationContext);
            final Assertion assertion = centralAuthenticationService.validateServiceTicket(serviceTicket.getId(),service);
            Principal principal = assertion.getPrimaryAuthentication().getPrincipal();
            //服务票据生成后进行属性的获取
            return principal;
        } catch (AuthenticationException e) {
            throw new SsoException("认证失败");
        } catch (AbstractTicketException e) {
            e.printStackTrace();
        }
        throw new SsoException("认证失败");
    }

}
