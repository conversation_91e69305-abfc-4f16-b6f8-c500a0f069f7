package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.entity.UserJob;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.UserJobService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * on 2018/6/15.
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/userjob")
@RequiresPermissions("job:manager")
public class UserJobController {
    @Autowired
    private UserJobService userJobService;

    @RequestMapping(path = "/list",method = RequestMethod.POST)
    @RestResponseBody
    public List<UserJob> getLogSource(){
        return userJobService.get();
    }

    @RequestMapping(path = "/page",method = RequestMethod.POST)
    @RestResponseBody
    public Page<UserJob> findLogSource(@RequestParam int page, @RequestParam int size){
        Pageable pageable = new PageRequest(page-1,size,new Sort(Sort.Direction.DESC, "id"));
        return userJobService.get(pageable);
    }

    @LogAction(action = "添加用户职务")
    @RequestMapping(path = "/add",method = RequestMethod.POST)
    @RestResponseBody
    public boolean add(UserJob userJob){
        boolean exist = userJobService.exist(userJob.getJob());
        if(exist){
            throw new SsoException("该职务已存在！");
        }
        userJobService.add(userJob);
        return true;
    }

    @LogAction(action = "删除用户职务")
    @RequestMapping(path = "/delete",method = RequestMethod.POST)
    @RestResponseBody
    public boolean delete(int id){
        userJobService.delete(id);
        return true;
    }

    @LogAction(action = "修改职务信息")
    @RequestMapping(path = "/edit",method = RequestMethod.POST)
    @RestResponseBody
    public boolean edit(UserJob userJob){
        UserJob old = userJobService.get(userJob.getId());
        boolean exist = userJobService.exist(userJob.getJob());
        if (exist && !old.getJob().equals(userJob.getJob())){
            throw new SsoException("该职务已存在！");
        }
        userJobService.update(userJob);
        return true;
    }
}
