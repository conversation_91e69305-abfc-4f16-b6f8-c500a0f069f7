package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.CacheService;
import cn.dahe.cas.auth.service.LockUserService;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/4
 * @createTime 11:18
 * @info 对应用缓存进行管理
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/cache")
@Validated
public class CacheController {

    @Value("${cache.prefix}")
    private String cachePrefix;

    @Autowired
    private CacheManager cacheManager;

    @Resource(name = "tokenRedisTemplate")
    private ValueOperations<String, String> operations;

    @Autowired
    private LockUserService lockUserInterface;

    private static final String USERLOCKED="sso:access-limit:sms*";

    @RequestMapping
    @RestResponseBody
    @RequiresPermissions("cache:manager")
    public Collection<String> getCaches(){
        return cacheManager.getCacheNames();
    }

    /**
     * 该方法假设使用redis作为缓存，如果使用其他缓存请更换逻辑
     * @param name
     * @return
     */
    @RequestMapping(path = "/{name}")
    @RestResponseBody
    @RequiresPermissions("cache:manager")
    public Set<String> getSingleCache(@PathVariable("name")String name){
        Cache cache = cacheManager.getCache(name);
        RedisOperations operation = (RedisOperations) cache.getNativeCache();
        return operation.keys(name+":*");
    }

    @LogAction(action = "整体删除缓存")
    @RequestMapping(path = "/delete/{name}")
    @RestResponseBody
    @RequiresPermissions("cache:manager")
    public boolean deleteCache(@PathVariable("name")String name){
        Cache cache = cacheManager.getCache(name);
        if(cache!=null){
            cache.clear();
        }
        return true;
    }

    /**
     * 该方法假设使用redis作为缓存，如果使用其他缓存请更换逻辑
     * @param name
     * @param key
     * @return
     */
    @LogAction(action = "删除单个缓存")
    @RequestMapping(path = "/delete/{name}/{key}")
    @RestResponseBody
    @RequiresPermissions("cache:manager")
    public boolean deleteSingleCache(@PathVariable("name")String name,@PathVariable("key")String key){
        Cache cache = cacheManager.getCache(name);
        if(cache!=null){
            cache.evict(key.replaceAll("^"+name+cachePrefix,""));
        }
        return true;
    }

    @RequestMapping(path = "/listlockeduser")
    @RestResponseBody
    @RequiresPermissions("user:manager:lock")
    public Page<User> listLockedUser(Pageable pageable){
        return lockUserInterface.getLockUsers(pageable);
    }

    @LogAction(action = "清除被锁住的用户")
    @RequestMapping(path = "/dellockeduser")
    @RestResponseBody
    @RequiresPermissions("user:manager:lock")
    public boolean deleteLockedUser(@NotNull(message = "id不能为空") Integer uid){
        return lockUserInterface.unlock(uid);
    }

    @LogAction(action = "清理登录缓存页面")
    @RequestMapping("/loginhelper")
    @RequiresPermissions("loginhelper")
    public String loginhelper(){
        return "user/loginhelper";
    }

    @LogAction(action = "清理登录缓存数据")
    @RequestMapping("/clearlogindata")
    @RestResponseBody
    @RequiresPermissions("loginhelper")
    public boolean clearLoginData(String key) {
        if (StrUtil.isBlank(key)) {
            return false;
        }
        if (Validator.isNumber(key)) {
            lockUserInterface.unlock(Integer.parseInt(key));
            return true;
        }
        String[] arr = key.split(cachePrefix);
        String kk = arr[arr.length - 1];
        String keyName = key.replace(cachePrefix+kk,"");
        Cache cache = cacheManager.getCache(keyName);
        if(cache!=null){
            cache.evict(kk);
            return true;
        }
        return false;
    }

    @LogAction(action = "获取短信验证码信息")
    @RequestMapping("/smscodes")
    @ResponseBody
    @RequiresPermissions("loginhelper")
    public JsonResult smscodes() {
        Map<String,Object> result = new HashMap<>();
        String name = "gd";
        Cache cache = cacheManager.getCache(name);
        RedisOperations operation = (RedisOperations) cache.getNativeCache();
        Set<String> keys = operation.keys(name + ":*");
        for (String key : keys) {
            String code = operations.get(key);
            key = key.replaceAll("^gd:ssoqdsms_code","");
            result.put(key,code);
        }
        return ResultUtil.success(result);
    }
}
