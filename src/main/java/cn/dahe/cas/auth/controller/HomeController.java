package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.*;
import cn.dahe.cas.auth.constants.*;
import cn.dahe.cas.auth.domain.UserLog;
import cn.dahe.cas.auth.dto.*;
import cn.dahe.cas.auth.entity.RemoteUserZn;
import cn.dahe.cas.auth.entity.RemoteUserZy;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.*;
import cn.dahe.cas.auth.util.*;
import cn.dahe.cas.auth.util.WxUtil.WxResult;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import okhttp3.Response;
import org.apache.shiro.SecurityUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
@RequestMapping("/home")
@Validated
public class HomeController {

    @Value("${cas.logout}")
    private String logoutUrl;
    @Value("${cms_recentnews}")
    private String recentnews;
    @Value("${cas.server}")
    private String casServer;
    @Value("${sm4.security.key}")
    private String sm4Key;
    @Value("${test_env}")
    private boolean testEnv;

    @Autowired
    private UserService userService;
    @Autowired
    private SiteTreeService siteTreeService;
    @Autowired
    private SiteService siteService;

    @Autowired
    private VerfiyCodeService verfiyCodeService;

    @Autowired
    private UserLogService userLogService;

    @Resource(name = "tokenRedisTemplate")
    private ValueOperations<String, String> operations;

    public static final String USERNAME_KEY = "username";
    public static final String USER = "user";

    private static final String BIND_CALLBACK_JS = "<script>parent.{0}({1})</script>";

    @RequestMapping
    public String home(){
        //String userUnit = "";
        //SiteTreeDto depart = siteTreeService.getOneDepart(user.getOrganization()+"");
        //Map<String,String> map=new HashMap<>(1);
        //map.put("uid",user.getUid()+"");
        //model.addAttribute("count","0");
        //model.addAttribute("unit",userUnit);
        //if(depart!=null){
        //    model.addAttribute("depart",depart.getName());
        //}
        //int siteCount = 0;
        ////拥有哪些应用看见哪些应用，不再区分超管和普通用户 2019/07/19
        //List<Site> userSiteList = siteService.getShowSite(user.getUid());
        //Site sso = siteService.getOneSite(casServer);
        ////几乎不用判断
        //if(sso!=null){
        //    userSiteList.removeIf(site -> site.getId() == sso.getId());
        //}
        //if (userSiteList.size() > 0){
        //    siteCount = (int) Math.floor((userSiteList.size() - 1) / 10);
        //}
        //model.addAttribute(USER,user);
        //model.addAttribute("site",userSiteList);
        //model.addAttribute("siteCount",siteCount);
        //model.addAttribute("version",operations.get("version:"+user.getPhone()));
        ////管理员登录
        //Site site = siteService.getOne("local");
        //UserInfo userInfo = userService.getDetail(user,site.getId());
        //Object mp = session.getAttribute("mp");
        //if ((mp != null && mp.toString().equals("ok")) && userInfo.getPermissions() != null && userInfo.getPermissions().size() > 0) {
        //    model.addAttribute("admin", true);
        //    return "redirect:" + casServer + "/index.html";
        //}
        //客户跳转选择页
        return "redirect:https://yuqingtong.dahe.cn/yqtj/";
    }

    @RequestMapping(path = "/upw",method = RequestMethod.GET)
    public String upw(@CurrentUser User user,Model model){
        model.addAttribute(USERNAME_KEY,user.getUsername());
        return "user/upw";
    }

    @LogAction(action = "查询用户信息")
    @RequestMapping(path = "/selectoneuser",method = RequestMethod.POST)
    @RestResponseBody
    public User getOneUser(@CurrentUser User user){
        return user;
    }

    @LogAction(action = "用户修改信息")
    @RequestMapping(path = "/edituser",method = RequestMethod.POST)
    @RestResponseBody
    public boolean editUser(@CurrentUser User old, User user) throws Exception {
        old.setEmail(user.getEmail());
        // todo 此处是一个陷阱，如果后期启用用户名+密码登录，则修改真实姓名时不能直接进行用户名同步
        old.setUsername(user.getUsername());
        old.setTruename(Sm4Util.encryptEcb(sm4Key, user.getTruename()));
        old.setSex(user.getSex());
        userService.update(old);
        return true;
    }

    @LogAction(action = "用户修改手机号")
    @RequestMapping(path = "/editmobile",method = RequestMethod.POST)
    @RestResponseBody
    public boolean editPhone(@TokenParameters String token, @CurrentUser User old,
                             @NotBlank(message = "手机号不能为空") @PhoneNumber @RequestParam("phone") String phone,
                             String code) throws Exception {
        // todo 新手机号加密查询验证
        String encodePhone = Sm4Util.encryptEcb(sm4Key, phone);
        boolean existPhone = userService.existPhone(encodePhone);
        if(existPhone){
            throw new SsoException("新手机号已存在！");
        }
        boolean check = false;
        if (testEnv) {
            check = "6666".equals(code) || verfiyCodeService.isValid(phone, code, SmsType.UPDATE);
        } else {
            check = verfiyCodeService.isValid(phone, code, SmsType.UPDATE);
        }
        if (!check) {
            throw new SsoException(ErrorCode.CODE_ERROR);
        }
        //验证码验证成功，一次之后短信即失效
        verfiyCodeService.deleteCode(phone,  SmsType.UPDATE);
        String oldPhone = old.getPhone();
        //todo 姓名和手机号加密后进行修改
        old.setPhone(encodePhone);
        old.setTruename(Sm4Util.encryptEcb(sm4Key, old.getTruename()));
        userService.update(old);
        //本地修改手机号完成之后，相应的修改微信绑定的服务手机号
        Map<String,String> map=new HashMap<>(1);
        map.put("oldPhone",oldPhone);
        map.put("newPhone",phone);
        // todo 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(old.getUid()).operateUserName(old.getUsername())
                .userId(old.getUid()).userName(old.getUsername()).oldParam(oldPhone).newParam(phone)
                .operateType(LogOperation.USER_PHONE).build();
        build.setOperateDescription("[手机号:" + oldPhone + "->"+ phone + "]");
        userLogService.add(build);
        return true;
    }

    @LogAction(action = "用户修改头像")
    @RequestMapping(path = "/editicon",method = RequestMethod.POST)
    @RestResponseBody
    public boolean editIcon(@CurrentUser User old, @NotBlank(message = "头像不能为空") @IconAddress @RequestParam("icon") String icon) throws Exception {
        String oldIcon = old.getIcon();
        old.setIcon(icon);
        //todo 加密塞回来
        old.setPhone(Sm4Util.encryptEcb(sm4Key, old.getPhone()));
        old.setTruename(Sm4Util.encryptEcb(sm4Key, old.getTruename()));
        userService.update(old);
        // todo 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(old.getUid()).operateUserName(old.getUsername())
                .userId(old.getUid()).userName(old.getUsername()).oldParam(oldIcon).newParam(icon)
                .operateType(LogOperation.USER_ICON).build();
        build.setOperateDescription("[头像:" + oldIcon + "->"+ icon + "]");
        userLogService.add(build);
        return true;
    }

    @RequestMapping(path = "/mymessage",method = RequestMethod.POST)
    @ResponseBody
    public JsonResult getMyMessage(@CurrentUser User user, @TokenParameters String token, int pageNumber, int pageSize){
        Map<String,String> map=new HashMap<>(3);
        map.put("pageNumber",pageNumber+"");
        map.put("uid",user.getUid()+"");
        map.put("pageSize",pageSize+"");
//        Response response = OkHttpUtils.newsTimeOutInstance(60).doPostHeader(mymessage,map,token);
//        if(response == null || response.code() != 200){
//            return null;
//        }
//        try {
//            Object parse = JSON.parse(response.body().string());
//            return ResultUtil.success(parse);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        return null;
    }

    @RequestMapping(path = "/myrecentnews",method = RequestMethod.POST)
    @ResponseBody
    public JsonResult getMyRecentNews(@CurrentUser User user,@TokenParameters String token,int length){
        Map<String,String> map=new HashMap<>(1);
        Response response = OkHttpUtils.newsTimeOutInstance(60).doPostHeader(recentnews+"/"+user.getUid()+"/"+length, map, token);
        if(response == null || response.code() != 200){
            return null;
        }
        try {
            String string = response.body().string();
            Object parse = JSON.parse(string);
            return ResultUtil.success(parse);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(path = "/bp",method = RequestMethod.GET)
    public String bp(@CurrentUser User user,Model model){
        model.addAttribute(USERNAME_KEY,user.getUsername());
        return "user/bp";
    }

    @RequestMapping(path = "/findpw")
    public String findPw(){
        return "user/findpw";
    }

    @RequestMapping(path = "/register")
    public String register(){
        return "user/register";
    }

    @RequestMapping("/logout")
    public String logout(){
        SecurityUtils.getSubject().logout();
        return "redirect:/logout?service="+logoutUrl;
    }

    /**
     * 登陆后的绑定操作,需要前端将code传入
     * 该方式要求前端进行微信扫码内嵌
     * 微信绑定
     * 绑定成功后如何让前端进行感知，目前考虑返回js进行调用，前端将需要调用的js作为参数传入，类似于jsonp方案
     * 绑定或解绑成功后需要刷新用户信息
     * 回调函数可接受错误参数
     * @param currentUser
     * @param code
     * @return
     */
    @RequestMapping(value = "/bind/wx",produces = "text/html;charset=UTF-8")
    @ResponseBody
    @LogAction(action = "绑定微信")
    public String bind(@CurrentUser User currentUser, @NotBlank(message = "code不能为空") String code,
                       String callback, HttpServletResponse response){
        Collection<String> headers = response.getHeaders("Set-Cookie");
        for (String header : headers) {
            header += ";SameSite=None; Secure";
            response.addHeader("Set-Cookie", header);
        }
        WxResult result = WxUtil.getWxResult(WxConstant.TOKEN_URL,WxConstant.APP_ID,WxConstant.APP_SECRET,code);
        //扫码微信用用户
        String openid = result.getOpenid();
        User user = userService.getUserByWx(openid);
        User origin = userService.get(currentUser.getUid());
        if(StringUtils.isEmpty(origin.getWxOpenId())){
            //绑定操作
            //该微信未被绑定
            if(user==null){
                origin.setWxOpenId(openid);
                userService.update(origin);
                //此处返回js，调用前端方法，用于关闭微信扫码框
                return MessageFormat.format(BIND_CALLBACK_JS,callback,"'绑定成功'");
            }
            //该微信已被其他用户绑定
            return MessageFormat.format(BIND_CALLBACK_JS,callback,"'该微信已经绑定其他用户,请先进行账号解绑操作'");
        }else{
            //解绑操作
            if(user!=null&&origin.getWxOpenId().equals(openid)){
                origin.setWxOpenId(null);
                userService.update(origin);
                return MessageFormat.format(BIND_CALLBACK_JS,callback,"'解绑成功'");
            }
            return MessageFormat.format(BIND_CALLBACK_JS,callback,"'请使用原微信进行解绑操作'");

        }
    }

    @RequestMapping("/getLoginType")
    @RestResponseBody
    public LoginType[] getLoginTypes(){
        return LoginType.values();
    }

    /**
     * 获取第三方登录绑定信息，包含所有支持的第三方以及绑定情况
     * @param user
     * @return
     */
    @RequestMapping("/bindInfo")
    @RestResponseBody
    public List<BindInfo> getBindInfo(@CurrentUser User user,
                                      HttpServletResponse response){
        Collection<String> headers = response.getHeaders("Set-Cookie");
        for (String header : headers) {
            header += ";SameSite=None; Secure";
            response.addHeader("Set-Cookie", header);
        }
        LoginType[] types = LoginType.values();
        return Arrays
                .stream(types)
                .filter(LoginType::isShow)
                .map(loginType -> {
                    BindInfo bindInfo = new BindInfo(loginType);
                    String field = loginType.getField();
                    //反射获取是否为空，最好不使用这种，奇技淫巧
                    Field rField = ReflectionUtils.findField(User.class,field);
                    if (rField != null) {
                        rField.setAccessible(true);
                        try {
                            if(rField.get(user)!=null){
                                bindInfo.setBinded(true);
                            }
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                        return bindInfo;
                    }
                    return bindInfo;
                }).collect(Collectors.toList());
    }

    /**
     * 解绑或者绑定，使用同一个接口，后端进行判断即可
     * 解绑逻辑可加入更安全机制，比如要求提供原第三方标识
     * @param type
     * @param user
     * @return
     */
    @RequestMapping("/unbind")
    @RestResponseBody
    @LogAction(action = "解绑第三方帐号")
    public boolean unBind(@NotNull(message = "类型不能为空") LoginType type, @CurrentUser User user,
                          HttpServletResponse response){
        Collection<String> headers = response.getHeaders("Set-Cookie");
        for (String header : headers) {
            header += ";SameSite=None; Secure";
            response.addHeader("Set-Cookie", header);
        }
        if(type==null){
            throw new SsoException("不支持的类型");
        }
        String field = type.getField();
        //通过反射拿到该值，奇技淫巧，不可滥用
        Field rField = ReflectionUtils.findField(User.class,field);
        rField.setAccessible(true);
        //直接将其置空即可，简单而不粗暴,不论其是否已经进行了绑定
        try {
            rField.set(user,null);
            userService.update(user);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return true;
    }

}
