package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.entity.SmsRecord;
import cn.dahe.cas.auth.service.SmsRecordService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jasig.cas.monitor.MemoryMonitor;
import org.jasig.cas.monitor.MemoryStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/monitor")
@RequiresPermissions("monitor:manager")
public class MonitorController {

    @Autowired
    private MemoryMonitor memoryMonitor;

    @Autowired
    private SmsRecordService smsRecordService;

    @RestResponseBody
    @RequestMapping("memory")
    public MemoryStatus getMemory(){
        return memoryMonitor.observe();
    }

    @RestResponseBody
    @RequestMapping("/sms")
    public Page<SmsRecord> smsRecord(@RequestParam(required = false) String phone, @PageableDefault(value = 20,direction = Sort.Direction.DESC,sort = {"id"}) Pageable page){
        if(StringUtils.isBlank(phone)){
            return smsRecordService.get(page);
        }else{
            return smsRecordService.search(phone,page);
        }
    }
}
