package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.util.ImgUtil;
import cn.dahe.cas.auth.util.OkHttpUtils;
import cn.dahe.cas.auth.zimg.ZimgService;
import com.alibaba.fastjson.JSONObject;
import okhttp3.Response;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * on 2018/1/30.
 */
@ApiIgnore
@Controller
@RequestMapping("/upload")
public class UploadController {

    @Value("${image.type}")
    private String imageType;
    @Value("${image.size}")
    private String maxSize;
    @Value("${image.host}")
    private String imageUrl;

    @Autowired
    private ZimgService zimgService;

    @LogAction(action = "上传头像")
    @RequestMapping(path = "/upload-img",method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<String> uploadImg(MultipartFile attach) throws IOException {
        String ext = FilenameUtils.getExtension(attach.getOriginalFilename());
        if(!imageType.contains(ext.toLowerCase())){
            throw new SsoException("不支持此类型的图片！");
        }

        if (attach.getSize()>Integer.valueOf(maxSize)){
            throw new SsoException("图片太大，请勿超过20M！");
        }
        String pathurl = zimgService.upload(attach.getInputStream(), "", ext);

        String tempHost = "";
        if (!imageUrl.endsWith("/")) {
            tempHost = imageUrl + "/";
        } else {
            tempHost = imageUrl;
        }

        OkHttpUtils okHttpUtils = OkHttpUtils.newInstance();
        Response md5 = okHttpUtils.doGet(tempHost + "info?md5=" + pathurl);
        String tempResult = md5.body().string();
        if (StringUtils.isNotBlank(tempResult)) {

            JSONObject jsonObject = JSONObject.parseObject(tempResult);

            if (jsonObject.getJSONObject("info") != null) {
                return ResultUtil.success(tempHost + pathurl);
            }
        }
        throw new SsoException("图片上传失败！");
    }
    @LogAction(action = "图片裁剪")
    @RequestMapping(path = "/img-cut",method = RequestMethod.POST)
    @ResponseBody
    /**
     * x:x轴坐标    y:y轴坐标   width:截图框宽  cutWidth:截图宽
     * height:截图框高    cutHeight:截图高    imgePath:图片路径
     */
    public JsonResult<String> cutImge(int x, int y, int cutWidth, int cutHeight, int width, int height, String imgePath) throws IOException {
        imgePath = ImgUtil.addHttp(imgePath);
        String tempHost = "";
        if (!imageUrl.endsWith("/")) {
            tempHost = imageUrl + "/";
        } else {
            tempHost = imageUrl;
        }
        if (StringUtils.isNotBlank(imgePath)) {
            if (imgePath.contains("?")) {
                imgePath = imgePath.substring(0, imgePath.indexOf("?"));
            }
            OkHttpUtils okHttpUtils = OkHttpUtils.newInstance();
            String md5Number = imgePath.substring(imgePath.lastIndexOf("/")+1, imgePath.length());

            Response md5 = okHttpUtils.doGet(tempHost + "info?md5=" + md5Number);
            String tempResult = md5.body().string();
            if (StringUtils.isNotBlank(tempResult)) {

                JSONObject jsonObject = JSONObject.parseObject(tempResult);

                String format ="";
                if (jsonObject.getJSONObject("info") != null) {

                    int trueWidth = (Integer) jsonObject.getJSONObject("info").get("width");

                    int trueHeight = (Integer) jsonObject.getJSONObject("info").get("height");

                    format =(String) jsonObject.getJSONObject("info").get("format");

                    format = format.toLowerCase();

                    if (!imageType.contains(format)) {
                        throw new SsoException("图片不支持此类型！");
                    }
                    double wRatio = ((double) trueWidth) / width;

                    double hRatio = ((double) trueHeight) / height;

                    imgePath += "?w=" + (int)(cutWidth * wRatio )+ "&h=" + (int)(cutHeight * hRatio) + "&x=" +(int)( x * wRatio )+ "&y=" + (int)(y * hRatio);

                    Response response = okHttpUtils.doGet(imgePath);
                    InputStream inputStream = response.body().byteStream();
                    String newMd5 =zimgService.upload(inputStream ,"",format);
                    return ResultUtil.success(tempHost+newMd5);
                }

            }

            throw new SsoException("上传失败，不能获取原图信息");
        } else {
            throw new SsoException("图片地址传入有误!");
        }
    }
}
