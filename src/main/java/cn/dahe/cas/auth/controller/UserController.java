package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.*;
import cn.dahe.cas.auth.constants.LogOperation;
import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.domain.UserLog;
import cn.dahe.cas.auth.dto.*;
import cn.dahe.cas.auth.entity.Role;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.entity.User_;
import cn.dahe.cas.auth.event.RolePermissionEvent;
import cn.dahe.cas.auth.exception.ShouldLoginException;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.realm.AdminAuthencationToken;
import cn.dahe.cas.auth.search.SearchUser;
import cn.dahe.cas.auth.service.*;
import cn.dahe.cas.auth.statistics.user.UserStatistics;
import cn.dahe.cas.auth.util.MiduUtil;
import cn.dahe.cas.auth.util.OkHttpUtils;
import cn.dahe.cas.auth.util.QingboUtil;
import cn.dahe.cas.auth.util.SecurityUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.google.common.base.Joiner;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static cn.dahe.cas.auth.util.Sm4Util.decryptEcb;
import static cn.dahe.cas.auth.util.Sm4Util.encryptEcb;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "用户管理各种接口")
@Controller
@RequestMapping("/admin/user")
@RequiresPermissions("user:manager2")
public class UserController {


    @Value("${sm4.security.key}")
    private String sm4Key;

    @Value("${cas.server}")
    private String casServer;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private WordService wordService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private CasService casService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private UserStatistics userStatistics;

    @Autowired
    private ConversionService conversionService;

    @Autowired
    private UserLogService userLogService;

    @Value("${yq.user}")
    private String yqUserUpdateUrl;

    @Autowired
    private ApplicationEventPublisher publisher;


    @ApiOperation("管理员或客户列表信息（分页）")
    @RequestMapping(method = RequestMethod.POST)
    @RestResponseBody
    public Page<UserProfileDto> getUsers(@ApiIgnore Pageable pageable, SearchUser searchUser) {
        Page<User> users = null;
        if (searchUser.getSiteId() != null && searchUser.getSiteId() > 0){
            users = userService.findBySiteId(pageable, searchUser.getSiteId());
        }else {
            users = userService.searchUser(pageable, searchUser);
        }
        List<Site> defaultSites = siteService.getDefaultSites();
        users.forEach(el->{
            if (el.getEndTime() != null && el.getEndTime().getTime() < System.currentTimeMillis()) {
                el.setIsEffect(1);
            }
            List<Site> sites = siteService.getAccessSite(el.getUid());
            if (sites != null && sites.size() > 0) {
                List<String> strList = sites.stream().filter(e1->e1.getId()!=defaultSites.get(0).getId()).map(Site::getDescription).collect(Collectors.toList());
                String sitesStr = Joiner.on(",").join(strList);
                el.setPermissions(sitesStr);
            }
        });
        return users.map(user -> conversionService.convert(user,UserProfileDto.class));
    }

    @ApiOperation("添加管理员或客户列表信息")
    @LogAction(action = "添加用户")
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    @RestResponseBody
    public boolean addUser(@Valid User user, @ApiIgnore @CurrentUser User currentUser) throws Exception {
        String bindAccountInfo = URLUtil.decode(Base64.decodeStr(user.getThirdAccountInfo()));
        if (StrUtil.isNotBlank(bindAccountInfo) && bindAccountInfo.equals("undefined")) {
            bindAccountInfo = "";
            user.setThirdAccountInfo("");
        }
        log.info("++++++++++++++++add-user++++++++++++++++++【{}】", bindAccountInfo);
        user.setUsername(user.getTruename());
        User addUser = new User();
        String phone = user.getPhone();
        String trueName = user.getTruename();
        BeanUtils.copyProperties(user, addUser);
        user.setTruename(encryptEcb(sm4Key, user.getTruename()));
        user.setPhone(encryptEcb(sm4Key, user.getPhone()));
        if (StringUtils.isNotBlank(user.getAreaCode())) {
            String[] codes = user.getAreaCode().split(",");
            user.setAreaCode(codes[codes.length - 1]);
        }
        //需要对用户密码进行加密处理
        if (wordService.keyWord(user.getUsername())) {
            throw new SsoException("用户名不能以dahe或者大河开头！");
        }
        User add = userService.add(user);
        // todo 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(currentUser.getUid())
                .operateUserName(currentUser.getTruename()).userId(user.getUid()).userName(user.getUsername())
                .oldParam("").newParam(addUser).operateType(LogOperation.USER_ADD).build();
        build.setOperateDescription("添加用户");
        userLogService.add(build);
        try{
            Map<String,String> map = new HashMap<>();
            map.put("type", "0");
            map.put("id", String.valueOf(add.getUid()));
            map.put("phone",phone);
            map.put("username",trueName);
            Response response = OkHttpUtils.newInstance().doPost(yqUserUpdateUrl, map);
            log.info("开始向舆情子系统同步数据，同步结果为【{}】",response.toString());
        }catch (Exception e){
            log.info("开始向舆情子系统同步数据时报【{}】",e.getMessage());
        }

        return true;
    }

    @ApiOperation("绑定第三方供应商账号")
    @LogAction(action = "绑定第三方供应商账号")
    @RequestMapping(path = "/bind", method = RequestMethod.POST)
    @RestResponseBody
    public boolean bind(@RequestParam(required = false, defaultValue = "") String zyb,
                        @RequestParam(required = false, defaultValue = "") String znb,
                        @RequestParam(required = false, defaultValue = "") String wxb,
                        @RequestParam(required = false, defaultValue = "0") int uid,
                        @ApiIgnore @CurrentUser User currentUser) throws Exception {
        String zybOrigin = URLUtil.decode(Base64.decodeStr(zyb));
        String znbOrigin = URLUtil.decode(Base64.decodeStr(znb));
        String wxbOrigin = URLUtil.decode(Base64.decodeStr(wxb));
        if (StrUtil.isNotBlank(zybOrigin) && zybOrigin.equals("undefined")) {
            zybOrigin = "";
        }
        if (StrUtil.isNotBlank(znbOrigin) && znbOrigin.equals("undefined")) {
            znbOrigin = "";
        }
        if (StrUtil.isNotBlank(znbOrigin) && znbOrigin.equals("undefined")) {
            wxbOrigin = "";
        }
        StringBuilder sb = new StringBuilder();
        if (StrUtil.isNotBlank(zybOrigin)) {
            String zybTmp = "midu:zyb:"+zybOrigin;
            if (!MiduUtil.isValidFormat(zybTmp)) {
                throw new SsoException("专业版账号绑定格式有误");
            }
            sb.append(zybTmp);
            sb.append(",");
        }
        if (StrUtil.isNotBlank(znbOrigin)) {
            String znbTmp = "midu:znb:"+znbOrigin;
            if (!MiduUtil.isValidFormat(znbTmp)) {
                throw new SsoException("智能版账号绑定格式有误");
            }
            sb.append(znbTmp);
            sb.append(",");
        }
        if (StrUtil.isNotBlank(wxbOrigin)) {
            String wxbTmp = "qingbo:wxb:"+wxbOrigin;
            if (!QingboUtil.isValidFormat(wxbTmp)) {
                throw new SsoException("五星版账号绑定格式有误");
            }
            sb.append(wxbTmp);
            sb.append(",");
        }
        User old = userService.get(uid);
        if (old == null){
            throw new SsoException("该用户不存在");
        }
        //设置绑定信息
        old.setThirdAccountInfo(URLUtil.encode(Base64.encode(sb.toString())));
        String userName = decryptEcb(sm4Key, old.getTruename());
        String phone = decryptEcb(sm4Key, old.getPhone());
        UserDto updateUser =  new UserDto();
        BeanUtils.copyProperties(old, updateUser);
        updateUser.setTruename(userName);
        updateUser.setPhone(phone);
        // 修改之前copy出来原始数据
        UserDto oldUser =  new UserDto();
        BeanUtils.copyProperties(old, oldUser);
        oldUser.setPhone(decryptEcb(sm4Key, oldUser.getPhone()));
        oldUser.setTruename(decryptEcb(sm4Key, oldUser.getTruename()));
        String description = updateUser.comparatorObject(oldUser);
        userService.update(old);
        // 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(currentUser.getUid())
                .operateUserName(currentUser.getTruename()).userId(uid).userName(userName)
                .oldParam(old).newParam(updateUser).operateType(LogOperation.USER_BIND).build();
        build.setOperateDescription(description);
        userLogService.add(build);

        Site local = siteService.getOne("local");
        String adminSn = "admin";
        String siteUserSn = "site_user";
        String vipUserSn = "vip_user";
        List<String> roleSns = roleService.getSnByUserId(uid, local.getId());
        if (roleSns.contains(adminSn) || roleSns.contains(siteUserSn)) {
            return true;
        }
        Role vipRole = roleService.findBySiteIdAndSn(local.getId(), vipUserSn);
        int[] roleIds = new int[]{vipRole.getId()};
        userRoleService.addRole(uid, roleIds);
        UserLog build2 = UserLog.builder().operateDate(new Date()).operateUserId(currentUser.getUid())
                .operateUserName(currentUser.getTruename()).userId(uid).userName(userName)
                .oldParam(vipUserSn).newParam(roleIds).operateType(LogOperation.USER_ROLE).build();
        build.setOperateDescription("[角色:" + vipUserSn + "->"+ vipRole.getId() + "]");
        userLogService.add(build2);
        publisher.publishEvent(new RolePermissionEvent(String.valueOf(uid)));
        return true;
    }

    @ApiOperation("删除管理员或客户信息")
    @LogAction(action = "删除用户")
    @RequestMapping(path = "/del", method = RequestMethod.POST)
    @RestResponseBody
    public boolean deleteUser(@ApiIgnore @CurrentUser User user, int id) {
        if (user.getUid() == id) {
            throw new SsoException("不能删除自己的个人信息！");
        }
        User old = userService.get(id);
        if (old == null){
            throw new SsoException("该用户已经不存在");
        }
        userService.delete(id);
        //删除用户相应可访问站点
        siteService.deleteAllSite(id);
        // todo 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(user.getUid())
                .operateUserName(user.getTruename()).userId(id).userName(old.getUsername())
                .oldParam(old).newParam("").operateType(LogOperation.USER_DELETE).build();
        build.setOperateDescription("删除用户");
        userLogService.add(build);
        return true;
    }

    @ApiOperation("修改管理员或客户信息")
    @LogAction(action = "修改管理员或客户信息")
    @RequestMapping(path = "/edit", method = RequestMethod.POST)
    @RestResponseBody
    public boolean editUser(@Valid User user, @ApiIgnore @CurrentUser User currentUser)  throws Exception{
        String bindAccountInfo = URLUtil.decode(Base64.decodeStr(user.getThirdAccountInfo()));
        log.info("++++++++++++++++edit-user++++++++++++++++++【{}】", bindAccountInfo);
        if (StrUtil.isNotBlank(bindAccountInfo) && bindAccountInfo.equals("undefined")) {
            bindAccountInfo = "";
            user.setThirdAccountInfo("");
        }
        if (StringUtils.isNotBlank(user.getAreaCode())) {
            String[] codes = user.getAreaCode().split(",");
            user.setAreaCode(codes[codes.length - 1]);
        }
        String phone = user.getPhone();
        String trueName = user.getTruename();
        UserDto updateUser =  new UserDto();
        BeanUtils.copyProperties(user, updateUser);
        User old = userService.get(user.getUid());
        // 修改之前copy出来原始数据
        UserDto oldUser =  new UserDto();
        BeanUtils.copyProperties(old, oldUser);
        oldUser.setPhone(decryptEcb(sm4Key, oldUser.getPhone()));
        oldUser.setTruename(decryptEcb(sm4Key, oldUser.getTruename()));
        String description = updateUser.comparatorObject(oldUser);
        // todo 加密姓名和手机号,此处为陷阱,如果启用用户名+密码登录，会直接修改登录名，需要注意
        user.setUsername(user.getTruename());
        user.setTruename(encryptEcb(sm4Key, user.getTruename()));
        user.setPhone(encryptEcb(sm4Key, user.getPhone()));
        //排除不可修改属性（password、salt、icon）
        BeanUtils.copyProperties(user, old, User_.password.getName(), User_.salt.getName());
        userService.update(old);
        // 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(currentUser.getUid())
                .operateUserName(currentUser.getTruename()).userId(user.getUid()).userName(user.getUsername())
                .oldParam(oldUser).newParam(updateUser).operateType(LogOperation.USER_UPDATE).build();
        build.setOperateDescription(description);
        userLogService.add(build);
        try{
            Map<String,String> map = new HashMap<>();
            map.put("type", "1");
            map.put("id", String.valueOf(user.getUid()));
            map.put("phone",phone);
            map.put("username",trueName);
            Response response = OkHttpUtils.newInstance().doPost(yqUserUpdateUrl, map);
            log.info("开始向舆情子系统同步数据，同步结果为【{}】",response.toString());
        }catch (Exception e){
            log.info("开始向舆情子系统同步数据时报【{}】",e.getMessage());
        }
        return true;
    }

    @ApiOperation("禁用管理员或客户信息")
    @LogAction(action = "禁用管理员或客户信息")
    @RequestMapping(path = "/forbid", method = RequestMethod.POST)
    @RestResponseBody
    public boolean forbidUser(int uid, @ApiIgnore @CurrentUser User currentUser) {
        User user = userService.get(uid);
        user.setFlag(UserFlag.FORBID);
        userService.update(user);
        // todo 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(currentUser.getUid())
                .operateUserName(currentUser.getTruename()).userId(user.getUid()).userName(user.getUsername())
                .oldParam(UserFlag.ALLOW).newParam(UserFlag.FORBID).operateType(LogOperation.USER_STATUS).build();
        build.setOperateDescription("禁用");
        userLogService.add(build);
        return true;
    }

    @ApiOperation("解禁管理员或客户信息")
    @RequestMapping(path = "/allow", method = RequestMethod.POST)
    @RestResponseBody
    public boolean allowUser(int uid, @ApiIgnore @CurrentUser User currentUser) {
        User user = userService.get(uid);
        user.setFlag(UserFlag.ALLOW);
        userService.update(user);
        // todo 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(currentUser.getUid())
                .operateUserName(currentUser.getTruename()).userId(user.getUid()).userName(user.getUsername())
                .oldParam(UserFlag.FORBID).newParam(UserFlag.ALLOW).operateType(LogOperation.USER_STATUS).build();
        build.setOperateDescription("解禁");
        userLogService.add(build);
        return true;
    }

    @ApiIgnore
    @LogAction(action = "用户登录")
    @RequestMapping(path = "/login")
    @AccessLimit(count = 3, time = 60, limitTime = 600, msg = "登录次数过多", limitParams = {"username"}, title = "login")
    @RestResponseBody
    public boolean login(String username, String password) {
        Subject subject = SecurityUtils.getSubject();
        AuthenticationToken token = new AdminAuthencationToken(username, password);
        subject.login(token);
        // todo 加密
        User user = userService.getUserByUsername(subject.getPrincipal().toString());
        subject.getSession(true).setAttribute("user", user);
        return true;
    }

    @ApiIgnore
    @RequestMapping(path = "/info")
    @RestResponseBody
    public UserInfo getInfo(@ApiIgnore @CurrentUser User user) {
        Site oneSite = siteService.getOneSite(casServer);
        //获取自己的详细信息，包括权限等信息
        return userService.getDetail(user, oneSite.getId());
    }

    @ApiIgnore
    @LogAction(action = "退出登录")
    @RequestMapping(path = "/logout")
    @RestResponseBody
    public boolean logout() {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        return true;
    }

    @ApiIgnore
    @LogAction(action = "提示需要登录")
    @RequestMapping(path = "/logintip")
    @ResponseBody
    public void loginTip() {
        throw new ShouldLoginException("需要登录");
    }

    @ApiOperation("根据手机号获取用户信息")
    @RequestMapping("/getUserByPhone")
    @RestResponseBody
    public User getUserByPhone(@PhoneNumber String phone) {
        // 加密
        return userService.getUserByPhone(phone);
    }

    @ApiOperation("踢除用户")
    @RequestMapping("/kickout")
    @RestResponseBody
    public boolean kickoutUser(@ApiIgnore @CurrentUser User user, @RequestParam(required = false) Integer uid) {
        //未传入uid认为是剔除全部用户
        if (null == uid) {
            casService.clearUsers();
            return true;
        }
        if (user.getUid() == uid) {
            throw new SsoException("不能踢出自己");
        }
        casService.logoutUser(uid);
        return true;
    }

    @ApiIgnore
    @RequestMapping("areas")
    @RestResponseBody
    public List<Area> getAreas() {
        return areaService.getAreas(null, null);
    }

    @ApiIgnore
    @RequestMapping("area")
    @RestResponseBody
    public Area getAreaByCode(@NotBlank(message = "行政区划码不能为空") String code) {
        return areaService.getByCode(code);
    }

    @ApiOperation("用户统计信息")
    @RequestMapping("/statistics")
    @ResponseBody
    public JsonResult statistics(){
        return ResultUtil.success(userStatistics.getStatistics());
    }

    @ApiOperation("导出用户信息")
    @RequestMapping("/export/profile")
    @RestResponseBody
    public List<UserProfileDto> exportUserProfileDto(){
        List<User> users = userService.get();
        return users.stream().map(user -> {
            UserProfileDto profileDto = conversionService.convert(user,UserProfileDto.class);
            profileDto.setSites(siteService.getAccessSite(profileDto.getUid()));
            return profileDto;
        }).collect(Collectors.toList());
    }

}
