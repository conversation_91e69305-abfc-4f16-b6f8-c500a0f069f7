package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.PhoneParam;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.SmsType;
import cn.dahe.cas.auth.dto.*;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.security.PasswordEncry;
import cn.dahe.cas.auth.service.SiteTreeService;
import cn.dahe.cas.auth.service.SmsService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.JJWT;
import cn.dahe.cas.auth.util.Sm4Util;
import com.octo.captcha.service.CaptchaServiceException;
import com.octo.captcha.service.image.ImageCaptchaService;
import com.wf.captcha.GifCaptcha;
import io.swagger.annotations.Api;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.dahe.cas.auth.util.Sm4Util.decryptEcb;

/**
 * 客户端api
 */
@ApiIgnore
@Controller
@RequestMapping("/client")
public class ClientController {

    private static final Logger log = LoggerFactory.getLogger(ClientController.class);

    private static final Long MAX_SIZE = Long.valueOf(3*1024*1024);
    private static final List<String> TYPE_OF_IMAGE = Arrays.asList("jpg,png,jpeg,gif".split(","));
    private static final String LOGIN_SMSCODE_PREFIX = "sso:client:login:smscode:";
    private static final int SMS_LIMIT_TIME = 5;
    private static final String CAPTCHA_CACHE_PREFIX="sso:client:captcha:";
    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    private ImageCaptchaService captchaService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private UserService userService;

    @Autowired
    private PasswordEncry passwordEncry;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Autowired
    private SiteTreeService siteTreeService;


    /**
     * 获取登录短信图片验证码
     */
    @GetMapping("/captchaImage")
    @ResponseBody
    public JsonResult generate() throws Exception {
        GifCaptcha gifCaptcha = new GifCaptcha(130,48,4);
        String imgCode = gifCaptcha.text().toLowerCase();
        String key = CAPTCHA_CACHE_PREFIX + UUID.randomUUID().toString();
        redisTemplate.opsForValue().set(key, imgCode, 5, TimeUnit.MINUTES);
        // 将key和base64返回给前端
        Map<String,String> map=new HashMap<>();
        map.put("key", key);
        map.put("image", gifCaptcha.toBase64());
        return ResultUtil.success(map);
    }

    /**
     * 获取登录短信验证码
     */
    @RequestMapping("/login/smsCode")
    @ResponseBody
    public JsonResult loginSmsCode(@PhoneParam String phone,
                                @NotBlank(message = "图形验证码不能为空")String captcha,
                                @NotBlank(message = "图形验证码唯一标识符")String verKey,
                                HttpSession session) {
        Boolean yz = false;
        String verCode  = redisTemplate.opsForValue().get(verKey);
        if (verCode != null && captcha.toLowerCase().equals(verCode.trim().toLowerCase())) {
            if (StringUtils.isNotEmpty(verKey)) {
                redisTemplate.delete(verKey);
            }
            yz = true;
        }
        if (yz) {
            //存储验证码到redis
            String loginSmsCode = LOGIN_SMSCODE_PREFIX+phone;
            if (redisTemplate.opsForValue().getOperations().hasKey(loginSmsCode)) {
                throw new SsoException("短信验证码已发送");
            }
            //重新发送短信验证码,存储到redis
            SsoSms ssoSms = SsoSms.newSsoSms().mobile(phone).type(SmsType.LOGIN).build();
            smsService.sendSsoSms(ssoSms);
            redisTemplate.opsForValue().set(loginSmsCode,ssoSms.getCode(),SMS_LIMIT_TIME, TimeUnit.MINUTES);
            return ResultUtil.success();
        }
        return ResultUtil.fail("图形验证码不正确");
    }

    /**
     * 检验登录手机号验证码否匹配
     */
    @RequestMapping("/login/matchPhone")
    @ResponseBody
    public JsonResult loginMatchPhone(@PhoneParam String phone,
                                      @NotBlank(message = "验证码不能为空")String smsCode) throws Exception {
        if ("6666".equals(smsCode)) {
            User userByPhone = userService.getUserByPhone(Sm4Util.encryptEcb(sm4Key, phone));
            if (userByPhone != null) {
                return ResultUtil.success(JJWT.generateByUserId(String.valueOf(userByPhone.getUid())));
            }
            return ResultUtil.fail("手机号未注册");
        }
        String loginSmsCode = LOGIN_SMSCODE_PREFIX+phone;
        if (!redisTemplate.opsForValue().getOperations().hasKey(loginSmsCode)) {
            throw new SsoException("该手机号未发送短信");
        }
        if (!smsCode.equals(String.valueOf(redisTemplate.opsForValue().get(loginSmsCode)))) {
            throw new SsoException("短信验证码错误");
        }
        User userByPhone = userService.getUserByPhone(Sm4Util.encryptEcb(sm4Key, phone));
        if (userByPhone != null) {
            return ResultUtil.success(JJWT.generateByUserId(String.valueOf(userByPhone.getUid())));
        }
        return ResultUtil.fail("手机号未注册");
    }

    @RequestMapping(path = "/user/profile")
    @RestResponseBody
    @LogAction(action = "根据用户id获取用户资料")
    public JsonResult getUserProfile(HttpServletRequest request){
        String userId = getUserId(request);
        if (StringUtils.isNotBlank(userId)) {
            User user =  userService.get(Integer.parseInt(userId));
            if(null == user){
                throw new SsoException("无该用户");
            }
            user = decodeUser(user);
            UserProfileDto userProfileDto = new UserProfileDto();
            BeanUtils.copyProperties(user,userProfileDto);
            SiteDto site = siteTreeService.getSite(user.getSid());
            userProfileDto.setSiteName(site==null?"":site.getSiteName());
            return ResultUtil.success(userProfileDto);
        }
        return ResultUtil.fail("token无效");
    }


    /**
     * 个人中心修改头像(1)
     */
    @RequestMapping("/center/uploadIcon")
    @ResponseBody
    public Object centerUploadIcon(MultipartFile attach) throws Exception {
        // 获取文件后缀
        String ext = FilenameUtils.getExtension(attach.getOriginalFilename());
        //后缀全部转换成小写
        ext = ext.toLowerCase();
        if (!TYPE_OF_IMAGE.contains(ext)) {
            return ResultUtil.fail("不支持该类型文件");
        }
        long size = attach.getSize();
        if (size > MAX_SIZE) {
            return ResultUtil.fail("文件太大，请勿超过 3M");
        }
        String icon = "上传后的图片地址";
        return ResultUtil.success(icon);
    }


    private User decodeUser(User user) {
        try {
            user.setPhone(decryptEcb(sm4Key, user.getPhone()));
            user.setTruename(decryptEcb(sm4Key, user.getTruename()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return user;
    }

    private void checkValidCode(String captchaId,Object imgCode){
        try {
            if (!captchaService.validateResponseForID(captchaId,imgCode)) {
                throw new SsoException("图形验证码错误");
            }
        }catch (CaptchaServiceException e){
            log.error("图形验证码校验错误{}",e.getMessage());
            throw new SsoException("图形验证码错误");
        }
    }

    private String getUserId(HttpServletRequest request){
        String token = request.getHeader("token");
        String userId = JJWT.resoveUserCenterUserId(token);
        if (StringUtils.isNotBlank(userId)) {
            return userId;
        }
        return "";
    }
}
