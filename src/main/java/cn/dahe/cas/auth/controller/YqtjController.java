package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.entity.FocusPerson;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.FocusPersonService;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.DateUtil;
import cn.dahe.cas.auth.util.OkHttpUtils;
import cn.dahe.cas.auth.util.Sm4Util;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 豫情通鉴接口
 */
@Controller
@RequestMapping("/yqtjapi")
@Validated
public class YqtjController {
    @Autowired
    private SiteService siteService;
    @Autowired
    private FocusPersonService focusPersonService;
    @Autowired
    private UserService userService;
    @Value("${sm4.security.key}")
    private String sm4Key;
    private static final Logger log = LoggerFactory.getLogger(YqtjController.class);
    /**
     * 举报中心-危害类型列表
     */
    private static final String JBZX_DANGER_TYPE_LIST = "https://portal.henanjubao.com/jubao/api/danger/type/list";
    /**
     * 举报中心-图形验证码
     */
    private static final String JBZX_CODE_IMAGE = "https://portal.henanjubao.com/jubao/api/code/image";
    /**
     * 举报中心-上传附件
     */
    private static final String JBZX_UPLOAD = "https://portal.henanjubao.com/jubao/api/upload";
    /**
     * 举报中心-提交举报信息
     */
    private static final String JBZX_INFO_ADD = "https://portal.henanjubao.com/jubao/api/info/add";
    /**
     * 政务榜单分类列表接口
     */
    private static final String ZW_LIST_CLASSIFY = "http://192.168.77.205:8080/data-api/inner/bd_classify/";
    /**
     * 政务榜单期数列表接口
     */
    private static final String ZW_LIST_SEASON = "http://192.168.77.205:8080/data-api/inner/bd_periods/";
    /**
     * 政务榜单查询列表接口
     */
    private static final String ZW_LIST = "http://192.168.77.205:8080/data-api/inner/bd_list/";
    /**
     * 舆情总览
     */
    private static final String COUNT = "http://192.168.77.205:8080/data-api/inner/data_count/";
    /**
     * 重点人物监测
     */
    private static final String PEOPLE_LIST = "http://192.168.77.205:8080/data-api/inner/keywords_monitor/";
    /**
     * 热点事件
     */
    private static final String EVENT_LIST = "http://192.168.77.205:8080/data-api/inner/hot_event/";
    /**
     * 舆情榜单-平台热榜
     */
    private static final String PLATFORM_LIST = "http://192.168.77.205:8080/data-api/inner/hot_search/";
    /**
     * 舆情信息报送-领域树
     */
    private static final String ALK_FIELD_TREE = "https://yqalk.dahe.cn/api/field/tree/";
    /**
     * 舆情信息报送-地市树
     */
    private static final String ALK_CITY_TREE = "https://yqalk.dahe.cn/api/city/";
    /**
     * 舆情信息报送-添加
     */
    private static final String ALK_ADD = "https://yqalk.dahe.cn/api/yqinfo/add/";
    /**
     * 关键词搜索
     */
    private static final String KEYWORDS_SEARCH = "http://192.168.77.205:8080/data-api/inner/keywords_search/";

    private static final String ALK_PUB_LIST = "https://yqalk.dahe.cn/api/yqcase/publist/";
    private static final String ALK_PUB_DETAIL = "https://yqalk.dahe.cn/api/yqcase/detail/";
    private static final String ALK_PUB_FIELD = "https://yqalk.dahe.cn/api/yqcase/field/";
    private static final String ALK_PUB_PDF = "https://yqalk.dahe.cn/api/yqcase/download/pdf/";

    /**
     * 举报中心-危害类型列表
     *
     * @return 危害类型列表
     */
    @LogAction(action = "豫情通鉴-举报中心-危害类型列表")
    @RequestMapping("/jbzx/danger/type/list")
    @ResponseBody
    public JsonResult jbzxDangerTypeList(@CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(JBZX_DANGER_TYPE_LIST, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取举报危害类型列表接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 200) {
                JSONArray data = resultDto.getJSONArray("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取举报危害类型列表接口返回失败，原因：{}", code);
                return ResultUtil.fail("未返回数据");
            }
        } catch (Exception e) {
            log.warn("获取举报危害类型列表接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 举报中心-图形验证码
     *
     * @return 图形验证码
     */
    @LogAction(action = "豫情通鉴-举报中心-图形验证码")
    @RequestMapping("/jbzx/code/image")
    @ResponseBody
    public JsonResult jbzxCodeImage(@CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(JBZX_CODE_IMAGE, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取举报图形验证码接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 200) {
                JSONObject data = resultDto.getJSONObject("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取举报图形验证码接口返回失败，原因：{}", code);
                return ResultUtil.fail("未返回数据");
            }
        } catch (Exception e) {
            log.warn("获取举报图形验证码接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 举报中心-上传文件
     *
     * @param file 文件
     * @return 上传结果
     */
    @LogAction(action = "豫情通鉴-举报中心-上传文件")
    @RequestMapping("/jbzx/upload")
    @ResponseBody
    public JsonResult jbzxUpload(MultipartFile file, @CurrentUser User user) {
        Map<String, String> params = Maps.newHashMap();
        params.put("siteId", "4000000002");
        String resultJson;

        try (Response response = OkHttpUtils.newInstance().doPostFile(JBZX_UPLOAD, params, file)) {
            if (response == null) {
                log.info("举报上传文件接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 200) {
                JSONArray data = resultDto.getJSONArray("data");
                return ResultUtil.success(data);
            } else {
                log.warn("举报上传文件接口返回失败，原因：{}", code + resultDto.getInteger("msg"));
                return ResultUtil.fail("未返回数据");
            }
        } catch (Exception e) {
            log.warn("举报上传文件接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 举报中心-提交举报信息
     *
     * @param source        举报来源  0：默认网站 1：APP 2：新媒体（微信、微博、抖音、其他）3:小程序
     * @param platform      被举报平台名称，比如网站名称、微信账号名称等
     * @param title         举报标题
     * @param infoUrl       被举报信息的链接
     * @param content       举报内容
     * @param dangerType    危害类型
     * @param attachmentIds 附件ID串
     * @param uuid          图形验证码uuid
     * @param imageCode     图形验证码
     * @return 提交结果
     */
    @LogAction(action = "豫情通鉴-举报中心-提交举报信息")
    @RequestMapping("/jbzx/info/add")
    @ResponseBody
    public JsonResult jbzxInfoAdd(@RequestParam(defaultValue = "0") int source,
                                  String platform,
                                  String title,
                                  String infoUrl,
                                  String content,
                                  @RequestParam(defaultValue = "0") int dangerType,
                                  String attachmentIds,
                                  String uuid,
                                  String imageCode,
                                  @CurrentUser User user) throws Exception {
        log.info("当前用户信息：{}", user);
        Map<String, String> params = Maps.newHashMap();
        // 不需要验证手机号和验证码
        params.put("ifVerify", String.valueOf(0));
        params.put("smsCode", "6666");
        params.put("siteId", "4000000002");
        params.put("address", "");
        // 不成为网络监督员
        params.put("ifAddUser", "0");
        params.put("province", String.valueOf(0));
        params.put("city", String.valueOf(0));
        params.put("sex", String.valueOf(-2));
        params.put("age", String.valueOf(-2));
        params.put("identity", String.valueOf(-2));
        params.put("duty", String.valueOf(-2));
        params.put("unit", "");
        // 实名举报
        params.put("anonymous", String.valueOf(1));
        params.put("uuid", uuid);
        params.put("imageCode", imageCode);
        // 举报人姓名
        params.put("username", user.getUsername());
        String phone = user.getPhone();
        if (StringUtils.isBlank(user.getPhone())) {
            User user1 = userService.getUser(user.getPhone());
            if (user1 != null) {
                String userPhone = user1.getPhone();
                if (userPhone.length() == 11) {
                    phone = userPhone;
                } else {
                    phone = Sm4Util.decryptEcb(sm4Key, userPhone);
                }
            }
        }
        log.info("当前用户手机号：{}", phone);
        params.put("phone", phone);

        // 填写的内容
        params.put("source", String.valueOf(source));
        params.put("platform", platform);
        params.put("title", title);
        params.put("infoUrl", infoUrl);
        params.put("content", content);
        params.put("dangerType", String.valueOf(dangerType));
        if (StringUtils.isBlank(attachmentIds)) {
            params.put("attachmentIds", "");
        } else {
            params.put("attachmentIds", attachmentIds);
        }
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(JBZX_INFO_ADD, params)) {
            if (response == null) {
                log.info("举报提交举报信息接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 200) {
                JSONObject data = resultDto.getJSONObject("data");
                return ResultUtil.success(data);
            } else if (code == 500) {
                String msg = resultDto.getString("msg");
                return ResultUtil.fail(msg);
            } else {
                log.warn("举报提交举报信息接口返回失败，原因：{}", code);
                return ResultUtil.fail("未返回数据");
            }
        } catch (Exception e) {
            log.warn("举报提交举报信息接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 政务榜单分类
     *
     * @return 政务榜单分类列表
     */
    @LogAction(action = "豫情通鉴-政务榜单分类")
    @RequestMapping("/zw/list/classify")
    @ResponseBody
    public JsonResult xwListClassify(@CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ZW_LIST_CLASSIFY, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取政务榜单分类列表接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONArray data = resultDto.getJSONArray("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取政务榜单分类列表接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取政务榜单分类列表接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 政务榜单--期数
     *
     * @return 政务榜单期数列表
     */
    @LogAction(action = "豫情通鉴-政务榜单期数")
    @RequestMapping("/zw/list/season")
    @ResponseBody
    public JsonResult zwListSeason(@CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ZW_LIST_SEASON, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取政务榜单期数列表接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONArray data = resultDto.getJSONArray("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取政务榜单期数列表接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取政务榜单期数接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 政务榜单
     *
     * @param season   期数
     * @param classify 分类
     * @return 榜单列表
     */
    @LogAction(action = "豫情通鉴-政务榜单数据列表")
    @RequestMapping("/zw/list")
    @ResponseBody
    public JsonResult zwList(String season, Integer classify, @CurrentUser User user) {
        int year;
        int month;
        if (StringUtils.isBlank(season)) {
            year = LocalDate.now().getYear();
            month = LocalDate.now().getMonthOfYear();
        } else {
            String[] split = season.split("-");
            year = Integer.parseInt(split[0]);
            month = Integer.parseInt(split[1]);
        }
        if (classify == null) {
            return ResultUtil.fail("请选择分类");
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("year", year);
        params.put("month", month);
        params.put("bd_classify_id", classify);
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ZW_LIST, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取政务榜单列表接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONArray data = resultDto.getJSONArray("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取政务榜单列表接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取政务榜单列表接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 舆情总览数据
     *
     * @return 舆情总览数据
     */
    @LogAction(action = "豫情通鉴-舆情总览数据")
    @RequestMapping("/count")
    @ResponseBody
    public JsonResult count(@CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        // 根据用户查询有权限的子系统
        String siteIds = getSiteIds(user.getUid());
        params.put("servicerIds", "1,2,3");
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(COUNT, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取舆情总览接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONObject data = resultDto.getJSONObject("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取舆情总览接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取舆情总览接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 重点人物监测
     *
     * @param page 页码
     * @param size 页码大小
     * @return 重点人物监测
     */
    @LogAction(action = "豫情通鉴-重点人物监测")
    @RequestMapping("/people/list")
    @ResponseBody
    public JsonResult peopleList(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "8") Integer size, @CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("page", page);
        params.put("size", size);
        // 根据用户查询有权限的子系统
        String siteIds = getSiteIds(user.getUid());
        params.put("servicerIds", "1,2,3");
        // 关键词
        String keywords = getKeywordsByUserId(user.getUid());
        if (StringUtils.isNotBlank(keywords)) {
            params.put("keywords", keywords);
        }
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(PEOPLE_LIST, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取重点人物监测接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONObject data = resultDto.getJSONObject("data");
                JSONArray result = data.getJSONArray("result");
                Integer total = data.getInteger("total");
                PageImpl<Object> dataPage = new PageImpl<>(result, null, total);
                return ResultUtil.success(dataPage);
            } else {
                log.warn("获取重点人物监测接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取重点人物监测接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 热点事件
     *
     * @param page 页码
     * @param size 页码大小
     * @return 热点事件
     */
    @LogAction(action = "豫情通鉴-热点事件")
    @RequestMapping("/event/list")
    @ResponseBody
    public JsonResult eventList(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "6") Integer size, @CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("page", page);
        params.put("size", size);
        // 根据用户查询有权限的子系统
        String siteIds = getSiteIds(user.getUid());
        params.put("servicerIds", "1,2,3");
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(EVENT_LIST, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取热点事件接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONObject data = resultDto.getJSONObject("data");
                JSONArray result = data.getJSONArray("result");
                Integer total = data.getInteger("total");
                PageImpl<Object> dataPage = new PageImpl<>(result, null, total);
                return ResultUtil.success(dataPage);
            } else {
                log.warn("获取热点事件接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取热点事件接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 舆情榜单-平台热榜
     *
     * @param platform 平台 1：微博 2：抖音 3：快手 4：百度 5：头条 6：知乎 100：其他
     * @param rankTime 时间范围 1：实时热榜 2：24小时热榜 3：近7天热榜 4：近30天热榜
     * @return 平台热榜
     */
    @LogAction(action = "豫情通鉴-舆情榜单-平台热榜")
    @RequestMapping("/platform/list")
    @ResponseBody
    public JsonResult platformList(@RequestParam(defaultValue = "1") Integer platform, @RequestParam(defaultValue = "1") Integer rankTime, @CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("platform", platform);
        params.put("rank_time", rankTime);
        // 根据用户查询有权限的子系统
        String siteIds = getSiteIds(user.getUid());
        params.put("servicerIds", "1,2,3");
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(PLATFORM_LIST, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取平台热榜接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONArray data = resultDto.getJSONArray("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取平台热榜接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取平台热榜接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 舆情信息报送-领域树
     *
     * @return 领域树
     */
    @LogAction(action = "豫情通鉴-舆情信息报送-领域树")
    @RequestMapping("/alk/field/tree")
    @ResponseBody
    public JsonResult alkFieldTree(@CurrentUser User user) {
        Map<String, String> params = Maps.newHashMap();
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ALK_FIELD_TREE, params)) {
            if (response == null) {
                log.info("获取舆情信息报送领域树接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONObject data = resultDto.getJSONObject("data");
                JSONArray result = data.getJSONArray("result");
                if (Objects.nonNull(result) || result.size() == 0) {
                    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(result.get(0)));
                    JSONArray childResult = jsonObject.getJSONArray("children");
                    return ResultUtil.success(childResult);
                } else {
                    return ResultUtil.success(result);
                }
            } else {
                log.warn("获取舆情信息报送领域树接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取舆情信息报送领域树接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 舆情信息报送-城市树
     *
     * @return 城市树
     */
    @LogAction(action = "豫情通鉴-舆情信息报送-城市树")
    @RequestMapping("/alk/city/tree")
    @ResponseBody
    public JsonResult alkCityTree(@CurrentUser User user) {
        Map<String, String> params = Maps.newHashMap();
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ALK_CITY_TREE, params)) {
            if (response == null) {
                log.info("获取舆情信息报送城市树接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONObject data = resultDto.getJSONObject("data");
                JSONArray result = data.getJSONArray("result");
                return ResultUtil.success(result);
            } else {
                log.warn("获取舆情信息报送城市树接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取舆情信息报送城市树接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 舆情信息报送-添加
     *
     * @param user                当前用户
     * @param pubTime             发生时间,yyyy-MM-dd HH:mm:ss（必填）
     * @param event               事件（必填）
     * @param source              来源（必填）
     * @param platform            平台: 1微博 2微信 3抖音 4今日头条 5小红书 6网页 7客户端 8快手 9论坛 10西瓜视频（必填）
     * @param mediaLevel          媒体级别: 1中央 2省内 3省外
     * @param sens                情感倾向: 0中性 1正面 2负面（必填）
     * @param url                 链接（必填）
     * @param firstFieldId        一级领域id（必填）
     * @param secondFieldId       二级领域id
     * @param thirdFieldId        三级领域id
     * @param subjectClassify     主体分类
     *                            二级领域-保险：1财产险公司 2寿险公司 3其他
     *                            二级领域-银行：1国有商业银行 2农村商业银行 3城市商业银行 4股份制商业银行 5村镇银行 6政策性银行 7中央银行 8信托银行 9金融公司
     * @param administrationLevel 行政级别: 1省 2市 3县（必填）
     * @param province            省（必填）
     * @param city                市
     * @param country             县
     * @return 添加结果
     */
    @LogAction(action = "豫情通鉴-舆情信息报送-添加")
    @RequestMapping("/alk/add")
    @ResponseBody
    public JsonResult alkAdd(Date pubTime,
                             String title,
                             String event,
                             String source,
                             Integer platform,
                             Integer mediaLevel,
                             Integer sens,
                             String url,
                             Integer firstFieldId,
                             Integer secondFieldId,
                             Integer thirdFieldId,
                             Integer subjectClassify,
                             Integer administrationLevel,
                             Integer province,
                             Integer city,
                             Integer country,
                             @CurrentUser User user) {
        if (pubTime == null) {
            return ResultUtil.fail("时间必填");
        }
        if (StringUtils.isBlank(event)) {
            return ResultUtil.fail("事件内容必填");
        }
        if (StringUtils.isBlank(source)) {
            return ResultUtil.fail("来源必填");
        }
        if (platform == null) {
            return ResultUtil.fail("请选择平台");
        }
        if (sens == null) {
            return ResultUtil.fail("请选择情感倾向");
        }
        if (StringUtils.isBlank(url)) {
            return ResultUtil.fail("请输入链接");
        }
        if (firstFieldId == null) {
            return ResultUtil.fail("请选择一级领域");
        }
        if (administrationLevel == null) {
            return ResultUtil.fail("请选择行政级别");
        }
        if (province == null) {
            return ResultUtil.fail("请选择省份");
        }
      /*  if (StringUtils.isBlank(title)) {
            return ResultUtil.fail("请输入标题");
        }*/
        Map<String, Object> params = Maps.newHashMap();
        params.put("userId", user.getUid());
        params.put("username", user.getUsername());
        params.put("pubTime", DateUtil.format(pubTime, "yyyy-MM-dd HH:mm:ss"));
        params.put("event", event);
        params.put("source", source);
        params.put("platform", platform);
        params.put("mediaLevel", mediaLevel);
        params.put("sens", sens);
        params.put("url", url);
        params.put("firstFieldId", firstFieldId);
        params.put("secondFieldId", secondFieldId);
        params.put("thirdFieldId", thirdFieldId);
        params.put("subjectClassify", subjectClassify);
        params.put("administrationLevel", administrationLevel);
        params.put("province", province);
        params.put("city", city);
        params.put("country", country);
        params.put("title", title);
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ALK_ADD, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("请求舆情信息报送-添加接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONArray data = resultDto.getJSONArray("data");
                return ResultUtil.success(data);
            } else {
                log.warn("请求舆情信息报送-添加返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("请求舆情信息报送-添加失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 获取用户有权限的子系统id
     *
     * @param userId 用户信息id
     * @return 子系统id串
     */
    private String getSiteIds(int userId) {
        List<Site> userSiteList = siteService.getAccessSite(userId);
        List<Integer> siteIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(userSiteList)) {
            userSiteList.stream().forEach(userSite -> siteIdList.add(userSite.getId()));
        }
        String siteIds = StringUtils.join(siteIdList, ",");
        return siteIds;
    }

    /**
     * 根据用户获取关键词
     *
     * @param userId 用户信息id
     * @return 关键词
     */
    private String getKeywordsByUserId(int userId) {
        List<FocusPerson> focusPeopleList = focusPersonService.listByUserId(userId);
        List<String> keywordList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(focusPeopleList)) {
            focusPeopleList.forEach(focusPerson -> {
                keywordList.add(focusPerson.getAreaKeywords());
                keywordList.add(focusPerson.getEventKeywords());
                keywordList.add(focusPerson.getSubjectKeywords());
            });
        }
        String keywords = StringUtils.join(keywordList, "||");
        return keywords;
    }

    /**
     * 搜索
     *
     * @param page     页码
     * @param size     页码大小
     * @param keywords 关键词
     * @return 搜索
     */
    @LogAction(action = "豫情通鉴-搜索")
    @RequestMapping("/keywords/search")
    @ResponseBody
    public JsonResult keywordsSearch(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "20") Integer size, String keywords, @CurrentUser User user) {
        if (StringUtils.isBlank(keywords)) {
            return ResultUtil.fail("请输入关键词");
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("page", page);
        params.put("size", size);
        params.put("keywords", keywords);
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(KEYWORDS_SEARCH, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("搜索接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONObject data = resultDto.getJSONObject("data");
                JSONArray result = data.getJSONArray("result");
                Integer total = data.getInteger("total");
                PageImpl<Object> dataPage = new PageImpl<>(result, null, total);
                return ResultUtil.success(dataPage);
            } else {
                log.warn("搜索接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("搜索接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 案例库-案例列表
     *
     * @param page 页码
     * @param size 页面大小
     * @return 案例列表
     */
    @LogAction(action = "豫情通鉴-案例库-案例列表")
    @RequestMapping("/alk/pub/list")
    @ResponseBody
    public JsonResult alkPubList(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "8") Integer size, Integer involvedField, @CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("page", page);
        params.put("size", size);
        if (involvedField != null) {
            params.put("involvedField", involvedField);
        }
        // 根据用户查询有权限的子系统
        String siteIds = getSiteIds(user.getUid());
        params.put("servicerIds", "1,2,3");
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ALK_PUB_LIST, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取案例库案例列表接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONObject data = resultDto.getJSONObject("data");
                JSONArray result = data.getJSONArray("result");
                Integer total = data.getInteger("total");
                PageImpl<Object> dataPage = new PageImpl<>(result, null, total);
                return ResultUtil.success(dataPage);
            } else {
                log.warn("获取案例库案例列表接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取案例库案例列表接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 案例库-案例详情
     *
     * @param yqcaseId id
     * @return 案例详情
     */
    @LogAction(action = "豫情通鉴-案例库-案例详情")
    @RequestMapping("/alk/pub/detail")
    @ResponseBody
    public JsonResult alkPubDetail(@RequestParam(defaultValue = "0") Integer yqcaseId, @CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("yqcaseId", yqcaseId);
        // 根据用户查询有权限的子系统
        String siteIds = getSiteIds(user.getUid());
        params.put("servicerIds", "1,2,3");
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ALK_PUB_DETAIL, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取案例库案例详情接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONObject data = resultDto.getJSONObject("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取案例库案例详情接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取案例库案例详情接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 案例库-案例领域列表
     *
     * @return 案例领域列表
     */
    @LogAction(action = "豫情通鉴-案例库-案例领域列表")
    @RequestMapping("/alk/pub/field")
    @ResponseBody
    public JsonResult alkPubField(@CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        // 根据用户查询有权限的子系统
        String siteIds = getSiteIds(user.getUid());
        params.put("servicerIds", "1,2,3");
        String resultJson;
        try (Response response = OkHttpUtils.newInstance().doPost(ALK_PUB_FIELD, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取案例库案例领域列表接口请求超时");
                return ResultUtil.fail("未返回数据");
            }
            resultJson = Objects.requireNonNull(response.body()).string();
            JSONObject resultDto = JSON.parseObject(resultJson);
            Integer code = resultDto.getInteger("code");
            if (code == 1000) {
                JSONArray data = resultDto.getJSONArray("data");
                return ResultUtil.success(data);
            } else {
                log.warn("获取案例库案例领域列表接口返回失败，原因：{}", code);
                return ResultUtil.fail(resultDto.getString("message"));
            }
        } catch (Exception e) {
            log.warn("获取案例库案例领域列表接口失败，原因：{}", e.getMessage());
            return ResultUtil.fail("未返回数据");
        }
    }

    /**
     * 案例库-案例pdf查看
     *
     * @param yqcaseId id
     * @param vType    1查看  2下载
     * @return 案例pdf
     */
    @LogAction(action = "豫情通鉴-案例库-案例pdf")
    @RequestMapping("/alk/pub/pdf")
//    @ResponseBody
    public void alkPubPdf(@RequestParam(defaultValue = "0") Integer yqcaseId, @RequestParam(defaultValue = "1") int vType, HttpServletResponse resp, @CurrentUser User user) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("yqcaseId", yqcaseId);
        params.put("vType", vType);
        // 根据用户查询有权限的子系统
//        String siteIds = getSiteIds(user.getUid());
        params.put("servicerIds", "1,2,3");
        try (Response response = OkHttpUtils.newInstance().doPost(ALK_PUB_PDF, JSON.toJSONString(params))) {
            if (response == null) {
                log.info("获取案例库案例pdf接口请求超时");
            }
            InputStream inputStream = response.body().byteStream();
            ServletOutputStream outputStream = resp.getOutputStream();
            if (vType == 1) {
                // 预览
                resp.setContentType("application/pdf");
                resp.setHeader("Content-Disposition", "inline;");
            } else {
                // 下载
                resp.setContentType(MediaType.APPLICATION_OCTET_STREAM.getType());
                resp.setHeader("Content-Disposition", "attachment;");
            }

            resp.setHeader("Cache-Control", "no-cache");
            byte[] buffer = new byte[1024];
            int bytesRead = -1;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
            inputStream.close();
            outputStream.close();
        } catch (Exception e) {
            log.warn("获取案例库案例pdf接口失败，原因：{}", e.getMessage());
        }
    }
}
