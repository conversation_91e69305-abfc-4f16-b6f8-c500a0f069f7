package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.constants.LogType;
import cn.dahe.cas.auth.constants.LoginType2;
import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.domain.Log;
import cn.dahe.cas.auth.domain.LoginLog;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.dto.SelectType;
import cn.dahe.cas.auth.entity.LogSource;
import cn.dahe.cas.auth.event.LogSourceEvent;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.search.SearchLog;
import cn.dahe.cas.auth.search.SearchLoginLog;
import cn.dahe.cas.auth.service.LogService;
import cn.dahe.cas.auth.service.LogSourceService;
import cn.dahe.cas.auth.service.SystemLogService;
import cn.dahe.cas.auth.service.UserLoginLogService;
import cn.dahe.cas.auth.statistics.log.SystemLogStatistics;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import cn.dahe.cas.auth.statistics.login.UserLoginStatistics;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import cn.dahe.cas.auth.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/log")
@RequiresPermissions("log:manager")
public class LogController {

    @Autowired
    private SystemLogService systemLogService;

    @Autowired
    private LogService logService;

    @Autowired
    private LogSourceService logSourceService;

    @Autowired
    private UserLoginLogService userLoginLogService;

    @Autowired
    private UserLoginStatistics loginStatistics;

    @Autowired
    private SystemLogStatistics systemLogStatistics;

    @Autowired
    private ApplicationEventPublisher publisher;

    @RequestMapping(method = RequestMethod.POST)
    @RestResponseBody
    public Page<Log> searchLog(@RequestParam int page, @RequestParam int size, SearchLog searchLog){
        Pageable pageable = new PageRequest(page-1,size,new Sort(Sort.Direction.DESC, "operateDate"));
        return systemLogService.page(searchLog, pageable);
    }

    @LogAction(action = "添加日志来源")
    @RequestMapping(path = "/add/source",method = RequestMethod.POST)
    @RestResponseBody
    public boolean addLogSource(LogSource logSource){
        LogSource exist = logSourceService.exist(logSource.getSource());
        if(exist != null){
            throw new SsoException("该来源已存在！");
        }
        LogSource result = logSourceService.add(logSource);
        publisher.publishEvent(new LogSourceEvent(result, EntityOperation.ADD));
        return true;
    }

    @LogAction(action = "修改日志来源")
    @RequestMapping(path = "/update/source",method = RequestMethod.POST)
    @RestResponseBody
    public boolean updateLogSource(LogSource logSource){
        LogSource oldSource = logSourceService.get(logSource.getId());
        if (oldSource == null){
            throw new SsoException("该日志来源不存在");
        }
        LogSource exist = logSourceService.exist(logSource.getSource());
        if(exist != null && exist.getId() != logSource.getId()){
            throw new SsoException("该日志来源已添加");
        }
        logSourceService.update(logSource);
        publisher.publishEvent(new LogSourceEvent(logSource,EntityOperation.EDIT));
        return true;
    }

    @LogAction(action = "修改日志来源状态")
    @RequestMapping(path = "/update/source/status",method = RequestMethod.POST)
    @RestResponseBody
    public boolean updateLogSource(int id, UserFlag status){
        LogSource logSource = logSourceService.get(id);
        logSource.setStatus(status);
        logSourceService.update(logSource);
        publisher.publishEvent(new LogSourceEvent(logSource,EntityOperation.EDIT));
        return true;
    }

    @LogAction(action = "删除日志来源")
    @RequestMapping(path = "/delete/source",method = RequestMethod.POST)
    @RestResponseBody
    public boolean deleteLogSource(int id){
        logSourceService.delete(id);
        publisher.publishEvent(new LogSourceEvent(null,EntityOperation.DELETE));
        return true;
    }

    @RequestMapping(path = "/source",method = RequestMethod.POST)
    @RestResponseBody
    public List<String> getLogSource(){
        return logSourceService.listAllLogSource();
    }

    @RequestMapping(path = "/findlogsource",method = RequestMethod.POST)
    @RestResponseBody
    public Page<LogSource> findLogSource(@RequestParam int page,@RequestParam int size){
        Pageable pageable = new PageRequest(page-1,size,new Sort(Sort.Direction.DESC, "id"));
        Page<LogSource> logSources = logSourceService.get(pageable);
        return logSources;
    }

    @RequestMapping(path = "/type",method = RequestMethod.POST)
    @RestResponseBody
    public SelectType[] getLogEnumType(){
        return LogType.toSelectType();
    }

    @LogAction(action = "删除日志")
    @RequestMapping(method = RequestMethod.POST,path = "/delete")
    @RestResponseBody
    public boolean deleteLog(@RequestParam int id){
        throw new SsoException("不支持删除操作");
    }

    @RequestMapping(method = RequestMethod.POST,path = "/clear_log_condition")
    @ResponseBody
    public JsonResult getClearLogCondition(){
        return ResultUtil.success(logService.getClearLogCondition());
    }

    @LogAction(action = "重新设置清理日志的条件")
    @RequestMapping(method = RequestMethod.POST,path = "/set_log_condition")
    @RestResponseBody
    public boolean setClearLogCondition(@RequestParam(defaultValue = "0",required = false) int count,
                                        @RequestParam(defaultValue = "0",required = false) int date,
                                        @RequestParam(defaultValue = "1",required = false) int logSwitch){
        if (count == 0 || date == 0){
            throw new SsoException("请设置要清理的条件！");
        }
        logService.saveClearLogCondition(count*10000, date, logSwitch);
        return true;
    }

    @RestResponseBody
    @RequestMapping(method = RequestMethod.GET,path = "/login/type")
    public LoginType2[] getLoginLogs(){
        return LoginType2.values();
    }

    @RestResponseBody
    @RequestMapping(method = RequestMethod.GET,path = "/login/statistics/period")
    public LoginStatisticsPeriod[] getLoginStatisticsPeriod(){
        return LoginStatisticsPeriod.values();
    }

    @RestResponseBody
    @RequestMapping(method = RequestMethod.GET,path = "/system/statistics/period")
    public LogStatisticsPeriod[] getLogStatisticsPeriod(){
        return LogStatisticsPeriod.values();
    }

    @RestResponseBody
    @RequestMapping(method = RequestMethod.POST,path = "/login")
    public Page<LoginLog> getLoginLogs(Pageable pageable, SearchLoginLog searchLoginLog){
        if(StringUtils.isNotBlank(searchLoginLog.getIdentify())){
            if(CommonUtil.isPhoneNumber(searchLoginLog.getIdentify())){
                searchLoginLog.setPhone(searchLoginLog.getIdentify());
            }else{
                searchLoginLog.setUsername(searchLoginLog.getIdentify());
            }
        }
        return userLoginLogService.search(pageable,searchLoginLog);
    }

    @RequestMapping(method = RequestMethod.POST,path = "/login/statistics")
    @ResponseBody
    public  JsonResult loginLogStatistics(@RequestParam(required = false) LoginStatisticsPeriod period){
        if(period==null){
            period = LoginStatisticsPeriod.MONTH;
        }
        Map<String,Object> statistics = loginStatistics.getStatistics(period);
        return ResultUtil.success(statistics);
    }

    @RequestMapping(method = RequestMethod.POST,path = "/system/statistics")
    @ResponseBody
    public  JsonResult systemLogStatistics(@RequestParam(required = false) LogStatisticsPeriod period){
        if(period==null){
            period = LogStatisticsPeriod.MONTH;
        }
        Map<String,Object> statistics = systemLogStatistics.getStatistics(period);
        return ResultUtil.success(statistics);
    }
}
