package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.dto.TgcInfo;
import cn.dahe.cas.auth.dto.UserInfo;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.SsoService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.*;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.URIBuilder;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.jasig.cas.web.support.CookieRetrievingCookieGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.text.MessageFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "各种接口")
@Controller
@RequestMapping("/front")
public class FrontController {

    @Value("${cas.server}")
    private String casServer;

    @Autowired
    private UserService userService;

    @Autowired
    private SiteService siteService;

    @Value("${cas.static.login}")
    private String loginUrl;

    @Value("${cas.server.logout}")
    private String logoutUrl;

    @Value("${cas.home}")
    private String homeUrl;

    @Autowired
    private SsoService ssoService;

    @Autowired
    private CookieRetrievingCookieGenerator ticketGrantingTicketCookieGenerator;

    @ApiIgnore
    @LogAction(action = "前台获取用户信息")
    @RequestMapping(path = "/user/info")
    @ResponseBody
    public JsonResult getInfo(HttpServletRequest request, String service) {
        String tgt = ticketGrantingTicketCookieGenerator.retrieveCookieValue(request);
        TgcInfo tgcInfo = new TgcInfo();
        tgcInfo.setLogoutUrl(logoutUrl);
        tgcInfo.setHomeUrl(homeUrl);
        if(StringUtils.isEmpty(service)){
            service = homeUrl;
        }
        tgcInfo.setLoginUrl(MessageFormat.format(loginUrl,new Object[]{service,service}));
        if(StringUtils.isEmpty(tgt)){
            return ResultUtil.fail("未登录");
        }
        Map<String,Object> attrs = ssoService.getUserInfo(tgt);
        tgcInfo.setPrincipal(attrs);
        User user = userService.get(Integer.parseInt(attrs.get("uid").toString()));
        Site oneSite = siteService.getOneSite(casServer);
        UserInfo userInfo = userService.getDetail(user, oneSite.getId());
        List<Site> userSiteList = siteService.getAccessSite(user.getUid());
        userInfo.setSites(userSiteList);
        //过滤敏感信息
        User tmp = userInfo.getUser();
        tmp.setThirdAccountInfo("");
        tmp.setTruename("");
        tmp.setPhone("");
        userInfo.setUser(tmp);
        return ResultUtil.success(userInfo);
    }

    @ApiIgnore
    @LogAction(action = "用户前台站点列表")
    @RequestMapping(path = "/user/sites")
    @RestResponseBody
    public List<Site> userSites(@ApiIgnore @CurrentUser User user) {
        if (user == null) {
            throw new SsoException("用户未登录");
        }
        List<Site> userSiteList = siteService.getAccessSite(user.getUid());
        // 定义默认版本顺序
        List<String> defaultTypes = Arrays.asList("五星版", "专业版", "智能版", "智享版");
        // 按类型分组并排序
        Map<String, List<Site>> siteMap = new LinkedHashMap<>();
        for (String type : defaultTypes) {
            siteMap.put(type, new ArrayList<>());
        }
        if (userSiteList != null) {
            for (Site site : userSiteList) {
                if (siteMap.containsKey(site.getDescription())) {
                    siteMap.get(site.getDescription()).add(site);
                }
            }
        }
        // 构建结果列表，每个类型取权重最高的站点
        List<Site> resultList = new ArrayList<>();
        int weightOffset = 0;
        for (Map.Entry<String, List<Site>> entry : siteMap.entrySet()) {
            List<Site> sites = entry.getValue();
            if (!sites.isEmpty()) {
                // 对每个类型的站点按权重排序
                sites.sort(Comparator.comparingInt(Site::getWeight).reversed());
                // 添加权重最高的站点
                resultList.add(sites.get(0));
            } else {
                // 填充默认站点，权重依次递减
                Site defaultSite = new Site();
                defaultSite.setDescription(entry.getKey());
                defaultSite.setWeight(-1 - weightOffset++);
                resultList.add(defaultSite);
            }
        }
        // 最终按权重排序（实际已按规则排列好）
        Collections.sort(resultList, Comparator.comparingInt(Site::getWeight).reversed());
        resultList.forEach(el->{
            if (StrUtil.isNotBlank(el.getUrl()) && el.getUrl().contains("zyvip")) {
                el.setIndexUrl("https://yuqingtong.dahe.cn/front/go/zyb");
            }
            if (StrUtil.isNotBlank(el.getUrl()) && el.getUrl().contains("spvip")) {
                el.setIndexUrl("https://yuqingtong.dahe.cn/front/go/znb");
            }
        });
        return resultList;
    }

    /**
     * //TODO 设置专业版链接，暂时写死，后续要改成一对一
     * @param user
     * @return
     */
    @ApiIgnore
    @LogAction(action = "访问专业版")
    @RequestMapping(path = "/go/zyb")
    public String goZyb(@ApiIgnore @CurrentUser User user) {
        if (user == null) {
            throw new SsoException("用户未登录");
        }
        //测试账号
        String appid = "";
        String appSecret = "";
        String username = "";
        String systemId = "";
        String channel = "";
        //动态绑定账号
        String thirdAccountInfo = user.getThirdAccountInfo();
        thirdAccountInfo = URLUtil.decode(Base64.decodeStr(thirdAccountInfo));
        log.info("++++++++++++++++/go/zyb++++++++++++++++++{}", thirdAccountInfo);
        if (StrUtil.isNotBlank(thirdAccountInfo) && MiduUtil.isValidFormat(thirdAccountInfo)) {
            String[] thirdAccountArr = thirdAccountInfo.split(",");
            for (String ta : thirdAccountArr) {
                String[] arr = ta.split(":");
                String tmp = arr[2];
                if (arr[0].contains("midu") && arr[1].contains("zyb")) {
                    appid = MiduUtil.getAppId(tmp);
                    appSecret = MiduUtil.getAppSecret(tmp);
                    systemId = MiduUtil.getSystemId(tmp);
                    channel = MiduUtil.getChannel(tmp);
                    String usernameTmp = MiduUtil.getUsername(tmp);
                    username =  StrUtil.isBlank(usernameTmp) ? appid : usernameTmp;
                }
            }
        }
        if (StrUtil.hasBlank(appid, appSecret, systemId, username, channel)) {
            return "redirect:https://yuqingtong.dahe.cn";
        }
        return "redirect:"+ Oauth2Util_Midu_Zhuanye2.getSecurityUrl(appid, appSecret, systemId, username, channel);
    }

    /**
     * //TODO 设置智能版链接，暂时写死，后续要改成一对一
     * @param user
     * @return
     */
    @ApiIgnore
    @LogAction(action = "访问智能版")
    @RequestMapping(path = "/go/znb")
    public String goZnb(@ApiIgnore @CurrentUser User user) {
        if (user == null) {
            throw new SsoException("用户未登录");
        }
        //测试账号
        String appid = "";
        String appSecret = "";
        String username = "";
        String systemId = "";
        String channel = "";
        //动态绑定账号
        String thirdAccountInfo = user.getThirdAccountInfo();
        thirdAccountInfo = URLUtil.decode(Base64.decodeStr(thirdAccountInfo));
        log.info("++++++++++++++++/go/znb++++++++++++++++++{}", thirdAccountInfo);
        if (StrUtil.isNotBlank(thirdAccountInfo) && MiduUtil.isValidFormat(thirdAccountInfo)) {
            String[] thirdAccountArr = thirdAccountInfo.split(",");
            for (String ta : thirdAccountArr) {
                String[] arr = ta.split(":");
                String tmp = arr[2];
                if (arr[0].contains("midu") && arr[1].contains("znb")) {
                    appid = MiduUtil.getAppId(tmp);
                    appSecret = MiduUtil.getAppSecret(tmp);
                    systemId = MiduUtil.getSystemId(tmp);
                    channel = MiduUtil.getChannel(tmp);
                    String usernameTmp = MiduUtil.getUsername(tmp);
                    username =  StrUtil.isBlank(usernameTmp) ? appid : usernameTmp;
                }
            }
        }
        if (StrUtil.hasBlank(appid, appSecret, systemId, username, channel)) {
            return "redirect:https://yuqingtong.dahe.cn";
        }
        return "redirect:"+ Oauth2Util_Midu_Zhineng2.getSecurityUrl(appid, appSecret, systemId, username, channel);
    }

    /**
     * //TODO 设置五星版链接，暂时写死，后续要改成一对一
     * @param user
     * @return
     */
    @ApiIgnore
    @LogAction(action = "访问五星版")
    @RequestMapping(path = "/go/wxb")
    public String goWxb(@ApiIgnore @CurrentUser User user) {
        if (user == null) {
            throw new SsoException("用户未登录");
        }
        String thirdAccountInfo = user.getThirdAccountInfo();
        thirdAccountInfo = URLUtil.decode(Base64.decodeStr(thirdAccountInfo));
        log.info("++++++++++++++++/go/wxb++++++++++++++++++{}", thirdAccountInfo);
        if (StrUtil.isNotBlank(thirdAccountInfo) && QingboUtil.isValidFormat(thirdAccountInfo)) {
            String[] thirdAccountArr = thirdAccountInfo.split(",");
            for (String ta : thirdAccountArr) {
                String[] arr = ta.split(":");
                if (arr[0].contains("qingbo") && arr[1].contains("wxb")) {
                    String username = arr[2];
                    String password = arr[3];
                    String body = HttpUtil.createPost("https://wxvip.dahe.cn/api/user/login/login")
                            .form("data[username]", username)
                            .form("data[password]", password)
                            .execute().body();
                    JSONObject jsonObject = JSONObject.parseObject(body);
                    if (jsonObject.getIntValue("code") == 10000) {
                        String token = jsonObject.getString("data");
                        return "redirect:https://wxvip.dahe.cn/api/site/auto?token="+token+"&access_type=blank";
                    }
                }
            }
        }
        return "redirect:https://yuqingtong.dahe.cn";
    }

    @ApiIgnore
    @LogAction(action = "前台退出登录")
    @RequestMapping(path = "/logout")
    @RestResponseBody
    public boolean logout() {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        return true;
    }

    @SneakyThrows
    @RequestMapping(name = "获取用户秘钥", value = "fp/token")
    @RestResponseBody
    public JsonResult getFpUserToken(@RequestParam(required = false, defaultValue = "") String q, @CurrentUser User user) {
        log.info("获取用户秘钥----------------------请求");
        String s = SecurityUtil.encryptFPMd5(user.getPhone());
        URIBuilder builder = new URIBuilder("https://fp.dahe.cn");
        builder.setParameter("q", q);
        builder.setParameter("mobile", user.getPhone());
        builder.setParameter("token", s);
        URI build = builder.build();
        return ResultUtil.success(build.toString() + "/#/auth");
    }

}
