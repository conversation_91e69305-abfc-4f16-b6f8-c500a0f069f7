package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.LogOperation;
import cn.dahe.cas.auth.domain.UserLog;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.entity.UserRole;
import cn.dahe.cas.auth.event.RolePermissionEvent;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.UserLogService;
import cn.dahe.cas.auth.service.UserRoleService;
import cn.dahe.cas.auth.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * on 2018/4/28.
 * 给用户添加角色
 */
@Api(tags = "给用户分配角色")
@Controller
@RequestMapping("/admin/userrole")
@RequiresPermissions("user:manager")
public class UserRoleController {
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private UserService userService;
    @Autowired
    private ApplicationEventPublisher publisher;
    @Autowired
    private UserLogService userLogService;

    @ApiOperation("给用户分配角色接口")
    @LogAction(action = "给用户分配角色")
    @RequestMapping(path = "/edit_user_role",method = RequestMethod.POST)
    @RestResponseBody
    public boolean add(int userId, @RequestParam(name = "roleIds", required = false) int[] roleIds,
                       @ApiIgnore @CurrentUser User currentUser){
        User user = userService.get(userId);
        if (user == null){
            throw new SsoException("该用户不存在");
        }
        List<UserRole> userRoles = userRoleService.findByUid(userId);
        List<Integer> oldRoleIds = new ArrayList<>();
        if (userRoles.size() > 0) {
            oldRoleIds = userRoles.stream().map(userRole -> userRole.getRoleId()).collect(Collectors.toList());
            userRoleService.deleteByUid(userId);
        }
        userRoleService.addRole(userId, roleIds);
        // todo 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(currentUser.getUid())
                .operateUserName(currentUser.getTruename()).userId(userId).userName(user.getUsername())
                .oldParam(oldRoleIds).newParam(roleIds).operateType(LogOperation.USER_ROLE).build();
        build.setOperateDescription("[角色:" + oldRoleIds.toString() + "->"+ Arrays.toString(roleIds) + "]");
        userLogService.add(build);
        publisher.publishEvent(new RolePermissionEvent(String.valueOf(userId)));
        return true;
    }


}
