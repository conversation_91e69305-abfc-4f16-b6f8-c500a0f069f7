package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.*;
import cn.dahe.cas.auth.constants.ErrorCode;
import cn.dahe.cas.auth.constants.SmsType;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.dto.UserCanAccessSite;
import cn.dahe.cas.auth.entity.RemoteUserZn;
import cn.dahe.cas.auth.entity.RemoteUserZy;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.SmsService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.service.VerfiyCodeService;
import cn.dahe.cas.auth.util.*;
import cn.hutool.json.JSONUtil;
import com.octo.captcha.service.CaptchaServiceException;
import com.octo.captcha.service.image.ImageCaptchaService;
import org.hibernate.validator.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import static cn.dahe.cas.auth.constants.UserFlag.FORBID;

/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
@RequestMapping("/dahe")
@Validated
public class DaheController {

    private static final Logger log = LoggerFactory.getLogger(DaheController.class);

    private static final String LOGIN_SMSCODE_PREFIX = "gd:ssoqdsms_code";
    private static final int SMS_LIMIT_TIME = 5;

    @Autowired
    private VerfiyCodeService verfiyCodeService;

    @Autowired
    private ImageCaptchaService captchaService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private UserService userService;

    @Resource(name = "tokenRedisTemplate")
    private ValueOperations<String, String> operations;

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Value("${cas.server}")
    private String casServer;

    @RequestMapping("/pub/getVerifyCodeImage")
    public void getVerfiyCode(HttpServletResponse response, HttpSession session, OutputStream outputStream) {
        ByteArrayOutputStream jpegOut = new ByteArrayOutputStream();
        String captchaId = session.getId();
        BufferedImage image = captchaService.getImageChallengeForID(captchaId);
        response.setContentType("image/jpeg");
        try {
            ImageIO.write(image, "jpeg", jpegOut);
            outputStream.write(jpegOut.toByteArray());
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @AccessLimit(time = 60 * 5, limitTime = 60 * 15, count = 15, msg = "短信发送过快",limitParams = {"phone"}, title = "sms")
    @LogAction(action = "发送短信验证码")
    @RequestMapping(path = "/common/getsms")
    @ResponseBody
    public JsonResult getSms(@NotBlank(message = "手机号不能为空") @PhoneNumber(message = "手机号格式有误") String phone,
                             @TokenParameters String token, String type,@CurrentUser(allowNull = true) User user,
                             HttpServletRequest request) throws Exception {
        CommonUtil.check(request);
        SmsType smsType = SmsType.getType(type);
        if(smsType==null){
            throw new SsoException("短信类型错误");
        }
        // todo 加密
        String encodePhone = Sm4Util.encryptEcb(sm4Key, phone);
        switch (smsType){
            //只有登录时 判断手机号是否注册 用户存在才发送验证码
            case LOGIN:
                User fingUser = userService.getUserByPhone(encodePhone);
                if (fingUser == null){
                    throw new SsoException(ErrorCode.USER_NOT_EXIST);
                }
                if (FORBID.equals(fingUser.getFlag())){
                    throw new SsoException(ErrorCode.PHONE_DENY);
                }
                break;
            //注册和修改操作时 判断手机号是否已注册 用户不存在才发送验证码,判断用户是否已登录
            case UPDATE:
                if(user==null){
                    log.error("更新手机号{}时未登录，可能存在恶意调用接口情形",phone);
                    throw new SsoException(ErrorCode.SMS_SEND_FAIL);
                }
                if (userService.existPhone(encodePhone)){
                    throw new SsoException(ErrorCode.PHONE_REGISTERED);
                }
                break;
            case WX_BIND:
                User findUser = userService.getUserByPhone(encodePhone);
                if (findUser == null){
                    throw new SsoException(ErrorCode.USER_NOT_EXIST);
                }
                if (FORBID.equals(findUser.getFlag())){
                    throw new SsoException(ErrorCode.PHONE_DENY);
                }
                break;
            default:
                break;
        }
        //判断验证码是否已发送，15分钟有效期，若还未失效，不再请求接口
        boolean valid = verfiyCodeService.isValid(phone, smsType);
        if (valid){
            throw new SsoException(ErrorCode.CODE_SENT);
        }
        return smsService.sendSms(phone, token, type);
    }


    /**
     * 检验登录手机号验证码否匹配
     */
    @AccessLimit(time = 60 * 5, limitTime = 60 * 15, count = 15, msg = "登录过于频繁")
    @RequestMapping("/login/matchPhone")
    @ResponseBody
    public JsonResult loginMatchPhone(@PhoneParam String phone,
                                   @NotBlank(message = "短信验证码不能为空")String smsCode,
                                   @NotBlank(message = "图形验证码不能为空")String imgCode,
                                   HttpSession session) throws Exception {
        User user = userService.getUserByPhone(Sm4Util.encryptEcb(sm4Key, phone));
        if (user == null) {
            return ResultUtil.fail("用户名不存在");
        }
        //图形验证码检查
        checkValidCode(session.getId(),imgCode);
        if (!ResourceUtil.isRelease() && "6666".equals(smsCode)) {
            return ResultUtil.success(casServer+"/login?service="+casServer+"/callback?client_name=CasClient&token="+JJWT.generateByUserId(user.getUid()+""));
        }
        String loginSmsCodeKey = LOGIN_SMSCODE_PREFIX+phone;
        if (!operations.getOperations().hasKey(loginSmsCodeKey)) {
            return ResultUtil.fail("该手机号未发送短信");
        }
        if (!operations.get(loginSmsCodeKey).equals(smsCode)) {
            return ResultUtil.fail("短信验证码错误");
        }
        return ResultUtil.success(casServer+"/login?service="+casServer+"/callback?client_name=CasClient&token="+JJWT.generateByUserId(user.getUid()+""));
    }

    /**
     * 检验登录手机号验证码否匹配
     */
    @AccessLimit(time = 60 * 5, limitTime = 60 * 15, count = 15,  msg = "登录过于频繁")
    @RequestMapping("/user/login/matchPhone")
    @ResponseBody
    public JsonResult userLoginMatchPhone(@PhoneParam String phone,
                                      @NotBlank(message = "短信验证码不能为空")String smsCode,
                                      @NotBlank(message = "图形验证码不能为空")String imgCode,
                                      @NotBlank(message = "版本")int version,
                                      HttpSession session) throws Exception {
        User user = userService.getUserByPhone(Sm4Util.encryptEcb(sm4Key, phone));
        if (user == null) {
            return ResultUtil.fail("用户名不存在");
        }
        //图形验证码检查
        checkValidCode(session.getId(),imgCode);
        operations.set("version:"+phone,String.valueOf(version));
        if (!ResourceUtil.isRelease() && "6666".equals(smsCode)) {
            return ResultUtil.success(casServer+"/login?service="+casServer+"/callback?client_name=CasClient&token="+JJWT.generateByUserId(user.getUid()+""));
        }
        String loginSmsCodeKey = LOGIN_SMSCODE_PREFIX+phone;
        if (!operations.getOperations().hasKey(loginSmsCodeKey)) {
            return ResultUtil.fail("该手机号未发送短信");
        }
        if (!operations.get(loginSmsCodeKey).equals(smsCode)) {
            return ResultUtil.fail("短信验证码错误");
        }
        return ResultUtil.success(casServer+"/login?service="+casServer+"/callback?client_name=CasClient&token="+JJWT.generateByUserId(user.getUid()+""));
    }

    private void checkValidCode(String captchaId,String imgCode){
        try {
            if (!captchaService.validateResponseForID(captchaId,imgCode)) {
                throw new SsoException("图形验证码错误");
            }
        }catch (CaptchaServiceException e){
            log.error("图形验证码校验错误{}",e.getMessage());
            throw new SsoException("图形验证码错误");
        }
    }
}
