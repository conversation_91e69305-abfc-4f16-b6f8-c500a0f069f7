package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.entity.FocusPerson;
import cn.dahe.cas.auth.entity.ServiceConsult;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.search.SearchFocus;
import cn.dahe.cas.auth.service.CasService;
import cn.dahe.cas.auth.service.FocusPersonService;
import cn.dahe.cas.auth.service.ServiceConsultService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jasig.cas.services.RegisteredService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Date;


@ApiIgnore
@Controller
@RequestMapping("/admin/consult")
@Data
public class ServiceConsultController {


    private final ServiceConsultService serviceConsultService;


    @ApiOperation("服务咨询列表接口（分页）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
    })
    @RequestMapping(path = "/page", method = RequestMethod.POST)
    @RestResponseBody
    public Page<ServiceConsult> pageRoles(Pageable pageable, SearchFocus searchFocus, @CurrentUser User user){
        return serviceConsultService.getPage(pageable, searchFocus,user);
    }

    @ApiOperation("添加服务咨询")
    @LogAction(action = "添加服务咨询")
    @RestResponseBody
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    public boolean add(int type, @CurrentUser User user) {
        return serviceConsultService.addServiceConsult(type,user);
    }
}
