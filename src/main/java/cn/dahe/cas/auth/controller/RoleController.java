package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.RolePermissionType;
import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.dto.ResourceDto;
import cn.dahe.cas.auth.dto.SiteRoleDto;
import cn.dahe.cas.auth.dto.UserProfileDto;
import cn.dahe.cas.auth.entity.*;
import cn.dahe.cas.auth.event.RolePermissionEvent;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.search.SearchRole;
import cn.dahe.cas.auth.search.SearchUser;
import cn.dahe.cas.auth.service.*;
import cn.dahe.cas.auth.util.CommonUtil;
import cn.dahe.cas.auth.util.SsoStringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "角色管理各种接口")
@Controller
@RequestMapping(path = "/admin/role")
@Validated
@RequiresPermissions("role:manager")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @Autowired
    private RoleResourceService roleResourceService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private ConversionService conversionService;

    @ApiOperation("角色列表接口（分页）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
    })
    @RequestMapping(path = "/page", method = RequestMethod.POST)
    @RestResponseBody
    public Page<Role> pageRoles(Pageable pageable, SearchRole searchRole){
        return roleService.getPage(pageable, searchRole);
    }

    @ApiOperation("角色列表(不分页),要传入站点id")
    @ApiImplicitParam(name = "siteId", value = "siteId", required = true, dataType = "int")
    @RestResponseBody
    @RequestMapping(path = "/list", method = RequestMethod.POST)
    public List<Role> listRole(@NotNull(message = "请传系统id") Integer siteId) {
        Site site = siteService.get(siteId);
        if (site == null){
            throw new SsoException("该站点不存在");
        }
        return roleService.findBySiteId(siteId);
    }

    @ApiOperation("给角色分配权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "resIds", value = "权限id，用数组形式传入后台"),
            @ApiImplicitParam(name = "roleId", value = "角色id")
    })
    @LogAction(action = "给角色分配权限")
    @RestResponseBody
    @RequestMapping(path = "/grant", method = RequestMethod.POST)
    public boolean addRoleResource(@NotNull(message = "请传角色id") Integer roleId, int[] resIds) {
        if (resIds == null || resIds.length == 0){
            throw new SsoException("请选择要分配的权限");
        }
        Role role = roleService.get(roleId);
        if (role == null){
            throw new SsoException("该角色不存在");
        }
        List<RoleResource> roleResources = roleResourceService.getByRoleId(roleId);
        if (roleResources != null && !roleResources.isEmpty()){
            // 此时操作为编辑，先删除后添加
            roleResourceService.deleteByRoleId(roleId);
        }
        for (int resId : resIds) {
            RoleResource roleAuthority = new RoleResource();
            roleAuthority.setRoleId(roleId);
            roleAuthority.setResId(resId);
            roleResourceService.add(roleAuthority);
        }
        publisher.publishEvent(new RolePermissionEvent(role.getSiteId(), RolePermissionType.PERMISSION));
        return true;
    }

    @ApiOperation("添加角色")
    @LogAction(action = "添加角色")
    @RestResponseBody
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    public boolean add(@Valid Role role, @CurrentUser User user) {
        roleService.addRole(role, user);
        return true;
    }

    @ApiOperation("编辑角色")
    @LogAction(action = "编辑角色")
    @RestResponseBody
    @RequestMapping(path = "/edit", method = RequestMethod.POST)
    public boolean edit(@Valid Role role, @ApiIgnore @CurrentUser User user) {
        Role old = roleService.get(role.getId());
        if (old == null){
            throw new SsoException("该角色不存在");
        }
        old.setName(SsoStringUtil.delSpace(role.getName()));
        old.setModifyUserId(user.getUid());
        roleService.update(old);
        // todo 整理更改
        publisher.publishEvent(new RolePermissionEvent(old.getSiteId(), RolePermissionType.PERMISSION));
        return true;
    }

    @ApiOperation(value = "更改角色状态")
    @ApiImplicitParam(name = "id", value = "接口后/加id", required = true, dataType = "int")
    @LogAction(action = "更改角色状态")
    @RestResponseBody
    @RequestMapping(path = "/status", method = RequestMethod.POST)
    public boolean updateStatus(@NotNull(message = "请传角色id") Integer id, @ApiIgnore @CurrentUser User user) {
        Role one = roleService.get(id);
        if (one == null) {
            throw new SsoException("角色不存在");
        }
        if (one.getStatus() == Status.DENY) {
            one.setStatus(Status.ALLOW);
        } else if (one.getStatus() == Status.ALLOW) {
            one.setStatus(Status.DENY);
        }
        one.setModifyUserId(user.getUid());
        roleService.update(one);
        // todo 带更改
        publisher.publishEvent(new RolePermissionEvent(one.getSiteId(), RolePermissionType.PERMISSION));
        return true;
    }

    @RestResponseBody
    @RequestMapping(path = "/list-user",method = RequestMethod.POST)
    @ApiOperation(notes = "角色下用户列表，带分页", value = "角色下用户列表，带分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
            @ApiImplicitParam(name = "truename", value = "登录账号或者用户姓名", dataType = "String"),
            @ApiImplicitParam(name = "roleId", value = "当前角色id", required = true, dataType = "int"),
    })
    public Page<UserProfileDto> listUser(Pageable pageable, SearchUser searchUser) {
        if(CommonUtil.isPhoneNumber(searchUser.getTruename())){
            searchUser.setPhone(searchUser.getTruename());
            searchUser.setTruename("");
        }else{
            searchUser.setTruename(searchUser.getTruename());
        }
        Page<User> users = userService.findUserByRoleId(pageable, searchUser);
        return users.map(user -> conversionService.convert(user,UserProfileDto.class));
    }

    @ApiOperation(value = "列出用户可使用的所有站点/系统以及角色")
    @ApiImplicitParam(name = "userId", value = "用户id", required = true)
    @RestResponseBody
    @RequestMapping(path = "/list-site-role",method = RequestMethod.POST)
    public List<SiteRoleDto> listSiteRole(@NotNull(message = "请传用户id") Integer userId){
        List<SiteRoleDto> siteRoleDtos = new ArrayList<>();
        List<Site> accessSite = siteService.getAccessSite(userId);
        boolean admin = SecurityUtils.getSubject().hasRole("admin");
        accessSite = accessSite.stream().filter(new Predicate<Site>() {
            @Override
            public boolean test(Site site) {
                if("local".equals(site.getName())){
                    return admin;
                }
                return true;
            }
        }).collect(Collectors.toList());
        //对于非超管角色，直接屏蔽用户中心角色
        for (Site site : accessSite) {
            SiteRoleDto siteRoleDto = new SiteRoleDto();
            siteRoleDto.setId(site.getId());
            siteRoleDto.setName(site.getName());
            siteRoleDto.setDescription(site.getDescription());
            // 根据可用的站点id查找角色
            List<ResourceDto> resourceDtos = new ArrayList<>();
            List<Role> bySiteId = roleService.findBySiteId(site.getId());
            for (Role role : bySiteId) {
                ResourceDto resourceDto = new ResourceDto();
                resourceDto.setId(role.getId());
                resourceDto.setName(role.getName());
                UserRole one = userRoleService.getOne(userId, role.getId());
                resourceDto.setChecked(one == null ? 0 : 1);
                resourceDtos.add(resourceDto);
            }
            siteRoleDto.setRoleList(resourceDtos);
            siteRoleDtos.add(siteRoleDto);
        }

        return siteRoleDtos;
    }
}
