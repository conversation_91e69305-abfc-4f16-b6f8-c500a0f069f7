package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.AccessLimit;
import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.SmsType;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.dto.TgcInfo;
import cn.dahe.cas.auth.dto.UserInfo;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.realm.SmsRealm;
import cn.dahe.cas.auth.service.*;
import cn.dahe.cas.auth.util.Sm4Util;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.octo.captcha.service.CaptchaServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.mgt.DefaultSecurityManager;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.jasig.cas.web.support.CookieRetrievingCookieGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/login2")
public class Login2Controller {

    private static final Logger _logger = LoggerFactory.getLogger(Login2Controller.class);

    @Autowired
    private CookieRetrievingCookieGenerator ticketGrantingTicketCookieGenerator;

    @Autowired
    private SsoService ssoService;

    @Autowired
    private UserService userService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private DefaultWebSessionManager defaultWebSessionManager;

    @Autowired
    private DefaultSecurityManager securityManager;

    @Autowired
    private CasService casService;

    @Autowired
    private VerfiyCodeService verfiyCodeService;

    @Autowired
    private com.octo.captcha.service.image.ImageCaptchaService captchaService;

    @Value("${cas.server}")
    private String casServer;

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Value("${test_env}")
    private boolean testEnv;

    /**
     * for common login21
     * @param username
     * @param password
     * @param imageCode
     * @param request
     * @param response
     * @return
     */
    @AccessLimit(time = 60 * 5, limitTime = 60 * 15, count = 15,  msg = "短信验证码登录-过快",limitParams = {"username"}, title = "login2-pwd")
    @LogAction(action = "短信验证码登录")
    @RequestMapping(value = "",method = RequestMethod.POST)
    @RestResponseBody
    public boolean login2(String username, String password, String imageCode,
                          HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.isEmpty(username)) {
            throw new SsoException("手机号不能为空");
        }
        if (!Validator.isMobile(username)) {
            throw new SsoException("手机号格式不正确");
        }
        if (StringUtils.isEmpty(password)) {
            throw new SsoException("短信验证码不能为空");
        }
        if (StringUtils.isEmpty(imageCode)) {
            throw new SsoException("图形验证码不能为空");
        }
        //校验图形验证码
        try {
            String sessionId = request.getSession().getId();
            if (!captchaService.validateResponseForID(sessionId, imageCode)) {
                throw new SsoException("图形验证码错误");
            }
        } catch (CaptchaServiceException e) {
            log.error("图形验证码校验错误: {}", e.getMessage());
            throw new SsoException("图形验证码错误");
        }
        //校验短信验证码
        boolean result = verfiyCodeService.isValid(username, password, SmsType.LOGIN);
        if (testEnv && "6666".equals(password)) {
            result = true;
        } else if (!testEnv && !result) {
            throw new SsoException("短信验证码错误");
        }
        //检验用户存在性
        if (result && StrUtil.isAllNotBlank(username,password)) {
            MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
            requestBody.add("username", username);
            requestBody.add("password", password);
            requestBody.add("service", casServer);
            if (StrUtil.isNotBlank(username)) {
                String encryptUsername ="";
                try {
                    encryptUsername = Sm4Util.encryptEcb(sm4Key, username);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                User user = userService.getUserByPhone(encryptUsername);
                if (user == null) {
                    throw new SsoException("用户不存在!");
                }
                //判断用户有效期
                Date now = new Date();
                log.info("-------------->用户：{}，登录的当前时间：{}",user.getUsername(), DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
                if(user.getStartTime() != null && user.getEndTime() != null && (now.getTime() < user.getStartTime().getTime() || now.getTime() > user.getEndTime().getTime())){
                    throw new SsoException("账号已过期，请及时续费！");
                }
                try {
                    ssoService.login(request,response,requestBody);
                } catch (Exception e) {
                    throw new SsoException("系统异常!");
                }
                return true;
            }
        }
        return false;
    }

    @RequestMapping("/logout")
    @RestResponseBody
    public boolean logout(HttpServletRequest request, HttpServletResponse response){
        String tgt = ticketGrantingTicketCookieGenerator.retrieveCookieValue(request);
        Map<String,Object> attrs = ssoService.getUserInfo(tgt);
        Object o = attrs.get("uid");
        int uid = Integer.parseInt(o.toString());
        if(o == null){
            throw new SsoException("当前用户不存在");
        }
        casService.logoutUser(uid);


        PrincipalCollection principals = new SimplePrincipalCollection(uid, SmsRealm.NAME);
        Subject subject = new Subject.Builder(securityManager).principals(principals).buildSubject();

        SessionDAO sessionDAO = defaultWebSessionManager.getSessionDAO();
        sessionDAO.delete(subject.getSession());

        clearCookie(request, response, "TGC");
        clearCookie(request, response, "sid");
        return true;
    }

    private void clearCookie(HttpServletRequest request, HttpServletResponse response, String cookieName) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(cookieName)) {
                    cookie.setPath("/");
                    cookie.setSecure(true); // 仅在 HTTPS 下发送 Cookie
                    // 设置 Cookie 过期时间为一个过去的时间
                    cookie.setMaxAge(0);
                    // 通知浏览器删除 Cookie
                    response.addCookie(cookie);
                    break;
                }
            }
        }
    }

    @RequestMapping(method = RequestMethod.GET)
    public String login2(){
        return "default/ui/casLoginView2";
    }

}
