package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.dto.SiteDto;
import cn.dahe.cas.auth.dto.SiteTreeDto;
import cn.dahe.cas.auth.service.SiteTreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * on 2018/1/30.
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/tree")
public class SiteTreeController {
    @Autowired
    private SiteTreeService siteTreeService;

    @RestResponseBody
    @RequestMapping(path = "/gettree")
    public List<SiteTreeDto> getTree(){
        //return siteTreeService.getallSite();
        List<SiteTreeDto> list = new ArrayList<>();
        SiteTreeDto siteTreeDto = new SiteTreeDto();
        siteTreeDto.setId("10000");
        siteTreeDto.setName("豫情通管理平台");
        list.add(siteTreeDto);
        return list;
    }

    @RestResponseBody
    @RequestMapping(path = "/getdepartment")
    public List<SiteTreeDto> getDepartment(String sid){
        //return siteTreeService.getDepartment(sid);
        List<SiteTreeDto> list = new ArrayList<>();
        SiteTreeDto siteTreeDto = new SiteTreeDto();
        siteTreeDto.setId("1");
        siteTreeDto.setName("机关党委");
        siteTreeDto.setPid("0");
        list.add(siteTreeDto);
        siteTreeDto = new SiteTreeDto();
        siteTreeDto.setId("2");
        siteTreeDto.setName("集团办公室");
        siteTreeDto.setPid("0");
        list.add(siteTreeDto);
        siteTreeDto = new SiteTreeDto();
        siteTreeDto.setId("3");
        siteTreeDto.setName("人力资源部");
        siteTreeDto.setPid("0");
        list.add(siteTreeDto);
        siteTreeDto = new SiteTreeDto();
        siteTreeDto.setId("4");
        siteTreeDto.setName("计划财务部");
        siteTreeDto.setPid("0");
        list.add(siteTreeDto);
        siteTreeDto = new SiteTreeDto();
        siteTreeDto.setId("4");
        siteTreeDto.setName("经营管理部");
        siteTreeDto.setPid("0");
        list.add(siteTreeDto);
        return list;
    }


    @RestResponseBody
    @RequestMapping(path = "/getonesite")
    public SiteDto getOneSite(String sid){
        return siteTreeService.getSite(sid);
    }

    @RestResponseBody
    @RequestMapping(path = "/getonedepart")
    public SiteTreeDto getOneDepartment(int deptId){
        return siteTreeService.getOneDepart(deptId+"");
    }

}
