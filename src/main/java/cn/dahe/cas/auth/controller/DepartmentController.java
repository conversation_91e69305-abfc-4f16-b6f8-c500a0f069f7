package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.dto.TreeNode;
import cn.dahe.cas.auth.entity.Department;
import cn.dahe.cas.auth.service.DepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * Date: 20250707
 * @author: zgf
 */
@Api(tags = "组织机构模块", value = "组织机构模块")
@Controller
@RequestMapping("/admin/department")
@RequiresPermissions("user:manager")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 添加组织机构
     */
    @PostMapping("/add")
    @ResponseBody
    @ApiOperation(value = "添加组织机构", notes = "添加组织机构", httpMethod = "POST")
    public JsonResult addDepartment(Department department) {
        try {
            // 参数验证
            if (!StringUtils.hasText(department.getName())) {
                return ResultUtil.fail("组织机构名称不能为空");
            }
            // 检查父部门是否存在
            if (department.getPid() > 0) {
                Department parentDept = departmentService.get(department.getPid());
                if (parentDept == null || parentDept.getStatus() != 1) {
                    return ResultUtil.fail("父部门不存在或已被删除");
                }
            }
            // 检查名称是否重复（同一父级下）
            if (departmentService.existsByNameAndPid(department.getName(), department.getPid())) {
                return ResultUtil.fail("同一父级下已存在相同名称的组织机构");
            }
            // 设置创建时间
            department.setCreateDate(new Date());
            department.setStatus(1); // 默认状态为正常
            departmentService.add(department);
            return ResultUtil.success("添加组织机构成功");
        } catch (Exception e) {
            return ResultUtil.fail("添加组织机构失败：" + e.getMessage());
        }
    }

    /**
     * 编辑组织机构
     */
    @PostMapping("/update")
    @ResponseBody
    @ApiOperation(value = "编辑组织机构", notes = "编辑组织机构", httpMethod = "POST")
    public JsonResult updateDepartment(Department department) {
        try {
            // 参数验证
            if (department.getId() <= 0) {
                return ResultUtil.fail("组织机构ID不能为空");
            }
            if (!StringUtils.hasText(department.getName())) {
                return ResultUtil.fail("组织机构名称不能为空");
            }
            // 检查部门是否存在
            Department existingDepartment = departmentService.get(department.getId());
            if (existingDepartment == null) {
                return ResultUtil.fail("组织机构不存在");
            }
            // 检查父部门是否存在
            if (department.getPid() > 0) {
                Department parentDept = departmentService.get(department.getPid());
                if (parentDept == null || parentDept.getStatus() != 1) {
                    return ResultUtil.fail("父部门不存在或已被删除");
                }
                // 检查是否会形成循环引用
                if (departmentService.wouldCreateCircularReference(department.getId(), department.getPid())) {
                    return ResultUtil.fail("不能将部门设置为自己的子部门");
                }
            }
            // 检查名称是否重复（同一父级下，排除自己）
            if (departmentService.existsByNameAndPidAndIdNot(department.getName(), department.getPid(), department.getId())) {
                return ResultUtil.fail("同一父级下已存在相同名称的组织机构");
            }
            // 保留原有的创建时间
            department.setCreateDate(existingDepartment.getCreateDate());

            departmentService.update(department);
            return ResultUtil.success("编辑组织机构成功");

        } catch (Exception e) {
            return ResultUtil.fail("编辑组织机构失败：" + e.getMessage());
        }
    }

    /**
     * 组织机构删除
     */
    @PostMapping(value = "/update-status")
    @ResponseBody
    @ApiOperation(value = "组织机构删除", notes = "post请求")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "组织机构ID", required = true, dataType = "int"),
            @ApiImplicitParam(name = "status", value = "-1(删除)", required = true, dataType = "int")
    })
    public JsonResult disabledDepartment(int id, int status) {
        try {
            if (id <= 0) {
                return ResultUtil.fail("组织机构ID不能为空");
            }
            Department department = departmentService.get(id);
            if (department == null) {
                return ResultUtil.fail("组织机构不存在");
            }
            // 检查是否有子部门
            if (status == -1 && departmentService.hasChildren(id)) {
                return ResultUtil.fail("该组织机构下还有子部门，不能删除");
            }
            department.setStatus(status);
            departmentService.update(department);
            String message = status == -1 ? "删除组织机构成功" : "恢复组织机构成功";
            return ResultUtil.success(message);
        } catch (Exception e) {
            return ResultUtil.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 组织机构拖拽排序
     */
    @PostMapping(value = "/update-seq")
    @ResponseBody
    @ApiOperation(value = "组织机构拖拽排序", notes = "组织机构拖拽排序", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "newIds", value = "新的组织机构的ids", required = true, dataType = "String"),
            @ApiImplicitParam(name = "seqIds", value = "旧的组织机构排序用ids", required = true, dataType = "String")
    })
    public JsonResult updateSeq(String newIds, String seqIds) {
        try {
            if (!StringUtils.hasText(newIds)) {
                return ResultUtil.fail("新的组织机构ID列表不能为空");
            }
            boolean success = departmentService.updateSeq(newIds, seqIds);
            if (success) {
                return ResultUtil.success("排序更新成功");
            } else {
                return ResultUtil.fail("排序更新失败");
            }

        } catch (Exception e) {
            return ResultUtil.fail("排序更新失败：" + e.getMessage());
        }
    }

    /**
     * 组织机构列表
     */
    @PostMapping("/list")
    @ResponseBody
    @ApiOperation(value = "查询组织机构列表(分页)", notes = "根据组织机构名字和分页参数查询组织机构列表", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNumber", value = "当前页数", required = true, dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "分页长度", required = true, dataType = "int"),
            @ApiImplicitParam(name = "keywords", value = "名字", dataType = "String"),
            @ApiImplicitParam(name = "pid", value = "父部门ID", dataType = "int")
    })
    public JsonResult list(@RequestParam(defaultValue = "0") int pageNumber,
                           @RequestParam(defaultValue = "10") int pageSize,
                           @RequestParam(required = false) String keywords,
                           @RequestParam(required = false, defaultValue = "0") int pid) {
        try {
            // 参数验证
            if (pageNumber < 0) {
                pageNumber = 0;
            }
            if (pageSize <= 0) {
                pageSize = 10;
            }
            // 构建分页对象
            Pageable pageable = new PageRequest(pageNumber, pageSize,
                    new Sort(Sort.Direction.ASC, "seq", "id"));
            Page<Department> departmentPage = departmentService.findByConditions(keywords, pid, pageable);
            return ResultUtil.success(departmentPage);
        } catch (Exception e) {
            return ResultUtil.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取组织机构列表结构
     */
    @PostMapping("/list-all")
    @ResponseBody
    public JsonResult listAll() {
        try {
            List<Department> departmentTree = departmentService.listAll();
            return ResultUtil.success(departmentTree);
        } catch (Exception e) {
            return ResultUtil.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取组织机构列表
     */
    @PostMapping("/list-son")
    @ResponseBody
    @ApiOperation(value = "获取组织机构列表", notes = "获取组织机构列表", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pid", value = "父部门ID", dataType = "int")
    })
    public JsonResult listDepartment(@RequestParam(required = false, defaultValue = "0") int pid) {
        try {
            List<Department> departmentList = departmentService.findByPidAndStatus(pid, 1);
            return ResultUtil.success(departmentList);
        } catch (Exception e) {
            return ResultUtil.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取组织机构树形结构
     */
    @PostMapping("/tree")
    @ResponseBody
    @ApiOperation(value = "获取组织机构树形结构", notes = "获取组织机构树形结构", httpMethod = "POST")
    public JsonResult getDepartmentTree() {
        try {
            List<TreeNode> departmentTree = departmentService.getDepartmentTree();
            return ResultUtil.success(departmentTree);
        } catch (Exception e) {
            return ResultUtil.fail("查询失败：" + e.getMessage());
        }
    }


    /**
     * 获取组织机构详情
     */
    @PostMapping("/detail")
    @ResponseBody
    @ApiOperation(value = "获取组织机构详情", notes = "根据ID获取组织机构详情", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "组织机构ID", required = true, dataType = "int")
    })
    public JsonResult detail(int id) {
        try {
            if (id <= 0) {
                return ResultUtil.fail("组织机构ID不能为空");
            }
            Department department = departmentService.get(id);
            if (department == null) {
                return ResultUtil.fail("组织机构不存在");
            }
            return ResultUtil.success("查询成功");
        } catch (Exception e) {
            return ResultUtil.fail("查询失败：" + e.getMessage());
        }
    }
}