package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.UserVipDurationStatus;
import cn.dahe.cas.auth.dto.PayDto;
import cn.dahe.cas.auth.entity.*;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.*;
import io.swagger.annotations.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@Api(tags = "客户管理")
@Controller
@RequestMapping("/admin/vip")
//@RequiresPermissions("user:vip")
public class VipController {

    @Autowired
    private ConversionService conversionService;

    @Autowired
    private UserService userService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private UserVipPayRecordService userVipPayRecordService;

    @Autowired
    private UserVipDurationService userVipDurationService;

    @ApiOperation("所有站点列表展示接口")
    @RequestMapping(path = "/list_site",method = RequestMethod.POST)
    @RestResponseBody
    public List<Site> getAllSite(){
        return siteService.get();
    }

    @ApiOperation("角色列表(不分页),要传入站点siteId")
    @RestResponseBody
    @RequestMapping(path = "/list_role", method = RequestMethod.POST)
    @ApiImplicitParam(name = "siteId", value = "siteId", required = true, dataType = "int")
    public List<Role> listRole(@NotNull(message = "请传系统id") Integer siteId) {
        Site site = siteService.get(siteId);
        if (site == null){
            throw new SsoException("该站点不存在");
        }
        return roleService.findBySiteId(siteId);
    }

    @ApiOperation("客户充值接口")
    @RequestMapping(path = "/add_pay", method = RequestMethod.POST)
    @RestResponseBody
    public boolean addVipPay(@RequestBody PayDto param, @ApiIgnore @CurrentUser User user) {
        //添加充值记录
        UserVipPayRecord userVipPayRecord = new UserVipPayRecord();
        BeanUtils.copyProperties(param,userVipPayRecord);
        userVipPayRecord.setStartTime(new Date(param.getStartTime()));
        userVipPayRecord.setEndTime(new Date(param.getEndTime()));
        userVipPayRecord.setCreateUid(user.getUid());
        userVipPayRecordService.add(userVipPayRecord);
        //添加或修改会员周期
        List<PayDto.SiteRoleDto> siteRoleList = param.getSiteRoleList();
        for(PayDto.SiteRoleDto el : siteRoleList){
            //会员周期更新
            UserVipDuration userVipDuration = userVipDurationService.getBySiteIdAndRoleId(param.getCustomerId(),el.getSiteId(),el.getRoleId());
            if (userVipDuration == null) {
                userVipDuration = new UserVipDuration();
                userVipDuration.setCustomerId(param.getCustomerId());
                userVipDuration.setSiteId(el.getSiteId());
                userVipDuration.setRoleId(el.getRoleId());
                userVipDuration.setStatus(UserVipDurationStatus.NORMAL);
                userVipDuration.setMinStartTime(new Date(param.getStartTime()));
                userVipDuration.setMaxEndTime(new Date(param.getEndTime()));
                userVipDurationService.add(userVipDuration);
            } else {
                userVipDuration.setMinStartTime(userVipPayRecordService.getMinStartTime(param.getCustomerId()));
                userVipDuration.setMaxEndTime(userVipPayRecordService.getMaxEndTime(param.getCustomerId()));
                userVipDurationService.update(userVipDuration);
            }
            //在用户站点表里面添加记录
            List<UserSite> userSites = siteService.getUserSite(param.getCustomerId());
            if (userSites.size() > 0) {
                List<Integer> sites = userSites.stream().map(UserSite::getSiteId).collect(Collectors.toList());
                if (!sites.contains(el.getSiteId())) {
                    siteService.addAccessSite(param.getCustomerId(), el.getSiteId());
                }
            } else {
                siteService.addAccessSite(param.getCustomerId(), el.getSiteId());
            }
            //在用户角色表里添加记录
            List<UserRole> userRoles = userRoleService.findByUid(param.getCustomerId());
            if (userRoles.size() > 0) {
                List<Integer> roles = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
                if (!roles.contains(el.getRoleId())) {
                    userRoleService.addRole(param.getCustomerId(), new int[]{el.getRoleId()});
                }
            } else {
                userRoleService.addRole(param.getCustomerId(), new int[]{el.getRoleId()});
            }
        };
        return true;
    }

    @ApiOperation("客户充值记录获取接口")
    @RequestMapping(path = "/edit_pay_info/{id}", method = RequestMethod.POST)
    @RestResponseBody
    public PayDto editVipPayInfo(@ApiParam(value = "客户充值的记录id",required = true) @PathVariable int id) {
        PayDto payDto = new PayDto();
        UserVipPayRecord userVipPayRecord = userVipPayRecordService.get(id);
        payDto.setPayId(id);
        payDto.setCustomerId(userVipPayRecord.getCustomerId());
        payDto.setMoney(userVipPayRecord.getMoney());
        payDto.setStartTime(userVipPayRecord.getStartTime().getTime());
        payDto.setEndTime(userVipPayRecord.getEndTime().getTime());
        List<PayDto.SiteRoleDto> siteRoleList = new ArrayList<>();
        List<UserVipDuration> userVipDurations = userVipDurationService.getByCustomerId(userVipPayRecord.getCustomerId());
        for (UserVipDuration el : userVipDurations) {
            int siteId = el.getSiteId();
            int roleId = el.getRoleId();
            PayDto.SiteRoleDto tmp = new PayDto.SiteRoleDto();
            tmp.setSiteId(siteId);
            tmp.setRoleId(roleId);
            siteRoleList.add(tmp);
        }
        payDto.setSiteRoleList(siteRoleList);
        return payDto;
    }

    @ApiOperation("客户充值修改接口")
    @RequestMapping(path = "/edit_pay", method = RequestMethod.POST)
    @RestResponseBody
    public boolean editVipPay(@RequestBody PayDto param) {
        //修改充值记录
        UserVipPayRecord userVipPayRecordOld = userVipPayRecordService.get(param.getPayId());
        if (userVipPayRecordOld == null) {
            return false;
        }
        userVipPayRecordOld.setMoney(param.getMoney());
        userVipPayRecordOld.setStartTime(new Date(param.getStartTime()));
        userVipPayRecordOld.setEndTime(new Date(param.getEndTime()));
        userVipPayRecordService.update(userVipPayRecordOld);

        List<PayDto.SiteRoleDto> siteRoleList = param.getSiteRoleList();
        for(PayDto.SiteRoleDto el : siteRoleList){
            //会员周期更新
            UserVipDuration userVipDuration = userVipDurationService.getBySiteIdAndRoleId(param.getCustomerId(),el.getSiteId(),el.getRoleId());
            if (userVipDuration == null) {
                userVipDuration = new UserVipDuration();
                userVipDuration.setCustomerId(param.getCustomerId());
                userVipDuration.setSiteId(el.getSiteId());
                userVipDuration.setRoleId(el.getRoleId());
                userVipDuration.setStatus(UserVipDurationStatus.NORMAL);
                userVipDuration.setMinStartTime(new Date(param.getStartTime()));
                userVipDuration.setMaxEndTime(new Date(param.getEndTime()));
                userVipDurationService.add(userVipDuration);
            } else {
                userVipDuration.setMinStartTime(userVipPayRecordService.getMinStartTime(param.getCustomerId()));
                userVipDuration.setMaxEndTime(userVipPayRecordService.getMaxEndTime(param.getCustomerId()));
                userVipDurationService.update(userVipDuration);
            }
            //在用户站点表里面添加记录
            List<UserSite> userSites = siteService.getUserSite(param.getCustomerId());
            if (userSites.size() > 0) {
                List<Integer> sites = userSites.stream().map(UserSite::getSiteId).collect(Collectors.toList());
                if (!sites.contains(el.getSiteId())) {
                    siteService.addAccessSite(param.getCustomerId(), el.getSiteId());
                }
            } else {
                siteService.addAccessSite(param.getCustomerId(), el.getSiteId());
            }
            //在用户角色表里添加记录
            List<UserRole> userRoles = userRoleService.findByUid(param.getCustomerId());
            if (userRoles.size() > 0) {
                List<Integer> roles = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
                if (!roles.contains(el.getRoleId())) {
                    userRoleService.addRole(param.getCustomerId(), new int[]{el.getRoleId()});
                }
            } else {
                userRoleService.addRole(param.getCustomerId(), new int[]{el.getRoleId()});
            }
        };
        return false;
    }

    @ApiOperation("暂停或开启客户对某个站点的访问")
    @RequestMapping(path = "/stop_or_start/{id}", method = RequestMethod.POST)
    @RestResponseBody
    public boolean forbidOrStart(@ApiParam(value = "会员周期的记录id",required = true) @PathVariable int id,@ApiIgnore @CurrentUser User user) {
        //在用户站点表里面和用户角色表里添加或删除记录
        UserVipDuration userVipDuration = userVipDurationService.get(id);
        if (userVipDuration == null) {
            return false;
        }
        int customerId = userVipDuration.getCustomerId();
        int siteId = userVipDuration.getSiteId();
        int roleId = userVipDuration.getRoleId();
        if (UserVipDurationStatus.NORMAL.getStatus() == userVipDuration.getStatus().getStatus()) {
            userVipDuration.setStatus(UserVipDurationStatus.STOPPED);
            siteService.deleteUserSite(customerId,siteId);
            UserRole userRole = userRoleService.getOne(customerId, roleId);
            if (userRole != null) {
                userRoleService.delete(userRole.getId());
            }
        } else {
            userVipDuration.setStatus(UserVipDurationStatus.NORMAL);
            List<Site> sites = siteService.getAccessSite(customerId);
            if (sites == null || !sites.stream().map(Site::getId).collect(Collectors.toList()).contains(siteId)) {
                siteService.addAccessSite(customerId,siteId);
            }
            UserRole userRole = userRoleService.getOne(customerId, roleId);
            if (userRole == null) {
                userRoleService.addRole(customerId, new int[]{roleId});
            }
        }
        userVipDuration.setUpdateUid(user.getUid());
        userVipDurationService.update(userVipDuration);
        return true;
    }

    @ApiOperation("客户充值记录信息列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
            @ApiImplicitParam(name = "customerName", value = "客户姓名", dataType = "String"),
    })
    @RequestMapping(path = "/list_pay", method = RequestMethod.POST)
    @RestResponseBody
    public Page<Map<String,Object>> getVipPays(@ApiIgnore Pageable pageable, @ApiIgnore UserVipPayRecord param) {
        return userVipPayRecordService.getPage(pageable,param);
    }

    @ApiOperation("客户会员周期列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
            @ApiImplicitParam(name = "customerName", value = "客户姓名", dataType = "String"),
    })
    @RequestMapping(path = "/list_duration", method = RequestMethod.POST)
    @RestResponseBody
    public Page<Map<String,Object>> getVipDurations(@ApiIgnore Pageable pageable, @ApiIgnore UserVipDuration param) {
        return userVipDurationService.getPage(pageable,param);
    }

}
