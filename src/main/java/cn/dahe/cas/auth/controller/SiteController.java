package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.constants.LogOperation;
import cn.dahe.cas.auth.constants.SiteDelete;
import cn.dahe.cas.auth.domain.UserLog;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.entity.UserSite;
import cn.dahe.cas.auth.event.SiteEvent;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.search.SearchSite;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.UserLogService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "站点管理各种接口")
@Controller
@RequestMapping(path = "/admin/site")
@RequiresPermissions("site:manager")
public class SiteController {

    @Autowired
    private SiteService siteService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private UserLogService userLogService;

    @Autowired
    private UserService userService;

    @ApiOperation("站点列表信息（分页）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "站点名称", dataType = "String"),
            @ApiImplicitParam(name = "url", value = "站点url", dataType = "String"),
    })
    @RequestMapping(method = RequestMethod.POST)
    @RestResponseBody
    public Page<Site> getSite(@RequestParam int page, @RequestParam int size, SearchSite searchSite){
        Pageable pageable = new PageRequest(page-1,size,new Sort(Sort.Direction.DESC,"weight"));
        return siteService.searchSite(pageable, searchSite);
    }

    @ApiOperation("根据站点名称获取站点列表信息（分页）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
            @ApiImplicitParam(name = "siteName", value = "站点名称", dataType = "String"),
    })
    @RequestMapping(path = "/getbysitename",method = RequestMethod.POST)
    @RestResponseBody
    public Page<Site> getBySiteName(@PageableDefault(value = 20,direction = Sort.Direction.DESC,sort = {"id"}) Pageable page, String siteName){
        return siteService.getByName(page, siteName);
    }

    @ApiOperation("获取所有站点列表信息（不分页）")
    @RequestMapping(path = "/list",method = RequestMethod.POST)
    @RestResponseBody
    public List<Site> getAllSite(){
        List<Site> defaultSites = siteService.getDefaultSites();
        List<Site> sites = siteService.get();
        List<Site> result = sites.stream()
                .filter(e1 -> e1.getId() != defaultSites.get(0).getId())
                .sorted(Comparator.comparingInt(Site::getWeight).reversed())
                .collect(Collectors.toList());
        return result;
    }

    @ApiOperation("添加站点")
    @LogAction(action = "添加站点")
    @RequestMapping(path = "/add",method = RequestMethod.POST)
    @RestResponseBody
    public boolean addSite(@ApiIgnore @CurrentUser User current, @Valid Site site){
        if(siteService.exist(site)){
            throw new SsoException("站点名称或者url已存在");
        }
        if(SsoStringUtil.isNotBlank(site.getLogTag())){
            if (siteService.getByLogTag(site.getLogTag()) != null){
                throw new SsoException("tag标签已存在");
            }
        }
        siteService.add(site);
        return true;
    }

    @ApiOperation("修改站点")
    @LogAction(action = "修改站点")
    @RequestMapping(path = "/edit",method = RequestMethod.POST)
    @RestResponseBody
    public boolean editSite(@Valid Site site){
        Site one = siteService.getOne(site.getName());
        Site two = siteService.getOneSite(site.getUrl());
        if(one != null && site.getId() != one.getId()){
            throw new SsoException("站点名称已存在");
        }
        if(two != null && site.getId() != two.getId()){
            throw new SsoException("站点url已存在");
        }
        if(SsoStringUtil.isNotBlank(site.getLogTag())){
            Site three = siteService.getByLogTag(site.getLogTag());
            if (three != null && site.getId() != three.getId()){
                throw new SsoException("tag标签已存在");
            }
        }
        siteService.update(site);
        return true;
    }

    @ApiOperation("给用户分配站点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "int"),
            @ApiImplicitParam(name = "sid", value = "站点id数组", required = true, dataType = "list"),
    })
    @LogAction(action = "给用户分配站点")
    @RequestMapping(path = "/add_user_site",method = RequestMethod.POST)
    @RestResponseBody
    public boolean addUserSite(int userId,@NotBlank @RequestParam(name = "sid",required = false) List<Integer> sid,
                               @CurrentUser User currentUser){
        User user = userService.get(userId);
        if (user == null){
            throw new SsoException("该用户不存在");
        }
        List<UserSite> userSiteList = siteService.getUserSite(userId);
        List<Integer> siteIds = new ArrayList<>();
        //粗暴一点，全部清空，此处未进行事务控制，如果老的删除但新的未能添加则，但无关紧要，再添加就是
        if (userSiteList != null && userSiteList.size() > 0) {
            siteIds = userSiteList.stream().map(userSite -> userSite.getSiteId()).collect(Collectors.toList());
            siteService.deleteAllSite(userId);
        }
        if(sid==null){
            return true;
        }
        List<Site> defaultSites = siteService.getDefaultSites();
        defaultSites.forEach(site -> sid.add(site.getId()));
        siteService.addAccessSite(userId, sid);
        // todo 记录修改跟踪链
        UserLog build = UserLog.builder().operateDate(new Date()).operateUserId(currentUser.getUid())
                .operateUserName(currentUser.getTruename()).userId(userId).userName(user.getUsername())
                .oldParam(siteIds).newParam(sid.stream().distinct().collect(Collectors.toList()))
                .operateType(LogOperation.USER_SITE).build();
        build.setOperateDescription("[可通过系统:" + siteIds.toString() + "->"
                + sid.stream().distinct().collect(Collectors.toList()).toString() + "]");
        userLogService.add(build);
        return true;
    }

    @ApiOperation("删除用户可通过站点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "int"),
            @ApiImplicitParam(name = "sid", value = "站点id数组", required = true, dataType = "int[]"),
    })
    @LogAction(action = "删除用户可通过站点")
    @RequestMapping(path = "/delete_user_site",method = RequestMethod.POST)
    @RestResponseBody
    public boolean deleteUserSite(int userId,@NotBlank @RequestParam(name = "sid") int[] sid){
        if (sid.length == 0){
            throw new SsoException("请选择要删除的站点！");
        }
        for (int i : sid) {
           if( SiteDelete.DENY == siteService.get(i).getIsdelete()){
               throw new SsoException(siteService.get(i).getName()+"该站点，不可删,嘿嘿！");
           }
        }
        siteService.deleteUserSite(userId, sid);
        return true;
    }

    @ApiOperation("获取用户可访问的所有站点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "int"),
    })
    @RequestMapping(path = "/getusersite",method = RequestMethod.POST)
    @RestResponseBody
    public List<Site> getUserSite(int userId){
        List<Site> userSiteList = siteService.getAccessSite(userId);
        return userSiteList;
    }

    @ApiOperation("删除站点（根据id）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "站点id", required = true, dataType = "int"),
    })
    @LogAction(action = "删除站点")
    @RequestMapping(path = "/delete",method = RequestMethod.POST)
    @RestResponseBody
    public boolean delete(int id){
        Site site = siteService.get(id);
        siteService.delete(site);
        //发布站点删除事件
        if(site!=null){
            publisher.publishEvent(new SiteEvent(this,site, EntityOperation.DELETE));
        }
        return true;
    }

    @ApiOperation("修改站点站点状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "站点id", required = true, dataType = "int"),
    })
    @LogAction(action = "修改站点站点状态")
    @RequestMapping(path = "/forbid",method = RequestMethod.POST)
    @RestResponseBody
    public boolean status(int id){
        Site site = siteService.forbidOrAllow(id);
        //发布站点状态改变事件
        publisher.publishEvent(new SiteEvent(this,site,EntityOperation.FORBID));
        return true;
    }

}
