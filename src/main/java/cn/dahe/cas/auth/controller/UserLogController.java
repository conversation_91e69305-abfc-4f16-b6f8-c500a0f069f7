package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.LogOperation;
import cn.dahe.cas.auth.domain.UserLog;
import cn.dahe.cas.auth.search.SearchUserLog;
import cn.dahe.cas.auth.service.UserLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * on 2020/5/7.
 */
@Api(tags = "用户修改记录跟踪管理")
@Controller
@RequestMapping("/admin/userlog")
@RequiresPermissions("user:manager")
public class UserLogController {

    @Autowired
    private UserLogService userLogService;


    @RequestMapping(path = "/type", method = RequestMethod.GET)
    @RestResponseBody
    @ApiOperation(notes = "获取用户操作类型",value = "获取用户操作类型")
    public List<LogOperation> getLogEnumType(){
        return userLogService.getOperateType();
    }

    @ApiOperation(value = "分页获取用户操作记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
    })
    @RequestMapping(path = "/page", method = RequestMethod.POST)
    @RestResponseBody
    public Page<UserLog> pages(@ApiIgnore Pageable pageable, SearchUserLog search){
        return userLogService.page(search, pageable);
    }
}
