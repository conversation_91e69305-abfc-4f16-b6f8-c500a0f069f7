package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.RolePermissionType;
import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.entity.FocusPerson;
import cn.dahe.cas.auth.entity.Role;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.event.RolePermissionEvent;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.search.SearchFocus;
import cn.dahe.cas.auth.service.FocusPersonService;
import cn.dahe.cas.auth.util.SsoStringUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiIgnore
@Controller
@RequestMapping("/admin/focus")
@Data
public class FocusController {

    private final FocusPersonService focusPersonService;


    @ApiOperation("重点人物列表接口（分页）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
    })
    @RequestMapping(path = "/person/page", method = RequestMethod.POST)
    @RestResponseBody
    public Page<FocusPerson> pageRoles(Pageable pageable, SearchFocus searchFocus,@CurrentUser User user){
        return focusPersonService.getPage(pageable, searchFocus,user);
    }


    @ApiOperation("添加重点人物")
    @LogAction(action = "添加重点人物")
    @RestResponseBody
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    public boolean add(@Valid FocusPerson focusPerson, @CurrentUser User user) {
        focusPerson.setCreateUserId(user.getUid());
        focusPerson.setCreateTime(new Date());
        focusPerson.setCreateUnit(user.getCompany());
        focusPerson.setStatus(Status.DENY);
        focusPersonService.add(focusPerson);
        return true;
    }

    @ApiOperation("编辑重点人物")
    @LogAction(action = "编辑重点人物")
    @RestResponseBody
    @RequestMapping(path = "/edit", method = RequestMethod.POST)
    public boolean edit(@Valid FocusPerson focusPerson, @ApiIgnore @CurrentUser User user) {
        FocusPerson old = focusPersonService.get(focusPerson.getId());
        if (old == null) {
            throw new SsoException("重点人物不存在");
        }
        old.setDuty(focusPerson.getDuty());
        old.setAreaKeywords(focusPerson.getAreaKeywords());
        old.setEventKeywords(focusPerson.getEventKeywords());
        old.setSubjectKeywords(focusPerson.getSubjectKeywords());
        old.setName(SsoStringUtil.delSpace(focusPerson.getName()));
        old.setModifyUserId(user.getUid());
        old.setModifyTime(new Date());
        focusPersonService.update(old);
        return true;
    }

    @ApiOperation(value = "更改重点人物状态")
    @ApiImplicitParam(name = "id", value = "接口后/加id", required = true, dataType = "int")
    @LogAction(action = "更改重点人物状态")
    @RestResponseBody
    @RequestMapping(path = "/status", method = RequestMethod.POST)
    public boolean updateStatus(@NotNull(message = "请传重点人物id") Integer id, @ApiIgnore @CurrentUser User user) {
        FocusPerson one = focusPersonService.get(id);
        if (one == null) {
            throw new SsoException("重点人物不存在");
        }
        if (one.getStatus() == Status.DENY) {
            one.setStatus(Status.ALLOW);
        } else if (one.getStatus() == Status.ALLOW) {
            one.setStatus(Status.DENY);
        }
        one.setModifyUserId(user.getUid());
        one.setModifyTime(new Date());
        focusPersonService.update(one);
        return true;
    }

    @ApiOperation(value = "删除重点人物")
    @ApiImplicitParam(name = "id", value = "接口后/加id", required = true, dataType = "int")
    @LogAction(action = "删除重点人物")
    @RestResponseBody
    @RequestMapping(path = "/del", method = RequestMethod.POST)
    public boolean updateStatusDel(@NotNull(message = "请传重点人物id") Integer id, @ApiIgnore @CurrentUser User user) {
        focusPersonService.delete(id);
        return true;
    }
}
