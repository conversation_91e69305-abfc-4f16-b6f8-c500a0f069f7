package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.entity.Audit;
import cn.dahe.cas.auth.repositories.AuditRepository;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/audit")
@RequiresPermissions("audit:manager")
public class AuditController {

    @Autowired
    private AuditRepository auditRepository;

    @RequestMapping
    @RestResponseBody
    public Page<Audit> getAudits(@PageableDefault(value = 20,direction = Sort.Direction.DESC,sort = {"id"}) Pageable page){
        return auditRepository.findAll(page);
    }

    @RequestMapping("/del")
    @RestResponseBody
    public boolean deleteAudit(long id){
        auditRepository.delete(id);
        return true;
    }
}
