package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.dto.LoginSite;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.TicketService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.hibernate.validator.constraints.NotBlank;
import org.jasig.cas.ticket.Ticket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/ticket")
@Validated
@RequiresPermissions("ticket:manager")
public class TicketController {

    @Autowired
    private TicketService ticketService;

    @Value("${sm4.security.key}")
    private String sm4Key;

    @RequestMapping
    @RestResponseBody
    public Collection<Ticket> getTickets(){
        return ticketService.getTickets();
    }

    @RequestMapping("/keys")
    @RestResponseBody
    public Set<String> keys(@NotNull(message = "类型不能为空") Integer type){
        if(type==0){
            return ticketService.stKeys();
        }
        if(type==1){
            return ticketService.tgtKeys();
        }
        throw new SsoException("不支持的类型");
    }

    @RequestMapping("/values")
    @RestResponseBody
    public Ticket keys(@NotBlank(message = "key不能为空") String key, @NotNull(message = "类型不能为空")Integer type){
        if(type==0){
            return ticketService.getSt(key);
        }
        if(type==1){
            return ticketService.getTgt(key);
        }
        throw new SsoException("不支持的类型");
    }

    @RequestMapping("/sso")
    @RestResponseBody
    public Page<Ticket> getSso(@PageableDefault(value = 20) Pageable pageable){
        return ticketService.getTgts(pageable);
    }

    @RequestMapping("/st")
    @RestResponseBody
    public Page<Ticket> getSt(@PageableDefault(value = 20) Pageable pageable){
        return ticketService.getSts(pageable);
    }

    @RequestMapping("/service")
    @RestResponseBody
    public Page<LoginSite> getServices(@PageableDefault(value = 20) Pageable pageable){
        return ticketService.getServices(pageable);
    }
}
