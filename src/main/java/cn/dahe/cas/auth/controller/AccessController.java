package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.IpAddress;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.service.ForbidIpService;
import cn.dahe.cas.auth.service.PhoneService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.Size;
/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/access")
@Validated
@RequiresPermissions("access:manager")
public class AccessController {

    @Autowired
    private ForbidIpService ipService;

    @Autowired
    private PhoneService phoneService;

    @RestResponseBody
    @RequestMapping(path = "/ip")
    public Page<String> getIps(@PageableDefault(value = 20) Pageable pageable){
        return ipService.getForbidIps(pageable);
    }

    @RestResponseBody
    @RequestMapping(path = "/getbyip")
    public Page<String> getByIp(@PageableDefault(value = 20) Pageable pageable, @IpAddress(message = "ip格式不正确") String ip){
        if(StringUtils.isBlank(ip)){
            return ipService.getForbidIps(pageable);
        }
        return ipService.getForbidIps(pageable, ip);
    }

    @LogAction(action = "添加禁用ip")
    @RestResponseBody
    @RequestMapping(path = "/ip/add")
    public boolean addIp(@NotBlank @Size(min = 7,max = 17) String ip){
        ipService.addIp(ip);
        return true;
    }

    @RestResponseBody
    @RequestMapping(path = "/phone")
    public Page<String> getPhones(@PageableDefault(value = 20) Pageable pageable){
        return phoneService.getForbidPhones(pageable);
    }

    @RestResponseBody
    @RequestMapping(path = "/getbyphone")
    public Page<String> getByPhone(@PageableDefault(value = 20) Pageable pageable, String phone){
        if(StringUtils.isBlank(phone)){
            return phoneService.getForbidPhones(pageable);
        }
        return phoneService.getForbidPhones(pageable, phone);
    }

    @LogAction(action = "删除禁用ip")
    @RestResponseBody
    @RequestMapping(path = "/ip/del")
    public boolean delIp(String ip){
        ipService.remove(ip);
        return true;
    }

    @LogAction(action = "添加禁用手机号")
    @RestResponseBody
    @RequestMapping(path = "/phone/add")
    public boolean addPhone(@Size(min = 11,max = 11) String phone){
        phoneService.addPhone(phone);
        return true;
    }

    @LogAction(action = "删除禁用手机号")
    @RestResponseBody
    @RequestMapping(path = "/phone/del")
    public boolean delPhone(@Size(min = 11,max = 11) String phone){
        phoneService.remove(phone);
        return true;
    }
}
