package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.CasService;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpSession;

/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
public class AdminController {

    @Value("${sm4.security.key}")
    private String sm4Key;
    @Value("${cas.logout}")
    private String logoutUrl;

    @Autowired
    private CasService casService;

    @RequestMapping("/pro/login")
    public String index(){
        return "default/ui/casLoginViewAdmin";
    }

    @RequestMapping("/")
    public String root() {
        return "redirect:/home";
    }

    @RequestMapping("/admin")
    public String admin(@CurrentUser User currentUser) {
        if (currentUser != null) {
            boolean hasRole = SecurityUtils.getSubject().hasRole("admin") || SecurityUtils.getSubject().hasRole("site_user");
            if (hasRole) {
                return "redirect:/index.html";
            }
        }
        return "redirect:/home";
    }

    @RequestMapping("/exit")
    public String exit() {
        return "redirect:/home/<USER>";
    }

    @RequestMapping("/not_found")
    public String notFound() throws Exception {
        return "not_found";
    }
}
