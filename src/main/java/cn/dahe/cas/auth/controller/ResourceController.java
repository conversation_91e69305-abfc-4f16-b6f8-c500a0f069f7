package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.constants.RolePermissionType;
import cn.dahe.cas.auth.dto.AddResource;
import cn.dahe.cas.auth.dto.ResourceDto;
import cn.dahe.cas.auth.dto.ResourceTree;
import cn.dahe.cas.auth.entity.Resource;
import cn.dahe.cas.auth.entity.RoleResource;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.event.RolePermissionEvent;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.search.SearchResource;
import cn.dahe.cas.auth.service.ResourceService;
import cn.dahe.cas.auth.service.RoleResourceService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/resource")
@Validated
@RequiresPermissions("resource:manager")
public class ResourceController {

    @Autowired
    private ResourceService resourceService;
    @Autowired
    private RoleResourceService roleResourceService;
    @Autowired
    private ApplicationEventPublisher publisher;

    // todo 由于需要把子系统的权限集中在sso，接口全部进行系统隔离（区分siteID）******2020/04/27**********

    @ApiOperation(value = "分页查询权限信息，可以增加搜索条件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "int"),
            @ApiImplicitParam(name = "size", value = "每页的记录数", required = true, dataType = "int"),
    })
    @RequestMapping(path = "/page",method = RequestMethod.POST)
    @RestResponseBody
    public Page<Resource> page(Pageable pageable, SearchResource search){
        return resourceService.getPage(pageable, search);
    }

    @ApiOperation(value = "添加权限")
    @LogAction(action = "添加权限")
    @RestResponseBody
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    public boolean add(@Valid AddResource resource, @ApiIgnore @CurrentUser User user){
        Resource newResource = new Resource();
        resource.setId(0);
        resource.setPid(resource.getPid() == null ? 0 : resource.getPid());
        BeanUtils.copyProperties(resource, newResource);
        resourceService.add(newResource, user);
        publisher.publishEvent(new RolePermissionEvent(resource.getSiteId(), RolePermissionType.PERMISSION));
        return true;
    }

    @ApiOperation(value = "编辑")
    @LogAction(action = "编辑权限")
    @RestResponseBody
    @RequestMapping(path = "/edit", method = RequestMethod.POST)
    public boolean edit(@Valid AddResource resource, @ApiIgnore @CurrentUser User user){
        Resource newResource = new Resource();
        resource.setPid(resource.getPid() == null ? 0 : resource.getPid());
        BeanUtils.copyProperties(resource, newResource);
        Resource editResource = resourceService.edit(newResource, user.getUid());
        //更新下面子权限的上级权限名称
        List<Resource> resources = resourceService.findByPid(editResource.getId());
        if(resources != null && resources.size() > 0){
            for (Resource one : resources) {
                one.setPname(editResource.getName());
                resourceService.update(one);
            }
        }
        publisher.publishEvent(new RolePermissionEvent(resource.getSiteId(), RolePermissionType.PERMISSION));
        return true;
    }

    @ApiOperation(value = "更改状态")
    @ApiImplicitParam(name = "id", value = "权限id",required = true)
    @LogAction(action = "更改权限状态")
    @RestResponseBody
    @RequestMapping(path = "/status")
    public boolean updateStatus(@NotNull(message = "请传权限id") Integer id, @ApiIgnore @CurrentUser User user){
        Resource resource = resourceService.get(id);
        if (resource == null){
            throw new SsoException("该权限不存在");
        }
        resourceService.editStatus(id, user.getUid());
        publisher.publishEvent(new RolePermissionEvent(resource.getSiteId(), RolePermissionType.PERMISSION));
        return true;
    }

    @ApiOperation(value = "角色分配权限使用的树,返回的是带children")
    @ApiImplicitParam(name = "siteId", value = "站点id", required = true)
    @RestResponseBody
    @RequestMapping(path = "/tree/children",method = RequestMethod.POST)
    public List<ResourceTree> listTree(@NotNull(message = "请传站点id")Integer siteId,
                                       @RequestParam(defaultValue = "0") int pid) {
        return resourceService.listTree(siteId, pid);
    }

    @ApiOperation(value = "角色分配权限使用的树,返回的是带父子级关系和选择框的List")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "siteId", value = "站点id", required = true),
            @ApiImplicitParam(name = "roleId", value = "角色id,添加时传0")
    })
    @RestResponseBody
    @RequestMapping(path = "/tree",method = RequestMethod.POST)
    public List<ResourceDto> listAuthorityTree(@NotNull(message = "请传站点id")Integer siteId, Integer roleId) {
        List<Resource> resources = resourceService.getBySiteId(siteId);
        List<ResourceDto> resourceDtos = new ArrayList<>();
        for (Resource resource : resources) {
            ResourceDto dto = new ResourceDto();
            dto.setId(resource.getId());
            dto.setPid(resource.getPid());
            dto.setName(resource.getName());
            dto.setChecked(0);
            resourceDtos.add(dto);
        }
        roleId = roleId == null ? 0 : roleId;
        if (roleId == 0){
            return resourceDtos;
        }
        List<RoleResource> roleResources = roleResourceService.getByRoleId(roleId);
        if (roleResources != null && !roleResources.isEmpty()) {
            for (ResourceDto resourceDto : resourceDtos) {
                for (RoleResource roleResource : roleResources) {
                    if (roleResource.getResId() == resourceDto.getId()) {
                        resourceDto.setChecked(1);
                    }
                }
            }
        }
        return resourceDtos;
    }

    @ApiOperation(value = "根据角色查询权限")
    @ApiImplicitParam(name = "roleId", value = "角色id", required = true)
    @RestResponseBody
    @RequestMapping(path = "/get-by-role",method = RequestMethod.POST)
    public List<Resource> listTree(@NotNull(message = "请传角色id")Integer roleId) {
        return resourceService.listByRoleId(roleId);
    }
}
