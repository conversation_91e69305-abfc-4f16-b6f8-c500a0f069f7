package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.IpAddress;
import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.annotion.PhoneNumber;
import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.annotion.SignCheck;
import cn.dahe.cas.auth.annotion.TokenCheck;
import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.constants.SiteStatus;
import cn.dahe.cas.auth.dto.AliWeather;
import cn.dahe.cas.auth.dto.IpInfo;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.dto.RoleDto;
import cn.dahe.cas.auth.dto.SiteDto;
import cn.dahe.cas.auth.dto.SsoUserDto;
import cn.dahe.cas.auth.dto.UserProfileDto;
import cn.dahe.cas.auth.entity.Mobile;
import cn.dahe.cas.auth.entity.Role;
import cn.dahe.cas.auth.entity.Site;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.external.api.SsoSiteService;
import cn.dahe.cas.auth.external.api.SsoUserService;
import cn.dahe.cas.auth.service.IpService;
import cn.dahe.cas.auth.service.MobileService;
import cn.dahe.cas.auth.service.RoleService;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.SiteTreeService;
import cn.dahe.cas.auth.service.ThirdService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.IpUtils;
import cn.dahe.cas.auth.util.Sm4Util;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.URL;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.stream.Collectors;

import static cn.dahe.cas.auth.util.Sm4Util.decryptEcb;

/** 对外提供的服务
 * 例如手机号查询、ip查询等
 * 加入安全机制、防止随意调用
 * <AUTHOR>
 */
@Controller
@RequestMapping("/dahe/service")
@Validated
public class ThirdServiceController {

    @Autowired
    private MobileService mobileService;

    @Autowired
    private IpService ipService;

    @Autowired
    private SsoUserService ssoUserService;

    @Autowired
    private SsoSiteService ssoSiteService;

    @Autowired
    private UserService userService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private SiteTreeService siteTreeService;

    @Autowired
    private ThirdService thirdService;

    @Autowired
    private RoleService roleService;

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    @Qualifier("mvcConversionService")
    private ConversionService conversionService;

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @Resource(name = "hashTemplate")
    private HashOperations<String, String, RoleDto> cacheTemplate;

    @RequestMapping(path = "/ip")
    @RestResponseBody
    @SignCheck(checkParams = {"address","timestap"})
    @LogAction(action = "获取ip信息")
    public IpInfo getIpInfo(@NotBlank @IpAddress String address, @NotNull long timestap, @NotBlank String sign){
        return ipService.getIpInfo(address);
    }

    @RequestMapping(path = "/mobile")
    @RestResponseBody
    @SignCheck(checkParams = {"phone","timestap"})
    @LogAction(action = "获取手机号信息")
    public Mobile getMobileInfo(@NotBlank @PhoneNumber String phone, @NotNull long timestap, @NotBlank String sign){
        return mobileService.getMobileByNumber(phone);
    }

    @RequestMapping(path = "/user",method = RequestMethod.POST)
    @RestResponseBody
    @LogAction(action = "第三方获取用户列表信息")
    @TokenCheck
    public Page<User> getUser(@RequestParam(name = "page",required = false,defaultValue = "1") int page, @RequestParam(name = "size",required = false,defaultValue = "20") int size){
        Pageable pageable = new PageRequest(page-1, size, new Sort(Sort.Direction.DESC,"uid"));
        Page<User> userPage = ssoUserService.getUser(pageable);
        // todo 解密
        return decodeUsers(userPage);
    }

    @RequestMapping(path = "/user/ids")
    @RestResponseBody
    @LogAction(action = "第三方获取用户所有ID")
    @TokenCheck
    public List<Integer> getUser(){
        return ssoUserService.getAllUserIds();
    }

    @RequestMapping(path = "/site")
    @RestResponseBody
    @LogAction(action = "第三方获取站点信息")
    /**
     * todo 暂时去掉token，后期加上
     */
    public List<SiteDto> getSite(){
        return siteTreeService.getSite();
    }

    /**该接口为方便第三方调用加入较多非必要的判断，代码极其不优美，难受，想哭，考虑进行与第三方需求进行协调
     * @param page
     * @param size
     * @param siteId
     * @return
     */
    @RequestMapping(path = "/site/user")
    @RestResponseBody
    @LogAction(action = "获取站点下用户")
    @TokenCheck
    public Object getSiteUser(@RequestParam(required = false) Integer page,@RequestParam(required = false) Integer size,
                              @RequestParam(required = false) String siteId){
        //如果未传入分页信息，认为是直接获取所有用户
        if((page==null||size==null)){
            if(StringUtils.isBlank(siteId)){
                throw new SsoException("获取所有用户时站点id不能为空");
            }
            List<User> result = ssoSiteService.getSiteUser(siteId);
            // todo 解密
            return result.stream().map(user -> conversionService.convert(user,SsoUserDto.class)).collect(Collectors.toList());
        }
        Pageable pageable = new PageRequest(page-1, size, new Sort(Sort.Direction.DESC,"uid"));
        //如果未传入站点信息，认为是获取所有用户列表,此时分页不能为空
        if(siteId==null){
            // todo 解密
            return ssoUserService.getUser(pageable).map(source -> conversionService.convert(source,SsoUserDto.class));
        }
        //如果都不为空，认为是正常分页
        // todo 解密
        return ssoSiteService.getSiteUser(pageable,siteId).map(source -> conversionService.convert(source,SsoUserDto.class));
    }

    @RequestMapping(path = "/department/user")
    @RestResponseBody
    @LogAction(action = "获取组织机构下用户")
    @TokenCheck
    public Object getDepartmentUser(@RequestParam(required = false) Integer page,@RequestParam(required = false) Integer size,Integer departmentId){
        //如果未传入分页信息，认为是直接获取所有用户
        if(page==null||size==null){
            List<User> departmentUser = ssoUserService.getDepartmentUser(departmentId);
            // todo 解密
            return departmentUser.stream().map(user -> conversionService.convert(user,SsoUserDto.class)).collect(Collectors.toList());
        }
        Pageable pageable = new PageRequest(page-1,size, new Sort(Sort.Direction.DESC,"uid"));
        //如果都不为空，认为是正常分页
        Page<User> departmentUser = ssoUserService.getDepartmentUser(pageable, departmentId);
        // todo 解密
        return departmentUser.map(source -> conversionService.convert(source,SsoUserDto.class));
    }

    @RequestMapping(path = "/access/user")
    @RestResponseBody
    @LogAction(action = "根据组织机构获取拥有某个站点权限的所有用户")
    @TokenCheck
    public Object getUserByDepartmentIdAndSite(Integer departmentId, @URL(message = "站点地址不符合规则") String siteUrl){
        Site oneSite = siteService.getOneSite(siteUrl);
        if (oneSite == null){
            throw new SsoException("该系统未接入sso系统中");
        }
        // todo 解密
        List<User> userList = ssoUserService.getDepartmentUser(departmentId);
        for (User user : userList) {
            decodeUser(user);
        }
        userList = userList.stream().filter(user -> siteService.canAccess(user.getUid(),oneSite.getId())).collect(Collectors.toList());
        return userList;
    }

    /**
     * 第三方服务，进行用户检索，目前未进行模糊查询，后期考虑加入
     * @param page
     * @param size
     * @param identify
     * @param siteId
     * @return
     */
    @RequestMapping(path = "/user/search")
    @RestResponseBody
    @LogAction(action = "第三方检索用户")
    @TokenCheck
    public Page<User> searchUser(@RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "20") int size, @NotBlank String identify, @RequestParam(required = false) String siteId) throws Exception {
        Pageable pageable = new PageRequest(page-1, size, new Sort(Sort.Direction.DESC,"uid"));
        Page<User> user = ssoUserService.findUser(pageable, identify, siteId);
        return user.map(source -> conversionService.convert(source,SsoUserDto.class));
    }

    /**
     * 对外提供根据用户id获取用户信息接口
     * @param id
     * @return
     */
    @RequestMapping(path = "/user/profile")
    @RestResponseBody
    @LogAction(action = "根据用户id获取用户资料")
    @TokenCheck
    public UserProfileDto getUserProfile(int id){
        User user =  userService.get(id);
        if(null == user){
            throw new SsoException("无该用户");
        }
        // todo 解密
        user = decodeUser(user);
        UserProfileDto userProfileDto = new UserProfileDto();
        BeanUtils.copyProperties(user,userProfileDto);
        //SiteDto site = siteTreeService.getSite(user.getSid());
        //userProfileDto.setSiteName(site==null?"":site.getSiteName());
        //userProfileDto.setSites(siteService.getAccessSite(id));
        return userProfileDto;
    }

    @RequestMapping(path = "/user/profile_by_phone")
    @RestResponseBody
    @LogAction(action = "根据用户手机号获取用户资料")
    @TokenCheck
    public UserProfileDto getUserProfile(String phone) throws Exception {
        User user =  userService.getUserByPhone(Sm4Util.encryptEcb(sm4Key, phone));
        if(null == user){
            throw new SsoException("无该用户");
        }
        // todo 解密
        user = decodeUser(user);
        UserProfileDto userProfileDto = new UserProfileDto();
        BeanUtils.copyProperties(user,userProfileDto);
        //SiteDto site = siteTreeService.getSite(user.getSid());
        //userProfileDto.setSiteName(site==null?"":site.getSiteName());
        //userProfileDto.setSites(siteService.getAccessSite(user.getUid()));
        return userProfileDto;
    }

    @RequestMapping(path = "/user/exist")
    @RestResponseBody
    @LogAction(action = "判断用户是否存在")
    @TokenCheck
    public boolean exist(@NotBlank String phone) throws Exception {
        // todo 加密
        phone = Sm4Util.encryptEcb(sm4Key, phone);
        if(userService.existPhone(phone)){
            return true;
        }
        throw new SsoException("用户不存在");
    }

    @RequestMapping(path = "/weather")
    @ResponseBody
    @LogAction(action = "第三方获取天气信息")
    @TokenCheck
    public JsonResult getWeather(@NotNull @IpAddress String ip) throws UnsupportedEncodingException {
        return ResultUtil.success(thirdService.getWeather(ipService.getIpInfo(ip).getCity()));
    }

    @RequestMapping(path = "/weather/jsonp")
    @RestResponseBody(jsonp = true)
    @LogAction(action = "jsonp方式获取天气信息")
    public Object getWeatherJsonp(HttpServletRequest servletRequest) throws UnsupportedEncodingException {
        String city = ipService.getIpInfo(IpUtils.getRemoteIp(servletRequest)).getCity();
        Object result = thirdService.getWeather(city);
        return JSON.parseObject(JSON.toJSONString(result), AliWeather.class);
    }

    private Page<User> decodeUsers(Page<User> pageUser){
        pageUser.forEach(user -> {
            decodeUser(user);
        });
        return pageUser;
    }

    private User decodeUser(User user) {
        try {
            user.setPhone(decryptEcb(sm4Key, user.getPhone()));
            user.setTruename(decryptEcb(sm4Key, user.getTruename()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return user;
    }

    @RequestMapping(path = "/user/role")
    @RestResponseBody
    @LogAction(action = "根据用户id获取用户角色")
    @TokenCheck
    public RoleDto getUserRole(@NotNull(message = "请传用户id") Integer userId,
                               @NotBlank(message = "请传标签") String tag){
        User user =  userService.get(userId);
        if(null == user){
            throw new SsoException("无该用户");
        }
        Site oneSite = siteService.getByLogTag(tag);
        if (oneSite == null || oneSite.getStatus() == SiteStatus.DENY){
            throw new SsoException("该系统未接入sso");
        }
        boolean canAccess = siteService.canAccess(userId, oneSite.getId());
        if (!canAccess){
            throw new SsoException("该用户对此系统无访问权限");
        }
        RoleDto userProfileDto = userService.getBySiteAndUid(user.getUid(), oneSite.getId());
        cacheTemplate.put(CacheKey.USER_ROLE + ":" + oneSite.getLogTag(), user.getUid()+"", userProfileDto);
        return userProfileDto;
    }

    @RequestMapping(path = "/roles")
    @RestResponseBody
    @LogAction(action = "根据tag获取角色")
    @TokenCheck
    public List<Role> getRoles(@NotBlank(message = "请传标签") String tag){
        Site oneSite = siteService.getByLogTag(tag);
        if (oneSite == null || oneSite.getStatus() == SiteStatus.DENY){
            throw new SsoException("该系统未接入sso");
        }
        return roleService.findBySiteId(oneSite.getId());
    }

    @RequestMapping(path = "/role/users")
    @RestResponseBody
    @LogAction(action = "查询角色所属用户")
    @TokenCheck
    public List<User> getUserByRoleId(@NotBlank(message = "请传标签") String keyword,
                                      @NotNull(message = "请传角色id") Integer roleId){
        List<User> userList = ssoUserService.getByRoleId(roleId, keyword);
        // todo 解密
        userList.forEach(user -> decodeUser(user));
        return userList;
    }
}
