package cn.dahe.cas.auth.controller;

import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.service.CasService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jasig.cas.services.RegisteredService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * 后期加入分页和排序
 */
@ApiIgnore
@Controller
@RequestMapping("/admin/service")
@RequiresPermissions("service:manager")
public class ServiceRegisterController {

    @Autowired
    private CasService casService;

    @RequestMapping
    @RestResponseBody
    public Page<RegisteredService> getAllService(@PageableDefault(value = 20) Pageable page){
        return casService.getServices(page);
    }

    @RequestMapping(path = "/delete",method = RequestMethod.POST)
    @RestResponseBody
    public boolean deleteService(int id){
        casService.deleteService(id);
        return true;
    }
}
