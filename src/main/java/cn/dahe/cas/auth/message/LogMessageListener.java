package cn.dahe.cas.auth.message;

import cn.dahe.cas.auth.domain.Log;
import cn.dahe.cas.auth.service.SystemLogService;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.convert.ConversionService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/21
 * @createTime 20:38
 * @info
 */
@Component("logListener")
public class LogMessageListener implements MessageListenerConcurrently{

    private final SystemLogService logService;

    private final ConversionService conversionService;

    @Autowired
    public LogMessageListener(SystemLogService logService, @Qualifier("mvcConversionService") ConversionService conversionService) {
        this.logService = logService;
        this.conversionService = conversionService;
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> messages, final ConsumeConcurrentlyContext context) {
        List<Log> logs = Lists.transform(messages, messageExt -> conversionService.convert(messageExt,Log.class));
        recordLog(logs);
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 记录日志
     */
    private void recordLog(List<Log> logs){
        //todo 对日志ua头进行截取处理
        logService.add(logs);
    }
}
