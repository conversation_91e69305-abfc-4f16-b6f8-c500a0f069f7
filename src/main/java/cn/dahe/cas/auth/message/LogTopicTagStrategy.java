package cn.dahe.cas.auth.message;

import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.entity.LogSource;
import cn.dahe.cas.auth.repositories.LogSourceRepository;
import cn.dahe.cas.auth.service.impl.BaseServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/21
 * @createTime 21:00
 * @info
 */
@Component("logTopicTagStrategy")
public class LogTopicTagStrategy extends BaseServiceImpl<LogSource,Integer,LogSourceRepository> implements TopicTagStrategy{
    @Autowired
    private LogSourceRepository repository;

    @Override
    public List<String> getTag() {
        return Lists.transform(repository.findLogSourceByStatus(UserFlag.ALLOW), logSource -> logSource.getSource());
    }
}
