package cn.dahe.cas.auth.message;

import cn.dahe.cas.auth.service.ClearCache;
import com.alibaba.fastjson.JSONObject;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * on 2020/05/13
 */
@Component("roleListener")
public class RoleMessageListener implements MessageListenerConcurrently{

    @Autowired
    private ClearCache clearCache;

    private static final Logger logger = LoggerFactory.getLogger(RoleMessageListener.class);

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> messages, final ConsumeConcurrentlyContext context) {
        for (MessageExt message : messages) {
            String body = new String(message.getBody(), Charset.forName("UTF-8"));
            try {
                Map map = JSONObject.parseObject(body, Map.class);
                Integer userId = (Integer) map.get("userId");
                logger.info("<<<<<<<<<<<<<<<<<<<<<<<roleConsumer,userId  is {}", userId);
                clearCache.clear(userId);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
