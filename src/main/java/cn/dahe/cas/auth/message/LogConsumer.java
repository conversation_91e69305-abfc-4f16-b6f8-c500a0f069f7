package cn.dahe.cas.auth.message;

import cn.dahe.cas.auth.event.LogSourceEvent;
import cn.dahe.cas.auth.exception.SsoException;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/22
 * @createTime 15:24
 * @info 检测到日志源改变事件时重新进行消费者订阅
 */
@Component
public class LogConsumer {

    private static final Logger logger = LoggerFactory.getLogger(LogConsumer.class);

    @Autowired
    @Qualifier("logMqConsumer")
    private DefaultMQPushConsumer consumer;

    @Autowired
    private TopicTagStrategy topicTagStrategy;

    public static final String TOPIC = "systemlog";

    @PostConstruct
    public void init() {
        if (consumer == null) {
            throw new SsoException("consumer can not be null");
        }
        start();
    }

    @PreDestroy
    public void destroy() {
        stop();
    }

    @EventListener
    public void onApplicationEvent(LogSourceEvent logSourceEvent) {
        switch (logSourceEvent.getOperation()) {
            case ADD:
            case DELETE:
            case EDIT:
                refreshTag();
                break;
            default:
                break;
        }
    }

    private void start() {
        try {
            consumer.subscribe(TOPIC, getTags());
            consumer.start();
        } catch (MQClientException e) {
            e.printStackTrace();
        }
    }

    private void stop() {
        consumer.unsubscribe(TOPIC);
        consumer.shutdown();
    }

    /**
     * 当站点发生变化时重新进行日志tag的注册
     */
    private void refreshTag() {
        consumer.unsubscribe(TOPIC);
        try {
            consumer.subscribe(TOPIC, getTags());
            logger.debug("刷新日志来源{}",getTags());
        } catch (MQClientException e) {
            logger.error("刷新日志来源失败");
            e.printStackTrace();
        }
    }

    private String getTags() {
        List<String> names = topicTagStrategy.getTag();
        Optional<String> result = names
                .stream()
                .reduce((s, s2) -> s + " || " + s2);
        return result.isPresent()?result.get():"";
    }
}
