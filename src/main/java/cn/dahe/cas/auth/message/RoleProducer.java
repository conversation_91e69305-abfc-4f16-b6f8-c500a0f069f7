package cn.dahe.cas.auth.message;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * <AUTHOR>
 * on 2020/4/30.
 */
@Component
@Lazy()
public class RoleProducer {

    @Value("${role.producer.topic}")
    private String topic;

    @Autowired
    @Qualifier("roleMqProducer")
    private DefaultMQProducer producer;

    @PostConstruct
    public void init() {
        try {
            producer.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PreDestroy
    public void onDestroy() {
        if (producer != null) {
            producer.shutdown();
        }
    }

    public void sendMessage(String tag, String body) {
        try {
            Message message = new Message(topic, tag, body.getBytes("utf-8"));
            producer.send(message);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
