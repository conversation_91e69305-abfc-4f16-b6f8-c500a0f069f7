package cn.dahe.cas.auth.message;

import cn.dahe.cas.auth.exception.SsoException;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * <AUTHOR>
 * on 2020/05/13
 */
@Component
public class RoleConsumer {

    @Autowired
    @Qualifier("roleMqConsumer")
    private DefaultMQPushConsumer consumer;

    @Value("${role.producer.topic}")
    private String topic;

    @Value("${log.tag}")
    private String tag;

    @PostConstruct
    public void init() {
        if (consumer == null) {
            throw new SsoException("consumer can not be null");
        }
        start();
    }

    @PreDestroy
    public void destroy() {
        stop();
    }

    private void start() {
        try {
            consumer.subscribe(topic, tag);
            consumer.start();
        } catch (MQClientException e) {
            e.printStackTrace();
        }
    }

    private void stop() {
        consumer.unsubscribe(topic);
        consumer.shutdown();
    }
}
