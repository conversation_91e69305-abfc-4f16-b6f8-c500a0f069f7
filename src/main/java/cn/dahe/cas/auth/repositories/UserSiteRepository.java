package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.UserSite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface UserSiteRepository extends JpaRepository<UserSite,Integer>{

    /**
     * 根据userId查找用户和站点的关系
     * @param userId
     * @return
     */
    List<UserSite> findByUserId(int userId);

    /**
     * 根据用户id和站点id查找相关记录
     */
    UserSite findByUserIdAndSiteId(int userId,int siteId);

    /**
     * 根据用户Id或者站点名称删除所有可访问的站点记录
     * @param userId
     * @param sid
     * @return
     */
    void deleteByUserIdOrSiteId(int userId, int sid);
    /**
     * 根据用户Id和站点名称删除可访问的站点记录
     * @param userId
     * @param sid
     * @return
     */
    void deleteByUserIdAndSiteId(int userId, int sid);

    void deleteAllByUserIdAndSiteIdIn(int userId,int[] sids);

    /**
     * 删除用户站点表中的某个站点所有记录
     * @param sid
     */
    void deleteAllBySiteId(int sid);

    /**
     * 删除某个用户的所有站点
     * @param userId
     */
    void deleteAllByUserId(int userId);
}
