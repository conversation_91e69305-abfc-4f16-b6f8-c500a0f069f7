package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.entity.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface RoleRepository extends JpaRepository<Role,Integer>, JpaSpecificationExecutor<Role> {

    /**
     * 通过用户id查找用户角色
     * @param userId
     * @return
     */
    @Query("select r from Role r,UserRole ur,User u where r.id = ur.roleId and ur.uid=u.id and u.id=?1")
    List<Role> findRoleByUserId(int userId);

    // todo 由于需要把子系统的权限集中在sso，接口全部进行系统隔离（区分siteID）******2020/04/26**********

    /**
     * 根据站点id和状态查找角色集合
     * @param siteId 站点id
     * @param status 状态
     * @return 角色集合
     */
    List<Role> findBySiteIdAndStatus(int siteId, Status status);

    /**
     * 根据站点id和sn查找角色
     * @param siteId 站点id
     * @param sn sn
     * @return 角色
     */
    Role findBySiteIdAndSn(int siteId, String sn);

    /**
     * 通过用户id查找用户角色
     * @param userId
     * @return
     */
    @Query("select r.sn from Role r,UserRole ur,User u where r.id = ur.roleId and ur.uid=u.id and r.status = 0 and u.id=?1 and r.siteId =?2")
    List<String> findSnByUserId(int userId, int siteId);
}
