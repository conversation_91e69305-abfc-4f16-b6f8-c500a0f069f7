package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.UserVipPayRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public interface UserVipPayRecordRepository extends JpaRepository<UserVipPayRecord,Integer>, JpaSpecificationExecutor<UserVipPayRecord> {

    @Query(value = "select uvpr.*,c.username customerName,uc.username createUsername,uu.username updateUsername from user_vip_pay_record uvpr  " +
            "inner join user c on uvpr.customer_id = c.uid " +
            "inner join user uc on uvpr.create_uid = uc.uid " +
            "inner join user uu on uvpr.update_uid = uu.uid " +
            "where 1=1 " +
            "and if( ?1 != '' , c.username like CONCAT('%', ?1,'%') , 1 = 1)  ORDER BY ?#{#pageable}"
            ,
            countQuery = "select count(tmp.id) from (" +
            "select uvpr.*,c.username customerName,uc.username createUsername,uu.username updateUsername from user_vip_pay_record uvpr  " +
            "inner join user c on uvpr.customer_id = c.uid " +
            "inner join user uc on uvpr.create_uid = uc.uid " +
            "inner join user uu on uvpr.update_uid = uu.uid " +
            "where 1=1 " +
            "and if( ?1 != '' , c.username like CONCAT('%', ?1,'%') , 1 = 1) ORDER BY ?#{#pageable}" +
            ") tmp"
            , nativeQuery = true)
    Page<Map<String,Object>> pageList(String customerName, Pageable pageable);

    @Query(value = "select min(start_time) from user_vip_pay_record where customer_id=?1",nativeQuery = true)
    Date getMinStartTime(int customerId);

    @Query(value = "select max(end_time) from user_vip_pay_record where customer_id=?1",nativeQuery = true)
    Date getMaxEndTime(int customerId);
}
