package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.constants.SiteDelete;
import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.entity.Site;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface SiteRepository extends JpaRepository<Site,Integer>,JpaSpecificationExecutor<Site> {
    /**
     * 通过url或者站点名称查找站点，名称和url都是唯一
     * @param url
     * @param name
     * @return
     */
    Site findByUrlOrName(String url,String name);

    /**
     * 通过站点名称查找站点
     * @param pageable
     * @param name
     * @return
     */
    Page<Site> findSiteByName(Pageable pageable, String name);

    /**
     * 通过站点名查找站点
     * @param siteName
     * @return
     */
    Site findByName(String siteName);

    /**
     * 根据站点id和状态显示获取站点列表
     * @param ids 站点id集合
     * @param show 是否显示
     * @return 站点集合
     */
    List<Site> findAllByIdInAndIsShowOrderByWeightDesc(List<Integer> ids, Status show);

    /**
     * 展示所有显示的站点
     * @param show 是否显示
     * @return 站点集合
     */
    List<Site> findAllByIsShowOrderByWeightDesc(Status show);

    List<Site> findAllByIsdelete(SiteDelete delete);

    /**
     * 根据标签查找系统
     * @param logTag 日志标签
     * @return 系统
     */
    Site findByLogTag(String logTag);
}
