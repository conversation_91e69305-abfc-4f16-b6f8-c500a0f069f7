package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.SmsRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface SmsRecordRepository extends JpaRepository<SmsRecord,Integer>{
    /**
     * 根据手机号查询短信记录
     * @param phone
     * @return
     */
    Page<SmsRecord> findByPhone(Pageable pageable,String phone);
}
