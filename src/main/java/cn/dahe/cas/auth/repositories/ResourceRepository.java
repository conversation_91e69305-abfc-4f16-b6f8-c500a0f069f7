package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.entity.Resource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ResourceRepository extends JpaRepository<Resource,Integer>,JpaSpecificationExecutor<Resource> {

    // todo 由于需要把子系统的权限集中在sso，接口全部进行系统隔离（区分siteID）******2020/04/26**********

    /**
     * 根据站点和授权字符查找权限
     * @param permission 授权字符
     * @param siteId 站点id
     * @return 权限
     */
    Resource getByPermissionAndSiteId(String permission, int siteId);

    /**
     * 根据系统id查找正常使用的权限
     * @param siteId 系统id
     * @param status 状态
     * @return 权限
     */
    List<Resource> getBySiteIdAndStatus(int siteId, Status status);

    /**
     * 根据pid查找权限
     * @param pid pid
     * @return 权限
     */
    List<Resource> getByPid(int pid);

    /**
     * 根据pid查找每个站点状态正常的权限
     * @param siteId 站点id
     * @param status 状态
     * @param pid 父id
     * @return 权限
     */
    List<Resource> findBySiteIdAndStatusAndPid(int siteId, Status status, int pid);

    /**
     * 通过用户id查找用户权限
     * @param uid 用户id
     * @param siteId 系统id
     * @return 所有权限字符
     */
    @Query("select re.permission from Resource re,RoleResource rr, Role r,UserRole ur, User u where re.id = rr.resId " +
            "and rr.roleId = r.id and r.id=ur.roleId and ur.uid = u.uid and re.status = 0 and r.status = 0 and u.id=?1 and re.siteId=?2")
    List<String> getPermissionByUid(int uid, int siteId);

    /**
     * 通过站点id查找用户权限
     * @param siteId 系统id
     * @return 所有权限字符
     */
    @Query("select re.permission from Resource re where re.status = 0 and re.siteId=?1")
    List<String> getPermission(int siteId);

    /**
     * 通过角色id查找角色下权限
     * @param roleId 角色id
     * @return 权限
     */
    @Query("select re from Resource re,RoleResource rr,Role r where re.id = rr.resId and rr.roleId=r.id and r.id=?1")
    List<Resource> getByRoleId(int roleId);
}
