package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.UserJob;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * on 2018/6/15.
 */
@Repository
public interface UserJobRepository extends JpaRepository<UserJob,Integer>{
    /**
     * 根据职务搜索对象
     * @param job 职务
     * @return 职务对象
     */
    UserJob findByJob(String job);
}
