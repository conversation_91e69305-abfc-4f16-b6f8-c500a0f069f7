package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.ForbidMobile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * on 2018/6/11.
 */
@Repository
public interface ForbidMobileRepository extends JpaRepository<ForbidMobile,Long> {
    /**
     * 根据禁用的手机号删除数据
     * @param phone 手机号
     */
    void deleteByPhone(String phone);

    /**
     * 根据手机号查询数据
     * @param phone 手机号
     * @return 被禁的手机号
     */
    ForbidMobile findByPhone(String phone);
}
