package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface UserRoleRepository extends JpaRepository<UserRole,Integer>{
    /**
     * 通过用户id查找用户角色
     * @param uid
     * @return
     */
    List<UserRole> findByUid(int uid);

    /**
     * 删除某个用户的所有角色关系
     * @param uid
     */
    void deleteAllByUid(int uid);

    /**************************************************************************************/

    /**
     * 根据用户id和角色id查找对应关系
     * @param uid 用户id
     * @param roleId 角色id
     * @return 用户角色对应关系
     */
    UserRole findByUidAndRoleId(int uid, int roleId);
}
