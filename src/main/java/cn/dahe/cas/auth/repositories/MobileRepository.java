package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.Mobile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface MobileRepository extends JpaRepository<Mobile,Integer>{
    /**
     * 通过手机号查找手机归属地信息
     * @param mobile
     * @return
     */
    Mobile findByMobile(String mobile);
}
