package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.entity.FocusPerson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface FocusPersonRepository extends JpaRepository<FocusPerson, Integer>, JpaSpecificationExecutor<FocusPerson> {
    /**
     * 根据创建用户查询关键词列表
     *
     * @param createUserId 创建用户id
     * @param status       状态
     * @return
     */
    List<FocusPerson> findAllByCreateUserIdAndStatus(int createUserId, Status status);
}
