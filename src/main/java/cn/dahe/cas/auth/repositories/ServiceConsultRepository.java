package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.entity.ServiceConsult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ServiceConsultRepository extends JpaRepository<ServiceConsult,Integer>, JpaSpecificationExecutor<ServiceConsult> {

    /**
     * 根据站点id和状态查找角色集合
     * @param status 状态
     * @return 角色集合
     */
    List<ServiceConsult> findByConsultUserName(Status status);

    ServiceConsult findByConsultUserIdAndType(int uid, int type);
}
