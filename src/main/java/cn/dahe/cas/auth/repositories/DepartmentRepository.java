package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Integer>, JpaSpecificationExecutor<Department> {

    /**
     * 检查名称和父ID是否已存在
     * @param name 部门名称
     * @param pid 父ID
     * @return 是否存在
     */
    boolean existsByNameAndPid(String name, Integer pid);

    /**
     * 检查名称和父ID是否已存在（排除指定ID）
     * @param name 部门名称
     * @param pid 父ID
     * @param id 排除的ID
     * @return 是否存在
     */
    boolean existsByNameAndPidAndIdNot(String name, Integer pid, Integer id);

    /**
     * 根据父ID和状态查询，按seq和id排序
     * @param pid 父ID
     * @param status 状态
     * @return 部门列表
     */
    List<Department> findByPidAndStatusOrderBySeqAscIdAsc(Integer pid, Integer status);

    /**
     * 根据状态查询，按seq和id排序
     * @param status 状态
     * @return 部门列表
     */
    List<Department> findByStatusOrderBySeqAscIdAsc(Integer status);

    /**
     * 根据父ID查询所有子部门
     * @param pid 父ID
     * @return 部门列表
     */
    List<Department> findByPid(Integer pid);

    /**
     * 统计父ID下的子部门数量
     * @param pid 父ID
     * @param status 状态
     * @return 数量
     */
    long countByPidAndStatus(Integer pid, Integer status);

    /**
     * 获取所有正常的机构信息
     * @param status
     * @return
     */
    List<Department> findByStatus(Integer status);
}