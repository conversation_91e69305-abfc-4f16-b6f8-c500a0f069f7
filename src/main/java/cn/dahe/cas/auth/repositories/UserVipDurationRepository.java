package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.UserVipDuration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public interface UserVipDurationRepository extends JpaRepository<UserVipDuration,Integer>, JpaSpecificationExecutor<UserVipDuration> {

    @Query(value = "" +
            "select tmp.*,st.description siteName,re.name roleName from " +
            "( " +
            "select uvd.*,c.username customerName,uu.username updateUsername from user_vip_duration uvd " +
            "            inner join user c on uvd.customer_id = c.uid " +
            "            inner join user uu on uvd.update_uid = uu.uid " +
            "            where 1=1 " +
            ") tmp " +
            "inner join site st on st.id=tmp.site_id" +
            "inner join role re on re.id=tmp.role_id " +
            "where 1=1 " +
            "and if( ?1 != '' , tmp.customerName like CONCAT('%', ?1,'%') , 1 = 1) ORDER BY ?#{#pageable}"
            ,
            countQuery = "select count(tmp.id) from ( " +
                    "select tmp.*,st.description siteName,re.name roleName from " +
                    "(" +
                    "select uvd.*,c.username customerName,uu.username updateUsername from user_vip_duration uvd " +
                    "            inner join user c on uvd.customer_id = c.uid " +
                    "            inner join user uu on uvd.update_uid = uu.uid " +
                    "            where 1=1 " +
                    ") tmp " +
                    "inner join site st on st.id=tmp.site_id" +
                    "inner join role re on re.id=tmp.role_id " +
                    "where 1=1 " +
                    "and if( ?1 != '' , tmp.customerName like CONCAT('%', ?1,'%') , 1 = 1) ORDER BY ?#{#pageable}" +
            ") tmp"
            , nativeQuery = true)
    Page<Map<String,Object>> pageList(String customerName, Pageable pageable);

    @Query(value = "select * from user_vip_duration where customer_id=?1 and site_id=?2 and role_id=?3",nativeQuery = true)
    List<UserVipDuration> getBySiteIdAndRoleId(int customerId, int siteId, int roleId);

    @Query(value = "select * from user_vip_duration where customer_id=?1",nativeQuery = true)
    List<UserVipDuration> getByCustomerId(int customerId);
}
