package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.Audit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by Administrator on 2017/10/19.
 */
@Repository
public interface AuditRepository extends JpaRepository<Audit,Long>{
    /**
     * 获取audit列表
     * @param ip
     * @param user
     * @param action
     * @param appcode
     * @param date
     * @return
     */
    List<Audit> getAllByClientIpAndUserNameAndActionAndApplicationCodeAndDateAfter(String ip,String user,String action,String appcode,String date);

    /**
     * 获取特定用户审计日志
     * @param user
     * @param action
     * @param appcode
     * @param date
     * @return
     */
    List<Audit> getAllByUserNameAndActionAndApplicationCodeAndDateAfterOrderByDateDesc(String user,String action,String appcode,Date date);

    /**
     * 根据用户删除审计日志
     * @param user
     * @param action
     * @param appcode
     */
    void deleteByUserNameAndActionAndApplicationCode(String user,String action,String appcode);
}
