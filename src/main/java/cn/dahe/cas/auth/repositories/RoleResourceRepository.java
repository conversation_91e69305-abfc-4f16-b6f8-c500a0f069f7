package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.RoleResource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface RoleResourceRepository extends JpaRepository<RoleResource,Integer>{

    /**通过角色id查找角色权限对应关系列表
     * @param roleId 角色id
     * @return 角色权限
     */
    List<RoleResource> findByRoleId(int roleId);

    /**
     * 根据角色id删除权限
     * @param roleId 角色id
     * @return 删除结果
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    int deleteByRoleId(int roleId);

    /**
     * 根据角色id和权限id查找对应关系
     * @param roleId 角色id
     * @param resId 权限id
     * @return 对应关系
     */
    RoleResource findByRoleIdAndAndResId(int roleId, int resId);
}
