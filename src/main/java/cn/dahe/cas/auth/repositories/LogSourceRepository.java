package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.entity.LogSource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * on 2018/03/28.
 */
@Repository
public interface LogSourceRepository extends JpaRepository<LogSource,Integer> {
    /**
     * 获取正常使用的日志来源
     * @param status
     * @return
     */
    List<LogSource> findLogSourceByStatus(UserFlag status);

    /**
     * 根据来源搜索
     * @param source
     * @return
     */
    LogSource findLogSourceBySource(String source);
}
