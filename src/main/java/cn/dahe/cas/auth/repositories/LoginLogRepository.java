package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.LoginLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/7 13:45
 * @Description:
 */
@Repository
public interface LoginLogRepository extends JpaRepository<LoginLog,Integer> ,JpaSpecificationExecutor<LoginLog> {

    @Query("select distinct ll.uid from LoginLog ll where ll.createDate>?1")
    List<Integer> findLoginUserAfter(Date date);
}
