package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.constants.UserFlag;
import cn.dahe.cas.auth.entity.User;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface UserRepository extends JpaRepository<User,Integer>,JpaSpecificationExecutor<User>{
    /**
     * 通过用户名查找用户，用户名唯一
     * @param username
     * @return
     */
    User findByUsername(String username);

    /**
     * 通过用户手机号查找用户，手机号唯一
     * @param phone
     * @return
     */
    User findByPhone(String phone);

    /**
     * 通过手机号或者用户名查找用户
     * @param username
     * @param phone
     * @return
     */
    User findUserByUsernameOrPhone(String username,String phone);

    /**
     * 通过手机号或者用户名查找用户
     * @param username
     * @param phone
     * @return
     */
    Page<User> findUserByUsernameOrPhone(Pageable pageable,String username,String phone);

    /**
     * 通过手机号或者用户名查找某个站点下用户
     * @param username
     * @param phone
     * @return
     */
    Page<User> findUserByUsernameOrPhoneAndSid(Pageable pageable,String username,String phone,String sid);

    /**
     * 通过手机号或者用户名查找用户
     * @param username
     * @param phone
     * @return
     */
    Page<User> findUserByTruenameContainingOrPhone(Pageable pageable,String username,String phone);

    /**
     * 通过手机号或者用户名查找某个站点下用户
     * @param username
     * @param phone
     * @return
     */
    @Query("select u from User u where (u.truename like %?1% or u.phone = ?2) and u.sid = ?3")
    Page<User> findUserBySidAndTruenameContainingOrPhone(String username,String phone,String sid,Pageable pageable);

    /**
     * 获取禁用的用户
     * @param pageable
     * @param flag
     * @return
     */
    Page<User> findUserByFlag(Pageable pageable, UserFlag flag);

    /**
     * 查找某个站点下的所有
     * @param sid
     * @return
     */
    List<User> findUserBySid(String sid);

    /**
     * 分页获取站点下用户
     * @param pageable
     * @param sid
     * @return
     */
    Page<User> findUserBySid(Pageable pageable,String sid);

    /**
     * 不分页获取某个站点下的所有
     * @param departmentId
     * @return
     */
    List<User> findUserByOrganization(int departmentId);

    /**
     * 分页获取组织机构下用户
     * @param pageable
     * @param departmentId
     * @return
     */
    Page<User> findUserByOrganization(Pageable pageable,int departmentId);

    /**
     * 通过手机号或者用户名查找某个组织机构下用户
     * @param username
     * @param phone
     * @return
     */
    Page<User> findUserByOrganizationAndTruenameContainingOrPhone(Pageable pageable,int departmentId,String username,String phone);

    @Query("SELECT u.uid FROM User u where u.flag = cn.dahe.cas.auth.constants.UserFlag.ALLOW")
    List<Integer> findAllId();

    User findByWxOpenId(String openid);

    @Query(value = "select count(*) from user where flag = 0",nativeQuery = true)
    int getUserCount();


    User  findBySynchronizeId(String synchronizeId);
}
