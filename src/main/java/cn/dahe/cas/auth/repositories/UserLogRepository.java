package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.domain.UserLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * on 2020/5/7.
 */
@Repository
public interface UserLogRepository extends MongoRepository<UserLog, String> {
    /**
     * 根据用户id查找记录
     * @param id id
     * @return 用户日志
     */
    List<UserLog> findByUserId(int id);
}
