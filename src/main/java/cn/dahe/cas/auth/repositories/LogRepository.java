package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.Log;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface LogRepository extends JpaRepository<Log,Integer>,JpaSpecificationExecutor<Log> {
    /**
     * 根据截止时间为date的删除数据
     * @param date 时间
     */
    void deleteByOperateDateLessThan(Date date);

    /**
     * 查找id最大的数据
     * @return 日志对象
     */
    Log findTopByOrderByIdDesc();

    /**
     * 删除小于id的数据
     * @param id id
     */
    void deleteByIdLessThan(int id);
}
