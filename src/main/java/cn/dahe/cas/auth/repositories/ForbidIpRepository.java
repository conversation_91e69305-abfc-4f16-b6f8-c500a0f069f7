package cn.dahe.cas.auth.repositories;

import cn.dahe.cas.auth.entity.ForbidIp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * on 2018/6/11.
 */
@Repository
public interface ForbidIpRepository extends JpaRepository<ForbidIp,Long> {
    /**
     * 根据禁用的ip地址删除数据
     * @param ipAddress ip地址
     */
    void deleteByIpAddress(String ipAddress);

    /**
     * 根据ip地址查询数据
     * @param ipAddress IP地址
     * @return 被禁的数据
     */
    ForbidIp findByIpAddress(String ipAddress);
}
