package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.constants.ErrorCode;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import cn.dahe.cas.auth.exception.*;
import org.apache.shiro.authc.AuthenticationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * <AUTHOR>
 */
@ControllerAdvice
public class CommonControllerAdvice{

    private static final Logger log = LoggerFactory.getLogger(CommonControllerAdvice.class);

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public JsonResult exception(BindException e){
        String field = e.getBindingResult().getFieldError().getField();
        String msg=e.getBindingResult().getFieldError().getDefaultMessage();
        return ResultUtil.fail(msg);
    }

    @ExceptionHandler(AuthenticationException.class)
    @ResponseBody
    public JsonResult notAuth(){
        return ResultUtil.fail("登录失败");
    }

    @ExceptionHandler(ShouldLoginException.class)
    @ResponseBody
    public JsonResult tipLogin(){
        return ResultUtil.fail("需要登录",2);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public JsonResult constraTip(ConstraintViolationException e){
        Set<ConstraintViolation<?>> cs = e.getConstraintViolations();
        ConstraintViolation c=cs.iterator().next();
        return ResultUtil.fail(c.getMessage());
    }

    @ExceptionHandler(SsoException.class)
    @ResponseBody
    public JsonResult sso(SsoException exception){
        ErrorCode code = exception.getCode();
        if(code == ErrorCode.NORMAL&& !StringUtils.isEmpty(exception.getMessage())){
            return ResultUtil.fail(exception.getMessage());
        }
        return ResultUtil.fail(code.getCodeMsg(),code.getCode());
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public JsonResult all(Exception e){
        if (!e.getMessage().contains("getWriter()")) {
            e.printStackTrace();
        }
        log.error("happen error {} ",e.getMessage());
        return ResultUtil.fail("失败");
    }
}
