package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.annotion.AccessLimit;
import cn.dahe.cas.auth.exception.AccessLimitException;
import cn.dahe.cas.auth.service.CacheService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 对方法的调用频率进行限制，主要用于对敏感接口的保护
 * 简单的记录接口调用ip，超过限制后进行接口访问限制
 * 该方式对于同一出口ip的情形有弊端
 */
@Component
@Aspect
public class AccessLimitAspect {

    private static transient Logger logger = LoggerFactory.getLogger(AccessLimitAspect.class);

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private RedisTemplate<String,Integer> redisTemplate;

    @Autowired
    private CacheService cacheService;

    @Pointcut("@annotation(cn.dahe.cas.auth.annotion.AccessLimit)")
    public void limit(){

    }

    /**
     * 在方法访问之前进行判断，如果超频，直接抛出异常
     * ip加其他限定参数
     * 通过limitParams获取到限定参数进行匹配
     */
    @Before("limit()")
    public void beforeAccess(JoinPoint joinPoint){
        if(request==null){
            logger.debug("无HttpServletRequest可用，忽略");
            return;
        }
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        AccessLimit limit = method.getAnnotation(AccessLimit.class);
        int limitCount = limit.count();
        long interval = limit.time();
        long limitTime = limit.limitTime();
        String url = request.getRequestURI();
        String msg = limit.msg();
        //基于ip对url的访问进行控制
        String key = cacheService.getKey(joinPoint,signature);
//        String key = getKey(joinPoint,signature);
        ValueOperations<String,Integer> valueOperations = redisTemplate.opsForValue();
        int count = 0;
        if(valueOperations.getOperations().hasKey(key)){
            count = valueOperations.get(key);
        }
        if(count>=limitCount){
            valueOperations.set(key,++count,limitTime, TimeUnit.SECONDS);
            if(StringUtils.isEmpty(msg)){
               msg =  "对["+url+"]访问频率过快";
            }
            throw new AccessLimitException(msg+",已经被禁用["+limitTime+"秒]");
        }
        valueOperations.set(key,++count,interval, TimeUnit.SECONDS);
    }
}
