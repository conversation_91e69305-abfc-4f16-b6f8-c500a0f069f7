package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.annotion.CurrentUser;
import cn.dahe.cas.auth.exception.SsoException;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@Component("userParamResolver")
public class UserParamResolver implements HandlerMethodArgumentResolver{

    @Autowired
    private SecurityManager securityManager;

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(CurrentUser.class);
    }

    /**
     * 此处实现两种获取当前用户的方法，一种基于subject，另一种基于cas的session
     * 蛋疼，sso不清纯了
     */
    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        SecurityUtils.setSecurityManager(securityManager);
        CurrentUser annotation = methodParameter.getParameterAnnotation(CurrentUser.class);
        boolean authenticated = SecurityUtils.getSubject().isAuthenticated();
        //在用户未认证且允许返回null值情况下执行
        if(annotation.allowNull()&&!authenticated){
            return null;
        }
        if(!authenticated){
            throw new SsoException("当前登录用户获取失败");
        }
        Session session = SecurityUtils.getSubject().getSession();
        Collection<Object> attributes = session.getAttributeKeys();
        attributes.size();
        return session.getAttribute("user");
    }
}
