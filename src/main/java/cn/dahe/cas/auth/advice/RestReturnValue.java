package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.annotion.RestResponseBody;
import cn.dahe.cas.auth.dto.JsonResult;
import cn.dahe.cas.auth.dto.ResultUtil;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Component
public class RestReturnValue implements HandlerMethodReturnValueHandler {

    @Autowired
    private HttpMessageConverter converter;

    @Override
    public boolean supportsReturnType(MethodParameter returnType) {
        return returnType.getMethodAnnotation(RestResponseBody.class)!=null;
    }

    @Override
    public void handleReturnValue(Object o, MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest) throws Exception {
        modelAndViewContainer.setRequestHandled(true);
        if(o==null||o.equals(true)||o instanceof Void){
            o=new Object();
        }
        JsonResult jsonResult = ResultUtil.success(o);
        RestResponseBody annotation = methodParameter.getMethodAnnotation(RestResponseBody.class);
        if(annotation.jsonp()){
            if(!allowJsonp(nativeWebRequest.getNativeRequest(HttpServletRequest.class),annotation)){
                //throw new SsoException("域名不允许");
            }
            //对jsonp进行处理
            String result = JSON.toJSONString(jsonResult);
            //hack处理，对jsonp的特殊处理，暂未找到更好方法
            if(o instanceof JsonResult){
                result = JSON.toJSONString(o);
            }
            String callback = annotation.callback();
            String jsonp = callback+"("+result+")";
            HttpServletResponse response = (HttpServletResponse) nativeWebRequest.getNativeResponse();
            response.setContentType(MediaType.APPLICATION_JSON.getType());
            response.getWriter().write(jsonp);
        }else{
            converter.write(jsonResult, MediaType.APPLICATION_JSON,createOutputMessage(nativeWebRequest));
        }
    }

    private boolean allowJsonp(HttpServletRequest request,RestResponseBody annotation){
        String[] referers = annotation.referers();
        String referer = request.getHeader("referer");
        if(referer==null){
            return false;
        }
        //无法进行预编译也是难受
        for(String regex:referers){
            if(referer.matches(regex)){
                return true;
            }
        }
        return false;
    }

    protected ServletServerHttpResponse createOutputMessage(NativeWebRequest webRequest) {
        HttpServletResponse response = webRequest.getNativeResponse(HttpServletResponse.class);
        Assert.state(response != null, "No HttpServletResponse");
        return new ServletServerHttpResponse(response);
    }

}
