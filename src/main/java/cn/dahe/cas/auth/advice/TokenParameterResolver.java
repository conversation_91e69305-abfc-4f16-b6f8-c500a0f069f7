package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.annotion.TokenParameters;
import cn.dahe.cas.auth.security.TokenSecurity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/2/22
 * @createTime 12:25
 * @info 解析TokenParameters标记
 */
@Component("tokenParameterResolver")
public class TokenParameterResolver  implements HandlerMethodArgumentResolver {

    @Autowired
    private TokenSecurity tokenSecurity;

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(TokenParameters.class);
    }

    /**
     * 暂时未使用具体的用户参数进行token生成
     * @param methodParameter
     * @param modelAndViewContainer
     * @param nativeWebRequest
     * @param webDataBinderFactory
     * @return
     * @throws Exception
     */
    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        return tokenSecurity.tokenParameters();
    }
}
