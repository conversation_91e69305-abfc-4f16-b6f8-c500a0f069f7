package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.annotion.LogAction;
import cn.dahe.cas.auth.constants.LogType;
import cn.dahe.cas.auth.domain.Log;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.message.LogConsumer;
import cn.dahe.cas.auth.service.SystemLogService;
import cn.dahe.cas.auth.util.IpUtils;
import cn.dahe.cas.auth.util.ParamsUtil;
import cn.dahe.cas.auth.util.SsoStringUtil;
import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.MQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * 通用操作日志，对每个控制器的调用进行日志记录，包括操作人员以及操作信息
 */
@Component
@Aspect
public class SystemLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(SystemLogAspect.class);

    @Autowired
    @Qualifier("logMqProducer")
    private MQProducer mqProducer;

    @Qualifier("ssoThreadPoolTaskExecutor")
    @Autowired
    private TaskExecutor taskExecutor;

    @Autowired(required=false)
    private HttpServletRequest request;

    @Autowired
    private SystemLogService logService;

    @Value("${log.tag}")
    private String logTag;


    @PostConstruct
    public void init(){
        try {
            mqProducer.start();
        } catch (MQClientException e) {
            e.printStackTrace();
        }
    }

    /**
     * Controller层切点 注解拦截
     */
    @Pointcut("@annotation(cn.dahe.cas.auth.annotion.LogAction)")
    public void controllerAspect(){}

    @Around("controllerAspect()")
    public Object doLogRecord(ProceedingJoinPoint proceedingJoinPoint) throws Throwable{
        Date operateDate=new Date();
        Log log = generateLog(proceedingJoinPoint);
        log.setOperateDate(operateDate);
        try {
            Object[] args = proceedingJoinPoint.getArgs();
            Object result = proceedingJoinPoint.proceed(args);
            return result;
        } catch (Throwable throwable) {
            log.setType(LogType.ERROR);
            log.setException(SsoStringUtil.safeSub(throwable.getMessage(),0,254));
            throw throwable;
        }finally {
            Date endDate=new Date();
            log.setTimeout(endDate.getTime()-operateDate.getTime());
            //taskExecutor.execute(() -> {
            //    String json = JSON.toJSONString(log);
            //    Message message = new Message(LogConsumer.TOPIC,logTag,json.getBytes(Charset.forName("UTF-8")));
            //    try {
            //        mqProducer.sendOneway(message);
            //    }catch (Exception exception){
            //        logger.error("日志发送失败{}",exception.getMessage());
            //    }
            //});
            logService.add(log);
        }
    }

    private Log generateLog(JoinPoint joinPoint){
        User user = null;
        try {
            Subject subject = SecurityUtils.getSubject();
            user = (User) subject.getSession().getAttribute("user");
        }catch (Exception e){

        }
        String title="";
        String remoteAddress = IpUtils.getRemoteIp(request);
        int serverPort = request.getServerPort();
        String remoteAddr = remoteAddress + ":" + serverPort;
        String requestUri=request.getRequestURI();
        String method=request.getMethod();
        Map<String,String[]> params=request.getParameterMap();
        try {
            title=getControllerMethodDescription(joinPoint);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log log = Log.builder().source(logTag).title(title).type(LogType.NORMAL).remoteAddress(remoteAddr)
                .requestUri(requestUri).method(method).paramters(ParamsUtil.getParamsString(params)).build();
        if(user!=null){
            log.setUid(user.getUid());
            log.setUsername(user.getUsername());
        }
        return log;
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param joinPoint 切点
     * @return discription
     */
    public static String getControllerMethodDescription(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        LogAction controllerLog = method
                .getAnnotation(LogAction.class);
        return controllerLog.action();
    }
}
