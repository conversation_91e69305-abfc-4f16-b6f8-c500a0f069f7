package cn.dahe.cas.auth.advice;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/2/3
 * @createTime 10:12
 * @info 由于spring mvc中对自定义HandlerMethodReturnValueHandler的处理优先级低于系统默认的实现
 * 因此如果直接使用spring mvc的常规配置会造成对String和void返回值的controller method处理被系统
 * 默认的ViewNameHandlerMethodReturnValueHandler拦截
 * 使用直接更改系统RequestMappingHandlerAdapter的方式将自定义的HandlerMethodReturnValueHandler
 * 直接切入系统默认处理器队首
 * 过于hack的方法，修改请三思
 */
public class CustomRequestMappingHandlerAdapter extends RequestMappingHandlerAdapter{

    @Autowired
    private RestReturnValue restReturnValue;

    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();
        List<HandlerMethodReturnValueHandler> defaults = getReturnValueHandlers();
        List<HandlerMethodReturnValueHandler> custom = new ArrayList<>();
        custom.addAll(defaults);
        custom.add(0,restReturnValue);
        setReturnValueHandlers(custom);
    }
}
