package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.security.TokenSecurity;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/29
 * @createTime 10:05
 * @info 第三方服务接口token检验
 */
@Component
@Aspect
public class TokenCheckAspect {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private TokenSecurity tokenSecurity;

    public static final String TOKEN_HEADER = "token";

    @Pointcut("@annotation(cn.dahe.cas.auth.annotion.TokenCheck)")
    public void limit(){
    }

    /**
     * 检测接口请求token合法性
     * @param joinPoint
     */
    @Before("limit()")
    public void checkToken(JoinPoint joinPoint){
        String sign = request.getHeader(TOKEN_HEADER);
        if(SsoStringUtil.isBlank(sign)||!tokenSecurity.validateToken(sign)){
            throw new SsoException("token校验失败");
        }
    }
}
