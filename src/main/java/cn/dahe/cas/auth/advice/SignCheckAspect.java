package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.annotion.SignCheck;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.security.ApiSecurity;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 签名检测，用于sso中第三方服务的认证上，例如手机号查询和ip查询
 * <AUTHOR>
 */
@Component
@Aspect
public class SignCheckAspect {

    @Autowired
    private ApiSecurity apiSecurity;

    @Pointcut("@annotation(cn.dahe.cas.auth.annotion.SignCheck)")
    public void limit(){
    }

    @Before("limit()")
    public void checkSign(JoinPoint joinPoint){
        //必须包含sign字段
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] names=signature.getParameterNames();
        Method method = signature.getMethod();
        SignCheck signCheck = method.getAnnotation(SignCheck.class);
        String signFiled = signCheck.signFiled();
        String sign = null;
        long timestap = 0L;
        List<String> checks = Arrays.asList(signCheck.checkParams());
        Map<String,String> params = new HashMap<>();
        for(int i=0;i<names.length;i++){
            if(names[i].equals(signFiled)){
                sign = args[i].toString();
            }
            if(names[i].equals(signCheck.timestapField())){
                timestap = Long.valueOf(String.valueOf(args[i]));
            }
            if(checks.contains(names[i])){
                params.put(names[i],String.valueOf(args[i]));
            }
        }
        if(apiSecurity.isExpirse(timestap)){
            throw new SsoException("签名过期或者失效");
        }
        String serverSign = apiSecurity.sign(params);
        if(!serverSign.equals(sign)){
            throw new SsoException("签名有误");
        }
    }
}
