package cn.dahe.cas.auth.advice;

import cn.dahe.cas.auth.annotion.Wap;
import org.springframework.core.MethodParameter;
import org.springframework.util.PatternMatchUtils;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/5
 * @createTime 10:55
 * @info
 */
public class WapViewHandlerReturnValue implements HandlerMethodReturnValueHandler{

    @Nullable
    private String[] redirectPatterns;

    @Nullable
    public String[] getRedirectPatterns() {
        return redirectPatterns;
    }

    public void setRedirectPatterns(@Nullable String[] redirectPatterns) {
        this.redirectPatterns = redirectPatterns;
    }

    @Override
    public boolean supportsReturnType(MethodParameter returnType) {
        return returnType.getMethodAnnotation(Wap.class)!=null;
    }

    @Override
    public void handleReturnValue(Object returnValue, MethodParameter returnType, ModelAndViewContainer mavContainer, NativeWebRequest webRequest) throws Exception {
        if(returnValue instanceof CharSequence){
            String viewName = returnValue.toString();
            mavContainer.setViewName(viewName);
            if (isRedirectViewName(viewName)) {
                mavContainer.setRedirectModelScenario(true);
            }
        }
    }

    protected boolean isRedirectViewName(String viewName) {
        return (PatternMatchUtils.simpleMatch(this.redirectPatterns, viewName) || viewName.startsWith("redirect:"));
    }
}
