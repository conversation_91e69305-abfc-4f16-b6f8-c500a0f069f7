package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.constants.Status;
import org.springframework.core.convert.converter.Converter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/15
 * @createTime 16:31
 * @info
 */
public class StatusConverter implements Converter<String,Status>{

    @Override
    public Status convert(String source) {
        int status = Status.ALLOW.getStatus();
        try {
            status = Integer.valueOf(source);
        }catch (NumberFormatException exception){
        }
        return Status.get(status);
    }
}
