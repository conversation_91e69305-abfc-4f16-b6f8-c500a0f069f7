package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.dto.SiteDto;
import cn.dahe.cas.auth.dto.SiteTreeDto;
import cn.dahe.cas.auth.dto.UserProfileDto;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.SiteTreeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;

import static cn.dahe.cas.auth.util.Sm4Util.decryptEcb;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/27 12:23
 * @Description:
 */
public class UserProfileDtoConverter implements Converter<User,UserProfileDto>{

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    private SiteTreeService siteTreeService;

    @Override
    public UserProfileDto convert(User user) {
        UserProfileDto userProfileDto = new UserProfileDto();
        BeanUtils.copyProperties(user, userProfileDto);
        if (StringUtils.isNotBlank(user.getSid())) {
            SiteDto site = siteTreeService.getSite(user.getSid());
            userProfileDto.setSiteName(site == null ? "" : site.getSiteName());
            SiteTreeDto oneDepart = siteTreeService.getOneDepart(String.valueOf(user.getOrganization()));
            userProfileDto.setDepartmentName(oneDepart == null ? "" : oneDepart.getName());
        }
        // 信息解密
        try {
            userProfileDto.setTruename(decryptEcb(sm4Key, user.getTruename()));
            userProfileDto.setPhone(decryptEcb(sm4Key, user.getPhone()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userProfileDto;
    }
}
