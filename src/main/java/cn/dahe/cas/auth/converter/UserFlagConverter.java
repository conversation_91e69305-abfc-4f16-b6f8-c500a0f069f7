package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.constants.UserFlag;
import org.springframework.core.convert.converter.Converter;

import javax.persistence.AttributeConverter;
import javax.persistence.Convert;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/15
 * @createTime 16:44
 * @info 由于UserFlag中出现了-1，无法直接使用ORDINAL进行映射，因此实现该类
 */
@Convert
public class UserFlagConverter implements AttributeConverter<UserFlag,Integer> {

    @Override
    public Integer convertToDatabaseColumn(UserFlag attribute) {
        return attribute.getFlag();
    }

    @Override
    public UserFlag convertToEntityAttribute(Integer dbData) {
        return UserFlag.fromFlag(dbData);
    }
}
