package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.constants.SiteStatus;
import org.springframework.core.convert.converter.Converter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/14
 * @createTime 10:26
 * @info
 */
public class SiteStatusConverter implements Converter<String,SiteStatus>{

    @Override
    public SiteStatus convert(String source) {
        int status = SiteStatus.ALLOW.getStatus();
        try {
            status = Integer.valueOf(source);
        }catch (NumberFormatException exception){
        }
        return SiteStatus.get(status);
    }
}
