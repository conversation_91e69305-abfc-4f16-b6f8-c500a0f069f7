package cn.dahe.cas.auth.converter;

import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;

import javax.persistence.Convert;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/17
 * @createTime 16:37
 * @info 通用的string到enum的转换
 */
public class StringToEnumConverterFactory implements ConverterFactory<String,Enum>{

    @Override
    public <T extends Enum> Converter<String, T> getConverter(Class<T> targetType) {
        return new StringToEnumConverter<>(targetType);
    }

    private final class StringToEnumConverter<T extends Enum> implements Converter<String, T> {

        private Class<T> enumType;

        public StringToEnumConverter(Class<T> enumType) {
            this.enumType = enumType;
        }

        @Override
        public T convert(String source) {
            int ordinal = 0;
            try {
                ordinal = Integer.valueOf(source);
            }catch (Exception e){
            }
            T[] values = enumType.getEnumConstants();
            for(T value:values){
                if(value.ordinal()==ordinal){
                    return value;
                }
            }
            return values[0];
        }
    }
}
