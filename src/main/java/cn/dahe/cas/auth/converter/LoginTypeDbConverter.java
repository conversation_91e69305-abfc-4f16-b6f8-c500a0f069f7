package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.constants.LoginType;

import javax.persistence.AttributeConverter;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/7 10:20
 * @Description:
 */
public class LoginTypeDbConverter implements AttributeConverter<LoginType,Integer>{
    @Override
    public Integer convertToDatabaseColumn(LoginType attribute) {
        if(attribute==null){
            return null;
        }
        return attribute.getCode();
    }

    @Override
    public LoginType convertToEntityAttribute(Integer dbData) {
        if(dbData==null){
            return null;
        }
        return LoginType.getByType(dbData);
    }
}
