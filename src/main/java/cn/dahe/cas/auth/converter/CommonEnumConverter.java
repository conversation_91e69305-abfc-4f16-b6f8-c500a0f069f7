package cn.dahe.cas.auth.converter;


import cn.dahe.cas.auth.constants.EnumInterface;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;

/**
 * @Author: 杨振雨
 * @Date: 2018/12/4 09:56
 * @Description:
 */
public class CommonEnumConverter implements ConverterFactory<String,EnumInterface> {


    @Override
    public <T extends EnumInterface> Converter<String, T> getConverter(Class<T> targetType) {
        return new StringToEnumConverter<>(targetType);
    }

    private final class StringToEnumConverter<T extends EnumInterface> implements Converter<String, T> {

        private Class<T> enumType;

        StringToEnumConverter(Class<T> enumType) {
            this.enumType = enumType;
        }

        @Override
        public T convert(String source) {
            //首先将source转换为code
            int code = Integer.valueOf(source);
            //获取到所有枚举值
            T[] values = enumType.getEnumConstants();
            //根据枚举值的code进行枚举值的生成
            for(T value:values){
                if(value.getCode()==code){
                    return value;
                }
            }
            return null;
        }
    }
}
