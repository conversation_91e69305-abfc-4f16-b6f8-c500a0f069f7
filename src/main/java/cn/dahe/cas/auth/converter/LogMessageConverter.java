package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.domain.Log;
import com.alibaba.fastjson.JSONObject;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/22
 * @createTime 16:12
 * @info
 */
@Component
public class LogMessageConverter implements Converter<MessageExt,Log>{

    @Override
    public Log convert(MessageExt messageExt) {
        String tag = messageExt.getTags();
        String body = new String(messageExt.getBody(), Charset.forName("UTF-8"));
        try {
            Log log = JSONObject.parseObject(body,Log.class);
            log.setSource(tag);
            log.setId(null);
            return log;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}
