package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.constants.LogType;
import org.springframework.core.convert.converter.Converter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/22
 * @createTime 16:35
 * @info
 */
public class LogTypeConverter implements Converter<Integer,LogType> {

    @Override
    public LogType convert(Integer s) {
        for(LogType logType:LogType.values()){
            if(logType.ordinal()==s){
                return logType;
            }
        }
        return LogType.values()[0];
    }

}
