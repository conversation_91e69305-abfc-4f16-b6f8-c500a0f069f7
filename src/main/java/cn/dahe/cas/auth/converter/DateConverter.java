package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.core.convert.converter.Converter;

import javax.persistence.Convert;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @createDate 2018/1/23
 * @info 把String型转成Date
 */
@Convert
public class DateConverter implements Converter<String, Date> {
    @Override
    public Date convert(String s) {
        //兼容时间戳
        try {
            long time = Long.parseLong(s);
            return new Date(time);
        }catch (NumberFormatException e){

        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (SsoStringUtil.isNotBlank(s)){
                Date date = dateFormat.parse(s);
                return date;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }
}
