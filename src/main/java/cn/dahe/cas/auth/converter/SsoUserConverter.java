package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.external.entity.SsoUser;
import cn.dahe.cas.auth.service.SiteTreeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/27
 * @createTime 9:31
 * @info
 */
public class SsoUserConverter implements Converter<User,SsoUser> {

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Autowired
    private SiteTreeService siteTreeService;

    @Override
    public SsoUser convert(User user) {
        if(user==null){
            return null;
        }
        SsoUser ssoUser = new SsoUser();
        BeanUtils.copyProperties(user,ssoUser);
//        SiteDto site = siteTreeService.getSite(user.getSid());
//        if(site!=null){
//            ssoUser.setSiteName(site.getSiteName());
//        }
        // 转换之前已经解密，此处不需要信息解密
//        try {
//            ssoUser.setTruename(decryptEcb(sm4Key, user.getTruename()));
//            ssoUser.setPhone(decryptEcb(sm4Key, user.getPhone()));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return ssoUser;
    }
}
