package cn.dahe.cas.auth.converter;

import cn.dahe.cas.auth.dto.SsoUserDto;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.SiteService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import static cn.dahe.cas.auth.util.Sm4Util.decryptEcb;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/14 10:45
 * @Description:
 */
@Component
public class SsoUserDtoConverter implements Converter<User,SsoUserDto>{

    @Autowired
    private SiteService siteService;

    @Value("${sm4.security.key}")
    private String sm4Key;

    @Override
    public SsoUserDto convert(User source) {
        if(source==null){
            return null;
        }
        SsoUserDto ssoUserDto = new SsoUserDto();
        BeanUtils.copyProperties(source,ssoUserDto);
        // todo 信息解密
        try {
            ssoUserDto.setTruename(decryptEcb(sm4Key, ssoUserDto.getTruename()));
            ssoUserDto.setPhone(decryptEcb(sm4Key, ssoUserDto.getPhone()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        ssoUserDto.setSystems(siteService.getAccessSite(source.getUid()));
        return ssoUserDto;
    }
}
