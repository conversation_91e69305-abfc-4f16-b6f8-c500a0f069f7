package cn.dahe.cas.auth.cas;

import cn.dahe.cas.auth.config.RedisKey;
import cn.dahe.cas.auth.util.PageUtil;
import org.jasig.cas.services.AbstractRegisteredService;
import org.jasig.cas.services.RegisteredService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 使用redis作为ServiceRegistryDao实现
 * 直接以RegisteredService的id作为hash key，方便进行以id进行查找
 */
@Component
public class RedisServiceRegistryDao implements PageServiceRegistryDao{

    private static final Logger log = LoggerFactory.getLogger(RedisServiceRegistryDao.class);

    @Resource(name = "redisTemplate")
    private HashOperations<String,Long,RegisteredService> hashOperations;

    private String serviceRegistryKey = RedisKey.SERVICE_REGISTRY_KEY;

    @Override
    public RegisteredService save(RegisteredService registeredService) {
        log.debug("save RegisteredService name is {}",registeredService.getName());
        if(registeredService.getId()==RegisteredService.INITIAL_IDENTIFIER_VALUE&&registeredService instanceof AbstractRegisteredService){
            //默认直接以系统当前时间戳作为id，对于并发超过1000的情况会出现问题，但是理论上不会达到
            ((AbstractRegisteredService) registeredService).setId(getId());
        }
        hashOperations.put(serviceRegistryKey,registeredService.getId(),registeredService);
        return registeredService;
    }

    @Override
    public boolean delete(RegisteredService registeredService) {
        log.debug("delete RegisteredService name is {}",registeredService.getName());
        hashOperations.delete(serviceRegistryKey,registeredService.getId());
        return true;
    }

    @Override
    public List<RegisteredService> load() {
        //直接加载所有RegisteredService
        return hashOperations.values(serviceRegistryKey);
    }

    @Override
    public RegisteredService findServiceById(long id) {
        log.debug("find service by id {}",id);
        return hashOperations.get(serviceRegistryKey,id);
    }

    @Override
    public Page<RegisteredService> getServices(Pageable pageable) {
        List<RegisteredService> services = PageUtil.getPageForHash(pageable,hashOperations,serviceRegistryKey);
        return new PageImpl<>(services,pageable,hashOperations.size(serviceRegistryKey));
    }

    protected long getId(){
        return System.currentTimeMillis();
    }
}
