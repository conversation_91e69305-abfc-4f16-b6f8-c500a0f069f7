package cn.dahe.cas.auth.cas;

import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.UserService;
import org.jasig.cas.authentication.Credential;
import org.jasig.cas.authentication.HandlerResult;
import org.jasig.cas.authentication.PreventedException;
import org.jasig.cas.authentication.handler.support.AbstractPreAndPostProcessingAuthenticationHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.GeneralSecurityException;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/4 15:27
 * @Description: 针对微信登录进行认证，后期改造为通用形式
 */
@Component
public class WxAuthenticationHandler extends AbstractPreAndPostProcessingAuthenticationHandler {

    @Autowired
    private UserService userService;

    @Override
    protected HandlerResult doAuthentication(Credential credential) throws GeneralSecurityException, PreventedException {
        //做微信扫码登录记录
        OauthCredential oauthCredential = (OauthCredential) credential;
        String opneid = oauthCredential.getOpenid();
        User user = userService.getUserByWx(opneid);
        ((OauthCredential) credential).setId(String.valueOf(user.getUid()));
        //用户登录
        return createHandlerResult(credential, this.principalFactory.createPrincipal(String.valueOf(user.getUid())), null);
    }

    @Override
    public boolean supports(Credential credential) {
        return credential instanceof OauthCredential;
    }
}
