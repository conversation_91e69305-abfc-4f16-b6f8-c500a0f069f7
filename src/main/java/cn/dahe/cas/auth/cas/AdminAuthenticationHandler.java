package cn.dahe.cas.auth.cas;

import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.service.UserService;
import org.jasig.cas.authentication.Credential;
import org.jasig.cas.authentication.HandlerResult;
import org.jasig.cas.authentication.PreventedException;
import org.jasig.cas.authentication.handler.support.AbstractPreAndPostProcessingAuthenticationHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.GeneralSecurityException;

/**
 * @Author: zgf
 * @Date: 2025/06/06 15:27
 * @Description: 超管登录形式的认证
 */
@Component
public class AdminAuthenticationHandler extends AbstractPreAndPostProcessingAuthenticationHandler {

    @Autowired
    private UserService userService;

    @Override
    protected HandlerResult doAuthentication(Credential credential) throws GeneralSecurityException, PreventedException {
        //超管登录记录
        AdminOauthCredential adminOauthCredential = (AdminOauthCredential) credential;
        String uid = adminOauthCredential.getId();
        User user = userService.get(Integer.valueOf(uid));
        ((AdminOauthCredential) credential).setId(String.valueOf(user.getUid()));
        //用户登录
        return createHandlerResult(credential, this.principalFactory.createPrincipal(String.valueOf(user.getUid())), null);
    }

    @Override
    public boolean supports(Credential credential) {
        return credential instanceof AdminOauthCredential;
    }
}
