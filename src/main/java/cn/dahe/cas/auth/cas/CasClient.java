package cn.dahe.cas.auth.cas;

import org.pac4j.cas.authorization.DefaultCasAuthorizationGenerator;
import org.pac4j.cas.config.CasConfiguration;
import org.pac4j.cas.credentials.authenticator.CasAuthenticator;
import org.pac4j.cas.credentials.extractor.TicketAndLogoutRequestExtractor;
import org.pac4j.cas.logout.CasLogoutHandler;
import org.pac4j.cas.redirect.CasRedirectActionBuilder;
import org.pac4j.core.client.IndirectClient;
import org.pac4j.core.context.WebContext;
import org.pac4j.core.credentials.TokenCredentials;
import org.pac4j.core.logout.CasLogoutActionBuilder;
import org.pac4j.core.profile.CommonProfile;
import org.pac4j.core.util.CommonHelper;

/**
 * Created with IntelliJ IDEA.
 * User: LiYunLong
 * Date: 2022/3/10
 * Time: 9:38
 * Description:
 */
public class CasClient extends IndirectClient<TokenCredentials, CommonProfile> {
    private CasConfiguration configuration = new CasConfiguration();

    public CasClient() {
    }

    public CasClient(final CasConfiguration configuration) {
        setConfiguration(configuration);
    }

    @Override
    protected void clientInit(final WebContext context) {
        CommonHelper.assertNotNull("configuration", configuration);
        configuration.setUrlResolver(this.getUrlResolver());
        configuration.init(context);
        defaultRedirectActionBuilder(new CasRedirectActionBuilder(configuration, callbackUrl ));
        defaultCredentialsExtractor(new TicketAndLogoutRequestExtractor(configuration, getName()));
        defaultAuthenticator(new MyCasAuthenticator(configuration, callbackUrl));
        defaultLogoutActionBuilder(new CasLogoutActionBuilder<>(configuration.getPrefixUrl() + "logout", configuration.getPostLogoutUrlParameter()));
        addAuthorizationGenerator(new DefaultCasAuthorizationGenerator<>());
    }

    @Override
    public void notifySessionRenewal(final String oldSessionId, final WebContext context) {
        final CasLogoutHandler casLogoutHandler = configuration.getLogoutHandler();
        if (casLogoutHandler != null) {
            casLogoutHandler.renewSession(oldSessionId, context);
        }
    }

    public CasConfiguration getConfiguration() {
        return configuration;
    }

    public void setConfiguration(final CasConfiguration configuration) {
        this.configuration = configuration;
    }

    @Override
    public String toString() {
        return CommonHelper.toString(this.getClass(), "name", getName(), "callbackUrl", this.callbackUrl,
                "urlResolver", this.urlResolver, "ajaxRequestResolver", getAjaxRequestResolver(),
                "redirectActionBuilder", getRedirectActionBuilder(), "credentialsExtractor", getCredentialsExtractor(),
                "authenticator", getAuthenticator(), "profileCreator", getProfileCreator(),
                "logoutActionBuilder", getLogoutActionBuilder(), "configuration", this.configuration);
    }
}
