package cn.dahe.cas.auth.cas;

import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.service.LockUserService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.PageUtil;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.web.support.AbstractThrottledSubmissionHandlerInterceptorAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

import static cn.dahe.cas.auth.util.Sm4Util.decryptEcb;
import static cn.dahe.cas.auth.util.Sm4Util.encryptEcb;

/**
 * @Author: 杨振雨
 * @Date: 2018/11/12 10:11
 * @Description: 使用redis实现登录错误锁定
 */
@Component("redisAuthenticationThrottle")
public class RedisThrottledSubmissionByUsernameHandlerInterceptorAdapter extends AbstractThrottledSubmissionHandlerInterceptorAdapter implements InitializingBean,LockUserService {

    private static final Logger logger = LoggerFactory.getLogger(RedisThrottledSubmissionByUsernameHandlerInterceptorAdapter.class);

    private static final Long LOCK_PERIOD = 60*60*1000L;
    private static final String LOCK_KEY = "sso:lock:login";
    private static final String THROTTLED_KEY = "sso:login:throttled";

    @Resource(name = "redisTemplate")
    private ZSetOperations<String,String> lockUser;

    @Resource(name = "externalRedisTemplate")
    private ValueOperations<String,Integer> redisTemplate;

    @Autowired
    private UserService userService;

    @Value("${sm4.security.key}")
    private String sm4Key;


    @Override
    protected void recordSubmissionFailure(HttpServletRequest request) {

        String key = constructKey(request);
        //直接将错误次数加1并更新其过期时间，走事务
        if(StringUtils.isNotBlank(key)){
            boolean first = redisTemplate.setIfAbsent(key,1);
            redisTemplate.getOperations().expire(key,5, TimeUnit.MINUTES);
            if(!first){
                redisTemplate.increment(key,1);
            }
        }

        //将错误登录记录到redis中
    }

    @Override
    protected boolean exceedsThreshold(HttpServletRequest request) {
        //首先判断锁定列表里是否有该用户
        boolean locked = locked(request);
        if(locked){
            return true;
        }
        String key = constructKey(request);
        if(StringUtils.isBlank(key)){
            logger.debug("为何会出现无法获取用户的情况");
            return false;
        }
        Integer count = redisTemplate.get(key);
        boolean exceed = count!=null&&count>=3;
        if(exceed){
            //如果超过限制直接加入锁定列表，并以当前时间作为锁定时间，后期可作为过期依据使用
            lockUser.add(LOCK_KEY,userIdentify(request),System.currentTimeMillis());
        }
        return exceed;
    }


    /**
     * 构建限制key
     *
     * @param request
     * @return
     */
    protected String constructKey(final HttpServletRequest request) {
        String username = request.getParameter(getUsernameParameter());
        if (StringUtils.isBlank(username)) {
            logger.error("cas登录限制获取用户名为空");
            return null;
        }
        logger.error("cas登录限制获取用户名为{}", username);
        return THROTTLED_KEY+":"+username;
    }

    protected String userIdentify(final HttpServletRequest request){
        return request.getParameter(getUsernameParameter());
    }

    @Override
    protected String getName() {
        return "redisUsernameThrottled";
    }

    @Override
    public boolean unlock(int uid) {
        User user = userService.get(uid);
        if(user==null){
            throw new SsoException("用户不存在，怎么回事，好奇怪哦😯");
        }
        String phone = user.getPhone();
        // todo 解密
        try {
            phone = decryptEcb(sm4Key, user.getPhone());
        } catch (Exception e) {
            e.printStackTrace();
        }
        lockUser.remove(LOCK_KEY, phone);
        redisTemplate.getOperations().delete(THROTTLED_KEY+":"+phone);
        return true;
    }

    @Override
    public Page<User> getLockUsers(Pageable pageable) {
        //使用bitmap实现用户锁定状态，0为不锁定，1为锁定
        clean();
        Page<String> users = PageUtil.get(pageable,lockUser,LOCK_KEY);
        Page<User> map = users.map(source -> {
            try {
                // todo 加密
                source = encryptEcb(sm4Key, source);
                User user = userService.getUserByPhone(source);
                // todo 信息解密
                user.setTruename(decryptEcb(sm4Key, user.getTruename()));
                user.setPhone(decryptEcb(sm4Key, user.getPhone()));
                return user;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return new User();
        });
        return map;
    }

    /**
     * 清理过期key
     */
    private void clean(){
        long now = System.currentTimeMillis();
        //老数据起始点
        long old = now - LOCK_PERIOD;
        lockUser.removeRangeByScore(LOCK_KEY,0,old);
    }

    private boolean locked(HttpServletRequest request){
        String identify = userIdentify(request);
        Double score = lockUser.score(LOCK_KEY,identify);
        if(score==null){
            return false;
        }
        boolean locked = score + LOCK_PERIOD > System.currentTimeMillis();
        if(!locked){
            lockUser.remove(LOCK_KEY,identify);
        }
        return locked;
    }
}
