package cn.dahe.cas.auth.cas;

import org.aspectj.lang.JoinPoint;
import org.jasig.cas.CentralAuthenticationService;
import org.jasig.cas.audit.spi.PrincipalIdProvider;
import org.jasig.cas.audit.spi.TicketOrCredentialPrincipalResolver;
import org.jasig.cas.authentication.Authentication;
import org.jasig.cas.authentication.AuthenticationTransaction;
import org.jasig.cas.authentication.Credential;
import org.jasig.cas.ticket.InvalidTicketException;
import org.jasig.cas.ticket.ServiceTicket;
import org.jasig.cas.ticket.Ticket;
import org.jasig.cas.ticket.TicketGrantingTicket;
import org.jasig.cas.web.support.WebUtils;
import org.jasig.inspektr.common.spi.PrincipalResolver;
import org.pac4j.core.context.J2EContext;
import org.pac4j.core.profile.ProfileManager;
import org.pac4j.core.profile.UserProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/2/9
 * @createTime 14:14
 * @info 由于默认TicketOrCredentialPrincipalResolver使用了和新版本不兼容的jar，因此进行重写
 */
@Component("auditablePrincipalResolver")
public class SsoTicketOrCredentialPrincipalResolver extends TicketOrCredentialPrincipalResolver{

    /** Logger instance. */
    private static final Logger LOGGER = LoggerFactory.getLogger(SsoTicketOrCredentialPrincipalResolver.class);

    @Value("${st.key}")
    private String stKey;

    @Resource(name = "redisTemplate")
    private HashOperations<String,String,Ticket> ticketOperation;


    @NotNull
    @Resource(name="centralAuthenticationService")
    private CentralAuthenticationService centralAuthenticationService;

    @Autowired(required = false)
    @Qualifier("principalIdProvider")
    private PrincipalIdProvider principalIdProvider = new DefaultPrincipalIdProvider();
    /**
     * Instantiates a new Ticket or credential principal resolver.
     *
     * @param centralAuthenticationService the central authentication service
     * @since 4.1.0
     */
    public SsoTicketOrCredentialPrincipalResolver(CentralAuthenticationService centralAuthenticationService) {
        super(centralAuthenticationService);
    }

    /**
     * Resolve the principal from the join point given.
     *
     * @param joinPoint the join point
     * @return the principal id, or {@link PrincipalResolver#UNKNOWN_USER}
     */
    @Override
    protected String resolveFromInternal(final JoinPoint joinPoint) {
        final StringBuilder builder = new StringBuilder();

        final Object arg1 = joinPoint.getArgs()[0];
        if (arg1.getClass().isArray()) {
            final Object[] args1AsArray = (Object[]) arg1;
            resolveArguments(builder, args1AsArray);
        } else {
            builder.append(resolveArgument(arg1));
        }

        return builder.toString();

    }

    /**
     * Resolve the join point argument.
     *
     * @param arg1 the arg
     * @return the resolved string
     */
    private String resolveArgument(final Object arg1) {
        LOGGER.debug("Resolving argument [{}] for audit", arg1.getClass().getSimpleName());

        if (arg1 instanceof AuthenticationTransaction) {
            final AuthenticationTransaction transaction = AuthenticationTransaction.class.cast(arg1);
            return resolveArguments(new StringBuilder(), transaction.getCredentials());
        }
        if (arg1 instanceof Credential) {
            return arg1.toString();
        }
        if (arg1 instanceof String) {
            try {
                final Ticket ticket = this.centralAuthenticationService.getTicket((String) arg1, Ticket.class);
                Authentication authentication = null;
                if (ticket instanceof ServiceTicket) {
                    authentication = ServiceTicket.class.cast(ticket).getGrantingTicket().getAuthentication();
                    ticketOperation.delete(stKey,arg1.toString());
                } else if (ticket instanceof TicketGrantingTicket) {
                    authentication = TicketGrantingTicket.class.cast(ticket).getAuthentication();
                }
                return this.principalIdProvider.getPrincipalIdFrom(authentication);
            } catch (final InvalidTicketException e) {
                LOGGER.trace(e.getMessage(), e);
            }
            LOGGER.debug("Could not locate ticket [{}] in the registry", arg1);
        }
        return getAuthenticatedUsername();
    }

    private String resolveArguments(final StringBuilder builder, final Collection args1AsArray) {
        for (final Object arg: args1AsArray) {
            builder.append(resolveArgument(arg));
        }
        return builder.toString();
    }

    private String resolveArguments(final StringBuilder builder, final Object[] args1AsArray) {
        for (final Object arg: args1AsArray) {
            builder.append(resolveArgument(arg));
        }
        return builder.toString();
    }

    /**
     * Return the username of the authenticated user (based on pac4j security).
     *
     * @return the authenticated username.
     */
    public String getAuthenticatedUsername() {
        final HttpServletRequest request = WebUtils.getHttpServletRequest();
        final HttpServletResponse response = WebUtils.getHttpServletResponse();
        if (request != null && response != null) {
            final J2EContext context = new J2EContext(request, response);
            final ProfileManager manager = new ProfileManager(context);
            final Optional<UserProfile> profile = manager.get(true);
            if (profile.isPresent()) {
                final String id = profile.get().getId();
                if (id != null) {
                    return id;
                }
            }
        }
        return UNKNOWN_USER;
    }

    /**
     * Default implementation that simply returns principal#id.
     */
    static class DefaultPrincipalIdProvider implements PrincipalIdProvider {

        @Override
        public String getPrincipalIdFrom(final Authentication authentication) {
            return authentication.getPrincipal().getId();
        }
    }
}
