package cn.dahe.cas.auth.cas;

import cn.dahe.cas.auth.constants.LoginType;
import org.jasig.cas.authentication.Credential;

import java.io.Serializable;

/**
 * @Author: zgf
 * @Date: 2025/06/06 15:27
 * @Description: 超管登录形式
 */
public class AdminOauthCredential implements Credential,Serializable{

    private static final long serialVersionUID = -6473820022637017235L;

    private String id;

    private LoginType loginType;

    public AdminOauthCredential(String id, LoginType loginType) {
        this.id = id;
        this.loginType = loginType;
    }

    public LoginType getLoginType() {
        return loginType;
    }

    public void setLoginType(LoginType loginType) {
        this.loginType = loginType;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String getId() {
        return id;
    }
}
