package cn.dahe.cas.auth.cas;

import cn.dahe.cas.auth.dto.LoginSite;
import cn.dahe.cas.auth.entity.User;
import cn.dahe.cas.auth.repositories.UserRepository;
import cn.dahe.cas.auth.service.TicketService;
import cn.dahe.cas.auth.util.PageUtil;
import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.jasig.cas.authentication.principal.Service;
import org.jasig.cas.ticket.ServiceTicket;
import org.jasig.cas.ticket.Ticket;
import org.jasig.cas.ticket.TicketGrantingTicket;
import org.jasig.cas.ticket.registry.encrypt.AbstractCrypticTicketRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 使用redis实现ticket管理
 * 将tgt和st进行分别存储便于进行管理
 * 需要提供分页排序获取
 * <AUTHOR>
 */
@Component("redisTicketRegistry")
public class RedisTicketRegistryImpl extends AbstractCrypticTicketRegistry implements TicketService {

    @Resource(name = "redisTemplate")
    private HashOperations<String,String,Ticket> ticketOperation;

    @Resource(name = "redisTemplate")
    private ZSetOperations<String,String> ticketIdsOperation;

    @Autowired
    private UserRepository userRepository;

    @Value("${tgt.key}")
    private String tgtKey;

    @Value("${st.key}")
    private String stKey;

    @Value("${tgt.ids.key}")
    private String tgtIdsKey;

    @Value("${st.ids.key}")
    private String stIdsKey;

    private boolean supportRegistryState = true;

    public boolean isSupportRegistryState() {
        return supportRegistryState;
    }

    public void setSupportRegistryState(boolean supportRegistryState) {
        this.supportRegistryState = supportRegistryState;
    }

    @Override
    protected void updateTicket(Ticket ticket) {
        addTicket(ticket);
    }

    @Override
    protected boolean needsCallback() {
        return false;
    }

    @Override
    public boolean deleteSingleTicket(String s) {
        final Ticket ticket = getTicket(s);
        if(ticket == null){
            return true;
        }
        if(ticket instanceof ServiceTicket){
            ticketIdsOperation.remove(stIdsKey,0,ticket.getId());
            ticketOperation.delete(stKey,ticket.getId());
        }

        if(ticket instanceof TicketGrantingTicket){
            ticketIdsOperation.remove(tgtIdsKey,0,ticket.getId());
            ticketOperation.delete(tgtKey,ticket.getId());
        }

        return true;
    }

    @Override
    public void addTicket(Ticket ticketToAdd) {
        final Ticket ticket = encodeTicket(ticketToAdd);
        final long order = getOrder(ticketToAdd);
        if(ticket instanceof ServiceTicket){
            ticketOperation.put(stKey,ticket.getId(),ticket);
            ticketIdsOperation.add(stIdsKey,ticket.getId(), order);
        }else if(ticket instanceof TicketGrantingTicket){
            ticketOperation.put(tgtKey,ticket.getId(),ticket);
            ticketIdsOperation.add(tgtIdsKey,ticket.getId(),order);
        }else{
            throw new IllegalArgumentException("Invalid ticket type " + ticket);
        }
    }

    @Override
    public Ticket getTicket(String s) {
        final String ticketId = encodeTicketId(s);
        if(ticketId==null){
            return null;
        }
        Ticket t = null;
        if(ticketOperation.hasKey(stKey,ticketId)){
            t = ticketOperation.get(stKey,ticketId);
        }
        if(ticketOperation.hasKey(tgtKey,ticketId)){
            t = ticketOperation.get(tgtKey,ticketId);
        }
        if(t!=null){
            final Ticket proxiedTicket = decodeTicket(t);
            t = getProxiedTicketInstance(proxiedTicket);
        }
        return t;
    }

    @Override
    public int sessionCount() {
        Long count = ticketOperation.size(tgtKey);
        return BooleanUtils.toInteger(this.supportRegistryState,count.intValue(), super.sessionCount());
    }

    @Override
    public int serviceTicketCount() {
        Long count = ticketOperation.size(stKey);
        return BooleanUtils.toInteger(this.supportRegistryState, count.intValue(), super.serviceTicketCount());
    }

    @Override
    public Collection<Ticket> getTickets() {
        final Collection<Ticket> st = ticketOperation.values(stKey);
        final Collection<Ticket> tgt = ticketOperation.values(tgtKey);
        final Collection<Ticket> allTickets = new HashSet<>(st.size()+tgt.size());
        for(final Ticket ticket:st){
            allTickets.add(getProxiedTicketInstance(ticket));
        }
        for(final Ticket ticket:tgt){
            allTickets.add(getProxiedTicketInstance(ticket));
        }
        return allTickets;
    }

    @Override
    public List<Ticket> getTgts() {
        return ticketOperation.values(tgtKey);
    }

    @Override
    public List<Ticket> getSts() {
        return ticketOperation.values(stKey);
    }

    @Override
    public Page<Ticket> getTgts(Pageable page) {
        Set<String> ids = PageUtil.getPageForZset(page,ticketIdsOperation,tgtIdsKey);
        List<String> check = Lists.newArrayList(ids);
        List<Ticket> result = ticketOperation.multiGet(tgtKey,ids);
        for(int i=0;i<result.size();i++){
            if(result.get(i)==null){
                ticketIdsOperation.remove(tgtIdsKey,check.get(i));
            }
        }
        List<Ticket> tickets = result.stream().filter((Predicate<Ticket>) input -> input!=null).collect(Collectors.toList());
        return new PageImpl<>(tickets,null,ticketIdsOperation.size(tgtIdsKey));
    }

    @Override
    public Page<Ticket> getSts(Pageable page) {
        Set<String> ids = PageUtil.getPageForZset(page,ticketIdsOperation,stIdsKey);
        List<String> check = Lists.newArrayList(ids);
        List<Ticket> result = ticketOperation.multiGet(stKey,ids);
        for(int i=0;i<result.size();i++){
            if(result.get(i)==null){
                ticketIdsOperation.remove(stIdsKey,check.get(i));
            }
        }
        result = result.stream().filter((Predicate<Ticket>) input -> input!=null).collect(Collectors.toList());
        return new PageImpl<>(result,null,ticketIdsOperation.size(stIdsKey));
    }

    @Override
    public List<Service> getServices() {
        List<Ticket> origin = ticketOperation.values(stKey);
        List<Service> results = new ArrayList<>();
        for(Ticket ticket:origin){
           if(ticket instanceof ServiceTicket){
               ServiceTicket st = (ServiceTicket) ticket;
               results.add(st.getService());
           }
        }
        return results;
    }

    @Override
    public Page<LoginSite> getServices(Pageable page){
        Page<Ticket> tickets = getSts(page);
        List<LoginSite> result = Lists.transform(tickets.getContent(), new Function<Ticket, LoginSite>() {
            @Nullable
            @Override
            public LoginSite apply(@Nullable Ticket ticket) {
                LoginSite site = new LoginSite();
                if(ticket instanceof ServiceTicket){
                    Service service = ((ServiceTicket)ticket).getService();
                    site.setService(service);
                    site.setTime(ticket.getCreationTime());
                    String username = ticket.getGrantingTicket().getAuthentication().getPrincipal().getId();
                    User one = userRepository.findOne(Integer.valueOf(username));
                    site.setUsername(one.getUsername());
                }
                return site;
            }
        });
        return new PageImpl<LoginSite>(result,null,ticketIdsOperation.size(stIdsKey));
    }

    @Override
    public Set<String> stKeys() {
        return ticketOperation.keys(stKey);
    }

    @Override
    public Set<String> tgtKeys() {
        return ticketOperation.keys(tgtKey);
    }

    @Override
    public ServiceTicket getSt(String key) {
        return (ServiceTicket) ticketOperation.get(stKey,key);
    }

    @Override
    public TicketGrantingTicket getTgt(String key) {
        return (TicketGrantingTicket) ticketOperation.get(tgtKey,key);
    }

    @Override
    public void removeTgt(String key) {
        ticketOperation.delete(tgtKey,key);
    }

    @Override
    public void fix() {
        ticketOperation.keys(stKey).forEach(s -> {
            try {
                ticketOperation.get(stKey,s);
            }catch (Exception e){
                ticketOperation.delete(stKey,s);
            }

        });
        ticketOperation.keys(tgtKey).forEach(s -> {
            try {
                ticketOperation.get(tgtKey,s);
            }catch (Exception e){
                ticketOperation.delete(tgtKey,s);
            }

        });
    }

    /**
     * 获取ticket的排序权重
     * 直接使用其创建时间，有出现冲突的风险，但是可以忽略
     * @param ticket
     * @return
     */
    private long getOrder(Ticket ticket){
        return ticket.getCreationTime();
    }
}
