package cn.dahe.cas.auth.cas;

import org.jasig.cas.services.RegisteredService;
import org.jasig.cas.services.ServiceRegistryDao;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/14
 * @createTime 11:32
 * @info 分页获取注册服务
 */
public interface PageServiceRegistryDao extends ServiceRegistryDao{
    /**
     * 分页获取RegisteredService
     * @param pageable
     * @return
     */
    Page<RegisteredService> getServices(Pageable pageable);
}
