package cn.dahe.cas.auth.cas;

import cn.dahe.cas.auth.constants.LoginType;
import org.jasig.cas.authentication.Credential;

import java.io.Serializable;

/**
 * @Author: 杨振雨
 * @Date: 2019/6/4 15:28
 * @Description: 针对openid形式的认证
 */
public class OauthCredential implements Credential,Serializable{

    private static final long serialVersionUID = -6473820022637017235L;

    private String id;

    private String openid;

    private LoginType loginType;

    public OauthCredential(String openid, LoginType loginType) {
        this.openid = openid;
        this.loginType = loginType;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public LoginType getLoginType() {
        return loginType;
    }

    public void setLoginType(LoginType loginType) {
        this.loginType = loginType;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String getId() {
        return id;
    }
}
