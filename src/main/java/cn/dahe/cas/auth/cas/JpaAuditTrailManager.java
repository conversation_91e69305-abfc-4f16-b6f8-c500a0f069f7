package cn.dahe.cas.auth.cas;

import org.jasig.inspektr.audit.AuditActionContext;
import org.jasig.inspektr.audit.AuditTrailManager;
import org.jasig.inspektr.audit.support.NoMatchWhereClauseMatchCriteria;
import org.jasig.inspektr.audit.support.WhereClauseMatchCriteria;
import org.jasig.inspektr.common.Cleanable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcDaoSupport;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/13
 * @createTime 17:53
 * @info 实现定制的audit管理
 */
public class JpaAuditTrailManager extends NamedParameterJdbcDaoSupport implements AuditTrailManager, Cleanable, DisposableBean {
    private static final String INSERT_SQL_TEMPLATE = "INSERT INTO %s " +
            "(user_name, client_ip, server_ip, resource, action, application_code, date) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?)";

    private static final String DELETE_SQL_TEMPLATE = "DELETE FROM %s %s";

    private static final int DEFAULT_COLUMN_LENGTH = 100;

    /**
     * Logger instance
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * Instance of TransactionTemplate to manually execute a transaction since
     * threads are not in the same transaction.
     */
    @NotNull
    private final TransactionTemplate transactionTemplate;

    @NotNull
    @Size(min = 1)
    private String tableName = "audit_trail";

    @Min(50)
    private int columnLength = DEFAULT_COLUMN_LENGTH;

    /**
     * Criteria used to determine records that should be deleted on cleanup
     */
    private WhereClauseMatchCriteria cleanupCriteria = new NoMatchWhereClauseMatchCriteria();


    public JpaAuditTrailManager(final TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
    }

    @Override
    public void record(final AuditActionContext auditActionContext) {
        run(auditActionContext);
    }

    @Override
    public void destroy() throws Exception {

    }

    @Async
    public void run(final AuditActionContext auditActionContext) {
        this.transactionTemplate
                .execute(new TransactionCallbackWithoutResult() {
                    @Override
                    protected void doInTransactionWithoutResult(final TransactionStatus transactionStatus) {
                        final String userId = auditActionContext.getPrincipal().length() <= columnLength ? auditActionContext.getPrincipal() : auditActionContext.getPrincipal().substring(0, columnLength);
                        final String resource = auditActionContext.getResourceOperatedUpon().length() <= columnLength ? auditActionContext.getResourceOperatedUpon() : auditActionContext.getResourceOperatedUpon().substring(0, columnLength);
                        final String action = auditActionContext.getActionPerformed().length() <= columnLength ? auditActionContext.getActionPerformed() : auditActionContext.getActionPerformed().substring(0, columnLength);

                        getJdbcTemplate()
                                .update(
                                        String.format(INSERT_SQL_TEMPLATE, tableName),
                                        userId,
                                        auditActionContext.getClientIpAddress(),
                                        auditActionContext.getServerIpAddress(),
                                        resource,
                                        action,
                                        auditActionContext.getApplicationCode(),
                                        auditActionContext.getWhenActionWasPerformed());
                    }
                });
    }

    public void setTableName(final String tableName) {
        this.tableName = tableName;
    }

    public void setCleanupCriteria(final WhereClauseMatchCriteria criteria) {
        this.cleanupCriteria = criteria;
    }

    public void setColumnLength(final int columnLength) {
        this.columnLength = columnLength;
    }

    @Override
    public void clean() {
        this.transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(final TransactionStatus transactionStatus) {
                final String sql = String.format(DELETE_SQL_TEMPLATE, tableName, cleanupCriteria);
                final List<?> params = cleanupCriteria.getParameterValues();
                JpaAuditTrailManager.this.logger.info("Cleaning audit records with query " + sql);
                JpaAuditTrailManager.this.logger.debug("Query parameters: " + params);
                final int count = getJdbcTemplate().update(sql, params.toArray());
                JpaAuditTrailManager.this.logger.info(count + " records deleted.");
            }
        });
    }
}
