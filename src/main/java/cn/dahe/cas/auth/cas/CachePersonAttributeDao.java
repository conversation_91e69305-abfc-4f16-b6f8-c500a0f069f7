package cn.dahe.cas.auth.cas;

import cn.dahe.cas.auth.config.CacheKey;
import cn.dahe.cas.auth.util.Sm4Util;
import com.google.common.collect.Maps;
import org.jasig.services.persondir.IPersonAttributes;
import org.jasig.services.persondir.support.NamedPersonImpl;
import org.jasig.services.persondir.support.jdbc.SingleRowJdbcPersonAttributeDao;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @createDate 2018/1/15
 * @createTime 10:23
 * @info
 */
public class CachePersonAttributeDao extends SingleRowJdbcPersonAttributeDao{

    @Value("${sm4.security.key}")
    private String sm4Key;

    public CachePersonAttributeDao(){
        super();
    }

    public CachePersonAttributeDao(final DataSource ds, final String sql) {
        super(ds, sql);
    }

    /**
     * 对查询到的用户信息进行缓存
     * 缓存更新时机为修改用户信息
     * 手动添加额外的用户属性
     * @param uid
     * @return
     */
    @Override
    //@Cacheable(cacheNames = CacheKey.PERSON_CACHE_KEY,key = "#uid")
    public IPersonAttributes getPerson(String uid) {
        IPersonAttributes person = super.getPerson(uid);
        if (person == null) {
            return null;
        }
        Map<String, List<Object>> attributes = person.getAttributes();
        Map<String, List<Object>> newAttributes= Maps.newHashMap();
        newAttributes.putAll(attributes);
        try {
            String username = (String) newAttributes.get("username").get(0);
            //String phone = Sm4Util.decryptEcb(sm4Key, (String) newAttributes.get("phone").get(0));
            newAttributes.put("truename", Arrays.asList(username));
            //phone = phone.substring(0, 3) + "****" + phone.substring(7);
            //newAttributes.put("phone", Arrays.asList(phone));
            newAttributes.put("username", Arrays.asList(username));
        } catch (Exception e) {
            e.printStackTrace();
        }
        //Force set the name of the returned IPersonAttributes if it isn't provided in the return object
        if (person.getName() == null) {
            person = new NamedPersonImpl(uid, newAttributes);
        }else {
            person = new NamedPersonImpl(person.getName(), newAttributes);
        }
        return person;
    }
}
