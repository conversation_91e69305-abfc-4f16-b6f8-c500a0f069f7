package cn.dahe.cas.auth.cas;


import cn.dahe.cas.auth.realm.SmsAuthencationToken;
import org.apache.shiro.authc.*;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.SubjectContext;
import org.apache.shiro.subject.support.DefaultSubjectContext;
import org.jasig.cas.authentication.*;
import org.jasig.cas.authentication.handler.support.AbstractUsernamePasswordAuthenticationHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.security.auth.login.AccountLockedException;
import javax.security.auth.login.AccountNotFoundException;
import javax.security.auth.login.CredentialExpiredException;
import javax.security.auth.login.FailedLoginException;
import java.security.GeneralSecurityException;

/**
 * <AUTHOR>
 * 通过shiro进行sso的认证处理，该认证机制仅仅是利用shiro的认证功能而已，并没有web相关功能，
 * 比如cookie的设置等，因此并无法进行web功能的处理
 */
@Component("shiroAuthenticationHandler")
public class ShiroAuthenticationHandler extends AbstractUsernamePasswordAuthenticationHandler{

    private static final Logger log = LoggerFactory.getLogger(ShiroAuthenticationHandler.class);

    @Autowired
    private SecurityManager securityManager;

    @Override
    protected HandlerResult authenticateUsernamePasswordInternal(UsernamePasswordCredential transformedCredential) throws GeneralSecurityException, PreventedException {
        log.debug("sso auth for username {} ",transformedCredential.getUsername());
        try {
            final UsernamePasswordCredential credential = transformedCredential;
            final SmsAuthencationToken token = new SmsAuthencationToken(credential.getUsername(),
                    this.getPasswordEncoder().encode(credential.getPassword()));
            SubjectContext context = new DefaultSubjectContext();
            //对于单点登录，不需要进行shiro session机制，cas使用tgc进行处理
            //context.setSessionCreationEnabled(false);
            final Subject currentUser = securityManager.createSubject(context);
            currentUser.login(token);
            return createAuthenticatedSubjectResult(credential, currentUser);
        }catch (final UnknownAccountException uae) {
            throw new AccountNotFoundException(uae.getMessage());
        } catch (final IncorrectCredentialsException ice)  {
            throw new FailedLoginException(ice.getMessage());
        } catch (final LockedAccountException lae) {
            throw new AccountLockedException(lae.getMessage());
        } catch (final ExcessiveAttemptsException eae) {
            throw new AccountLockedException(eae.getMessage());
        } catch (final ExpiredCredentialsException eae) {
            throw new CredentialExpiredException(eae.getMessage());
        } catch (final DisabledAccountException eae) {
            throw new AccountDisabledException(eae.getMessage());
        }catch (Exception e){
            log.debug("sso auth for name name {} fail",transformedCredential.getUsername());
            throw new FailedLoginException(e.getMessage());
        }
    }

    protected HandlerResult createAuthenticatedSubjectResult(final Credential credential,
                                                             final Subject currentUser) {
        final String username = currentUser.getPrincipal().toString();
        //该类仅支持UsernamePasswordCredential，因此可以进行安全转换,credential以用户名为主
        ((UsernamePasswordCredential)credential).setUsername(username);
        return createHandlerResult(credential, this.principalFactory.createPrincipal(username), null);
    }

    @Override
    public boolean supports(final Credential credential) {
        return credential instanceof UsernamePasswordCredential;
    }

}
