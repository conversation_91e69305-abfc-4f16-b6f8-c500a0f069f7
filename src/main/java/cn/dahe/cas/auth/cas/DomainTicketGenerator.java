package cn.dahe.cas.auth.cas;

import org.jasig.cas.web.support.CookieValueManager;
import org.jasig.cas.web.support.TGCCookieRetrievingCookieGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 加入可设置domian的票据cookie功能
 */
@Component("domainTicketGenerator")
public class DomainTicketGenerator extends TGCCookieRetrievingCookieGenerator{

    private CookieValueManager cookieValueManager;

    @Autowired
    public DomainTicketGenerator(@Qualifier("defaultCookieValueManager") final CookieValueManager casCookieValueManager){
        super(casCookieValueManager);
        this.cookieValueManager = casCookieValueManager;
    }

    @Override
    @Autowired
    public void setCookieDomain(@Value("${tgc.domain:}") String domain){
        super.setCookieDomain(domain);
    }
}
