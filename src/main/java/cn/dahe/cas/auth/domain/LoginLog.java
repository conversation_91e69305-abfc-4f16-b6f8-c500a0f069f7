package cn.dahe.cas.auth.domain;

import cn.dahe.cas.auth.constants.LoginType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * on 2020/5/21.
 */
@Document(collection = "login_log")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginLog implements Serializable{

    private static final long serialVersionUID = 169414895802557652L;

    @Id
    @Field("_id")
    private String id;

//    @Indexed
    @ApiModelProperty(value = "登录类型")
    private LoginType type;

    @ApiModelProperty(value = "登录标识，不同登录方式所用标识不同，手机号登录对应手机号，微信登录对应openId")
    private String identify;

    @ApiModelProperty(value = "登录用户id")
    private int uid;

    @ApiModelProperty(value = "冗余手机号字段")
    private String phone;

//    @Indexed
    @ApiModelProperty(value = "冗余姓名字段")
    private String username;

    @ApiModelProperty(value = "附加信息")
    private String extras;

    @ApiModelProperty(value = "系统id")
    private int site;

//    @Indexed
    @ApiModelProperty(value = "系统名字")
    private String siteName;

    @ApiModelProperty(value = "系统url")
    private String siteUrl;

//    @Indexed
    @ApiModelProperty(value = "登录时间")
    @CreatedDate
    private Date createDate;

    @ApiModelProperty(value = "登录ip地址")
    private String loginAddress;

    @ApiModelProperty(value = "认证结果 0 成功 1 失败")
    private int result;
}
