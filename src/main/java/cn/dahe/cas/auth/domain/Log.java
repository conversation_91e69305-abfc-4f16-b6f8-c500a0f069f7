package cn.dahe.cas.auth.domain;

import cn.dahe.cas.auth.constants.LogType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * on 2020/5/19.
 */
@Document(collection = "log_system")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Log implements Serializable{

    private static final long serialVersionUID = -4073372560958402717L;

    @Id
    @Field("_id")
    private String id;

//    @Indexed
    @ApiModelProperty(value = "来源，日志标签")
    private String source;

    @ApiModelProperty(value = "日志类型")
    private LogType type;

//    @Indexed
    @ApiModelProperty(value = "日志标题")
    private String title;

    @ApiModelProperty(value = "请求地址")
    private String remoteAddress;

    @ApiModelProperty(value = "请求URI")
    private String requestUri;

    @ApiModelProperty(value = "请求方式")
    private String method;

    @ApiModelProperty(value = "提交参数")
//    @TextIndexed
    private String paramters;

    @ApiModelProperty(value = "异常")
    private String exception;

//    @Indexed
    @ApiModelProperty(value = "操作开始时间")
    private Date operateDate;

    @ApiModelProperty(value = "操作结束时间")
    private long timeout;

//    @Indexed
    @ApiModelProperty(value = "操作用户ID")
    private int uid;

    @ApiModelProperty(value = "操作用户姓名")
    private String username;

    @ApiModelProperty(value = "操作系统")
    private String os;

    @ApiModelProperty(value = "ua")
    private String ua;

}
