package cn.dahe.cas.auth.domain;

import cn.dahe.cas.auth.constants.LogOperation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * on 2020/4/30.
 */
@Document(collection = "user_log")
@Data
@Builder
public class UserLog implements Serializable {

    private static final long serialVersionUID = 443522685887692578L;

    @Id
    @Field("_id")
    private String id;

//    @Indexed
    @ApiModelProperty(value = "操作类型")
    private LogOperation operateType;

//    @Indexed
    @ApiModelProperty(value = "操作时间")
    private Date operateDate;

//    @Indexed
    @ApiModelProperty(value = "被修改用户id")
    private int userId;

    @ApiModelProperty(value = "被修改用户姓名")
    private String userName;

    @ApiModelProperty(value = "操作人Id")
    private int operateUserId;

    @ApiModelProperty(value = "操作人名字")
    private String operateUserName;

    @ApiModelProperty(value = "之前的参数")
    private Object oldParam;

    @ApiModelProperty(value = "现在的参数")
    private Object newParam;

    @ApiModelProperty(value = "操作描述，需要精确描述，例如 a用户修改了b用户的xx权限")
    private String operateDescription;

    public void setOperateDescription(String operateDescription) {
        this.operateDescription = "操作："+ this.operateType.getDescription() + "，操作人：" + this.operateUserName
                + "，操作对象：" + this.userName + "，操作内容：" + operateDescription;
    }
}
