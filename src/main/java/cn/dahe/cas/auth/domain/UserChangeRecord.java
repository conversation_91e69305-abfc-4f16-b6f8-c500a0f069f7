package cn.dahe.cas.auth.domain;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * @Author: 杨振雨
 * @Date: 2020/5/7 18:10
 * @Description:
 */
@Data
@Document
public class UserChangeRecord {

    @Id
    private String id;

    private Integer operateUserId;

    //冗余字段
    private String operateUserName;

//    @Indexed
    private Integer changedUserId;

    //冗余字段
    private String changedUserName;

    private Date operateDate;

    //原始数据，由于类型不固定，使用Object存储
    private Object originalData;

//    @Indexed
    private Integer operateType;

    //操作名字，冗余，必要时后置处理
    private String operateName;

    //操作描述，需要精确描述，例如 a用户修改了b用户的xx权限
    private String operateDescription;
}
