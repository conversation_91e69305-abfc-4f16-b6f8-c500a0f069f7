package cn.dahe.cas.auth.interceptor;

import cn.dahe.cas.auth.access.AccessStrategy;
import cn.dahe.cas.auth.util.IpUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * 基于ip的访问拦截器，对符合禁用规则的ip进行禁止访问
 */
@Component
public class IpInterceptor extends HandlerInterceptorAdapter{

    @Autowired
    @Qualifier("ipAccessStrategy")
    private AccessStrategy accessStrategy;

    @Value("#{'${interceptor.suffixs}'.split(',')}")
    private List<String> suffixs;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception{
        String path = request.getServletPath();
        String suffix = StringUtils.substringAfter(path,".");
        if(suffixs.contains(suffix)){
            return true;
        }
        String ip = IpUtils.getRemoteIp(request);
        accessStrategy.isAllow(ip);
        return true;
    }
}
