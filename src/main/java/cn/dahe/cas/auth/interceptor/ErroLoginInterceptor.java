package cn.dahe.cas.auth.interceptor;

import cn.dahe.cas.auth.constants.ErrorCode;
import cn.dahe.cas.auth.exception.SsoException;
import cn.dahe.cas.auth.util.IpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * 基于redis实现登录频率限制
 */
@Component
public class ErroLoginInterceptor extends HandlerInterceptorAdapter{

    private static final Logger log = LoggerFactory.getLogger(ErroLoginInterceptor.class);

    @Resource(name = "redisTemplate")
    private ValueOperations<String,Integer> redisTemplate;

    private static final String LOGIN_ERROR_KEY="login-error:";

    public static final String LOGIN_ERROR_ATTR = "login_error";

    public static final long DEFAULT_INTERVAL = 1000*60*3;

    public static final int DEFAULT_RETYY_COUNT = 3;

    public static final long DEFAULT_LOCK_INTERVAL = 1000*60*15;

    private long interval = DEFAULT_INTERVAL;
    private int retryCount = DEFAULT_RETYY_COUNT;
    private long lockInterval = DEFAULT_LOCK_INTERVAL;

    public long getInterval() {
        return interval;
    }

    public void setInterval(long interval) {
        this.interval = interval;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String ip = IpUtils.getRemoteIp(request);
        String key = LOGIN_ERROR_KEY+ip;
        if(!redisTemplate.getOperations().hasKey(key)){
            return true;
        }
        int count = redisTemplate.get(key);
        if(count>retryCount){
            log.debug("%s访问频率过快，被禁用",ip);
            throw new SsoException(ErrorCode.ACCOUNT_LOCK);
        }
        return true;
    }

    /**
     * This implementation is empty.
     */
    @Override
    public void afterCompletion(
            HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        String ip = IpUtils.getRemoteIp(request);
        String key = LOGIN_ERROR_KEY+ip;
        if(request.getAttribute(LOGIN_ERROR_ATTR)==null){
            return;
        }
        if(!redisTemplate.getOperations().hasKey(key)){
            redisTemplate.set(key,1,interval,TimeUnit.MILLISECONDS);
        }else{
            int count = redisTemplate.get(key);
            redisTemplate.set(key,++count);
            if(redisTemplate.get(key)>=retryCount){
                redisTemplate.set(key,retryCount+1,lockInterval,TimeUnit.MILLISECONDS);
            }
        }
    }
}
