package cn.dahe.cas.auth.interceptor;

import cn.dahe.cas.auth.access.AccessStrategy;
import cn.dahe.cas.auth.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: 杨振雨
 * @Date: 2018/11/9 15:38
 * @Description:
 */
@Component
public class PhoneInterceptor extends HandlerInterceptorAdapter{


    @Autowired
    @Qualifier("phoneAccessStrategy")
    private AccessStrategy accessStrategy;

    @Value("#{'${interceptor.suffixs}'.split(',')}")
    private List<String> suffixs;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String path = request.getServletPath();
        String suffix = StringUtils.substringAfter(path,".");
        if(suffixs.contains(suffix)){
            return true;
        }
        boolean authenticated = SecurityUtils.getSubject().isAuthenticated();
        if(!authenticated){
            return true;
        }
        Session session = SecurityUtils.getSubject().getSession();
        User user = (User) session.getAttribute("user");
        String phone = user.getPhone();
        accessStrategy.isAllow(phone);
        return true;
    }
}
