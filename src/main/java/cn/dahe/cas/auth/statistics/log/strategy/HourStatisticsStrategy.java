package cn.dahe.cas.auth.statistics.log.strategy;

import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsDimension;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.DoubleColumn;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import java.util.List;
import java.util.function.Function;

import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/21 16:32
 * @Description: 按小时进行接口调用次数统计，并区分各个站点
 */
public class HourStatisticsStrategy extends PeriodStatisticsStrategy{

    private static final String DIMENSION_UNIT = "点";

    @Override
    public Table statistics(Table table, LogStatisticsPeriod period, LogStatisticsDimension dimension) {
        Table periodTable = super.statistics(table, period, dimension);
        if(periodTable.isEmpty()){
            return periodTable;
        }
        periodTable = periodTable.summarize("id", AggregateFunctions.count).by(periodTable.instantColumn(LOGIN_TIME_FIELD).asLocalDateTimeColumn(DEFAULT_ZONE_ID).hour().setName("hour"),periodTable.stringColumn("source"));
        periodTable.column(aggregateColumnName("id", ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        StringColumn sources = periodTable.stringColumn("source").unique();
        DoubleColumn[] columns = new DoubleColumn[sources.size()];
        for(int i=0;i<sources.size();i++){
            columns[i] = DoubleColumn.create(sources.get(i),periodTable.rowCount());
        }
        periodTable.addColumns(columns);
        periodTable.forEach(row -> {
            String source = row.getString("source");
            row.setDouble(source,row.getDouble("count"));
        });
        final Table result = periodTable.summarize(sources.asList(),AggregateFunctions.sum).by("hour").copy().sortAscendingOn("hour");
        result.addColumns(result.intColumn("hour").mapInto((Function<Integer, String>) integer -> integer+DIMENSION_UNIT, StringColumn.create("date",result.rowCount())));
        result.removeColumns("hour");
        //修改各个来源的统计名称
        sources.asList().forEach(s -> result.column(aggregateColumnName(s, AggregateFunctions.sum.functionName())).setName(s));
        return result;
    }

    @Override
    public List<AggregationOperation> statistics(LogStatisticsPeriod period){
        List<AggregationOperation> aggregation = super.statistics(period);
        aggregation.add(
                project("operateDate")
                        .andExpression("{$dateToString:{format:'%H',date:'$operateDate',timezone:'+0800'}}").as("operateDate")
                        .and("source").as("source")
        );
        aggregation.add(
                group("operateDate", "source").count().as("count")
                        .first("operateDate").as("operateDate")
                        .first("source").as("title")
        );
        aggregation.add(sort(ASC, "operateDate"));
        return aggregation;
    }
}
