package cn.dahe.cas.auth.statistics.user.strategy;

import cn.dahe.cas.auth.statistics.annotation.StatisticsStrategy;
import cn.dahe.cas.auth.statistics.user.constant.UserStatisticsDimension;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.Table;

import java.time.ZoneId;

import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/22 15:40
 * @Description:
 */
@StatisticsStrategy
public class RegisterStatisticsStrategy extends AbstractStatisticsStrategyAdapter{
    @Override
    protected Table group(Table table, UserStatisticsDimension dimension) {
        table = table
                .summarize("uid", AggregateFunctions.count)
                .by(table.instantColumn("create_time").asLocalDateTimeColumn(ZoneId.of("GMT+8")).date().asStringColumn().setName("date"));
        table.column(aggregateColumnName("uid", ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        return table;
    }
}
