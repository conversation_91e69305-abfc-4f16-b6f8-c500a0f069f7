package cn.dahe.cas.auth.statistics.log.strategy;

import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import tech.tablesaw.api.Table;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/16 10:34
 * @Description:
 */
class DateDimensionStatisticsStrategyAdapter extends DateDimensionStatisticsStrategy {
    @Override
    Table group(Table table, LogStatisticsPeriod period) {
        return table.copy();
    }

    @Override
    Table postProcess(Table table, LogStatisticsPeriod period) {
        return table.copy();
    }
}
