package cn.dahe.cas.auth.statistics.user;

import cn.dahe.cas.auth.statistics.annotation.StatisticsService;
import cn.dahe.cas.auth.statistics.DatabaseStatistics;
import cn.dahe.cas.auth.statistics.user.constant.UserStatisticsDimension;
import cn.dahe.cas.auth.statistics.user.strategy.AreaStatisticsStrategy;
import cn.dahe.cas.auth.statistics.user.strategy.JobStatisticsStrategy;
import cn.dahe.cas.auth.statistics.user.strategy.RegisterStatisticsStrategy;
import cn.dahe.cas.auth.statistics.user.strategy.SexStatisticsStrategy;
import cn.dahe.cas.auth.statistics.user.strategy.SiteStatisticsStrategy;
import cn.dahe.cas.auth.statistics.user.strategy.StatisticsStrategy;
import cn.dahe.cas.auth.statistics.util.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import tech.tablesaw.api.Table;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Map;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/22 14:22
 * @Description:
 */
@StatisticsService
public class UserStatistics extends DatabaseStatistics{

    private static final String STATISTICS_CRON = "0 0 14 * * ?";

    private static final String SQL = "SELECT * from user";

    private final RedisTemplate<String, String> redisTemplate;

    @Autowired
    public UserStatistics(DataSource dataSource, @Qualifier("statisticsRedisTemplate") RedisTemplate<String, String> redisTemplate) {
        super(dataSource, SQL);
        this.redisTemplate = redisTemplate;
    }

    private String getKey(UserStatisticsDimension dimension){
        return dimension.getKey();
    }

    public Map<String,Object> getStatistics(){
        Map<String,Object> result = Maps.newHashMap();
        for(UserStatisticsDimension dimension:UserStatisticsDimension.values()){
            String value = redisTemplate.opsForValue().get(getKey(dimension));
            result.put(dimension.getName(), JSON.parse(value));
        }
        return result;
    }

    @Async
    @Scheduled(cron=STATISTICS_CRON)
    public void statisticsBySex() throws SQLException {
        ensureOriginTableValid();
        if(!originTable.isEmpty()){
            StatisticsStrategy strategy = new SexStatisticsStrategy();
            Table statisticsResult = strategy.statistics(originTable,UserStatisticsDimension.SEX);
            redisTemplate.opsForValue().set(getKey(UserStatisticsDimension.SEX),json(statisticsResult));
        }
    }

    @Async
    @Scheduled(cron=STATISTICS_CRON)
    public void statisticsByJob() throws SQLException {
        ensureOriginTableValid();
        if(!originTable.isEmpty()){
            StatisticsStrategy strategy = SpringUtil.getBean(JobStatisticsStrategy.class);
            Table statisticsResult = strategy.statistics(originTable,UserStatisticsDimension.JOB);
            redisTemplate.opsForValue().set(getKey(UserStatisticsDimension.JOB),json(statisticsResult));
        }
    }

    @Async
    @Scheduled(cron=STATISTICS_CRON)
    public void statisticsByRegisterDate() throws SQLException {
        ensureOriginTableValid();
        if(!originTable.isEmpty()){
            StatisticsStrategy strategy = SpringUtil.getBean(RegisterStatisticsStrategy.class);
            Table statisticsResult = strategy.statistics(originTable,UserStatisticsDimension.REGISTER_DATE);
            redisTemplate.opsForValue().set(getKey(UserStatisticsDimension.REGISTER_DATE),json(statisticsResult));
        }
    }

    @Async
    @Scheduled(cron=STATISTICS_CRON)
    public void statisticsBySite() throws SQLException {
        ensureOriginTableValid();
        if(!originTable.isEmpty()){
            StatisticsStrategy strategy = SpringUtil.getBean(SiteStatisticsStrategy.class);
            Table statisticsResult = strategy.statistics(originTable,UserStatisticsDimension.SITE);
            redisTemplate.opsForValue().set(getKey(UserStatisticsDimension.SITE),json(statisticsResult));
        }
    }

    @Async
    @Scheduled(cron=STATISTICS_CRON)
    public void statisticsByArea() throws SQLException {
        ensureOriginTableValid();
        if(!originTable.isEmpty()){
            StatisticsStrategy strategy = SpringUtil.getBean(AreaStatisticsStrategy.class);
            Table statisticsResult = strategy.statistics(originTable,UserStatisticsDimension.AREA);
            redisTemplate.opsForValue().set(getKey(UserStatisticsDimension.AREA),json(statisticsResult));
        }
    }
}
