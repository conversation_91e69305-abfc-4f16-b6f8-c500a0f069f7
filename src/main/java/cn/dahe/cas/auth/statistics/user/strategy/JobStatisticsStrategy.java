package cn.dahe.cas.auth.statistics.user.strategy;

import cn.dahe.cas.auth.entity.UserJob;
import cn.dahe.cas.auth.service.UserJobService;
import cn.dahe.cas.auth.statistics.annotation.StatisticsStrategy;
import cn.dahe.cas.auth.statistics.user.constant.UserStatisticsDimension;
import org.springframework.beans.factory.annotation.Autowired;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/22 15:26
 * @Description:
 */
@StatisticsStrategy
public class JobStatisticsStrategy extends AbstractStatisticsStrategyAdapter{

    private static final String GROUP_FILED = "job";

    @Autowired
    private UserJobService userJobService;

    @Override
    protected Table group(Table table, UserStatisticsDimension dimension) {
        Table jboGroupTable = table.summarize(GROUP_FILED, AggregateFunctions.count).by(GROUP_FILED);
        jboGroupTable.column(aggregateColumnName(GROUP_FILED, ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        return jboGroupTable;
    }

    @Override
    protected Table postProcess(Table table, UserStatisticsDimension dimension) {
        StringColumn jobNameColumn = StringColumn.create(GROUP_FILED);
        table.intColumn(GROUP_FILED).forEach(integer -> {
            UserJob job = userJobService.get(integer);
            if(job==null){
                jobNameColumn.append("其他");
            }else {
                jobNameColumn.append(job.getJob());
            }
        });
        table.removeColumns(GROUP_FILED);
        table.addColumns(jobNameColumn);
        return table.where(table.stringColumn(GROUP_FILED).isNotEqualTo("其他"));
    }
}
