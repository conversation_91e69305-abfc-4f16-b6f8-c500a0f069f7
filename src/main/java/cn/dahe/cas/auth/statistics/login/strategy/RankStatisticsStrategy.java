package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsDimension;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.Table;

import java.util.List;

import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.limit;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:47
 * @Description: 排行榜
 */
public class RankStatisticsStrategy extends PeriodStatisticsStrategy{

    private static final int RANK_COUNT = 10;
    private static final String RANK_FIELD = "username";

    @Override
    public Table statistics(Table table, LoginStatisticsPeriod period, LoginStatisticsDimension dimension) {
        Table periodTable = super.statistics(table, period, dimension);
        if(periodTable.isEmpty()){
            return periodTable;
        }
        //统计用户数量并排行取前10条
        periodTable = periodTable.summarize(RANK_FIELD,AggregateFunctions.count).by(RANK_FIELD);
        periodTable.column(aggregateColumnName(RANK_FIELD, ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        return periodTable.sortDescendingOn("count").first(RANK_COUNT);
    }

    @Override
    public List<AggregationOperation> statistics(LoginStatisticsPeriod period){
        List<AggregationOperation> aggregation = super.statistics(period);
        aggregation.add(
                group("username").count().as("count")
                        .first("username").as("title")
        );
        aggregation.add(sort(DESC, "count"));
        aggregation.add(limit(10));
        return aggregation;
    }
}
