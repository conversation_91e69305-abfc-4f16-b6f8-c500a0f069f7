package cn.dahe.cas.auth.statistics.login.constant;

import static cn.dahe.cas.auth.statistics.login.constant.DimensionKey.*;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:38
 * @Description:
 */
public enum LoginStatisticsDimension {
    DATE(STATISTICS_BY_DAY_KEY, "天", "date"),
    TYPE(STATISTICS_BY_TYPE_KEY, "登录类型", "type"),
    SITE(STATISTICS_SITE_RANK,"站点排行","site"),
    USER(STATISTICS_USER_RANK, "用户排行", "rank"),
    HOUR(STATISTICS_HOUR_HEAT, "小时热力", "hour")
    ;
    private String key;
    private String description;
    private String name;

    LoginStatisticsDimension(String key, String description, String name) {
        this.key = key;
        this.description = description;
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
