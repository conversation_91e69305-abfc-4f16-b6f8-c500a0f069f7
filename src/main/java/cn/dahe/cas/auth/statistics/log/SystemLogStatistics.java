package cn.dahe.cas.auth.statistics.log;

import cn.dahe.cas.auth.domain.Log;
import cn.dahe.cas.auth.statistics.annotation.StatisticsService;
import cn.dahe.cas.auth.statistics.dto.DimensionDto;
import cn.dahe.cas.auth.statistics.dto.StatisticsDto;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsDimension;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import cn.dahe.cas.auth.statistics.log.strategy.ApiDimensionStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.DateDimensionStatisticsStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.HourStatisticsStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.MonthStatisticsStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.SiteStatisticsStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.StatisticsStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * on 2020/5/26.
 */
@StatisticsService
public class SystemLogStatistics {

    private static final String STATISTICS_CRON = "0 0 14 * * ?";
    private final RedisTemplate<String, String> redisTemplate;
    private final MongoTemplate mongoTemplate;

    @Autowired
    public SystemLogStatistics(@Qualifier("statisticsRedisTemplate") RedisTemplate<String, String> redisTemplate,
                         @Qualifier("mongoTemplate") MongoTemplate mongoTemplate) {
        this.redisTemplate = redisTemplate;
        this.mongoTemplate = mongoTemplate;
    }

    public Map<String, Object> getStatistics(LogStatisticsPeriod period) {
        Map<String, Object> result = Maps.newHashMap();
        for (LogStatisticsDimension dimension : LogStatisticsDimension.values()) {
            String value = redisTemplate.opsForValue().get(getKey(period, dimension));
            result.put(dimension.getName(), JSON.parse(value));
        }
        return result;
    }

    private String getKey(LogStatisticsPeriod period, LogStatisticsDimension dimension) {
        return dimension.getKey() + ":" + period.getKey();
    }

    public static List<DimensionDto> getReturnMap(List<StatisticsDto> mappedResults){
        Map<String, List<StatisticsDto>> map = Maps.newTreeMap();
        if (mappedResults != null && !mappedResults.isEmpty()){
            for (StatisticsDto apiDto : mappedResults) {
                if (map.containsKey(apiDto.getOperateDate())){
                    List<StatisticsDto> apiDtos = map.get(apiDto.getOperateDate());
                    apiDtos.add(apiDto);
                    map.put(apiDto.getOperateDate(), apiDtos);
                }else {
                    map.put(apiDto.getOperateDate(), Lists.newArrayList(apiDto));
                }
            }
        }
        List<DimensionDto> arrayList = new ArrayList<>();
        for(Map.Entry<String, List<StatisticsDto>> entry : map.entrySet()){
            DimensionDto dimensionDto = DimensionDto.builder().date(entry.getKey()).data(entry.getValue()).build();
            arrayList.add(dimensionDto);
        }
        return arrayList;
    }
    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsRank() throws SQLException {
        StatisticsStrategy statisticsStrategy = new SiteStatisticsStrategy();
        for (LogStatisticsPeriod period : LogStatisticsPeriod.values()) {
            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, Log.class, StatisticsDto.class);
            List<StatisticsDto> mappedResults = results.getMappedResults();
            redisTemplate.opsForValue().set(getKey(period, LogStatisticsDimension.SITE), JSON.toJSONString(mappedResults));
        }
    }

    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsApi(){
        StatisticsStrategy statisticsStrategy = new ApiDimensionStrategy();
        for (LogStatisticsPeriod period : LogStatisticsPeriod.values()) {
            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, Log.class, StatisticsDto.class);
            List<StatisticsDto> mappedResults = results.getMappedResults();
            redisTemplate.opsForValue().set(getKey(period, LogStatisticsDimension.API), JSON.toJSONString(mappedResults));
        }
    }

    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsHour(){
        StatisticsStrategy statisticsStrategy = new HourStatisticsStrategy();
        for (LogStatisticsPeriod period : LogStatisticsPeriod.values()) {
            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, Log.class, StatisticsDto.class);
            List<StatisticsDto> mappedResults = results.getMappedResults();
            // 根据日期时间聚合
            List<DimensionDto> list = getReturnMap(mappedResults);
            redisTemplate.opsForValue().set(getKey(period, LogStatisticsDimension.HOUR), JSON.toJSONString(list));
        }
    }

    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByDay() {
        for(LogStatisticsPeriod period:LogStatisticsPeriod.values()){
            DateDimensionStatisticsStrategy statisticsStrategy;
            switch (period){
                case LAST_MONTH:
                    statisticsStrategy = new MonthStatisticsStrategy();
                    break;
                default:
                    statisticsStrategy = new MonthStatisticsStrategy();
            }
            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, Log.class, StatisticsDto.class);
            List<StatisticsDto> mappedResults = results.getMappedResults();
            // 根据日期时间聚合
            List<DimensionDto> list = getReturnMap(mappedResults);
            redisTemplate.opsForValue().set(getKey(period, LogStatisticsDimension.DATE), JSON.toJSONString(list));
        }
    }
}
