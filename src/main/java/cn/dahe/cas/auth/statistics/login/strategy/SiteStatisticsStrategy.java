package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsDimension;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.Table;

import java.util.List;

import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 16:44
 * @Description: 可望文生义，统计每个站点的登录数量
 */
public class SiteStatisticsStrategy extends PeriodStatisticsStrategy{

    private static final String SITE_NAME_FIELD = "site_name";

    @Override
    public Table statistics(Table table, LoginStatisticsPeriod period, LoginStatisticsDimension dimension) {
        Table periodTable = super.statistics(table, period, dimension);
        if(periodTable.isEmpty()){
            return periodTable;
        }
        periodTable = periodTable.summarize(SITE_NAME_FIELD, AggregateFunctions.count).by(SITE_NAME_FIELD);
        periodTable.column(aggregateColumnName(SITE_NAME_FIELD, ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        return periodTable.sortDescendingOn("count");
    }

    @Override
    public List<AggregationOperation> statistics(LoginStatisticsPeriod period){
        List<AggregationOperation> aggregation = super.statistics(period);
        aggregation.add(
                group("siteName").count().as("count")
                        .first("siteName").as("title")
        );
        aggregation.add(sort(DESC, "count"));
        return aggregation;
    }
}
