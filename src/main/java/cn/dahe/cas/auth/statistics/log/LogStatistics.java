package cn.dahe.cas.auth.statistics.log;

import cn.dahe.cas.auth.statistics.DatabaseStatistics;
import cn.dahe.cas.auth.statistics.annotation.StatisticsService;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsDimension;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import cn.dahe.cas.auth.statistics.log.strategy.ApiDimensionStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.DateDimensionStatisticsStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.HourStatisticsStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.MonthStatisticsStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.SiteStatisticsStrategy;
import cn.dahe.cas.auth.statistics.log.strategy.StatisticsStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import tech.tablesaw.api.Table;
import tech.tablesaw.io.Destination;
import tech.tablesaw.io.json.JsonWriter;

import javax.sql.DataSource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Map;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/21 14:26
 * @Description: 2020/05/27 更换了存储介质，详看SystemLogStatics
 */
@StatisticsService
public class LogStatistics extends DatabaseStatistics{

    private static final String STATISTICS_CRON = "0 0 14 * * ?";

    private static final String sql = "SELECT * FROM log_system";

    private final RedisTemplate<String, String> redisTemplate;

    @Autowired
    public LogStatistics(DataSource dataSource, @Qualifier("statisticsRedisTemplate") RedisTemplate<String, String> redisTemplate) {
        super(dataSource,sql);
        this.redisTemplate = redisTemplate;
    }

    public Map<String, Object> getStatistics(LogStatisticsPeriod period) {
        Map<String, Object> result = Maps.newHashMap();
        for (LogStatisticsDimension dimension : LogStatisticsDimension.values()) {
            String value = redisTemplate.opsForValue().get(getKey(period, dimension));
            result.put(dimension.getName(), JSON.parse(value));
        }
        return result;
    }

    private String getKey(LogStatisticsPeriod period, LogStatisticsDimension dimension) {
        return dimension.getKey() + ":" + period.getKey();
    }

    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsRank() throws SQLException {
        ensureOriginTableValid();
        if (!originTable.isEmpty()) {
            StatisticsStrategy statisticsStrategy = new SiteStatisticsStrategy();
            for (LogStatisticsPeriod period : LogStatisticsPeriod.values()) {
                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LogStatisticsDimension.SITE);
                redisTemplate.opsForValue().set(getKey(period, LogStatisticsDimension.SITE), json(statisticsResult));
            }
        }
    }

    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsApi() throws SQLException {
        ensureOriginTableValid();
        if (!originTable.isEmpty()) {
            StatisticsStrategy statisticsStrategy = new ApiDimensionStrategy();
            for (LogStatisticsPeriod period : LogStatisticsPeriod.values()) {
                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LogStatisticsDimension.API);
                redisTemplate.opsForValue().set(getKey(period, LogStatisticsDimension.API), json(statisticsResult));
            }
        }
    }

    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsHour() throws SQLException {
        ensureOriginTableValid();
        if (!originTable.isEmpty()) {
            StatisticsStrategy statisticsStrategy = new HourStatisticsStrategy();
            for (LogStatisticsPeriod period : LogStatisticsPeriod.values()) {
                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LogStatisticsDimension.HOUR);
                redisTemplate.opsForValue().set(getKey(period, LogStatisticsDimension.HOUR), json(statisticsResult));
            }
        }
    }

    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByDay() throws SQLException {
        ensureOriginTableValid();
        if (!originTable.isEmpty()) {
            for(LogStatisticsPeriod period:LogStatisticsPeriod.values()){
                DateDimensionStatisticsStrategy statisticsStrategy;
                switch (period){
                    case LAST_MONTH:
                        statisticsStrategy = new MonthStatisticsStrategy();
                        break;
                    default:
                        statisticsStrategy = new MonthStatisticsStrategy();
                }
                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LogStatisticsDimension.DATE);
                ByteArrayOutputStream destination = new ByteArrayOutputStream();
                try {
                    new JsonWriter().write(statisticsResult, new Destination(destination));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                redisTemplate.opsForValue().set(getKey(period, LogStatisticsDimension.DATE), destination.toString());
            }
        }
    }


}
