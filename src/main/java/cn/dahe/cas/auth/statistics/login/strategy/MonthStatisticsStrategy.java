package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import java.time.ZoneId;
import java.util.function.Function;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:33
 * @Description:
 */
public class MonthStatisticsStrategy extends DateDimensionStatisticsStrategy{

    private static final String DIMENSION_UNIT_NAME = "日";

    @Override
    Table group(Table table, LoginStatisticsPeriod period) {
        table = table
                .summarize("uid", AggregateFunctions.count)
                .by(table.instantColumn("login_time").asLocalDateTimeColumn(ZoneId.of("GMT+8")).dayOfMonth().setName(GROUP_DIMENSION_FIELD), table.intColumn("type"))
                .setName("statistic by day");
        return table;
    }

    @Override
    Table postProcess(Table table, LoginStatisticsPeriod period) {
        //排序
        table.sortAscendingOn(GROUP_DIMENSION_FIELD);
        //维度格式定制
        return table.addColumns(table.intColumn(GROUP_DIMENSION_FIELD).mapInto((Function<Integer, String>) integer -> integer + DIMENSION_UNIT_NAME, StringColumn.create(DIMENSION_FIELD,table.rowCount())));
    }
}
