package cn.dahe.cas.auth.statistics.log.strategy;

import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import java.util.function.Function;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/21 17:13
 * @Description:
 */
public class MonthStatisticsStrategy extends DateDimensionStatisticsStrategy{

    private static final String DIMENSION_UNIT_NAME = "日";

    @Override
    Table group(Table table, LogStatisticsPeriod period) {
        table = table
                .summarize("source", AggregateFunctions.count)
                .by(table.instantColumn(LOGIN_TIME_FIELD).asLocalDateTimeColumn(DEFAULT_ZONE_ID).dayOfMonth().setName(GROUP_DIMENSION_FIELD),table.stringColumn("source"));
        return table;
    }

    @Override
    Table postProcess(Table table, LogStatisticsPeriod period) {
        //排序
        table.sortAscendingOn(GROUP_DIMENSION_FIELD);
        //维度格式定制
        return table.addColumns(table.intColumn(GROUP_DIMENSION_FIELD).mapInto((Function<Integer, String>) integer -> integer + DIMENSION_UNIT_NAME, StringColumn.create(DIMENSION_FIELD,table.rowCount())));
    }
}
