package cn.dahe.cas.auth.statistics.user.strategy;

import cn.dahe.cas.auth.dto.Area;
import cn.dahe.cas.auth.service.AreaService;
import cn.dahe.cas.auth.statistics.user.constant.UserStatisticsDimension;
import org.springframework.beans.factory.annotation.Autowired;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/23 09:40
 * @Description:
 */
@cn.dahe.cas.auth.statistics.annotation.StatisticsStrategy
public class AreaStatisticsStrategy extends AbstractStatisticsStrategyAdapter{

    private static final String GROUP_FILED = "area_code";

    @Autowired
    private AreaService areaService;

    @Override
    protected Table group(Table table, UserStatisticsDimension dimension) {
        Table areaGroupTable = table.summarize(GROUP_FILED, AggregateFunctions.count).by(GROUP_FILED);
        areaGroupTable.column(aggregateColumnName(GROUP_FILED, ((AggregateFunction) AggregateFunctions.count).functionName())).setName(DEFAULT_METRICS_NAME);
        return areaGroupTable;
    }

    @Override
    protected Table postProcess(Table table, UserStatisticsDimension dimension) {
        StringColumn areaColumn = StringColumn.create("area");
        table.stringColumn(GROUP_FILED).forEach(code -> {
            Area area = areaService.getTop(code);
            if(area==null){
                areaColumn.appendMissing();
            }else {
                areaColumn.append(area.getName());
            }
        });
        table.removeColumns(GROUP_FILED);
        table.addColumns(areaColumn);
        //再次聚合
        table = table.summarize("count",AggregateFunctions.sum).by("area");
        table.column(aggregateColumnName("count",AggregateFunctions.sum.functionName())).setName(DEFAULT_METRICS_NAME);
        return table;
    }
}
