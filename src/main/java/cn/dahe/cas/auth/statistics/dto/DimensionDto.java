package cn.dahe.cas.auth.statistics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * on 2020/5/29.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DimensionDto implements Serializable {

    private static final long serialVersionUID = -6869127011754664721L;

    private String date;
    private List<StatisticsDto> data;
}
