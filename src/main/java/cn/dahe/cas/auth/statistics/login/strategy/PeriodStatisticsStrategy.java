package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsDimension;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import cn.dahe.cas.auth.statistics.util.DateUtils;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import tech.tablesaw.api.Table;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:32
 * @Description: 负责统计中时间段的选择，通过提供的StatisticsPeriod做源数据选择，以为下游操作提供数据源
 */
public abstract class PeriodStatisticsStrategy implements StatisticsStrategy{

    static final String LOGIN_TIME_FIELD = "login_time";
    @Override
    public Table statistics(Table table, LoginStatisticsPeriod period, LoginStatisticsDimension dimension) {
        //统计的时间点
        Date before = processPeriod(period).get(0);
        return table.where(table.instantColumn(LOGIN_TIME_FIELD).asLocalDateTimeColumn(ZoneId.of("GMT+8")).isAfter(before.toInstant().atZone(ZoneId.of("GMT+8")).toLocalDateTime()));
    }

    @Override
    public List<AggregationOperation> statistics(LoginStatisticsPeriod period){
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        List<Date> dates = processPeriod(period);
        aggregationOperationList.add(match(Criteria.where("result").ne(1)));
        aggregationOperationList.add(match(Criteria.where("createDate").gte(dates.get(0))));
        aggregationOperationList.add(match(Criteria.where("createDate").lte(dates.get(1))));
        return aggregationOperationList;
    }

    /**
     * 根据提供的统计时间段获取过滤条件
     *
     * @param period 统计时间段
     */
    private List<Date> processPeriod(LoginStatisticsPeriod period) {
        List<Date> dates = new ArrayList<>();
        switch (period) {
            case YESTERDAY:
                dates.add(0, DateUtils.yesterdayBegin());
                dates.add(1, DateUtils.yesterdayEnd());
                break;
            case LAST_WEEK:
                dates.add(0, DateUtils.lastWeekBegin());
                dates.add(1, DateUtils.lastWeekEnd());
                break;
            case MONTH:
                dates.add(0, DateUtils.monthBegin());
                dates.add(1, new Date());
                break;
            case LAST_MONTH:
                dates.add(0, DateUtils.lastMonthBegin());
                dates.add(1, DateUtils.lastMonthEnd());
                break;
            case YEAR:
                dates.add(0, DateUtils.yearBegin());
                dates.add(1, new Date());
                break;
            default:
                dates.add(0, DateUtils.monthBegin());
                dates.add(1, new Date());
                break;
        }
        return dates;
    }
}
