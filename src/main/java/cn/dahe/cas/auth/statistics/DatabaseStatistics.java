package cn.dahe.cas.auth.statistics;

import cn.hutool.core.date.DateUtil;
import com.alibaba.druid.util.JdbcUtils;
import org.apache.commons.lang3.StringUtils;
import tech.tablesaw.api.Table;
import tech.tablesaw.io.Destination;
import tech.tablesaw.io.json.JsonWriter;

import javax.sql.DataSource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.Instant;
import java.util.Date;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/21 14:29
 * @Description:
 */
public class DatabaseStatistics {

    private final DataSource dataSource;

    public DatabaseStatistics(DataSource dataSource, String sql) {
        if(StringUtils.isBlank(sql)){
            throw new RuntimeException("sql不能为空");
        }
        this.dataSource = dataSource;
        this.sql = sql;
    }

    //包含数据源,其他操作table皆由此进行派生，且该数据源应该以天为单位，即创建该源条件为不存在或其已过期
    protected Table originTable;
    private Instant createDate;

    private String sql;

    private boolean isExpired() {
        Instant yesterday = DateUtil.beginOfDay(new Date()).toInstant();
        return createDate == null || yesterday.isAfter(createDate);
    }

    protected void ensureOriginTableValid() throws SQLException {
        //需要进行统计的时间点（暂定为一年，即直接将一年数据加载到内存）
        if (originTable != null) {
            return;
        }
        synchronized (this) {
            if (originTable != null && !isExpired()) {
                return;
            }
            Statement statement = dataSource.getConnection().createStatement();
            ResultSet resultSet = statement.executeQuery(sql);
            originTable = Table.read().db(resultSet);
            JdbcUtils.close(resultSet);
            createDate = Instant.now();
        }
    }

    protected String json(Table table){
        ByteArrayOutputStream destination = new ByteArrayOutputStream();
        try {
            new JsonWriter().write(table, new Destination(destination));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return destination.toString();
    }
}
