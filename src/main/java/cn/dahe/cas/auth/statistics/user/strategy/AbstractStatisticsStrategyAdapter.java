package cn.dahe.cas.auth.statistics.user.strategy;

import cn.dahe.cas.auth.statistics.user.constant.UserStatisticsDimension;
import tech.tablesaw.api.Table;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/22 14:51
 * @Description:
 */
public class AbstractStatisticsStrategyAdapter extends AbstractStatisticsStrategy{
    @Override
    protected Table group(Table table, UserStatisticsDimension dimension) {
        return table.copy();
    }

    @Override
    protected Table postProcess(Table table, UserStatisticsDimension dimension) {
        return table.copy();
    }
}
