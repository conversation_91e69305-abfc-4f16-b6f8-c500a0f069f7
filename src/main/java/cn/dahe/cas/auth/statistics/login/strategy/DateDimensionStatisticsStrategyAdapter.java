package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import tech.tablesaw.api.Table;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/16 10:34
 * @Description:
 */
class DateDimensionStatisticsStrategyAdapter extends DateDimensionStatisticsStrategy{
    @Override
    Table group(Table table, LoginStatisticsPeriod period) {
        return table.copy();
    }

    @Override
    Table postProcess(Table table, LoginStatisticsPeriod period) {
        return table.copy();
    }
}
