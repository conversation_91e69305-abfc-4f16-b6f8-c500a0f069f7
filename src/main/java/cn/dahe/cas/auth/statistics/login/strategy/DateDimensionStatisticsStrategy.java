package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.constants.LoginType;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsDimension;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.DoubleColumn;
import tech.tablesaw.api.Table;

import java.util.List;
import java.util.Map;

import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static tech.tablesaw.aggregate.AggregateFunctions.sum;
import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:32
 * @Description:
 */
public abstract class DateDimensionStatisticsStrategy extends PeriodStatisticsStrategy{

    static final String GROUP_DIMENSION_FIELD = "group_by";
    static final String DIMENSION_FIELD = "date";

    @Override
    public Table statistics(Table table, LoginStatisticsPeriod period, LoginStatisticsDimension dimension) {
        //不同的时间段要根据不同的条件进行数据分组
        Table periodTable = super.statistics(table, period, dimension);
        if(periodTable.isEmpty()){
            return periodTable;
        }
        periodTable = group(periodTable, period);
        //不同类型添加不同的字段
        Map<Integer, DoubleColumn> columns = Maps.newHashMap();
        for (LoginType loginType : LoginType.values()) {
            columns.put(loginType.getCode(), DoubleColumn.create(loginType.getDescription()));
        }
        periodTable.column(aggregateColumnName("uid", ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        periodTable.stream().forEach(row -> {
            int type = row.getInt("type");
            columns.forEach((integer, integers) -> {
                if (integer.equals(type)) {
                    integers.append(row.getDouble("count"));
                } else {
                    integers.append(0);
                }
            });
        });
        //添加新列，统计各类型数量
        periodTable.removeColumns("type");
        List<String> summarizeColumns = Lists.newArrayList();
        for (DoubleColumn column : columns.values()) {
            periodTable.addColumns(column);
            summarizeColumns.add(column.name());
        }
        return postProcess(periodTable.summarize(summarizeColumns, sum).by(GROUP_DIMENSION_FIELD),period);
    }

    @Override
    public List<AggregationOperation> statistics(LoginStatisticsPeriod period){
        List<AggregationOperation> aggregation = super.statistics(period);
        aggregation.add(
                project("createDate")
                        .andExpression("{$dateToString:{format:'%Y-%m-%d',date:'$createDate',timezone:'+0800'}}").as("createDate")
                        .and("type").as("type")
        );
        aggregation.add(
                Aggregation.group("createDate", "type").count().as("count")
                        .first("createDate").as("operateDate")
                        .first("type").as("loginType")
        );
        aggregation.add(sort(ASC, "operateDate"));
        return aggregation;
    }

    /**
     * 根据period对table进行分组
     *
     * @param table 需要进行分组的原始表
     * @return 返回新表，不可对源表修改
     */
    abstract Table group(Table table, LoginStatisticsPeriod period);

    /**
     * 负责排序
     * @param table 需要排序的表
     * @param period
     * @return
     */
    abstract Table postProcess(Table table,LoginStatisticsPeriod period);
}
