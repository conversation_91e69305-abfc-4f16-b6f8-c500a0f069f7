package cn.dahe.cas.auth.statistics.login;

import cn.dahe.cas.auth.domain.LoginLog;
import cn.dahe.cas.auth.statistics.annotation.StatisticsService;
import cn.dahe.cas.auth.statistics.dto.DimensionDto;
import cn.dahe.cas.auth.statistics.dto.StatisticsDto;
import cn.dahe.cas.auth.statistics.log.SystemLogStatistics;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsDimension;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import cn.dahe.cas.auth.statistics.login.strategy.DateDimensionStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.HourStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.MonthStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.RankStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.SiteStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.StatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.WeekStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.YearStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.YesterdayStatisticsStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * on 2020/05/28 15:34
 * 时间段：昨天（小时）   一周（按天）   一月（按天）  一年（按月）
 * 维度：天  类型
 */
@StatisticsService
public class UserLoginStatistics{

    private static final String STATISTICS_CRON = "0 0 14 * * ?";

    private final RedisTemplate<String, String> redisTemplate;

    private final MongoTemplate mongoTemplate;

    @Autowired
    public UserLoginStatistics(@Qualifier("statisticsRedisTemplate") RedisTemplate<String, String> redisTemplate,
                               @Qualifier("mongoTemplate") MongoTemplate mongoTemplate) {
        this.redisTemplate = redisTemplate;
        this.mongoTemplate = mongoTemplate;
    }

    private String getKey(LoginStatisticsPeriod period, LoginStatisticsDimension dimension) {
        return dimension.getKey() + ":" + period.getKey();
    }

    public Map<String, Object> getStatistics(LoginStatisticsPeriod period) {
        Map<String, Object> result = Maps.newHashMap();
        for (LoginStatisticsDimension dimension : LoginStatisticsDimension.values()) {
            String value = redisTemplate.opsForValue().get(getKey(period, dimension));
            result.put(dimension.getName(), JSON.parse(value));
        }
        return result;
    }


    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByType() {
//        StatisticsStrategy statisticsStrategy = new TypeDimensionStatisticsStrategy();
//        for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
//            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
//            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
//            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, LoginLog.class, StatisticsDto.class);
//            List<StatisticsDto> mappedResults = results.getMappedResults();
//            redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.TYPE), JSON.toJSONString(mappedResults));
//        }
    }

    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByUser(){
        StatisticsStrategy statisticsStrategy = new RankStatisticsStrategy();
        for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, LoginLog.class, StatisticsDto.class);
            List<StatisticsDto> mappedResults = results.getMappedResults();
            redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.USER), JSON.toJSONString(mappedResults));
        }
    }

    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsBySite() {
        StatisticsStrategy statisticsStrategy = new SiteStatisticsStrategy();
        for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, LoginLog.class, StatisticsDto.class);
            List<StatisticsDto> mappedResults = results.getMappedResults();
            redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.SITE), JSON.toJSONString(mappedResults));
        }
    }

    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByHourHeat() {
        StatisticsStrategy statisticsStrategy = new HourStatisticsStrategy();
        for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, LoginLog.class, StatisticsDto.class);
            List<StatisticsDto> mappedResults = results.getMappedResults();
            redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.HOUR), JSON.toJSONString(mappedResults));
        }
    }

    /**
     * 将统计数据写入缓存
     *
     * @throws SQLException
     */
    @Async
    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByDay(){
        for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
            DateDimensionStatisticsStrategy statisticsStrategy;
            switch (period){
                case YESTERDAY:
                    statisticsStrategy = new YesterdayStatisticsStrategy();
                    break;
                case LAST_MONTH:
                    statisticsStrategy = new MonthStatisticsStrategy();
                    break;
                case LAST_WEEK:
                    statisticsStrategy = new WeekStatisticsStrategy();
                    break;
                case YEAR:
                    statisticsStrategy = new YearStatisticsStrategy();
                    break;
                default:
                    statisticsStrategy = new MonthStatisticsStrategy();
            }
            List<AggregationOperation> aggregationOperationList = statisticsStrategy.statistics(period);
            Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
            AggregationResults<StatisticsDto> results = mongoTemplate.aggregate(aggregation, LoginLog.class, StatisticsDto.class);
            List<StatisticsDto> mappedResults = results.getMappedResults();
            List<DimensionDto> list = SystemLogStatistics.getReturnMap(mappedResults);
            redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.DATE), JSON.toJSONString(list));
        }
    }
}
