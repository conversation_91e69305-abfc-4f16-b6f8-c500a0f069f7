package cn.dahe.cas.auth.statistics.log.strategy;

import cn.dahe.cas.auth.service.LogSourceService;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsDimension;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import cn.dahe.cas.auth.statistics.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.DoubleColumn;
import tech.tablesaw.api.Table;

import java.util.List;
import java.util.Map;

import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static tech.tablesaw.aggregate.AggregateFunctions.sum;
import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:32
 * @Description:
 */
public abstract class DateDimensionStatisticsStrategy extends PeriodStatisticsStrategy {

    static final String GROUP_DIMENSION_FIELD = "group_by";
    static final String DIMENSION_FIELD = "date";

    private LogSourceService logSourceService;

    DateDimensionStatisticsStrategy() {
        this.logSourceService = SpringUtil.getBean(LogSourceService.class);
    }

    @Override
    public Table statistics(Table table, LogStatisticsPeriod period, LogStatisticsDimension dimension) {
        //不同的时间段要根据不同的条件进行数据分组
        Table periodTable = super.statistics(table, period, dimension);
        if(periodTable.isEmpty()){
            return periodTable;
        }
        periodTable = group(periodTable, period);
        //不同类型添加不同的字段
        Map<String, DoubleColumn> columns = Maps.newHashMap();
        logSourceService.listAllLogSource().forEach(s -> columns.put(s, DoubleColumn.create(s)));
        periodTable.column(aggregateColumnName("source", ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        periodTable.stream().forEach(row -> {
            String source = row.getString("source");
            columns.forEach((s, doubles) -> {
                if(source.equals(s)){
                    doubles.append(row.getDouble("count"));
                }else {
                    doubles.append(0);
                }
            });
        });
        //添加新列，统计各类型数量
        periodTable.removeColumns("source");
        List<String> summarizeColumns = Lists.newArrayList();
        for (DoubleColumn column : columns.values()) {
            periodTable.addColumns(column);
            summarizeColumns.add(column.name());
        }
        return postProcess(periodTable.summarize(summarizeColumns, sum).by(GROUP_DIMENSION_FIELD),period);
    }

    @Override
    public List<AggregationOperation> statistics(LogStatisticsPeriod period){
        List<AggregationOperation> aggregation = super.statistics(period);
        aggregation.add(
                project("operateDate")
                        .andExpression("{$dateToString:{format:'%Y-%m-%d',date:'$operateDate',timezone:'+0800'}}").as("operateDate")
                        .and("source").as("source")
        );
        aggregation.add(
                Aggregation.group("operateDate", "source").count().as("count")
                        .first("operateDate").as("operateDate")
                        .first("source").as("title")
        );
        aggregation.add(sort(ASC, "operateDate"));
        return aggregation;
    }

    /**
     * 根据period对table进行分组
     *
     * @param table 需要进行分组的原始表
     * @return 返回新表，不可对源表修改
     */
    abstract Table group(Table table, LogStatisticsPeriod period);

    /**
     * 负责排序
     * @param table 需要排序的表
     * @param period
     * @return
     */
    abstract Table postProcess(Table table,LogStatisticsPeriod period);
}
