package cn.dahe.cas.auth.statistics.dto;

import cn.dahe.cas.auth.constants.LoginType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * on 2020/5/26.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsDto implements Serializable {

    private static final long serialVersionUID = 8061132971462310653L;

    private String title;
    private int count;
    private String operateDate;
    private LoginType loginType;
}
