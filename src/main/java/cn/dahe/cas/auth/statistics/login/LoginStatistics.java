package cn.dahe.cas.auth.statistics.login;

import cn.dahe.cas.auth.statistics.DatabaseStatistics;
import cn.dahe.cas.auth.statistics.annotation.StatisticsService;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsDimension;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import cn.dahe.cas.auth.statistics.login.strategy.DateDimensionStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.HourStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.MonthStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.RankStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.SiteStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.StatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.WeekStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.YearStatisticsStrategy;
import cn.dahe.cas.auth.statistics.login.strategy.YesterdayStatisticsStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import tech.tablesaw.api.Table;
import tech.tablesaw.io.Destination;
import tech.tablesaw.io.json.JsonWriter;

import javax.sql.DataSource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Map;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/13 15:34   更换了存储介质，详看UserLoginStatistics
 * @Description: 统计如何做到更加具有扩展性
 * 时间段：昨天（小时）   一周（按天）   一月（按天）  一年（按月）
 * 维度：天  类型
 *
 */
@StatisticsService
public class LoginStatistics extends DatabaseStatistics{

    private static final String STATISTICS_CRON = "0 0 14 * * ?";

    private static final String sql = "SELECT * FROM log_login";


    private final RedisTemplate<String, String> redisTemplate;

    @Autowired
    public LoginStatistics(DataSource dataSource, @Qualifier("statisticsRedisTemplate") RedisTemplate<String, String> redisTemplate) {
        super(dataSource,sql);
        this.redisTemplate = redisTemplate;
    }

    private String getKey(LoginStatisticsPeriod period, LoginStatisticsDimension dimension) {
        return dimension.getKey() + ":" + period.getKey();
    }

    public Map<String, Object> getStatistics(LoginStatisticsPeriod period) {
        Map<String, Object> result = Maps.newHashMap();
        for (LoginStatisticsDimension dimension : LoginStatisticsDimension.values()) {
            String value = redisTemplate.opsForValue().get(getKey(period, dimension));
            result.put(dimension.getName(), JSON.parse(value));
        }
        return result;
    }


    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByType() throws SQLException {
        ensureOriginTableValid();
//        if (!originTable.isEmpty()) {
//            StatisticsStrategy statisticsStrategy = new TypeDimensionStatisticsStrategy();
//            for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
//                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LoginStatisticsDimension.TYPE);
//                ByteArrayOutputStream destination = new ByteArrayOutputStream();
//                try {
//                    new JsonWriter().write(statisticsResult, new Destination(destination));
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//                redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.TYPE), destination.toString());
//            }
//        }
    }

    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByUser() throws SQLException {
        ensureOriginTableValid();
        if (!originTable.isEmpty()) {
            StatisticsStrategy statisticsStrategy = new RankStatisticsStrategy();
            for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LoginStatisticsDimension.USER);
                ByteArrayOutputStream destination = new ByteArrayOutputStream();
                try {
                    new JsonWriter().write(statisticsResult, new Destination(destination));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.USER), destination.toString());
            }
        }
    }

    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsBySite() throws SQLException {
        ensureOriginTableValid();
        if (!originTable.isEmpty()) {
            StatisticsStrategy statisticsStrategy = new SiteStatisticsStrategy();
            for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LoginStatisticsDimension.SITE);
                ByteArrayOutputStream destination = new ByteArrayOutputStream();
                try {
                    new JsonWriter().write(statisticsResult, new Destination(destination));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.SITE), destination.toString());
            }
        }
    }

    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByHourHeat() throws SQLException {
        ensureOriginTableValid();
        if (!originTable.isEmpty()) {
            StatisticsStrategy statisticsStrategy = new HourStatisticsStrategy();
            for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LoginStatisticsDimension.HOUR);
                ByteArrayOutputStream destination = new ByteArrayOutputStream();
                try {
                    new JsonWriter().write(statisticsResult, new Destination(destination));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.HOUR), destination.toString());
            }
        }
    }

    /**
     * 将统计数据写入缓存
     *
     * @throws SQLException
     */
    @Async
//    @Scheduled(cron = STATISTICS_CRON)
    public void statisticsByDay() throws SQLException {
        ensureOriginTableValid();
        if (!originTable.isEmpty()) {

            for (LoginStatisticsPeriod period : LoginStatisticsPeriod.values()) {
                DateDimensionStatisticsStrategy statisticsStrategy;
                switch (period){
                    case YESTERDAY:
                        statisticsStrategy = new YesterdayStatisticsStrategy();
                        break;
                    case LAST_WEEK:
                        statisticsStrategy = new WeekStatisticsStrategy();
                        break;
                    case LAST_MONTH:
                        statisticsStrategy = new MonthStatisticsStrategy();
                        break;
                    case YEAR:
                        statisticsStrategy = new YearStatisticsStrategy();
                        break;
                    default:
                        statisticsStrategy = new MonthStatisticsStrategy();
                }
                Table statisticsResult = statisticsStrategy.statistics(originTable, period, LoginStatisticsDimension.DATE);
                ByteArrayOutputStream destination = new ByteArrayOutputStream();
                try {
                    new JsonWriter().write(statisticsResult, new Destination(destination));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                redisTemplate.opsForValue().set(getKey(period, LoginStatisticsDimension.DATE), destination.toString());
            }

        }
    }


}
