package cn.dahe.cas.auth.statistics.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/21 14:50
 * @Description:
 */
public class DateUtils {

    public static Date todayBegin() {
        return DateUtil.beginOfDay(DateUtil.date()).toJdkDate();
    }

    public static Date yesterdayBegin() {
        return DateUtil.beginOfDay(DateUtil.yesterday()).toJdkDate();
    }

    public static Date yesterdayEnd() {
        return DateUtil.endOfDay(DateUtil.yesterday()).toJdkDate();
    }

    public static Date lastWeekBegin() {
        return DateUtil.beginOfDay(DateUtil.beginOfWeek(new Date())).offset(DateField.WEEK_OF_MONTH, -1);
    }

    public static Date lastWeekEnd() {
        return DateUtil.endOfDay(DateUtil.endOfWeek(new Date())).offset(DateField.WEEK_OF_MONTH, -1);
    }

    public static Date monthBegin() {
        return DateUtil.beginOfMonth(new Date());
    }

    public static Date yearBegin() {
        return DateUtil.beginOfYear(new Date());
    }

    public static Date lastMonthBegin() {
        return DateUtil.beginOfDay(DateUtil.beginOfMonth(new Date())).offset(DateField.MONTH, -1);
    }

    public static Date lastMonthEnd() {
        return DateUtil.endOfDay(DateUtil.endOfMonth(new Date())).offset(DateField.MONTH, -1);
    }
}
