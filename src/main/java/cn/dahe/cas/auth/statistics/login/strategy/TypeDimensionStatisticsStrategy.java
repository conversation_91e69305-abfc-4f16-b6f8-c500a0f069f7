package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.constants.LoginType;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsDimension;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import java.util.List;

import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:35
 * @Description: 可望文生义，以登录类型为维度进行统计
 */
public class TypeDimensionStatisticsStrategy extends PeriodStatisticsStrategy {
    @Override
    public Table statistics(Table table, LoginStatisticsPeriod period, LoginStatisticsDimension dimension) {
        Table periodTable = super.statistics(table, period, dimension);
        if(periodTable.rowCount()==0){
            return periodTable;
        }
        periodTable = periodTable.summarize("uid", AggregateFunctions.count)
                .by("type")
                .setName("statistic by type");
        periodTable.column(aggregateColumnName("uid", ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        //对类型字段进行处理，由类型code映射到类型名字并添加新列
        StringColumn typeNameColumn = StringColumn.create("type");
        periodTable.intColumn("type").forEach(value -> {
            LoginType loginType = LoginType.getByType(value);
            if (loginType == null) {
                typeNameColumn.appendMissing();
            } else {
                typeNameColumn.append(loginType.getDescription());
            }
        });
        periodTable.removeColumns("type");
        periodTable.addColumns(typeNameColumn);
        return periodTable;
    }

    @Override
    public List<AggregationOperation> statistics(LoginStatisticsPeriod period){
        List<AggregationOperation> aggregation = super.statistics(period);
        aggregation.add(
                group("type").count().as("count")
                        .first("type").as("loginType")
        );
        aggregation.add(sort(DESC, "count"));
        return aggregation;
    }
}
