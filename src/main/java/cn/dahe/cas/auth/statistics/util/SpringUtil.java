package cn.dahe.cas.auth.statistics.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/21 17:02
 * @Description:
 */
@Component
public class SpringUtil implements ApplicationContextAware{
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
       this.applicationContext = applicationContext;
    }

    public static <T> T getBean(Class<T> type){
        if(applicationContext==null){
            throw new RuntimeException("请等待spring初始化完成");
        }
        return applicationContext.getBean(type);
    }
}
