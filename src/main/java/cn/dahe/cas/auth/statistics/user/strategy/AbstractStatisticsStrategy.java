package cn.dahe.cas.auth.statistics.user.strategy;

import cn.dahe.cas.auth.statistics.user.constant.UserStatisticsDimension;
import tech.tablesaw.api.Table;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/22 14:48
 * @Description: 模板模式
 */
public abstract class AbstractStatisticsStrategy implements StatisticsStrategy{

    @Override
    public final Table statistics(Table table, UserStatisticsDimension dimension) {
        Table group = group(table,dimension);
        return postProcess(group,dimension);
    }

    protected abstract Table group(Table table,UserStatisticsDimension dimension);

    protected abstract Table postProcess(Table table,UserStatisticsDimension dimension);
}
