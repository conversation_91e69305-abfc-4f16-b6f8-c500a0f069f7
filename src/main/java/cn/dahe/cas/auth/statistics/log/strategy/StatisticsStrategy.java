package cn.dahe.cas.auth.statistics.log.strategy;

import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsDimension;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.api.Table;

import java.util.List;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:31
 * @Description:
 */
public interface StatisticsStrategy {
    /**
     * 通过维度、时间段、以及数据源进行统计
     *
     * @param table     数据源
     * @param period    时间段
     * @param dimension 维度
     * @return 统计结果
     */
    Table statistics(Table table, LogStatisticsPeriod period, LogStatisticsDimension dimension);

    /**
     * 通过维度、时间段进行统计
     * @param period    时间段
     * @return 统计结果
     */
    List<AggregationOperation> statistics(LogStatisticsPeriod period);
}
