package cn.dahe.cas.auth.statistics.log.constant;

import cn.dahe.cas.auth.constants.EnumInterface;

/**
 * <AUTHOR>
 * Date: 2019/8/14 15:25
 * Description:
 */
public enum LogStatisticsPeriod implements EnumInterface {
    YESTERDAY(1, "yesterday", "昨天"),
    LAST_WEEK(2, "week", "上周"),
    MONTH(3, "month", "本月"),
    LAST_MONTH(4, "last_month", "上月"),
    YEAR(5, "year", "今年");
    private int code;
    private String key;
    private String description;

    LogStatisticsPeriod(int code, String key, String description) {
        this.code = code;
        this.key = key;
        this.description = description;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

}
