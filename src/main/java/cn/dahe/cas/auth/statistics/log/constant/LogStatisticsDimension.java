package cn.dahe.cas.auth.statistics.log.constant;

import static cn.dahe.cas.auth.statistics.log.constant.DimensionKey.STATISTICS_BY_API_KEY;
import static cn.dahe.cas.auth.statistics.log.constant.DimensionKey.STATISTICS_BY_DAY_KEY;
import static cn.dahe.cas.auth.statistics.log.constant.DimensionKey.STATISTICS_HOUR_HEAT;
import static cn.dahe.cas.auth.statistics.log.constant.DimensionKey.STATISTICS_SITE_RANK;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:38
 * @Description:
 */
public enum LogStatisticsDimension {
    DATE(STATISTICS_BY_DAY_KEY, "天", "date"),
    SITE(STATISTICS_SITE_RANK,"站点排行","rank"),
    HOUR(STATISTICS_HOUR_HEAT, "小时热力", "hour"),
    API(STATISTICS_BY_API_KEY, "接口次数", "api")
    ;
    private String key;
    private String description;
    private String name;

    LogStatisticsDimension(String key, String description, String name) {
        this.key = key;
        this.description = description;
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
