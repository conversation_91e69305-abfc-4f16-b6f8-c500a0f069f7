package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import java.time.ZoneId;
import java.util.function.Function;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 15:34
 * @Description:
 */
public class WeekStatisticsStrategy extends DateDimensionStatisticsStrategyAdapter {

    private static final String DIMENSION_UNIT_NAME = "星期";
    private static final String GROUP_FIELD = "day";

    @Override
    Table group(Table table, LoginStatisticsPeriod period) {
        table = table
                .summarize("uid", AggregateFunctions.count)
                .by(table.instantColumn(LOGIN_TIME_FIELD).asLocalDateTimeColumn(ZoneId.of("GMT+8")).dayOfWeekValue().setName(GROUP_DIMENSION_FIELD), table.intColumn("type"))
                .setName("statistic by day");
        return table;
    }

    @Override
    Table postProcess(Table table, LoginStatisticsPeriod period) {
        //排序
        table.sortAscendingOn(GROUP_DIMENSION_FIELD);
        //维度格式定制
        return table.addColumns(table.intColumn(GROUP_DIMENSION_FIELD).mapInto((Function<Integer, String>) integer -> DIMENSION_UNIT_NAME+integer, StringColumn.create(DIMENSION_FIELD,table.rowCount())));
    }
}
