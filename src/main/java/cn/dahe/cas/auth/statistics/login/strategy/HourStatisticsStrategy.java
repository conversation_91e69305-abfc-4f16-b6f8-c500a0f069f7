package cn.dahe.cas.auth.statistics.login.strategy;

import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsDimension;
import cn.dahe.cas.auth.statistics.login.constant.LoginStatisticsPeriod;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.Table;

import java.time.ZoneId;
import java.util.List;

import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/15 16:54
 * @Description: 按小时统计登录量以分析登录热时段
 */
public class HourStatisticsStrategy extends PeriodStatisticsStrategy{

    @Override
    public Table statistics(Table table, LoginStatisticsPeriod period, LoginStatisticsDimension dimension) {
        Table periodTable = super.statistics(table, period, dimension);
        if(periodTable.isEmpty()){
            return periodTable;
        }
        periodTable = periodTable.summarize("uid", AggregateFunctions.count).by(periodTable.instantColumn(LOGIN_TIME_FIELD).asLocalDateTimeColumn(ZoneId.of("GMT+8")).hourMinute().setName("date"));
        periodTable.column(aggregateColumnName("uid", ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        return periodTable;
    }

    @Override
    public List<AggregationOperation> statistics(LoginStatisticsPeriod period){
        List<AggregationOperation> aggregation = super.statistics(period);
        aggregation.add(
                project("createDate")
                        .andExpression("{$dateToString:{format:'%H:%M',date:'$createDate',timezone:'+0800'}}").as("createDate")
        );
        aggregation.add(
                group("createDate").count().as("count")
                        .first("createDate").as("operateDate")
        );
        aggregation.add(sort(ASC, "operateDate"));
        return aggregation;
    }
}
