package cn.dahe.cas.auth.statistics.log.strategy;

import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsDimension;
import cn.dahe.cas.auth.statistics.log.constant.LogStatisticsPeriod;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.Table;

import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/21 17:36
 * @Description:
 */
public class ApiDimensionStrategy extends PeriodStatisticsStrategy{
    private static final String API_NAME_FIELD = "title";

    @Override
    public Table statistics(Table table, LogStatisticsPeriod period, LogStatisticsDimension dimension) {
        Table periodTable = super.statistics(table, period, dimension);
        if(periodTable.isEmpty()){
            return periodTable;
        }
        periodTable = periodTable.summarize(API_NAME_FIELD, AggregateFunctions.count).by(API_NAME_FIELD);
        periodTable.column(aggregateColumnName(API_NAME_FIELD, ((AggregateFunction) AggregateFunctions.count).functionName())).setName(COUNT_DIMENSION_NAME);
        return periodTable.sortDescendingOn("count");
    }

    @Override
    public List<AggregationOperation> statistics(LogStatisticsPeriod period){
        List<AggregationOperation> aggregation = super.statistics(period);
        aggregation.add(
                group(API_NAME_FIELD).count().as(COUNT_DIMENSION_NAME)
                .first(API_NAME_FIELD).as(API_NAME_FIELD)
        );
        aggregation.add(sort(Sort.Direction.DESC, COUNT_DIMENSION_NAME));
        return aggregation;
    }
}
