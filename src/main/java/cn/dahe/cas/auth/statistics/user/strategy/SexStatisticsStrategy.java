package cn.dahe.cas.auth.statistics.user.strategy;

import cn.dahe.cas.auth.constants.Sex;
import cn.dahe.cas.auth.statistics.user.constant.UserStatisticsDimension;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/22 14:33
 * @Description:
 */
public class SexStatisticsStrategy extends AbstractStatisticsStrategyAdapter{

    private static final String GROUP_FILED = "sex";

    @Override
    protected Table group(Table table, UserStatisticsDimension dimension) {
        Table sexGroupTable = table.summarize(GROUP_FILED, AggregateFunctions.count).by(GROUP_FILED);
        sexGroupTable.column(aggregateColumnName(GROUP_FILED, ((AggregateFunction) AggregateFunctions.count).functionName())).setName("count");
        return sexGroupTable;
    }

    @Override
    protected Table postProcess(Table table, UserStatisticsDimension dimension) {
        StringColumn sexNameColumn = StringColumn.create("sex");
        table.shortColumn(GROUP_FILED).forEach(integer -> {
            Sex sex = Sex.get(integer);
            if(sex==null){
                sexNameColumn.appendMissing();
            }else {
                sexNameColumn.append(sex.getDes());
            }
        });
        table.removeColumns(GROUP_FILED);
        table.addColumns(sexNameColumn);
        return table;
    }
}
