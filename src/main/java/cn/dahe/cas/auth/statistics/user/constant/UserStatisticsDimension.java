package cn.dahe.cas.auth.statistics.user.constant;

import static cn.dahe.cas.auth.statistics.user.constant.DimensionKey.*;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/22 14:13
 * @Description:
 */
public enum UserStatisticsDimension {
    SEX(STATISTICS_BY_SEX_KEY, "性别", "sex"),
    JOB(STATISTICS_BY_JOB_KEY,"职务","job"),
    REGISTER_DATE(STATISTICS_BY_REGISTER_DATE_KEY,"注册时间","register_date"),
    SITE(STATISTICS_BY_SITE_KEY,"站点","site"),
    AREA(STATISTICS_BY_AREA_KEY,"区划","area")
    ;
    private String key;
    private String description;
    private String name;

    UserStatisticsDimension(String key, String description, String name) {
        this.key = key;
        this.description = description;
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
