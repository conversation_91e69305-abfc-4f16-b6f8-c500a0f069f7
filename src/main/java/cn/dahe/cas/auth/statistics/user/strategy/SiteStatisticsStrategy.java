package cn.dahe.cas.auth.statistics.user.strategy;

import cn.dahe.cas.auth.dto.SiteDto;
import cn.dahe.cas.auth.service.SiteTreeService;
import cn.dahe.cas.auth.statistics.annotation.StatisticsStrategy;
import cn.dahe.cas.auth.statistics.user.constant.UserStatisticsDimension;
import org.springframework.beans.factory.annotation.Autowired;
import tech.tablesaw.aggregate.AggregateFunction;
import tech.tablesaw.aggregate.AggregateFunctions;
import tech.tablesaw.api.StringColumn;
import tech.tablesaw.api.Table;

import static tech.tablesaw.table.TableSliceGroup.aggregateColumnName;

/**
 * @Author: 杨振雨
 * @Date: 2019/8/22 16:01
 * @Description:
 */
@StatisticsStrategy
public class SiteStatisticsStrategy extends AbstractStatisticsStrategyAdapter{

    private static final String GROUP_FILED = "department";

    @Autowired
    private SiteTreeService siteTreeService;

    @Override
    protected Table group(Table table, UserStatisticsDimension dimension) {
        Table siteGroupTable = table.summarize(GROUP_FILED, AggregateFunctions.count).by(GROUP_FILED);
        siteGroupTable.column(aggregateColumnName(GROUP_FILED, ((AggregateFunction) AggregateFunctions.count).functionName())).setName(DEFAULT_METRICS_NAME);
        return siteGroupTable;
    }

    @Override
    protected Table postProcess(Table table, UserStatisticsDimension dimension) {
        StringColumn siteColumn = StringColumn.create("site");
        table.stringColumn(GROUP_FILED).forEach(department -> {
            SiteDto site = siteTreeService.getSite(department);
            if(site==null){
                siteColumn.append(department);
            }else {
                siteColumn.append(site.getSiteName());
            }
        });
        table.removeColumns(GROUP_FILED);
        table.addColumns(siteColumn);
        return table.sortDescendingOn(DEFAULT_METRICS_NAME);
    }
}
