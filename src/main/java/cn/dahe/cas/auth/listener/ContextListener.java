package cn.dahe.cas.auth.listener;

import cn.dahe.cas.auth.constants.EntityOperation;
import cn.dahe.cas.auth.constants.RolePermissionNames;
import cn.dahe.cas.auth.constants.Status;
import cn.dahe.cas.auth.constants.UserType;
import cn.dahe.cas.auth.entity.*;
import cn.dahe.cas.auth.event.SiteEvent;
import cn.dahe.cas.auth.external.entity.SsoUser;
import cn.dahe.cas.auth.external.repository.SsoUserRepository;
import cn.dahe.cas.auth.service.LogService;
import cn.dahe.cas.auth.service.ResourceService;
import cn.dahe.cas.auth.service.RoleService;
import cn.dahe.cas.auth.service.SiteService;
import cn.dahe.cas.auth.service.TicketService;
import cn.dahe.cas.auth.service.UserRoleService;
import cn.dahe.cas.auth.service.UserService;
import cn.dahe.cas.auth.util.Sm4Util;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class ContextListener implements ApplicationListener<ContextRefreshedEvent>{

    private static final Logger log = LoggerFactory.getLogger(ContextListener.class);

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private LogService logService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private SsoUserRepository ssoUserRepository;

    @Autowired
    private ConversionService conversionService;

    @Qualifier("ssoThreadPoolTaskExecutor")
    @Autowired
    private TaskExecutor taskExecutor;

    @SuppressWarnings("SpringJavaAutowiringInspection")
    @javax.annotation.Resource(name = "redisTemplate")
    private ValueOperations<String,Integer> redisTemplate;

    @Autowired
    private RedisTemplate cacheTemplate;

    @Autowired
    private TicketService ticketService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${init.trueName}")
    private String trueName;

    @Value("${init.phone}")
    private String phone;

    @Value("${log.count}")
    private String logCount;
    @Value("${log.date}")
    private String logDate;
    @Value("${log.switch}")
    private String logSwitch;

    @Value("${init.logcount}")
    private int initLogCount;
    @Value("${init.logdate}")
    private int initLogDate;
    @Value("${init.logswitch}")
    private int initLogSwitch;
    @Value("${sm4.security.key}")
    private String sm4Key;


    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        if(contextRefreshedEvent.getApplicationContext().getParent()==null){
            //对fastjson的enum序列化进行处理，输出oridinal值
            JSON.DEFAULT_GENERATE_FEATURE &= ~SerializerFeature.WriteEnumUsingName.mask;
            try {
                //addAdmin();
            } catch (Exception e) {
                e.printStackTrace();
            }
            addDefaultSite();
            ensureService();
            //setProxy();
            logCondition();
            //cleanInvalidateTicket();
            //ticketService.fix();
            // 用于清洗用户名和手机号 加密 todo 一次过后注释
            //sm4User();
            // 用于清洗用户相关的缓存 todo 一次过后注释
            //用户量较小，可以直接全部读取
//            taskExecutor.execute(() -> {
//                ensureExternalUserCache();
//            });
        }
    }

    /**
     * 添加默认站点
     */
    private void addDefaultSite(){
        log.debug("添加默认站点");
        siteService.addDefaultSites();
    }

    private void ensureService(){
        log.debug("从数据库添加服务到redis");
        List<Site> sites = siteService.get();
        if(sites==null){
            return;
        }
        for(Site site:sites){
            publisher.publishEvent(new SiteEvent(this,site, EntityOperation.ADD));
        }
    }

    private void addAdmin() {
        // todo 加密
        try {
            phone = Sm4Util.encryptEcb(sm4Key, phone);
            trueName = Sm4Util.encryptEcb(sm4Key, trueName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.debug("添加初始管理员");
        Role role = roleService.findBySiteIdAndSn(72, "admin");
        if(role==null){
            role = new Role();
            role.setName(RolePermissionNames.ADMIN_NAME);
            role.setSn(RolePermissionNames.ADMIN_ROLE);
            role.setSiteId(1);
            role.setSiteName("local");
            role.setStatus(Status.ALLOW);
            role = roleService.add(role);
            Resource resource = new Resource();
            resource.setName(RolePermissionNames.SUPER_RESOURCE);
            resourceService.add(resource);
            resourceService.grantToRole(role,resource);
        }
        User user = userService.getUserByPhone(phone);
        if(user==null){
            user = new User();
            user.setPhone(phone);
            user.setTruename(trueName);
            user.setType(UserType.MANAGER);
            user=userService.add(user);
        }
        UserRole userRole = userRoleService.getOne(user.getUid(), role.getId());
        if(userRole == null){
            userRoleService.add(UserRole.builder().roleId(role.getId()).uid(user.getUid()).build());
        }

    }

//    //设置代理，解决外部接口无法访问问题
//    private void setProxy(){
//        System.setProperty("http.proxySet", "true");
//        System.setProperty("http.proxyHost", "**********");
//        System.setProperty("http.proxyPort", "" + "24680");
//        // 针对https也开启代理
//        System.setProperty("https.proxyHost", "**********");
//        System.setProperty("https.proxyPort", "" + "24680");
//        System.setProperty("https.noProxyHosts", "127.0.0.1|  *.aliyuncs.com| *-beta.dahe.cn");
//        //System.setProperty("http.noProxyHosts", "127.0.0.1|  *.aliyuncs.com| *-beta.dahe.cn");
//    }
    /**
     * 初始化清理日志的条件（保留的天数和保留的数量）
     */
    private void logCondition(){
        if (redisTemplate.get(logCount) == null){
            redisTemplate.set(logCount,initLogCount);
        }
        if (redisTemplate.get(logDate) == null){
            redisTemplate.set(logDate, initLogDate);
        }
        if (redisTemplate.get(logSwitch) == null){
            redisTemplate.set(logSwitch, initLogSwitch);
        }
    }

    private void cleanInvalidateTicket(){
        Set<String> tgts =  ticketService.tgtKeys();
        tgts.forEach(s -> {
            try {
                ticketService.getTgt(s);
            }catch (Exception e){
                ticketService.removeTgt(s);
            }
        });
    }

    private void sm4User(){
        List<User> userList = userService.get();
        userList.forEach(user -> {
            try {
                user.setPhone(Sm4Util.encryptEcb(sm4Key, user.getPhone()));
                user.setTruename(Sm4Util.encryptEcb(sm4Key, user.getTruename()));
                userService.update(user);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public void clearCache(){
        Set singleKeys = cacheTemplate.keys("sso:single_user:info*");
        cacheTemplate.delete(singleKeys);
        Set departmentKeys = cacheTemplate.keys("sso:department:users*");
        cacheTemplate.delete(departmentKeys);
        Set siteKeys = cacheTemplate.keys("sso:site:users*");
        cacheTemplate.delete(siteKeys);
        Set pageKeys = cacheTemplate.keys("sso:page_user:info*");
        cacheTemplate.delete(pageKeys);
        Set attributeKeys = cacheTemplate.keys("sso:person:attribute*");
        cacheTemplate.delete(attributeKeys);
    }

    private void ensureExternalUserCache() {
        ssoUserRepository.clear();
        List<User> users = userService.get();
        users.forEach(user -> {
            try {
                user.setPhone(Sm4Util.decryptEcb(sm4Key, user.getPhone()));
                user.setTruename(Sm4Util.decryptEcb(sm4Key, user.getTruename()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            ssoUserRepository.add(conversionService.convert(user, SsoUser.class));
        });
    }
}
