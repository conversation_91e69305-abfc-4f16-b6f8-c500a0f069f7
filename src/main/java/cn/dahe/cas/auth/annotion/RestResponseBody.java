package cn.dahe.cas.auth.annotion;

import java.lang.annotation.*;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RestResponseBody {
    boolean jsonp() default false;

    /**
     * 回调函数名
     * @return
     */
    String callback() default "success_jsonpCallback";

    /**
     * 允许调用该jsonp接口的referers
     * @return
     */
    String[] referers() default {};
}
