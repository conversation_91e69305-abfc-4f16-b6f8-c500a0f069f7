package cn.dahe.cas.auth.annotion;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.constraintvalidation.SupportedValidationTarget;
import javax.validation.constraintvalidation.ValidationTarget;

@SupportedValidationTarget(ValidationTarget.PARAMETERS)
public class EqualParametersValidator implements ConstraintValidator<EqualParameters,Object[]>{

    @Override
    public void initialize(EqualParameters constraintAnnotation) {

    }

    @Override
    public boolean isValid(Object[] value, ConstraintValidatorContext context) {
        if(value[0]==null||value[1]==null){
            return true;
        }
        if(value[0] instanceof String&&value[1] instanceof String){
            String first = (String) value[0];
            String second = (String) value[1];
            return first.equals(second);
        }
        return false;
    }
}
