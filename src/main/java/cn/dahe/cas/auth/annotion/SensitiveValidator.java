package cn.dahe.cas.auth.annotion;

import cn.dahe.cas.auth.service.WordService;
import cn.dahe.cas.auth.util.InjectUtil;
import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class SensitiveValidator implements ConstraintValidator<Sensitive,String>{

    private WordService wordService;



    @Override
    public void initialize(Sensitive sensitive) {
        wordService = InjectUtil.getWordService();
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        if(SsoStringUtil.isBlank(s)){
            return true;
        }
        return !wordService.sensitive(s);
    }
}
