package cn.dahe.cas.auth.annotion;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AccessLimit {
    /**
     * 允许访问的次数
     * @return
     */
    int count() default 3;

    /**
     * 时间间隔,单位为秒
     */
    long time() default 60;

    /**
     *  用到注解的名称
     */
    String title() default "";

    /**
     * 禁用时长,单位为秒
     */
    long limitTime() default 60;

    /**
     * 除ip外的其他限定参数，比如用户名等
     */
    String[] limitParams() default {};

    /**
     * 访问超频提示
     * @return
     */
    String msg() default "";
}
