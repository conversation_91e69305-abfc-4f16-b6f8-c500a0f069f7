package cn.dahe.cas.auth.annotion;

import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 */
public class UsernameValidator implements ConstraintValidator<Username,String> {

    @Override
    public void initialize(Username constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if(StringUtils.isEmpty(value)){
            return true;
        }
        if(value.matches("^\\d+.*$")){
            return false;
        }
        return true;
    }
}
