package cn.dahe.cas.auth.annotion;

import cn.dahe.cas.auth.util.SsoStringUtil;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 密码校验
 * <AUTHOR>
 */
public class PasswordValidator implements ConstraintValidator<Password,String>{

    @Override
    public void initialize(Password constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if(StringUtils.isEmpty(value)){
            return true;
        }
        boolean same = SsoStringUtil.isAllSame(value);
        boolean allNumber = SsoStringUtil.isNumeric(value);
        return !same&!allNumber;
    }
}
